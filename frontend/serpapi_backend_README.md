# Backend SerpAPI per Astra Meccanica

Questo backend funge da proxy tra il frontend di Astra Meccanica e l'API di SerpAPI, risolvendo il problema CORS.

## Requisiti

- Python 3.7 o superiore
- pip (gestore pacchetti Python)

## Installazione

1. Installa le dipendenze necessarie:

```bash
pip install flask flask-cors google-search-results
```

2. Avvia il server:

```bash
python serpapi_backend.py
```

Il server sarà in ascolto su `http://localhost:5000`.

## Endpoint API

### GET /api/serpapi

Questo endpoint accetta i seguenti parametri di query:

- `q`: Il nome dell'azienda da cercare

Esempio di richiesta:

```
GET http://localhost:5000/api/serpapi?q=Mandelli+S.R.L.
```

La risposta sarà un oggetto JSON contenente i risultati della ricerca di SerpAPI.

## Integrazione con il Frontend

Il frontend è già configurato per utilizzare questo backend. Il servizio `serpApiService.ts` fa richieste a `http://localhost:5000/api/serpapi` invece che direttamente a SerpAPI.

## Note

- La chiave API di SerpAPI è hardcoded nel backend. In un ambiente di produzione, dovresti utilizzare variabili d'ambiente o un file di configurazione.
- Il server è configurato per essere in modalità debug. In un ambiente di produzione, dovresti disabilitare la modalità debug.