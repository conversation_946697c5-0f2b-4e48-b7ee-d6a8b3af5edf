import { useState, useEffect } from 'react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { UserPlus, RefreshCw, Mail, Key, Shield, Calendar, UserCog, Trash2, Edit, Loader2 } from 'lucide-react'
import { userService, User as UserType, CreateUserData } from '@/services/userService'
import { toast } from 'sonner'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'


export default function Users() {
  // Stato per gli utenti
  const [users, setUsers] = useState<UserType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<UserType | null>(null)
  
  // Form state
  const [formData, setFormData] = useState<CreateUserData>({
    email: '',
    name: '',
    password: '',
    role: 'user'
  })
  
  // Carica gli utenti al mount
  useEffect(() => {
    loadUsers()
  }, [])
  
  const loadUsers = async () => {
    setIsLoading(true)
    try {
      const loadedUsers = await userService.getAllUsers()
      setUsers(loadedUsers)
    } catch (error) {
      toast.error('Errore nel caricamento degli utenti')
    } finally {
      setIsLoading(false)
    }
  }

  // Stato per la modale di reset password
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false)
  const [_selectedUserId, setSelectedUserId] = useState<string | null>(null)
  const [newPassword, setNewPassword] = useState('')

  // Crea un nuovo utente
  const handleCreateUser = async () => {
    try {
      await userService.createUser(formData)
      toast.success('Utente creato con successo')
      setIsCreateDialogOpen(false)
      setFormData({ email: '', name: '', password: '', role: 'user' })
      loadUsers()
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Errore nella creazione dell\'utente')
    }
  }
  
  // Aggiorna un utente
  const handleUpdateUser = async () => {
    if (!editingUser) return
    
    try {
      const updateData: any = {
        name: formData.name,
        role: formData.role
      }
      if (formData.password) {
        updateData.password = formData.password
      }
      
      await userService.updateUser(editingUser.id, updateData)
      toast.success('Utente aggiornato con successo')
      setIsEditDialogOpen(false)
      setEditingUser(null)
      setFormData({ email: '', name: '', password: '', role: 'user' })
      loadUsers()
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Errore nell\'aggiornamento dell\'utente')
    }
  }
  
  // Elimina un utente
  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Sei sicuro di voler eliminare questo utente?')) return
    
    try {
      await userService.deleteUser(userId)
      toast.success('Utente eliminato con successo')
      loadUsers()
    } catch (error) {
      toast.error('Errore nell\'eliminazione dell\'utente')
    }
  }

  // Apre la modale di reset password
  const handleOpenResetDialog = (userId: string) => {
    setSelectedUserId(userId)
    setNewPassword(generateRandomPassword())
    setIsResetDialogOpen(true)
  }
  
  // Apre la modale di modifica
  const handleOpenEditDialog = (user: UserType) => {
    setEditingUser(user)
    setFormData({
      email: user.email,
      name: user.name,
      password: '',
      role: user.role
    })
    setIsEditDialogOpen(true)
  }

  // Genera una password casuale
  const generateRandomPassword = (): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }

  // Conferma il reset della password
  const handleConfirmReset = async () => {
    if (!_selectedUserId) return
    
    try {
      await userService.updateUser(_selectedUserId, { password: newPassword })
      toast.success('Password resettata con successo')
      setIsResetDialogOpen(false)
      setSelectedUserId(null)
      setNewPassword('')
    } catch (error) {
      toast.error('Errore nel reset della password')
    }
  }

  // Ottiene il colore del ruolo
  const getRoleBadgeVariant = (role: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (role) {
      case 'admin': return 'destructive'
      case 'analyst': return 'secondary'
      default: return 'default'
    }
  }

  // Traduce il ruolo
  const translateRole = (role: string): string => {
    switch (role) {
      case 'admin': return 'Amministratore'
      case 'analyst': return 'Analista'
      default: return role
    }
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Gestione Utenti</h1>
          <div className='flex items-center space-x-2'>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <UserPlus className="mr-2 h-4 w-4" />
              Nuovo Utente
            </Button>
            <Button variant="outline" onClick={loadUsers}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Aggiorna
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Utenti</CardTitle>
            <CardDescription>
              Gestisci gli utenti dell'applicazione e i loro permessi
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Ruolo</TableHead>
                    <TableHead>Ultimo Accesso</TableHead>
                    <TableHead>Abilitato</TableHead>
                    <TableHead className="text-right">Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto" />
                      </TableCell>
                    </TableRow>
                  ) : users.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        Nessun utente trovato
                      </TableCell>
                    </TableRow>
                  ) : (
                    users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <UserCog className="mr-2 h-4 w-4 text-muted-foreground" />
                          {user.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                          {user.email}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getRoleBadgeVariant(user.role)}>
                          {translateRole(user.role)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {user.lastLogin ? (
                          <div className="flex items-center">
                            <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                            {new Date(user.lastLogin).toLocaleDateString('it-IT')}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Mai</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          Attivo
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleOpenEditDialog(user)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleOpenResetDialog(user.id)}
                          >
                            <Key className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteUser(user.id)}
                            disabled={user.email === '<EMAIL>'}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Modale Crea Utente */}
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Nuovo Utente</DialogTitle>
              <DialogDescription>
                Inserisci i dati del nuovo utente
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="create-name">Nome</Label>
                <Input
                  id="create-name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Nome completo"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="create-email">Email</Label>
                <Input
                  id="create-email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="create-password">Password</Label>
                <Input
                  id="create-password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  placeholder="Password sicura"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="create-role">Ruolo</Label>
                <Select value={formData.role} onValueChange={(value) => setFormData({ ...formData, role: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona ruolo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">Utente</SelectItem>
                    <SelectItem value="admin">Amministratore</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Annulla
              </Button>
              <Button onClick={handleCreateUser}>
                Crea Utente
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Modale Modifica Utente */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Modifica Utente</DialogTitle>
              <DialogDescription>
                Modifica i dati dell'utente
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Nome</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Nome completo"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-email">Email</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={formData.email}
                  disabled
                  className="opacity-50"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-password">Nuova Password (opzionale)</Label>
                <Input
                  id="edit-password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  placeholder="Lascia vuoto per non cambiarla"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-role">Ruolo</Label>
                <Select value={formData.role} onValueChange={(value) => setFormData({ ...formData, role: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona ruolo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">Utente</SelectItem>
                    <SelectItem value="admin">Amministratore</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Annulla
              </Button>
              <Button onClick={handleUpdateUser}>
                Salva Modifiche
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Modale Reset Password */}
        <Dialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Reset Password</DialogTitle>
              <DialogDescription>
                Stai per reimpostare la password dell'utente. La nuova password generata è:
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="new-password" className="flex items-center">
                  <Shield className="mr-2 h-4 w-4" />
                  Nuova Password
                </Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="new-password"
                    value={newPassword}
                    readOnly
                    className="font-mono"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setNewPassword(generateRandomPassword())}
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  Assicurati di comunicare questa password all'utente in modo sicuro.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsResetDialogOpen(false)}>
                Annulla
              </Button>
              <Button onClick={handleConfirmReset}>
                Conferma Reset
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Utenti',
    href: '/users',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Ruoli',
    href: '/users/roles',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Permessi',
    href: '/users/permissions',
    isActive: false,
    disabled: false,
  },
]