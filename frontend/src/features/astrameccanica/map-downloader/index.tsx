import { useState, useEffect } from 'react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Download, MapPin, RefreshCw, Loader2, Map, Globe, FileImage, Maximize2, Eye, Satellite } from 'lucide-react'
import { toast } from 'sonner'
import { mapDownloadService, MapStyle, DownloadedArea } from '@/services/mapDownloadService'
import { MapPreview } from '@/components/MapPreview'
import { cn } from '@/lib/utils'
import { useMapSettings } from '@/hooks/useMapSettings'


export default function MapDownloader() {
  // Carica le impostazioni salvate
  const { settings, isLoading: isLoadingSettings } = useMapSettings()
  
  // Stato per il form - usa i valori dalle impostazioni come predefiniti
  const [coordinateMethod, setCoordinateMethod] = useState<'coordinates' | 'address'>('address')
  const [swLat, setSwLat] = useState('')
  const [swLng, setSwLng] = useState('')
  const [neLat, setNeLat] = useState('')
  const [neLng, setNeLng] = useState('')
  const [address, setAddress] = useState('')
  const [radius, setRadius] = useState('')
  const [style, setStyle] = useState('s')
  const [format, setFormat] = useState<'jpeg' | 'geotiff'>('jpeg')
  const [zoom, setZoom] = useState(18)
  const [isLoading, setIsLoading] = useState(false)
  const [mapStyles, setMapStyles] = useState<MapStyle[]>([])
  
  // Stato per i download
  const [downloadedAreas, setDownloadedAreas] = useState<DownloadedArea[]>([])
  const [isLoadingAreas, setIsLoadingAreas] = useState(false)
  
  // Stato per l'anteprima
  const [previewBbox, setPreviewBbox] = useState<[number, number, number, number] | undefined>()
  const [previewCenter, setPreviewCenter] = useState<{ lat: number; lng: number } | undefined>()
  const [previewRadius, setPreviewRadius] = useState<number | undefined>()
  
  // Aggiorna i valori quando le impostazioni cambiano
  useEffect(() => {
    if (!isLoadingSettings) {
      setStyle(settings.defaultStyle)
      setFormat(settings.defaultFormat as 'jpeg' | 'geotiff')
      setZoom(parseInt(settings.defaultZoom))
    }
  }, [settings, isLoadingSettings])
  
  // Carica gli stili disponibili
  useEffect(() => {
    loadMapStyles()
    loadDownloadedAreas()
  }, [])
  
  const loadMapStyles = async () => {
    const styles = await mapDownloadService.getMapStyles()
    setMapStyles(styles)
  }
  
  const loadDownloadedAreas = async () => {
    setIsLoadingAreas(true)
    try {
      const areas = await mapDownloadService.getDownloadedAreas()
      setDownloadedAreas(areas)
    } catch (_error) {
      // Gestito con fallback UI altrove; nessun log in console per rispettare il linter.
    } finally {
      setIsLoadingAreas(false)
    }
  }
  
  // Dummy data rimosso - ora usiamo downloadedAreas dal backend
  // const downloads = []
  
  // Funzione per mostrare l'anteprima
  const showPreview = async () => {
    let bbox: [number, number, number, number] | undefined
    let center: { lat: number; lng: number } | undefined
    let radiusKm: number | undefined
    
    try {
      if (coordinateMethod === 'address') {
        // Geocodifica l'indirizzo per l'anteprima
        if (!address.trim()) {
          toast.error('Inserisci un indirizzo valido')
          return
        }
        
        const radiusValue = parseFloat(radius)
        if (isNaN(radiusValue) || radiusValue <= 0) {
          toast.error('Inserisci un raggio valido in km')
          return
        }
        
        const geocodeResult = await mapDownloadService.geocodeAddress({
          address: address.trim(),
          radius: radiusValue
        })
        
        if (geocodeResult.success && geocodeResult.location && geocodeResult.bbox) {
          center = { lat: geocodeResult.location.lat, lng: geocodeResult.location.lng }
          radiusKm = radiusValue
          bbox = geocodeResult.bbox
          setPreviewCenter(center)
          setPreviewRadius(radiusKm)
          setPreviewBbox(bbox)
          toast.info(`Indirizzo trovato: ${geocodeResult.location.formatted_address || address}`)
        } else {
          toast.error(geocodeResult.error || 'Impossibile trovare l\'indirizzo')
        }
      } else {
        // Usa le coordinate per l'anteprima
        const minLon = parseFloat(swLng)
        const minLat = parseFloat(swLat)
        const maxLon = parseFloat(neLng)
        const maxLat = parseFloat(neLat)
        
        if (isNaN(minLon) || isNaN(minLat) || isNaN(maxLon) || isNaN(maxLat)) {
          toast.error('Inserisci coordinate valide')
          return
        }
        
        if (minLon >= maxLon || minLat >= maxLat) {
          toast.error('Le coordinate SW devono essere minori delle coordinate NE')
          return
        }
        
        bbox = [minLon, minLat, maxLon, maxLat]
        setPreviewBbox(bbox)
        setPreviewCenter(undefined)
        setPreviewRadius(undefined)
      }
    } catch (_error) {
      toast.error('Errore nella generazione dell\'anteprima')
    }
  }

  // Gestisce il submit del form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Se non c'è un'anteprima, mostrala prima
    if (!previewBbox) {
      await showPreview()
      return
    }
    
    setIsLoading(true)
    
    let bbox: [number, number, number, number]
    
    try {
      if (coordinateMethod === 'address') {
        // Geocodifica l'indirizzo
        if (!address.trim()) {
          toast.error('Inserisci un indirizzo valido')
          setIsLoading(false)
          return
        }
        
        const radiusValue = parseFloat(radius)
        if (isNaN(radiusValue) || radiusValue <= 0) {
          toast.error('Inserisci un raggio valido in km')
          setIsLoading(false)
          return
        }
        
        const geocodeResult = await mapDownloadService.geocodeAddress({
          address: address.trim(),
          radius: radiusValue
        })
        
        if (!geocodeResult.success || !geocodeResult.bbox) {
          toast.error(geocodeResult.error || 'Impossibile trovare l\'indirizzo')
          setIsLoading(false)
          return
        }
        
        bbox = geocodeResult.bbox
      } else {
        // Usa le coordinate inserite manualmente
        const minLon = parseFloat(swLng)
        const minLat = parseFloat(swLat)
        const maxLon = parseFloat(neLng)
        const maxLat = parseFloat(neLat)
        
        if (isNaN(minLon) || isNaN(minLat) || isNaN(maxLon) || isNaN(maxLat)) {
          toast.error('Inserisci coordinate valide')
          setIsLoading(false)
          return
        }
        
        if (minLon >= maxLon || minLat >= maxLat) {
          toast.error('Le coordinate SW devono essere minori delle coordinate NE')
          setIsLoading(false)
          return
        }
        
        bbox = [minLon, minLat, maxLon, maxLat]
      }
      
      // Genera la mappa con il bbox calcolato
      const result = await mapDownloadService.generateMap({
        bbox: bbox,
        zoom: zoom, // Usa il livello zoom selezionato
        style: style,
        format: format
      })
      
      if (result.success) {
        toast.success(result.message)
        // Ricarica la lista delle aree scaricate
        await loadDownloadedAreas()
        
        // Reset del form
        if (coordinateMethod === 'coordinates') {
          setSwLat('')
          setSwLng('')
          setNeLat('')
          setNeLng('')
        } else {
          setAddress('')
          setRadius('')
        }
        
        // Reset preview
        setPreviewBbox(undefined)
        setPreviewCenter(undefined)
        setPreviewRadius(undefined)
      } else {
        toast.error(result.error || 'Errore nel download della mappa')
      }
    } catch (_error) {
      toast.error('Errore di connessione al server')
    } finally {
      setIsLoading(false)
    }
  }

  // Ottiene il nome dello stile
  const getStyleName = (styleId: string): string => {
    const style = mapStyles.find(s => s.id === styleId)
    return style?.name || styleId
  }

  // Scarica una mappa
  const handleDownload = async (area: DownloadedArea) => {
    try {
      // Usa storageKey direttamente dall'area, non dal metadata
      const s3Key = area.storageKey || area.metadata?.s3?.key || area.metadata?.storageKey
      if (!s3Key) {
        toast.error('Chiave di download non trovata')
        return
      }
      const url = await mapDownloadService.downloadMap(s3Key)
      window.open(url, '_blank')
    } catch (_error) {
      toast.error('Errore nel download del file')
    }
  }

  // Formatta la data
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('it-IT')
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-8 flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent'>Download Mappe</h1>
            <p className='text-muted-foreground mt-2'>Scarica mappe ad alta risoluzione per le tue analisi</p>
          </div>
          <Button onClick={loadDownloadedAreas} variant="outline" size="sm" className="gap-2">
            <RefreshCw className={cn("h-4 w-4", isLoadingAreas && "animate-spin")} />
            Aggiorna
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Form di download */}
          <Card className="lg:col-span-1 shadow-lg border-0 bg-gradient-to-br from-background to-muted/20">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-blue-500/10 rounded-lg">
                  <Map className="h-5 w-5 text-blue-500" />
                </div>
                Nuova Area
              </CardTitle>
              <CardDescription>
                Seleziona un'area da scaricare specificando le coordinate o un indirizzo con raggio
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <Tabs value={coordinateMethod} onValueChange={(v) => setCoordinateMethod(v as 'coordinates' | 'address')}>
                  <TabsList className="grid w-full grid-cols-2 bg-muted/50">
                    <TabsTrigger value="address" className="data-[state=active]:bg-background data-[state=active]:shadow-sm">
                      <MapPin className="h-4 w-4 mr-2" />
                      Indirizzo
                    </TabsTrigger>
                    <TabsTrigger value="coordinates" className="data-[state=active]:bg-background data-[state=active]:shadow-sm">
                      <Globe className="h-4 w-4 mr-2" />
                      Coordinate
                    </TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="address" className="space-y-4 pt-4">
                    <div className="space-y-2">
                      <Label htmlFor="address" className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        Indirizzo
                      </Label>
                      <Input 
                        id="address" 
                        placeholder="es. Via Roma 1, Milano" 
                        value={address}
                        onChange={(e) => setAddress(e.target.value)}
                        className="transition-all focus:ring-2 focus:ring-blue-500/20"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="radius" className="flex items-center gap-2">
                        <Maximize2 className="h-4 w-4 text-muted-foreground" />
                        Raggio (km)
                      </Label>
                      <Input 
                        id="radius" 
                        type="number" 
                        placeholder="es. 2" 
                        value={radius}
                        onChange={(e) => setRadius(e.target.value)}
                        className="transition-all focus:ring-2 focus:ring-blue-500/20"
                      />
                      <p className="text-xs text-muted-foreground">Area quadrata con lato = 2 × raggio</p>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="coordinates" className="space-y-4 pt-4">
                    <div className="p-3 bg-muted/50 rounded-lg mb-4">
                      <p className="text-xs text-muted-foreground flex items-center gap-2">
                        <Globe className="h-3 w-3" />
                        Inserisci le coordinate del rettangolo (SW: Sud-Ovest, NE: Nord-Est)
                      </p>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="sw-lat" className="text-sm">Latitudine SW</Label>
                        <Input 
                          id="sw-lat" 
                          placeholder="es. 45.4642" 
                          value={swLat}
                          onChange={(e) => setSwLat(e.target.value)}
                          className="transition-all focus:ring-2 focus:ring-blue-500/20"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="sw-lng" className="text-sm">Longitudine SW</Label>
                        <Input 
                          id="sw-lng" 
                          placeholder="es. 9.1900" 
                          value={swLng}
                          onChange={(e) => setSwLng(e.target.value)}
                          className="transition-all focus:ring-2 focus:ring-blue-500/20"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="ne-lat" className="text-sm">Latitudine NE</Label>
                        <Input 
                          id="ne-lat" 
                          placeholder="es. 45.4842" 
                          value={neLat}
                          onChange={(e) => setNeLat(e.target.value)}
                          className="transition-all focus:ring-2 focus:ring-blue-500/20"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="ne-lng" className="text-sm">Longitudine NE</Label>
                        <Input 
                          id="ne-lng" 
                          placeholder="es. 9.2100" 
                          value={neLng}
                          onChange={(e) => setNeLng(e.target.value)}
                          className="transition-all focus:ring-2 focus:ring-blue-500/20"
                        />
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
                
                <div className="space-y-2">
                  <Label htmlFor="style" className="flex items-center gap-2">
                    <Satellite className="h-4 w-4 text-muted-foreground" />
                    Stile Mappa
                  </Label>
                  <Select value={style} onValueChange={setStyle}>
                    <SelectTrigger id="style" className="transition-all focus:ring-2 focus:ring-blue-500/20">
                      <SelectValue placeholder="Seleziona stile" />
                    </SelectTrigger>
                    <SelectContent>
                      {mapStyles.map((mapStyle) => (
                        <SelectItem key={mapStyle.id} value={mapStyle.id}>
                          <div className="flex items-center gap-2">
                            <div className={cn(
                              "w-4 h-4 rounded-full",
                              mapStyle.id === 's' && "bg-blue-500",
                              mapStyle.id === 'm' && "bg-green-500",
                              mapStyle.id === 'y' && "bg-purple-500",
                              mapStyle.id === 't' && "bg-orange-500",
                              mapStyle.id === 'p' && "bg-amber-500",
                              mapStyle.id === 'h' && "bg-gray-500"
                            )} />
                            {mapStyle.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="zoom" className="flex items-center gap-2">
                    <Maximize2 className="h-4 w-4 text-muted-foreground" />
                    Livello Zoom
                  </Label>
                  <Select value={zoom.toString()} onValueChange={(v) => setZoom(parseInt(v))}>
                    <SelectTrigger id="zoom" className="transition-all focus:ring-2 focus:ring-blue-500/20">
                      <SelectValue placeholder="Seleziona zoom" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 - Città (bassa risoluzione)</SelectItem>
                      <SelectItem value="16">16 - Quartiere</SelectItem>
                      <SelectItem value="17">17 - Strada</SelectItem>
                      <SelectItem value="18">18 - Edifici (consigliato)</SelectItem>
                      <SelectItem value="19">19 - Dettagli edifici</SelectItem>
                      <SelectItem value="20">20 - Alta risoluzione</SelectItem>
                      <SelectItem value="21">21 - Massima risoluzione</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">Zoom più alto = maggiore dettaglio e dimensione file</p>
                </div>
                
                {/* Anteprima mappa */}
                {(previewBbox || previewCenter) && (
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Eye className="h-4 w-4 text-muted-foreground" />
                      Anteprima Area
                    </Label>
                    <div className="rounded-lg overflow-hidden border shadow-sm">
                      <MapPreview 
                        bbox={previewBbox}
                        center={previewCenter}
                        radius={previewRadius}
                        style={style}
                        height="300px"
                      />
                    </div>
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="format" className="flex items-center gap-2">
                    <FileImage className="h-4 w-4 text-muted-foreground" />
                    Formato Output
                  </Label>
                  <Select value={format} onValueChange={(v) => setFormat(v as 'jpeg' | 'geotiff')}>
                    <SelectTrigger id="format" className="transition-all focus:ring-2 focus:ring-blue-500/20">
                      <SelectValue placeholder="Seleziona formato" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jpeg">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">JPEG</Badge>
                          <span>Più leggero</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="geotiff">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">GeoTIFF</Badge>
                          <span>Georeferenziato</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    type="button" 
                    variant="outline" 
                    className="flex-1 transition-all hover:shadow-md" 
                    onClick={showPreview}
                    disabled={isLoading}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Anteprima
                  </Button>
                  <Button 
                    type="submit" 
                    className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 transition-all hover:shadow-md" 
                    disabled={isLoading || !previewBbox}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Download...
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-4 w-4" />
                        {previewBbox ? 'Scarica Area' : 'Mostra Prima'}
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Lista download */}
          <Card className="lg:col-span-2 shadow-lg border-0">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <div className="p-2 bg-purple-500/10 rounded-lg">
                      <Download className="h-5 w-5 text-purple-500" />
                    </div>
                    Download Recenti
                  </CardTitle>
                  <CardDescription>
                    {downloadedAreas.length} {downloadedAreas.length === 1 ? 'area scaricata' : 'aree scaricate'}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px] pr-4">
                <Table>
                  <TableHeader>
                    <TableRow className="hover:bg-transparent">
                      <TableHead className="font-semibold">Area</TableHead>
                      <TableHead className="font-semibold">Stile</TableHead>
                      <TableHead className="font-semibold">Zoom</TableHead>
                      <TableHead className="font-semibold">Formato</TableHead>
                      <TableHead className="font-semibold">Data</TableHead>
                      <TableHead className="text-right font-semibold">Azioni</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoadingAreas ? (
                      [...Array(3)].map((_, i) => (
                        <TableRow key={i}>
                          <TableCell><Skeleton className="h-4 w-full" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-20 ml-auto" /></TableCell>
                        </TableRow>
                      ))
                    ) : downloadedAreas.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-16">
                          <div className="flex flex-col items-center gap-2 text-muted-foreground">
                            <Map className="h-12 w-12 opacity-30" />
                            <p className="font-medium">Nessuna area scaricata</p>
                            <p className="text-sm">Inizia scaricando la tua prima mappa</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      downloadedAreas.map((area) => (
                        <TableRow key={area.id} className="hover:bg-muted/50 transition-colors">
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-2">
                              <div className="p-1 bg-blue-500/10 rounded">
                                <MapPin className="h-3 w-3 text-blue-500" />
                              </div>
                              <span className="text-xs font-mono">
                                {`${area.boundingBoxNW[1].toFixed(4)}, ${area.boundingBoxNW[0].toFixed(4)}`}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary" className="text-xs">
                              {getStyleName(area.mapStyle)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className="font-mono text-xs">{area.zoomLevel}</span>
                          </TableCell>
                          <TableCell>
                            <Badge variant={area.outputFormat === 'geotiff' ? 'outline' : 'secondary'} className="text-xs">
                              {area.outputFormat.toUpperCase()}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-muted-foreground text-sm">
                            {formatDate(area.createdAt)}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-1">
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleDownload(area)}
                                className="hover-primary transition-colors"
                                title="Scarica mappa"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                className="hover-accent transition-colors"
                                title="Visualizza sulla mappa"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                  </TableBody>
                </Table>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Download',
    href: '/map-downloader',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Archivio',
    href: '/map-downloader/archive',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Impostazioni',
    href: '/map-downloader/settings',
    isActive: false,
    disabled: false,
  },
]