import { useState, useEffect } from 'react'
import { Head<PERSON> } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Paintbrush, Save, RefreshCw, Key } from 'lucide-react'

export default function Settings() {
  // Stato per le impostazioni
  const [primaryColor, setPrimaryColor] = useState('#007bff')
  const [shadowSize, setShadowSize] = useState(10)
  const [google<PERSON><PERSON><PERSON><PERSON>, setGoogle<PERSON><PERSON><PERSON><PERSON>] = useState('')
  const [isSaved, setIsSaved] = useState(false)

  // Carica le impostazioni all'avvio
  useEffect(() => {
    // Ottieni il colore primario corrente
    const root = document.documentElement
    const computedStyle = getComputedStyle(root)
    const currentColor = computedStyle.getPropertyValue('--color-primary').trim()
    
    if (currentColor) {
      // Converti da rgb a hex se necessario
      if (currentColor.startsWith('rgb')) {
        const rgb = currentColor.match(/\d+/g)
        if (rgb && rgb.length === 3) {
          const hex = '#' + rgb.map(x => parseInt(x).toString(16).padStart(2, '0')).join('')
          setPrimaryColor(hex)
        }
      } else if (currentColor.startsWith('#')) {
        setPrimaryColor(currentColor)
      }
    }

    // Ottieni la dimensione dell'ombra corrente
    const shadowValue = computedStyle.getPropertyValue('--shadow-card').trim()
    if (shadowValue) {
      const match = shadowValue.match(/(\d+)px/)
      if (match && match[1]) {
        setShadowSize(parseInt(match[1]))
      }
    }

    // Carica l'API key da localStorage
    const savedApiKey = localStorage.getItem('google_api_key')
    if (savedApiKey) {
      setGoogleApiKey(savedApiKey)
    }
  }, [])

  // Applica le modifiche al CSS
  const applyChanges = () => {
    const root = document.documentElement
    
    // Imposta il colore primario
    root.style.setProperty('--color-primary', primaryColor)
    
    // Imposta l'ombra delle card
    root.style.setProperty('--shadow-card', `0 ${shadowSize}px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)`)
    
    // Salva l'API key in localStorage
    localStorage.setItem('google_api_key', googleApiKey)
    
    // Mostra il messaggio di conferma
    setIsSaved(true)
    setTimeout(() => setIsSaved(false), 3000)
  }

  // Ripristina le impostazioni predefinite
  const resetDefaults = () => {
    setPrimaryColor('#007bff')
    setShadowSize(10)
    setGoogleApiKey('')
    
    const root = document.documentElement
    root.style.setProperty('--color-primary', '#007bff')
    root.style.setProperty('--shadow-card', '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)')
    
    localStorage.removeItem('google_api_key')
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Impostazioni</h1>
          <div className='flex items-center space-x-2'>
            <Button variant="outline" onClick={resetDefaults}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Ripristina Default
            </Button>
            <Button onClick={applyChanges}>
              <Save className="mr-2 h-4 w-4" />
              Salva Modifiche
            </Button>
          </div>
        </div>

        <Tabs defaultValue="appearance">
          <TabsList className="mb-4">
            <TabsTrigger value="appearance">Aspetto</TabsTrigger>
            <TabsTrigger value="api">API Keys</TabsTrigger>
          </TabsList>
          
          <TabsContent value="appearance">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Color Picker */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Paintbrush className="mr-2 h-5 w-5" />
                    Colore Primario
                  </CardTitle>
                  <CardDescription>
                    Modifica il colore primario dell'applicazione
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4">
                    <div className="flex items-center gap-4">
                      <div 
                        className="h-10 w-10 rounded-full border"
                        style={{ backgroundColor: primaryColor }}
                      ></div>
                      <Input
                        type="color"
                        value={primaryColor}
                        onChange={(e) => setPrimaryColor(e.target.value)}
                        className="h-10 w-20"
                      />
                      <Input
                        type="text"
                        value={primaryColor}
                        onChange={(e) => setPrimaryColor(e.target.value)}
                        className="w-32"
                      />
                    </div>
                    
                    <div className="grid grid-cols-5 gap-2">
                      {['#007bff', '#6610f2', '#6f42c1', '#e83e8c', '#dc3545',
                        '#fd7e14', '#ffc107', '#28a745', '#20c997', '#17a2b8'].map((color) => (
                        <button
                          key={color}
                          className="h-8 w-8 rounded-full border"
                          style={{ backgroundColor: color }}
                          onClick={() => setPrimaryColor(color)}
                        ></button>
                      ))}
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <div className="flex flex-wrap gap-2">
                    <div 
                      className="flex h-10 items-center justify-center rounded-md bg-primary px-4 text-white"
                    >
                      Testo su Primario
                    </div>
                    <div 
                      className="flex h-10 items-center justify-center rounded-md border border-primary px-4 text-primary"
                    >
                      Bordo Primario
                    </div>
                    <Button>Pulsante Primario</Button>
                  </div>
                </CardFooter>
              </Card>

              {/* Shadow Slider */}
              <Card>
                <CardHeader>
                  <CardTitle>Ombra Card</CardTitle>
                  <CardDescription>
                    Regola la dimensione dell'ombra delle card
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="shadow-size">Dimensione Ombra</Label>
                      <span className="text-sm">{shadowSize}px</span>
                    </div>
                    <input
                      id="shadow-size"
                      type="range"
                      min={0}
                      max={30}
                      step={1}
                      value={shadowSize}
                      onChange={(e) => setShadowSize(parseInt(e.target.value))}
                      className="w-full h-2 appearance-none bg-secondary rounded-full outline-none"
                      style={{
                        background: `linear-gradient(to right, var(--color-primary) 0%, var(--color-primary) ${(shadowSize / 30) * 100}%, var(--secondary) ${(shadowSize / 30) * 100}%, var(--secondary) 100%)`
                      }}
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <div className="grid w-full grid-cols-3 gap-4">
                    <div 
                      className="h-20 rounded-md border bg-card p-4"
                      style={{ boxShadow: `0 0px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)` }}
                    >
                      <div className="text-center text-xs">0px</div>
                    </div>
                    <div 
                      className="h-20 rounded-md border bg-card p-4"
                      style={{ boxShadow: `0 ${shadowSize}px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)` }}
                    >
                      <div className="text-center text-xs">{shadowSize}px</div>
                    </div>
                    <div 
                      className="h-20 rounded-md border bg-card p-4"
                      style={{ boxShadow: `0 30px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)` }}
                    >
                      <div className="text-center text-xs">30px</div>
                    </div>
                  </div>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="api">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Key className="mr-2 h-5 w-5" />
                  API Keys
                </CardTitle>
                <CardDescription>
                  Configura le chiavi API per i servizi esterni
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="google-api-key">Google Maps API Key</Label>
                    <Input
                      id="google-api-key"
                      type="text"
                      value={googleApiKey}
                      onChange={(e) => setGoogleApiKey(e.target.value)}
                      placeholder="Inserisci la tua API key di Google Maps"
                    />
                    <p className="text-xs text-muted-foreground">
                      Questa chiave verrà utilizzata per le funzionalità di mappa e geocoding
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Messaggio di conferma */}
        {isSaved && (
          <div className="fixed bottom-4 right-4 z-50 rounded-md bg-green-500 px-4 py-2 text-white shadow-lg">
            Impostazioni salvate con successo!
          </div>
        )}
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Impostazioni',
    href: '/settings',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Profilo',
    href: '/settings/profile',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Notifiche',
    href: '/settings/notifications',
    isActive: false,
    disabled: false,
  },
]