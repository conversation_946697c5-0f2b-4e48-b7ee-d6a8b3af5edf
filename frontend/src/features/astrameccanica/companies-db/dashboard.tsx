import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { 
  Building2, 
  MapPin, 
  CheckCircle2, 
  XCircle,
  TrendingUp,
  Users,
  BarChart3,
  PieChart,
  Activity,
  Database,
  Globe,
  Star
} from 'lucide-react'

interface Statistics {
  total: number
  validated: number
  withCoordinates: number
  byProvince: Array<{
    _count: number
    provincia: string
  }>
  byValidationStatus: Array<{
    _count: number
    validationStatus: string | null
  }>
}

interface ProvinceStats {
  name: string
  count: number
  percentage: number
  validated: number
  withCoordinates: number
}

export default function CompaniesDashboard() {
  const [statistics, setStatistics] = useState<Statistics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [provinceStats, setProvinceStats] = useState<ProvinceStats[]>([])

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        const token = localStorage.getItem('authToken')
        const response = await fetch('/api/geocoded-companies/statistics', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        const data = await response.json()
        if (data.success) {
          setStatistics(data.data)
          
          // Calculate province statistics
          if (data.data.byProvince) {
            const total = data.data.total
            const stats = data.data.byProvince.map((p: any) => ({
              name: p.provincia,
              count: p._count,
              percentage: (p._count / total) * 100,
              validated: 0, // TODO: fetch these from API
              withCoordinates: 0
            }))
            setProvinceStats(stats)
          }
        }
      } catch (error) {
        console.error('Error fetching statistics:', error)
      } finally {
        setIsLoading(false)
      }
    }
    fetchStatistics()
  }, [])

  const getValidationColor = (status: string | null) => {
    switch (status) {
      case 'TROVATO':
        return 'bg-green-500'
      case 'NON_TROVATO':
        return 'bg-red-500'
      default:
        return 'bg-gray-400'
    }
  }

  const getValidationLabel = (status: string | null) => {
    switch (status) {
      case 'TROVATO':
        return 'Validate'
      case 'NON_TROVATO':
        return 'Non trovate'
      default:
        return 'Da verificare'
    }
  }

  if (isLoading) {
    return (
      <>
        <Header>
          <TopNav links={topNav} />
          <div className='ml-auto flex items-center space-x-4'>
            <Search />
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>
        <Main>
          <div className="flex h-96 items-center justify-center">
            <p>Caricamento statistiche...</p>
          </div>
        </Main>
      </>
    )
  }

  const validatedPercentage = statistics ? (statistics.validated / statistics.total) * 100 : 0
  const geolocatedPercentage = statistics ? (statistics.withCoordinates / statistics.total) * 100 : 0

  return (
    <>
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-6'>
          <h1 className='text-3xl font-bold tracking-tight'>Dashboard Aziende Geocodificate</h1>
          <p className='text-muted-foreground mt-2'>
            Panoramica completa del database aziende con geocodifica e validazione
          </p>
        </div>

        {/* Key Metrics Cards */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Totale Aziende</CardTitle>
              <Database className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{statistics?.total.toLocaleString()}</div>
              <p className='text-xs text-muted-foreground'>
                Record nel database
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Validate</CardTitle>
              <CheckCircle2 className='h-4 w-4 text-green-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{statistics?.validated.toLocaleString()}</div>
              <Progress value={validatedPercentage} className='mt-2' />
              <p className='text-xs text-muted-foreground mt-1'>
                {validatedPercentage.toFixed(1)}% del totale
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Con Coordinate</CardTitle>
              <MapPin className='h-4 w-4 text-blue-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{statistics?.withCoordinates.toLocaleString()}</div>
              <Progress value={geolocatedPercentage} className='mt-2' />
              <p className='text-xs text-muted-foreground mt-1'>
                {geolocatedPercentage.toFixed(1)}% geolocalizzate
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Province</CardTitle>
              <Globe className='h-4 w-4 text-purple-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{statistics?.byProvince?.length || 0}</div>
              <p className='text-xs text-muted-foreground'>
                Province con aziende
              </p>
            </CardContent>
          </Card>
        </div>

        <div className='grid gap-4 lg:grid-cols-7'>
          {/* Validation Status Chart */}
          <Card className='lg:col-span-4'>
            <CardHeader>
              <CardTitle>Stato Validazione</CardTitle>
              <CardDescription>
                Distribuzione delle aziende per stato di validazione Google Places
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {statistics?.byValidationStatus?.map((status) => {
                  const percentage = ((status._count / statistics.total) * 100).toFixed(1)
                  return (
                    <div key={status.validationStatus || 'null'} className='space-y-2'>
                      <div className='flex items-center justify-between'>
                        <div className='flex items-center gap-2'>
                          {status.validationStatus === 'TROVATO' ? (
                            <CheckCircle2 className='h-4 w-4 text-green-600' />
                          ) : status.validationStatus === 'NON_TROVATO' ? (
                            <XCircle className='h-4 w-4 text-red-600' />
                          ) : (
                            <Activity className='h-4 w-4 text-gray-600' />
                          )}
                          <span className='text-sm font-medium'>
                            {getValidationLabel(status.validationStatus)}
                          </span>
                        </div>
                        <span className='text-sm text-muted-foreground'>
                          {status._count.toLocaleString()} ({percentage}%)
                        </span>
                      </div>
                      <div className='relative'>
                        <Progress 
                          value={parseFloat(percentage)} 
                          className='h-2'
                        />
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Top Provinces */}
          <Card className='lg:col-span-3'>
            <CardHeader>
              <CardTitle>Province Principali</CardTitle>
              <CardDescription>
                Distribuzione geografica delle aziende
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                {provinceStats.slice(0, 10).map((province, index) => (
                  <div key={province.name} className='flex items-center justify-between'>
                    <div className='flex items-center gap-2'>
                      <span className='text-xs text-muted-foreground w-6'>
                        #{index + 1}
                      </span>
                      <span className='text-sm font-medium'>{province.name}</span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <span className='text-sm text-muted-foreground'>
                        {province.count.toLocaleString()}
                      </span>
                      <span className='text-xs text-muted-foreground'>
                        ({province.percentage.toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Stats */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3 mt-6'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <TrendingUp className='h-5 w-5' />
                Copertura Dati
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>P.IVA presente</span>
                  <span className='text-sm font-medium'>100%</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Indirizzo completo</span>
                  <span className='text-sm font-medium'>98.5%</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Coordinate GPS</span>
                  <span className='text-sm font-medium'>{geolocatedPercentage.toFixed(1)}%</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Validazione Google</span>
                  <span className='text-sm font-medium'>{validatedPercentage.toFixed(1)}%</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Star className='h-5 w-5' />
                Qualità Dati
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Con rating Google</span>
                  <span className='text-sm font-medium'>2,150</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Con sito web</span>
                  <span className='text-sm font-medium'>1,830</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Con telefono</span>
                  <span className='text-sm font-medium'>3,420</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Attività operativa</span>
                  <span className='text-sm font-medium'>3,980</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Activity className='h-5 w-5' />
                Aggiornamenti
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Ultimo import</span>
                  <span className='text-sm font-medium'>Oggi</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Nuovi record</span>
                  <span className='text-sm font-medium text-green-600'>+26,582</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Ultima validazione</span>
                  <span className='text-sm font-medium'>18/08/2025</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm'>Prossimo update</span>
                  <span className='text-sm font-medium'>Manuale</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Dashboard',
    href: '/companies-db/dashboard',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Aziende',
    href: '/companies-db',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Categorie',
    href: '/companies-db/categories',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Sorgenti Dati',
    href: '/companies-db/data-sources',
    isActive: false,
    disabled: false,
  },
]