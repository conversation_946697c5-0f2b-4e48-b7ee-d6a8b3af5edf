import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { 
  Building2, 
  MapPin, 
  Phone, 
  Globe, 
  Star, 
  CheckCircle2, 
  XCircle,
  ChevronLeft,
  ChevronRight,
  Filter,
  Download,
  Eye
} from 'lucide-react'

interface GeocodedCompany {
  id: string
  piva: string | null
  ragioneSociale: string
  toponimo: string | null
  indirizzo: string | null
  civico: string | null
  cap: string | null
  citta: string | null
  provincia: string | null
  regione: string | null
  latitudine: number | null
  longitudine: number | null
  indirizzoCompleto: string | null
  geocodingStatus: string | null
  businessNameGoogle: string | null
  businessStatus: string | null
  website: string | null
  phoneNumber: string | null
  rating: number | null
  totalRatings: number | null
  validationStatus: string | null
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
}

export default function CompaniesDB() {
  const [companies, setCompanies] = useState<GeocodedCompany[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProvince, setSelectedProvince] = useState<string>('')
  const [selectedCity, setSelectedCity] = useState<string>('')
  const [selectedValidation, setSelectedValidation] = useState<string>('')
  const [provinces, setProvinces] = useState<string[]>([])
  const [cities, setCities] = useState<string[]>([])
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  })
  const [selectedCompany, setSelectedCompany] = useState<GeocodedCompany | null>(null)
  const [isDetailOpen, setIsDetailOpen] = useState(false)
  const [statistics, setStatistics] = useState<any>(null)

  // Fetch provinces
  useEffect(() => {
    const fetchProvinces = async () => {
      try {
        const token = localStorage.getItem('authToken')
        const response = await fetch('/api/geocoded-companies/provinces', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        const data = await response.json()
        if (data.success) {
          setProvinces(data.data)
        }
      } catch (error) {
        console.error('Error fetching provinces:', error)
      }
    }
    fetchProvinces()
  }, [])

  // Fetch cities when province changes
  useEffect(() => {
    const fetchCities = async () => {
      try {
        const token = localStorage.getItem('authToken')
        const params = selectedProvince ? `?provincia=${selectedProvince}` : ''
        const response = await fetch(`/api/geocoded-companies/cities${params}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        const data = await response.json()
        if (data.success) {
          setCities(data.data)
        }
      } catch (error) {
        console.error('Error fetching cities:', error)
      }
    }
    
    // Reset selected city when province changes
    setSelectedCity('')
    fetchCities()
  }, [selectedProvince])

  // Fetch statistics
  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        const token = localStorage.getItem('authToken')
        const response = await fetch('/api/geocoded-companies/statistics', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        const data = await response.json()
        if (data.success) {
          setStatistics(data.data)
        }
      } catch (error) {
        console.error('Error fetching statistics:', error)
      }
    }
    fetchStatistics()
  }, [])

  // Fetch companies
  const fetchCompanies = async (page = 1) => {
    setIsLoading(true)
    try {
      const token = localStorage.getItem('authToken')
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '50',
        ...(searchTerm && { search: searchTerm }),
        ...(selectedProvince && { provincia: selectedProvince }),
        ...(selectedCity && { citta: selectedCity }),
        ...(selectedValidation && { validationStatus: selectedValidation })
      })

      const response = await fetch(`/api/geocoded-companies?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      const data = await response.json()
      
      if (data.success) {
        setCompanies(data.data)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Error fetching companies:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Initial fetch and refetch on filter changes
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchCompanies(1)
    }, searchTerm ? 500 : 0)

    return () => clearTimeout(debounceTimer)
  }, [searchTerm, selectedProvince, selectedCity, selectedValidation])

  const handleViewDetails = (company: GeocodedCompany) => {
    setSelectedCompany(company)
    setIsDetailOpen(true)
  }

  const handleExport = () => {
    const csv = [
      ['P.IVA', 'Ragione Sociale', 'Indirizzo', 'Città', 'Provincia', 'CAP', 'Latitudine', 'Longitudine', 'Stato Validazione'].join(','),
      ...companies.map(c => [
        c.piva || '',
        `"${c.ragioneSociale}"`,
        `"${c.indirizzoCompleto || ''}"`,
        c.citta || '',
        c.provincia || '',
        c.cap || '',
        c.latitudine || '',
        c.longitudine || '',
        c.validationStatus || ''
      ].join(','))
    ].join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `companies_${new Date().toISOString().split('T')[0]}.csv`
    a.click()
  }

  const getValidationBadge = (status: string | null) => {
    if (!status) return null
    
    switch (status) {
      case 'TROVATO':
        return <Badge className="bg-green-500"><CheckCircle2 className="w-3 h-3 mr-1" />Validato</Badge>
      case 'NON_TROVATO':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Non trovato</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <>
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-6 flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold tracking-tight'>Database Aziende Geocodificate</h1>
            <p className='text-muted-foreground'>
              {statistics && `${statistics.total.toLocaleString()} aziende • ${statistics.validated.toLocaleString()} validate • ${statistics.withCoordinates.toLocaleString()} con coordinate`}
            </p>
          </div>
          <Button onClick={handleExport} variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Esporta CSV
          </Button>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Aziende Geocodificate</CardTitle>
                <CardDescription>
                  Dati importati con geocodifica e validazione Google Places
                </CardDescription>
              </div>
              <div className="flex items-center gap-2 flex-wrap">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <Select value={selectedProvince || "all"} onValueChange={(value) => setSelectedProvince(value === "all" ? "" : value)}>
                  <SelectTrigger className="w-[160px]">
                    <SelectValue placeholder="Provincia" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutte le province</SelectItem>
                    {provinces.map(province => (
                      <SelectItem key={province} value={province}>{province}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select 
                  value={selectedCity || "all"} 
                  onValueChange={(value) => setSelectedCity(value === "all" ? "" : value)}
                  disabled={cities.length === 0}
                >
                  <SelectTrigger className="w-[160px]">
                    <SelectValue placeholder="Città" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutte le città</SelectItem>
                    {cities.map(city => (
                      <SelectItem key={city} value={city}>{city}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={selectedValidation || "all"} onValueChange={(value) => setSelectedValidation(value === "all" ? "" : value)}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Validazione" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutti</SelectItem>
                    <SelectItem value="TROVATO">Validato</SelectItem>
                    <SelectItem value="NON_TROVATO">Non trovato</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  placeholder="Cerca nome o P.IVA..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-[200px]"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex h-40 items-center justify-center">
                <p>Caricamento dati...</p>
              </div>
            ) : (
              <>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Ragione Sociale</TableHead>
                        <TableHead>P.IVA</TableHead>
                        <TableHead>Indirizzo</TableHead>
                        <TableHead>Città</TableHead>
                        <TableHead>Provincia</TableHead>
                        <TableHead>Coordinate</TableHead>
                        <TableHead>Validazione</TableHead>
                        <TableHead>Rating</TableHead>
                        <TableHead className="text-right">Azioni</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {companies.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={9} className="text-center">
                            Nessuna azienda trovata
                          </TableCell>
                        </TableRow>
                      ) : (
                        companies.map((company) => (
                          <TableRow key={company.id}>
                            <TableCell className="font-medium max-w-xs truncate">
                              {company.ragioneSociale}
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {company.piva || '-'}
                            </TableCell>
                            <TableCell className="max-w-xs truncate">
                              {company.indirizzo ? `${company.indirizzo} ${company.civico || ''}` : '-'}
                            </TableCell>
                            <TableCell>{company.citta || '-'}</TableCell>
                            <TableCell>{company.provincia || '-'}</TableCell>
                            <TableCell>
                              {company.latitudine && company.longitudine ? (
                                <div className="flex items-center text-green-600">
                                  <MapPin className="w-3 h-3 mr-1" />
                                  <span className="text-xs">Sì</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground text-xs">-</span>
                              )}
                            </TableCell>
                            <TableCell>{getValidationBadge(company.validationStatus)}</TableCell>
                            <TableCell>
                              {company.rating ? (
                                <div className="flex items-center gap-1">
                                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                  <span className="text-xs">{company.rating}</span>
                                  <span className="text-xs text-muted-foreground">({company.totalRatings})</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground text-xs">-</span>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleViewDetails(company)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div className="flex items-center justify-between mt-4">
                    <p className="text-sm text-muted-foreground">
                      Mostrando {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} di {pagination.total} risultati
                    </p>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchCompanies(pagination.page - 1)}
                        disabled={pagination.page === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Precedente
                      </Button>
                      <span className="text-sm">
                        Pagina {pagination.page} di {pagination.totalPages}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchCompanies(pagination.page + 1)}
                        disabled={pagination.page === pagination.totalPages}
                      >
                        Successiva
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Detail Dialog */}
        <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{selectedCompany?.ragioneSociale}</DialogTitle>
              <DialogDescription>
                Dettagli completi dell'azienda
              </DialogDescription>
            </DialogHeader>
            {selectedCompany && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">P.IVA</Label>
                    <p className="font-medium">{selectedCompany.piva || 'Non disponibile'}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Stato Validazione</Label>
                    <div className="mt-1">{getValidationBadge(selectedCompany.validationStatus)}</div>
                  </div>
                </div>

                <div>
                  <Label className="text-muted-foreground">Indirizzo Completo</Label>
                  <p className="font-medium flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    {selectedCompany.indirizzoCompleto || 'Non disponibile'}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">Coordinate</Label>
                    <p className="font-medium">
                      {selectedCompany.latitudine && selectedCompany.longitudine 
                        ? `${selectedCompany.latitudine.toFixed(6)}, ${selectedCompany.longitudine.toFixed(6)}`
                        : 'Non disponibili'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Stato Business</Label>
                    <p className="font-medium">{selectedCompany.businessStatus || 'Non disponibile'}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">Telefono</Label>
                    <p className="font-medium flex items-center gap-2">
                      {selectedCompany.phoneNumber ? (
                        <>
                          <Phone className="w-4 h-4" />
                          {selectedCompany.phoneNumber}
                        </>
                      ) : 'Non disponibile'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Sito Web</Label>
                    <p className="font-medium">
                      {selectedCompany.website ? (
                        <a href={selectedCompany.website} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 text-blue-500 hover:underline">
                          <Globe className="w-4 h-4" />
                          Visita sito
                        </a>
                      ) : 'Non disponibile'}
                    </p>
                  </div>
                </div>

                {selectedCompany.rating && (
                  <div>
                    <Label className="text-muted-foreground">Valutazione Google</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star 
                            key={i} 
                            className={`w-4 h-4 ${i < Math.floor(selectedCompany.rating!) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} 
                          />
                        ))}
                      </div>
                      <span className="font-medium">{selectedCompany.rating}</span>
                      <span className="text-muted-foreground">({selectedCompany.totalRatings} recensioni)</span>
                    </div>
                  </div>
                )}

                {selectedCompany.latitudine && selectedCompany.longitudine && (
                  <div className="pt-4 border-t">
                    <Button 
                      className="w-full" 
                      onClick={() => window.open(`https://www.google.com/maps/search/?api=1&query=${selectedCompany.latitudine},${selectedCompany.longitudine}`, '_blank')}
                    >
                      <MapPin className="mr-2 h-4 w-4" />
                      Apri in Google Maps
                    </Button>
                  </div>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Dashboard',
    href: '/companies-db/dashboard',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Aziende',
    href: '/companies-db',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Categorie',
    href: '/companies-db/categories',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Sorgenti Dati',
    href: '/companies-db/data-sources',
    isActive: false,
    disabled: false,
  },
]