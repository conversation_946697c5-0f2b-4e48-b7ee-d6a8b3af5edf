import { useState, useEffect } from 'react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Calendar } from '@/components/ui/calendar'
import { Popover, Popover<PERSON>ontent, PopoverTrigger } from '@/components/ui/popover'
import { Download, MapPin, Archive, Filter, Calendar as CalendarIcon, Search as SearchIcon, RefreshCw, Eye, Trash2, FileImage } from 'lucide-react'
import { toast } from 'sonner'
import { mapDownloadService, MapStyle, DownloadedArea } from '@/services/mapDownloadService'
import { format } from 'date-fns'
import { it } from 'date-fns/locale'
import { cn } from '@/lib/utils'

export default function MapArchive() {
  const [downloadedAreas, setDownloadedAreas] = useState<DownloadedArea[]>([])
  const [filteredAreas, setFilteredAreas] = useState<DownloadedArea[]>([])
  const [isLoadingAreas, setIsLoadingAreas] = useState(false)
  const [mapStyles, setMapStyles] = useState<MapStyle[]>([])
  
  // Filtri
  const [searchTerm, setSearchTerm] = useState('')
  const [styleFilter, setStyleFilter] = useState('all')
  const [formatFilter, setFormatFilter] = useState('all')
  const [dateFrom, setDateFrom] = useState<Date>()
  const [dateTo, setDateTo] = useState<Date>()
  
  // Carica dati all'avvio
  useEffect(() => {
    loadMapStyles()
    loadDownloadedAreas()
  }, [])
  
  // Applica filtri
  useEffect(() => {
    let filtered = [...downloadedAreas]
    
    // Filtro per ricerca
    if (searchTerm) {
      filtered = filtered.filter(area => {
        const coords = `${area.boundingBoxNW[1]}, ${area.boundingBoxNW[0]} - ${area.boundingBoxSE[1]}, ${area.boundingBoxSE[0]}`
        return coords.toLowerCase().includes(searchTerm.toLowerCase())
      })
    }
    
    // Filtro per stile
    if (styleFilter !== 'all') {
      filtered = filtered.filter(area => area.mapStyle === styleFilter)
    }
    
    // Filtro per formato
    if (formatFilter !== 'all') {
      filtered = filtered.filter(area => area.outputFormat === formatFilter)
    }
    
    // Filtro per data
    if (dateFrom) {
      filtered = filtered.filter(area => new Date(area.createdAt) >= dateFrom)
    }
    if (dateTo) {
      filtered = filtered.filter(area => new Date(area.createdAt) <= dateTo)
    }
    
    setFilteredAreas(filtered)
  }, [downloadedAreas, searchTerm, styleFilter, formatFilter, dateFrom, dateTo])
  
  const loadMapStyles = async () => {
    const styles = await mapDownloadService.getMapStyles()
    setMapStyles(styles)
  }
  
  const loadDownloadedAreas = async () => {
    setIsLoadingAreas(true)
    try {
      const areas = await mapDownloadService.getDownloadedAreas()
      setDownloadedAreas(areas)
    } catch (_error) {
      // Silenziato per linter; il messaggio utente è sufficiente
      toast.error('Errore nel caricamento delle aree')
    } finally {
      setIsLoadingAreas(false)
    }
  }
  
  const getStyleName = (styleId: string): string => {
    const style = mapStyles.find(s => s.id === styleId)
    return style?.name || styleId
  }
  
  const handleDownload = async (area: DownloadedArea) => {
    try {
      const s3Key = area.storageKey || area.metadata?.s3?.key || area.metadata?.storageKey
      if (!s3Key) {
        toast.error('Chiave di download non trovata')
        return
      }
      const url = await mapDownloadService.downloadMap(s3Key)
      window.open(url, '_blank')
    } catch (_error) {
      toast.error('Errore nel download del file')
    }
  }
  
  const formatDate = (dateString: string): string => {
    return format(new Date(dateString), 'dd MMM yyyy HH:mm', { locale: it })
  }
  
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  const resetFilters = () => {
    setSearchTerm('')
    setStyleFilter('all')
    setFormatFilter('all')
    setDateFrom(undefined)
    setDateTo(undefined)
  }

  const topNav = [
    {
      title: 'Download',
      href: '/map-downloader',
      isActive: false,
      disabled: false,
    },
    {
      title: 'Archivio',
      href: '/map-downloader/archive',
      isActive: true,
      disabled: false,
    },
    {
      title: 'Impostazioni',
      href: '/map-downloader/settings',
      isActive: false,
      disabled: false,
    },
  ]

  return (
    <>
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-8 flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent'>
              Archivio Mappe
            </h1>
            <p className='text-muted-foreground mt-2'>
              {filteredAreas.length} di {downloadedAreas.length} mappe nell'archivio
            </p>
          </div>
          <Button onClick={loadDownloadedAreas} variant="outline" size="sm" className="gap-2">
            <RefreshCw className={cn("h-4 w-4", isLoadingAreas && "animate-spin")} />
            Aggiorna
          </Button>
        </div>

        {/* Filtri */}
        <Card className="mb-6 shadow-lg border-0">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 rounded-lg hover-accent">
                <Filter className="h-5 w-5 text-accent-foreground" />
              </div>
              Filtri di Ricerca
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="lg:col-span-2">
                <Label htmlFor="search" className="flex items-center gap-2 mb-2">
                  <SearchIcon className="h-4 w-4 text-muted-foreground" />
                  Cerca Coordinate
                </Label>
                <Input
                  id="search"
                  placeholder="es. 45.4642, 9.1900"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-all"
                />
              </div>
              
              <div>
                <Label htmlFor="style" className="flex items-center gap-2 mb-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  Stile
                </Label>
                <Select value={styleFilter} onValueChange={setStyleFilter}>
                  <SelectTrigger className="transition-all">
                    <SelectValue placeholder="Tutti gli stili" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutti gli stili</SelectItem>
                    {mapStyles.map((style) => (
                      <SelectItem key={style.id} value={style.id}>
                        {style.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="format" className="flex items-center gap-2 mb-2">
                  <FileImage className="h-4 w-4 text-muted-foreground" />
                  Formato
                </Label>
                <Select value={formatFilter} onValueChange={setFormatFilter}>
                  <SelectTrigger className="transition-all">
                    <SelectValue placeholder="Tutti i formati" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutti i formati</SelectItem>
                    <SelectItem value="jpeg">JPEG</SelectItem>
                    <SelectItem value="geotiff">GeoTIFF</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="flex-1 justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateFrom ? format(dateFrom, 'dd/MM/yyyy') : 'Dal'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateFrom}
                      onSelect={setDateFrom}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="flex-1 justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateTo ? format(dateTo, 'dd/MM/yyyy') : 'Al'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateTo}
                      onSelect={setDateTo}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            
            <div className="flex justify-end mt-4">
              <Button onClick={resetFilters} variant="ghost" size="sm">
                Reimposta Filtri
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tabella */}
        <Card className="shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 rounded-lg hover-primary">
                <Archive className="h-5 w-5" />
              </div>
              Mappe Scaricate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[600px]">
              <Table>
                <TableHeader>
                  <TableRow className="hover:bg-transparent">
                    <TableHead className="font-semibold">Coordinate</TableHead>
                    <TableHead className="font-semibold">Stile</TableHead>
                    <TableHead className="font-semibold">Zoom</TableHead>
                    <TableHead className="font-semibold">Formato</TableHead>
                    <TableHead className="font-semibold">Data</TableHead>
                    <TableHead className="font-semibold">Dimensione</TableHead>
                    <TableHead className="text-right font-semibold">Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoadingAreas ? (
                    [...Array(5)].map((_, i) => (
                      <TableRow key={i}>
                        <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-24 ml-auto" /></TableCell>
                      </TableRow>
                    ))
                  ) : filteredAreas.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-16">
                        <div className="flex flex-col items-center gap-2 text-muted-foreground">
                          <Archive className="h-12 w-12 opacity-30" />
                          <p className="font-medium">Nessuna mappa trovata</p>
                          <p className="text-sm">Prova a modificare i filtri di ricerca</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAreas.map((area) => (
                      <TableRow key={area.id} className="hover:bg-muted/50 transition-colors">
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className="p-1 rounded hover-primary">
                              <MapPin className="h-3 w-3" />
                            </div>
                            <div>
                              <span className="text-xs font-mono">
                                {`${area.boundingBoxNW[1].toFixed(4)}, ${area.boundingBoxNW[0].toFixed(4)}`}
                              </span>
                              <br />
                              <span className="text-xs font-mono text-muted-foreground">
                                {`${area.boundingBoxSE[1].toFixed(4)}, ${area.boundingBoxSE[0].toFixed(4)}`}
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary" className="text-xs">
                            {getStyleName(area.mapStyle)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className="font-mono text-xs">{area.zoomLevel}</span>
                        </TableCell>
                        <TableCell>
                          <Badge variant={area.outputFormat === 'geotiff' ? 'outline' : 'secondary'} className="text-xs">
                            {area.outputFormat.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {formatDate(area.createdAt)}
                        </TableCell>
                        <TableCell className="text-sm">
                          {area.metadata?.fileSize ? formatFileSize(area.metadata.fileSize) : '-'}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDownload(area)}
                              className="hover-primary transition-colors"
                              title="Scarica mappa"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="hover-accent transition-colors"
                              title="Visualizza"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="transition-colors hover:bg-[color-mix(in_oklch,theme(colors.red.500)_10%,transparent)] hover:text-[color-mix(in_oklch,theme(colors.red.500)_80%,white)]"
                              title="Elimina"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </CardContent>
        </Card>
      </Main>
    </>
  )
}