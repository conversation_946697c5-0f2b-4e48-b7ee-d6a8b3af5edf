import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useCompaniesStore } from '@/stores/companiesStore'
import { Upload, FileText, Download, RefreshCw, CheckCircle2, XCircle } from 'lucide-react'

// Tipo per i dati delle bollette
interface BillData {
  id: string
  piva: string
  ragioneSociale: string
  kwhAnno: number
  costoKwh: number
  totaleAnnuo: number
  matched: boolean
}

export default function BillsAnalyzer() {
  const { companies } = useCompaniesStore()
  const [billData, setBillData] = useState<BillData[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [fileName, setFileName] = useState<string | null>(null)
  const [matchCount, setMatchCount] = useState(0)

  // Carica i dati delle aziende all'avvio
  useEffect(() => {
    const loadCompanies = async () => {
      try {
        const response = await fetch('/dummy-data/companies.json')
        const data = await response.json()
        useCompaniesStore.getState().setCompanies(data)
      } catch (_err) {
        // In un'applicazione reale, qui gestiremmo l'errore in modo appropriato
      }
    }

    if (companies.length === 0) {
      loadCompanies()
    }
  }, [companies.length])

  // Gestisce il drag and drop
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      processFile(files[0])
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      processFile(files[0])
    }
  }

  // Processa il file CSV
  const processFile = (file: File) => {
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      alert('Per favore carica un file CSV')
      return
    }

    setIsLoading(true)
    setFileName(file.name)

    // Simula il caricamento del file
    setTimeout(() => {
      // In un'applicazione reale, qui leggeremmo effettivamente il file
      // Per ora generiamo dati di esempio
      const mockData: BillData[] = generateMockBillData()
      setBillData(mockData)
      
      // Conta le corrispondenze
      const matches = mockData.filter(bill => bill.matched).length
      setMatchCount(matches)
      
      setIsLoading(false)
    }, 1000)
  }

  // Genera dati di esempio per le bollette
  const generateMockBillData = (): BillData[] => {
    // Utilizziamo alcune aziende reali dal nostro store
    const data: BillData[] = []
    
    // Aggiungi aziende dal nostro database
    companies.slice(0, 5).forEach((company, index) => {
      data.push({
        id: `existing-${index}`,
        piva: `IT${Math.floor(10000000000 + Math.random() * 90000000000)}`,
        ragioneSociale: company.name,
        kwhAnno: Math.floor(10000 + Math.random() * 90000),
        costoKwh: parseFloat((0.15 + Math.random() * 0.1).toFixed(2)),
        totaleAnnuo: 0, // Calcolato sotto
        matched: true
      })
    })
    
    // Aggiungi aziende non presenti nel database
    for (let i = 0; i < 10; i++) {
      data.push({
        id: `new-${i}`,
        piva: `IT${Math.floor(10000000000 + Math.random() * 90000000000)}`,
        ragioneSociale: `Azienda Sconosciuta ${i + 1}`,
        kwhAnno: Math.floor(10000 + Math.random() * 90000),
        costoKwh: parseFloat((0.15 + Math.random() * 0.1).toFixed(2)),
        totaleAnnuo: 0, // Calcolato sotto
        matched: false
      })
    }
    
    // Calcola il totale annuo
    data.forEach(bill => {
      bill.totaleAnnuo = parseFloat((bill.kwhAnno * bill.costoKwh).toFixed(2))
    })
    
    // Mescola l'array
    return data.sort(() => Math.random() - 0.5)
  }

  // Formatta i numeri con separatore delle migliaia
  const formatNumber = (num: number): string => {
    return num.toLocaleString('it-IT')
  }

  // Formatta i numeri come valuta
  const formatCurrency = (num: number): string => {
    return num.toLocaleString('it-IT', { style: 'currency', currency: 'EUR' })
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Analisi Bollette</h1>
          <div className='flex items-center space-x-2'>
            {billData.length > 0 && (
              <Button>
                <Download className="mr-2 h-4 w-4" />
                Esporta Analisi
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6">
          {/* Area di drop */}
          {billData.length === 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Carica File CSV</CardTitle>
                <CardDescription>
                  Trascina un file CSV contenente i dati delle bollette o fai clic per selezionarlo
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  className={`flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-12 transition-colors ${
                    isDragging ? 'border-primary bg-primary/5' : 'border-gray-300'
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  {isLoading ? (
                    <div className="flex flex-col items-center space-y-4">
                      <RefreshCw className="h-12 w-12 animate-spin text-primary" />
                      <p>Elaborazione in corso...</p>
                    </div>
                  ) : (
                    <>
                      <FileText className="h-16 w-16 mb-4 text-gray-400" />
                      <p className="mb-2 text-lg font-medium">Trascina qui il tuo file CSV</p>
                      <p className="mb-6 text-sm text-gray-500">
                        oppure
                      </p>
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <Button>
                          <Upload className="mr-2 h-4 w-4" />
                          Seleziona File
                        </Button>
                        <input
                          id="file-upload"
                          type="file"
                          accept=".csv"
                          className="hidden"
                          onChange={handleFileChange}
                        />
                      </label>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <>
              {/* Statistiche */}
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">File Analizzato</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{fileName}</div>
                    <p className="text-xs text-muted-foreground">
                      {billData.length} record trovati
                    </p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Aziende Corrispondenti</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{matchCount}</div>
                    <p className="text-xs text-muted-foreground">
                      {((matchCount / billData.length) * 100).toFixed(1)}% del totale
                    </p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Consumo Totale</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatNumber(billData.reduce((sum, bill) => sum + bill.kwhAnno, 0))} kWh
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Spesa: {formatCurrency(billData.reduce((sum, bill) => sum + bill.totaleAnnuo, 0))}
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Tabella dati */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Dati Bollette</CardTitle>
                      <CardDescription>
                        Le righe evidenziate in verde corrispondono ad aziende presenti nel database
                      </CardDescription>
                    </div>
                    <Button variant="outline" onClick={() => { setBillData([]); setFileName(null); }}>
                      Carica Nuovo File
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Partita IVA</TableHead>
                          <TableHead>Ragione Sociale</TableHead>
                          <TableHead className="text-right">kWh/Anno</TableHead>
                          <TableHead className="text-right">€/kWh</TableHead>
                          <TableHead className="text-right">Totale Annuo</TableHead>
                          <TableHead className="text-center">Match</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {billData.map((bill) => (
                          <TableRow 
                            key={bill.id}
                            className={bill.matched ? 'bg-green-50' : ''}
                          >
                            <TableCell className="font-medium">{bill.piva}</TableCell>
                            <TableCell>{bill.ragioneSociale}</TableCell>
                            <TableCell className="text-right">{formatNumber(bill.kwhAnno)}</TableCell>
                            <TableCell className="text-right">{bill.costoKwh.toFixed(2)}</TableCell>
                            <TableCell className="text-right">{formatCurrency(bill.totaleAnnuo)}</TableCell>
                            <TableCell className="text-center">
                              {bill.matched ? (
                                <CheckCircle2 className="h-5 w-5 text-green-500 mx-auto" />
                              ) : (
                                <XCircle className="h-5 w-5 text-gray-300 mx-auto" />
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Analisi',
    href: '/bills-analyzer',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Storico',
    href: '/bills-analyzer/history',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Impostazioni',
    href: '/bills-analyzer/settings',
    isActive: false,
    disabled: false,
  },
]