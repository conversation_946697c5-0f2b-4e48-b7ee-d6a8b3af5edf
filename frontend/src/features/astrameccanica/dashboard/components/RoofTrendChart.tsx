import { <PERSON>, <PERSON>Chart, ResponsiveContainer, XAxis, YAxis, Tooltip, CartesianGrid } from 'recharts'
import { useEffect, useState } from 'react'

// Tipo per i dati del grafico
interface RoofTrendData {
  name: string
  tetti: number
}

export function RoofTrendChart() {
  // Stato locale per i dati del grafico
  const [data, setData] = useState<RoofTrendData[]>([])
  
  // Genera dati di esempio per i mesi
  useEffect(() => {
    // In un'applicazione reale, questi dati verrebbero caricati da un'API
    const monthlyData: RoofTrendData[] = [
      { name: 'Gen', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: 'Feb', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: 'Mar', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: 'Apr', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: 'Mag', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: '<PERSON><PERSON>', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: 'Lug', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: 'Ago', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: 'Set', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: 'Ott', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: 'Nov', tetti: Math.floor(Math.random() * 100) + 50 },
      { name: 'Dic', tetti: Math.floor(Math.random() * 100) + 50 },
    ]
    
    setData(monthlyData)
  }, [])

  return (
    <ResponsiveContainer width="100%" height={350}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" vertical={false} />
        <XAxis 
          dataKey="name" 
          stroke="#888888" 
          fontSize={12} 
          tickLine={false} 
          axisLine={false}
        />
        <YAxis
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `${value}`}
        />
        <Tooltip 
          formatter={(value) => [`${value} tetti`, 'Analizzati']}
          labelFormatter={(label) => `Mese: ${label}`}
        />
        <Line
          type="monotone"
          dataKey="tetti"
          stroke="var(--color-primary)"
          strokeWidth={2}
          dot={{ r: 4 }}
          activeDot={{ r: 6 }}
        />
      </LineChart>
    </ResponsiveContainer>
  )
}