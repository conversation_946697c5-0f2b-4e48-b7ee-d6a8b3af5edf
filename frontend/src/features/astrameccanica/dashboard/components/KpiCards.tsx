import { useStatsStore } from '@/stores/statsStore'
import { useEffect } from 'react'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { 
  Building2, 
  Factory, 
  AlertTriangle, 
  Sun 
} from 'lucide-react'

export function KpiCards() {
  const { stats, calculateRoofStats, calculateCompanyStats } = useStatsStore()

  // Carica i dati all'avvio
  useEffect(() => {
    const loadData = async () => {
      try {
        // In un'applicazione reale, questi dati verrebbero caricati da un'API
        // Per ora utilizziamo i dati di esempio dallo store
        const roofsResponse = await fetch('/src/dummy-data/roofsClass.json')
        const roofsData = await roofsResponse.json()
        calculateRoofStats(roofsData)

        const companiesResponse = await fetch('/src/dummy-data/companies.json')
        const companiesData = await companiesResponse.json()
        calculateCompanyStats(companiesData)
        
      } catch (_err) {
        // In un'applicazione reale, qui gestiremmo l'errore in modo appropriato
      }
    }

    loadData()
  }, [calculateRoofStats, calculateCompanyStats])

  // Formatta i numeri con separatore delle migliaia
  const formatNumber = (num: number): string => {
    return num.toLocaleString('it-IT')
  }

  return (
    <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Tetti Totali
          </CardTitle>
          <Building2 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.roofs.total)}</div>
          <p className="text-xs text-muted-foreground">
            Area totale: {formatNumber(Math.round(stats.roofs.totalArea))} m²
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Tetti in Amianto
          </CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.roofs.byType.asbestos)}</div>
          <p className="text-xs text-muted-foreground">
            {stats.roofs.asbestosPercentage.toFixed(1)}% del totale
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Tetti Solari
          </CardTitle>
          <Sun className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.roofs.byType.solar)}</div>
          <p className="text-xs text-muted-foreground">
            {stats.roofs.solarPercentage.toFixed(1)}% del totale
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Aziende Totali
          </CardTitle>
          <Factory className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.companies.total)}</div>
          <p className="text-xs text-muted-foreground">
            Dipendenti: {formatNumber(stats.companies.totalEmployees)}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}