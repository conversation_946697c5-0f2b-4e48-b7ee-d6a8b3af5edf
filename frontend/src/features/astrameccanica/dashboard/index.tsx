import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { KpiCards } from './components/KpiCards'
import { RoofTrendChart } from './components/RoofTrendChart'
import { Download } from 'lucide-react'

export default function Dashboard() {
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Dashboard Astrameccanica</h1>
          <div className='flex items-center space-x-2'>
            <Button>
              <Download className="mr-2 h-4 w-4" />
              Esporta Report
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="mb-6">
          <KpiCards />
        </div>

        {/* Trend Chart */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Tetti Analizzati per Mese</CardTitle>
          </CardHeader>
          <CardContent className="pl-2">
            <RoofTrendChart />
          </CardContent>
        </Card>

        {/* Statistiche Aggiuntive */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Distribuzione Tetti per Tipo</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex h-[200px] items-center justify-center">
                <p className="text-sm text-muted-foreground">
                  Qui verrà visualizzato un grafico a torta con la distribuzione dei tipi di tetto
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Distribuzione Aziende per Categoria</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex h-[200px] items-center justify-center">
                <p className="text-sm text-muted-foreground">
                  Qui verrà visualizzato un grafico a torta con la distribuzione delle categorie di aziende
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Panoramica',
    href: '/',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Tetti',
    href: '/tetti',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Aziende',
    href: '/aziende',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Analisi',
    href: '/analisi',
    isActive: false,
    disabled: false,
  },
]