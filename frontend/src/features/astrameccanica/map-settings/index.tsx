import { useState, useEffect } from 'react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Settings, Map, Save, RefreshCw, Shield, Bell, Database, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'
import { userPreferencesService } from '@/services/userPreferencesService'

export default function MapSettings() {
  const [isLoadingSettings, setIsLoadingSettings] = useState(true)

  // Impostazioni generali
  const [defaultStyle, setDefaultStyle] = useState('s')
  const [defaultFormat, setDefaultFormat] = useState('jpeg')
  const [defaultZoom, setDefaultZoom] = useState('18')
  const [autoDownload, setAutoDownload] = useState(false)
  const [saveHistory, setSaveHistory] = useState(true)
  
  // Impostazioni avanzate
  const [cacheEnabled, setCacheEnabled] = useState(true)
  const [cacheSize, setCacheSize] = useState('500')
  const [compressionLevel, setCompressionLevel] = useState('80')
  const [maxConcurrentDownloads, setMaxConcurrentDownloads] = useState('3')
  
  // Notifiche
  const [notifyOnComplete, setNotifyOnComplete] = useState(true)
  const [notifyOnError, setNotifyOnError] = useState(true)
  const [emailNotifications, setEmailNotifications] = useState(false)
  const [notificationEmail, setNotificationEmail] = useState('')
  
  // API
  const [apiKey, setApiKey] = useState('')
  const [apiEndpoint, setApiEndpoint] = useState('http://localhost:3000/api')
  
  const [isSaving, setIsSaving] = useState(false)
  
  // Carica le preferenze all'avvio
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        setIsLoadingSettings(true)
        const preferences = await userPreferencesService.getPreferences()
        const mapSettings = preferences.mapDownloader
        
        // Imposta tutti i valori dalle preferenze
        setDefaultStyle(mapSettings.defaultStyle)
        setDefaultFormat(mapSettings.defaultFormat)
        setDefaultZoom(mapSettings.defaultZoom)
        setAutoDownload(mapSettings.autoDownload)
        setSaveHistory(mapSettings.saveHistory)
        setCacheEnabled(mapSettings.cacheEnabled)
        setCacheSize(mapSettings.cacheSize)
        setCompressionLevel(mapSettings.compressionLevel)
        setMaxConcurrentDownloads(mapSettings.maxConcurrentDownloads)
        setNotifyOnComplete(mapSettings.notifyOnComplete)
        setNotifyOnError(mapSettings.notifyOnError)
        setEmailNotifications(mapSettings.emailNotifications)
        setNotificationEmail(mapSettings.notificationEmail)
        setApiKey(mapSettings.apiKey)
        setApiEndpoint(mapSettings.apiEndpoint)
      } catch (error) {
        console.error('Error loading preferences:', error)
        toast.error('Errore nel caricamento delle preferenze')
      } finally {
        setIsLoadingSettings(false)
      }
    }
    
    loadPreferences()
  }, [])
  
  const handleSaveSettings = async () => {
    setIsSaving(true)
    
    // Crea oggetto con tutte le impostazioni
    const preferences = {
      mapDownloader: {
        defaultStyle,
        defaultFormat,
        defaultZoom,
        autoDownload,
        saveHistory,
        cacheEnabled,
        cacheSize,
        compressionLevel,
        maxConcurrentDownloads,
        notifyOnComplete,
        notifyOnError,
        emailNotifications,
        notificationEmail,
        apiKey,
        apiEndpoint
      }
    }
    
    // Salva nel database tramite API
    try {
      await userPreferencesService.updatePreferences(preferences)
      toast.success('Preferenze salvate con successo')
    } catch (error) {
      toast.error('Errore nel salvataggio delle preferenze')
    } finally {
      setIsSaving(false)
    }
  }
  
  const handleResetSettings = async () => {
    try {
      setIsSaving(true)
      const defaultPreferences = await userPreferencesService.resetPreferences()
      const mapSettings = defaultPreferences.mapDownloader
      
      // Ripristina ai valori predefiniti
      setDefaultStyle(mapSettings.defaultStyle)
      setDefaultFormat(mapSettings.defaultFormat)
      setDefaultZoom(mapSettings.defaultZoom)
      setAutoDownload(mapSettings.autoDownload)
      setSaveHistory(mapSettings.saveHistory)
      setCacheEnabled(mapSettings.cacheEnabled)
      setCacheSize(mapSettings.cacheSize)
      setCompressionLevel(mapSettings.compressionLevel)
      setMaxConcurrentDownloads(mapSettings.maxConcurrentDownloads)
      setNotifyOnComplete(mapSettings.notifyOnComplete)
      setNotifyOnError(mapSettings.notifyOnError)
      setEmailNotifications(mapSettings.emailNotifications)
      setNotificationEmail(mapSettings.notificationEmail)
      setApiKey(mapSettings.apiKey)
      setApiEndpoint(mapSettings.apiEndpoint)
      
      toast.success('Preferenze ripristinate ai valori predefiniti')
    } catch (error) {
      toast.error('Errore nel ripristino delle preferenze')
    } finally {
      setIsSaving(false)
    }
  }

  const topNav = [
    {
      title: 'Download',
      href: '/map-downloader',
      isActive: false,
      disabled: false,
    },
    {
      title: 'Archivio',
      href: '/map-downloader/archive',
      isActive: false,
      disabled: false,
    },
    {
      title: 'Impostazioni',
      href: '/map-downloader/settings',
      isActive: true,
      disabled: false,
    },
  ]

  return (
    <>
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-8 flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent'>
              Impostazioni
            </h1>
            <p className='text-muted-foreground mt-2'>
              Configura le preferenze per il download delle mappe
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleResetSettings} variant="outline" size="sm" className="gap-2">
              <RefreshCw className="h-4 w-4" />
              Ripristina
            </Button>
            <Button onClick={handleSaveSettings} size="sm" className="gap-2" disabled={isSaving}>
              {isSaving ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              Salva
            </Button>
          </div>
        </div>

        <Tabs defaultValue="general" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:w-[600px]">
            <TabsTrigger value="general" className="gap-2">
              <Map className="h-4 w-4" />
              Generali
            </TabsTrigger>
            <TabsTrigger value="advanced" className="gap-2">
              <Settings className="h-4 w-4" />
              Avanzate
            </TabsTrigger>
            <TabsTrigger value="notifications" className="gap-2">
              <Bell className="h-4 w-4" />
              Notifiche
            </TabsTrigger>
            <TabsTrigger value="api" className="gap-2">
              <Shield className="h-4 w-4" />
              API
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <Card className="shadow-lg border-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="p-2 bg-green-500/10 rounded-lg">
                    <Map className="h-5 w-5 text-green-500" />
                  </div>
                  Impostazioni Generali
                </CardTitle>
                <CardDescription>
                  Configura le impostazioni predefinite per i download
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="default-style">Stile Predefinito</Label>
                    <Select value={defaultStyle} onValueChange={setDefaultStyle}>
                      <SelectTrigger className="transition-all focus:ring-2 focus:ring-green-500/20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="s">Satellite</SelectItem>
                        <SelectItem value="m">Stradale</SelectItem>
                        <SelectItem value="y">Ibrido</SelectItem>
                        <SelectItem value="t">Terreno</SelectItem>
                        <SelectItem value="p">Terreno con etichette</SelectItem>
                        <SelectItem value="h">Solo etichette</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="default-format">Formato Predefinito</Label>
                    <Select value={defaultFormat} onValueChange={setDefaultFormat}>
                      <SelectTrigger className="transition-all focus:ring-2 focus:ring-green-500/20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="jpeg">JPEG</SelectItem>
                        <SelectItem value="geotiff">GeoTIFF</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="default-zoom">Livello Zoom Predefinito</Label>
                    <Select value={defaultZoom} onValueChange={setDefaultZoom}>
                      <SelectTrigger className="transition-all focus:ring-2 focus:ring-green-500/20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="15">15 - Città</SelectItem>
                        <SelectItem value="16">16 - Quartiere</SelectItem>
                        <SelectItem value="17">17 - Strade principali</SelectItem>
                        <SelectItem value="18">18 - Strade</SelectItem>
                        <SelectItem value="19">19 - Edifici</SelectItem>
                        <SelectItem value="20">20 - Dettagli edifici</SelectItem>
                        <SelectItem value="21">21 - Massimo dettaglio</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4 pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="auto-download">Download Automatico</Label>
                      <p className="text-sm text-muted-foreground">
                        Avvia il download automaticamente dopo la preview
                      </p>
                    </div>
                    <Switch
                      id="auto-download"
                      checked={autoDownload}
                      onCheckedChange={setAutoDownload}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="save-history">Salva Cronologia</Label>
                      <p className="text-sm text-muted-foreground">
                        Mantieni la cronologia dei download nell'archivio
                      </p>
                    </div>
                    <Switch
                      id="save-history"
                      checked={saveHistory}
                      onCheckedChange={setSaveHistory}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced">
            <Card className="shadow-lg border-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="p-2 bg-blue-500/10 rounded-lg">
                    <Settings className="h-5 w-5 text-blue-500" />
                  </div>
                  Impostazioni Avanzate
                </CardTitle>
                <CardDescription>
                  Configurazioni avanzate per ottimizzare le performance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="cache-enabled">Abilita Cache</Label>
                      <p className="text-sm text-muted-foreground">
                        Salva temporaneamente le mappe per accesso rapido
                      </p>
                    </div>
                    <Switch
                      id="cache-enabled"
                      checked={cacheEnabled}
                      onCheckedChange={setCacheEnabled}
                    />
                  </div>

                  {cacheEnabled && (
                    <div className="space-y-2 pl-4 border-l-2 border-muted">
                      <Label htmlFor="cache-size">Dimensione Cache (MB)</Label>
                      <Input
                        id="cache-size"
                        type="number"
                        value={cacheSize}
                        onChange={(e) => setCacheSize(e.target.value)}
                        className="transition-all focus:ring-2 focus:ring-blue-500/20"
                      />
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="compression">Livello Compressione JPEG (%)</Label>
                    <Input
                      id="compression"
                      type="number"
                      min="1"
                      max="100"
                      value={compressionLevel}
                      onChange={(e) => setCompressionLevel(e.target.value)}
                      className="transition-all focus:ring-2 focus:ring-blue-500/20"
                    />
                    <p className="text-xs text-muted-foreground">
                      Maggiore è il valore, migliore è la qualità
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="concurrent">Download Simultanei</Label>
                    <Select value={maxConcurrentDownloads} onValueChange={setMaxConcurrentDownloads}>
                      <SelectTrigger className="transition-all focus:ring-2 focus:ring-blue-500/20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1</SelectItem>
                        <SelectItem value="2">2</SelectItem>
                        <SelectItem value="3">3</SelectItem>
                        <SelectItem value="5">5</SelectItem>
                        <SelectItem value="10">10</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="p-4 bg-yellow-500/10 rounded-lg flex gap-3">
                  <AlertCircle className="h-5 w-5 text-yellow-500 shrink-0 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-yellow-500 mb-1">Attenzione</p>
                    <p className="text-muted-foreground">
                      Aumentare il numero di download simultanei potrebbe causare rallentamenti
                      o errori se la connessione non è sufficientemente veloce.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications">
            <Card className="shadow-lg border-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="p-2 bg-purple-500/10 rounded-lg">
                    <Bell className="h-5 w-5 text-purple-500" />
                  </div>
                  Notifiche
                </CardTitle>
                <CardDescription>
                  Gestisci come ricevere notifiche sui download
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="notify-complete">Notifica Completamento</Label>
                      <p className="text-sm text-muted-foreground">
                        Ricevi una notifica quando un download è completato
                      </p>
                    </div>
                    <Switch
                      id="notify-complete"
                      checked={notifyOnComplete}
                      onCheckedChange={setNotifyOnComplete}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="notify-error">Notifica Errori</Label>
                      <p className="text-sm text-muted-foreground">
                        Ricevi una notifica in caso di errori durante il download
                      </p>
                    </div>
                    <Switch
                      id="notify-error"
                      checked={notifyOnError}
                      onCheckedChange={setNotifyOnError}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="email-notifications">Notifiche Email</Label>
                      <p className="text-sm text-muted-foreground">
                        Ricevi notifiche anche via email
                      </p>
                    </div>
                    <Switch
                      id="email-notifications"
                      checked={emailNotifications}
                      onCheckedChange={setEmailNotifications}
                    />
                  </div>

                  {emailNotifications && (
                    <div className="space-y-2 pl-4 border-l-2 border-muted">
                      <Label htmlFor="email">Indirizzo Email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={notificationEmail}
                        onChange={(e) => setNotificationEmail(e.target.value)}
                        className="transition-all focus:ring-2 focus:ring-purple-500/20"
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="api">
            <Card className="shadow-lg border-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="p-2 bg-orange-500/10 rounded-lg">
                    <Shield className="h-5 w-5 text-orange-500" />
                  </div>
                  Configurazione API
                </CardTitle>
                <CardDescription>
                  Configura l'accesso alle API per il download delle mappe
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="api-key">Chiave API</Label>
                    <div className="relative">
                      <Input
                        id="api-key"
                        type="password"
                        placeholder="Inserisci la tua chiave API"
                        value={apiKey}
                        onChange={(e) => setApiKey(e.target.value)}
                        className="transition-all focus:ring-2 focus:ring-orange-500/20 pr-20"
                      />
                      <Badge variant="outline" className="absolute right-2 top-2.5 text-xs">
                        Opzionale
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      La chiave API è richiesta solo per accesso avanzato
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="api-endpoint">Endpoint API</Label>
                    <Input
                      id="api-endpoint"
                      type="url"
                      value={apiEndpoint}
                      onChange={(e) => setApiEndpoint(e.target.value)}
                      className="transition-all focus:ring-2 focus:ring-orange-500/20"
                    />
                    <p className="text-xs text-muted-foreground">
                      URL base per le richieste API
                    </p>
                  </div>
                </div>

                <div className="p-4 bg-blue-500/10 rounded-lg">
                  <h4 className="font-medium text-blue-500 mb-2 flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    Limiti API
                  </h4>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <p>• Richieste al minuto: 60</p>
                    <p>• Download giornalieri: 1000</p>
                    <p>• Dimensione massima area: 10 km²</p>
                    <p>• Zoom massimo: 21</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </Main>
    </>
  )
}