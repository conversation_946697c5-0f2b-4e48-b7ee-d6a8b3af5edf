import { useState, useEffect } from 'react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useCompaniesStore } from '@/stores/companiesStore'
import { useStatsStore } from '@/stores/statsStore'
import { Download, ArrowUpDown, Star, BarChart4 } from 'lucide-react'

// Tipo per i dati ROI
interface RoiData {
  id: string
  name: string
  category: string
  roofArea: number
  solarPotential: number
  currentKwh: number
  potentialSavings: number
  roi: number
  priority: 'high' | 'medium' | 'low'
}

export default function CrmEvaluation() {
  const { companies } = useCompaniesStore()
  // Utilizziamo useStatsStore solo per getState() più avanti
  const [isLoading, setIsLoading] = useState(false)
  const [matchScore, setMatchScore] = useState(0)
  const [roiData, setRoiData] = useState<RoiData[]>([])
  const [sortField, setSortField] = useState<keyof RoiData>('roi')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  // Carica i dati all'avvio
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      try {
        // Carica i dati delle aziende se non sono già presenti
        if (companies.length === 0) {
          const companiesResponse = await fetch('/src/dummy-data/companies.json')
          const companiesData = await companiesResponse.json()
          useCompaniesStore.getState().setCompanies(companiesData)
        }

        // Carica i dati dei tetti
        const roofsResponse = await fetch('/src/dummy-data/roofsClass.json')
        const roofsData = await roofsResponse.json()
        useStatsStore.getState().calculateRoofStats(roofsData)

        // Calcola il match score (% di aziende con tetto FV possibile)
        // In un'applicazione reale, questo verrebbe calcolato dal backend
        const totalCompanies = companies.length
        const companiesWithSolarPotential = Math.floor(totalCompanies * 0.65)
        setMatchScore(totalCompanies > 0 ? (companiesWithSolarPotential / totalCompanies) * 100 : 0)

        // Genera dati ROI di esempio
        generateRoiData()
      } catch (_err) {
        // In un'applicazione reale, qui gestiremmo l'errore in modo appropriato
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [companies.length])

  // Genera dati ROI di esempio
  const generateRoiData = () => {
    const data: RoiData[] = []

    companies.forEach((company) => {
      // Genera valori casuali per simulare i dati
      const roofArea = Math.floor(100 + Math.random() * 900)
      const solarPotential = parseFloat((0.3 + Math.random() * 0.6).toFixed(2))
      const currentKwh = Math.floor(10000 + Math.random() * 90000)
      const potentialSavings = Math.floor(currentKwh * solarPotential * 0.15)
      const roi = parseFloat((1 + Math.random() * 4).toFixed(1))
      
      // Determina la priorità in base al ROI
      let priority: 'high' | 'medium' | 'low'
      if (roi > 3.5) {
        priority = 'high'
      } else if (roi > 2) {
        priority = 'medium'
      } else {
        priority = 'low'
      }

      data.push({
        id: company.id,
        name: company.name,
        category: company.category,
        roofArea,
        solarPotential,
        currentKwh,
        potentialSavings,
        roi,
        priority
      })
    })

    setRoiData(data)
  }

  // Gestisce il cambio dell'ordinamento
  const handleSort = (field: keyof RoiData) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  // Ordina i dati
  const sortedData = [...roiData].sort((a, b) => {
    if (a[sortField] < b[sortField]) {
      return sortDirection === 'asc' ? -1 : 1
    }
    if (a[sortField] > b[sortField]) {
      return sortDirection === 'asc' ? 1 : -1
    }
    return 0
  })

  // Formatta i numeri con separatore delle migliaia
  const formatNumber = (num: number): string => {
    return num.toLocaleString('it-IT')
  }

  // Formatta le percentuali
  const formatPercentage = (num: number): string => {
    return `${num.toFixed(1)}%`
  }

  // Ottiene il colore della priorità
  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return 'text-green-500'
      case 'medium': return 'text-yellow-500'
      case 'low': return 'text-gray-500'
      default: return ''
    }
  }

  // Traduce la priorità
  const translatePriority = (priority: string): string => {
    switch (priority) {
      case 'high': return 'Alta'
      case 'medium': return 'Media'
      case 'low': return 'Bassa'
      default: return priority
    }
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Valutazione CRM</h1>
          <div className='flex items-center space-x-2'>
            <Button>
              <Download className="mr-2 h-4 w-4" />
              Esporta Report
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {/* Match Score Gauge */}
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle>Match Score</CardTitle>
              <CardDescription>
                Percentuale di aziende con potenziale fotovoltaico
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center justify-center">
              <div className="relative h-48 w-48">
                {/* Gauge Background */}
                <div className="absolute inset-0 rounded-full border-8 border-gray-200"></div>
                
                {/* Gauge Fill */}
                <div 
                  className="absolute inset-0 rounded-full border-8 border-primary"
                  style={{ 
                    clipPath: `polygon(50% 50%, 0 0, ${matchScore <= 25 ? matchScore * 4 : 100}% 0${matchScore > 25 ? `, 100% ${(matchScore - 25) * 4 <= 100 ? (matchScore - 25) * 4 : 100}%${matchScore > 50 ? `, ${100 - (matchScore - 50) * 4 >= 0 ? 100 - (matchScore - 50) * 4 : 0}% 100%${matchScore > 75 ? `, 0 ${100 - (matchScore - 75) * 4}%` : ''}` : ''}` : ''})` 
                  }}
                ></div>
                
                {/* Gauge Value */}
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <span className="text-4xl font-bold">{Math.round(matchScore)}%</span>
                  <span className="text-sm text-muted-foreground">Match Score</span>
                </div>
              </div>
              
              <div className="mt-4 text-center">
                <p className="text-sm text-muted-foreground">
                  {matchScore < 30 ? 'Basso potenziale di conversione' : 
                   matchScore < 60 ? 'Medio potenziale di conversione' : 
                   'Alto potenziale di conversione'}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Statistiche */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Statistiche ROI</CardTitle>
              <CardDescription>
                Analisi del ritorno sull'investimento per le aziende
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div className="flex flex-col items-center justify-center rounded-lg border p-4">
                  <Star className="mb-2 h-8 w-8 text-yellow-500" />
                  <span className="text-2xl font-bold">
                    {roiData.filter(item => item.priority === 'high').length}
                  </span>
                  <span className="text-sm text-muted-foreground">Priorità Alta</span>
                </div>
                
                <div className="flex flex-col items-center justify-center rounded-lg border p-4">
                  <BarChart4 className="mb-2 h-8 w-8 text-primary" />
                  <span className="text-2xl font-bold">
                    {formatNumber(roiData.reduce((sum, item) => sum + item.potentialSavings, 0))} €
                  </span>
                  <span className="text-sm text-muted-foreground">Risparmio Potenziale</span>
                </div>
                
                <div className="flex flex-col items-center justify-center rounded-lg border p-4">
                  <span className="text-2xl font-bold">
                    {roiData.length > 0 ? (roiData.reduce((sum, item) => sum + item.roi, 0) / roiData.length).toFixed(1) : '0.0'}x
                  </span>
                  <span className="text-sm text-muted-foreground">ROI Medio</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabella ROI */}
          <Card className="md:col-span-3">
            <CardHeader>
              <CardTitle>Priorità ROI</CardTitle>
              <CardDescription>
                Aziende ordinate per potenziale ritorno sull'investimento
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex h-40 items-center justify-center">
                  <p>Caricamento dati...</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>
                          <Button
                            variant="ghost"
                            onClick={() => handleSort('name')}
                            className="flex items-center"
                          >
                            Azienda
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </Button>
                        </TableHead>
                        <TableHead>Categoria</TableHead>
                        <TableHead className="text-right">
                          <Button
                            variant="ghost"
                            onClick={() => handleSort('roofArea')}
                            className="flex items-center"
                          >
                            Area Tetto (m²)
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </Button>
                        </TableHead>
                        <TableHead className="text-right">
                          <Button
                            variant="ghost"
                            onClick={() => handleSort('solarPotential')}
                            className="flex items-center"
                          >
                            Potenziale Solare
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </Button>
                        </TableHead>
                        <TableHead className="text-right">
                          <Button
                            variant="ghost"
                            onClick={() => handleSort('currentKwh')}
                            className="flex items-center"
                          >
                            kWh Attuali
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </Button>
                        </TableHead>
                        <TableHead className="text-right">
                          <Button
                            variant="ghost"
                            onClick={() => handleSort('potentialSavings')}
                            className="flex items-center"
                          >
                            Risparmio Potenziale
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </Button>
                        </TableHead>
                        <TableHead className="text-right">
                          <Button
                            variant="ghost"
                            onClick={() => handleSort('roi')}
                            className="flex items-center"
                          >
                            ROI
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </Button>
                        </TableHead>
                        <TableHead>
                          <Button
                            variant="ghost"
                            onClick={() => handleSort('priority')}
                            className="flex items-center"
                          >
                            Priorità
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </Button>
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {sortedData.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center">
                            Nessun dato disponibile
                          </TableCell>
                        </TableRow>
                      ) : (
                        sortedData.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell className="font-medium">{item.name}</TableCell>
                            <TableCell>{item.category}</TableCell>
                            <TableCell className="text-right">{formatNumber(item.roofArea)}</TableCell>
                            <TableCell className="text-right">{formatPercentage(item.solarPotential * 100)}</TableCell>
                            <TableCell className="text-right">{formatNumber(item.currentKwh)}</TableCell>
                            <TableCell className="text-right">{formatNumber(item.potentialSavings)} €</TableCell>
                            <TableCell className="text-right">{item.roi.toFixed(1)}x</TableCell>
                            <TableCell className={getPriorityColor(item.priority)}>
                              {translatePriority(item.priority)}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Valutazione',
    href: '/crm-evaluation',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Simulazioni',
    href: '/crm-evaluation/simulations',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Reportistica',
    href: '/crm-evaluation/reports',
    isActive: false,
    disabled: false,
  },
]