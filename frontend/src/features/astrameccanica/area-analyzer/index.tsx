import { useState, useEffect, useRef, useCallback } from 'react'
import type { LatLngBounds } from '@/stores/mapStore'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import Map from '@/components/MapCanvas/Map'
import SearchBox from '@/components/common/SearchBox'
import StatsPanel from '@/components/common/StatsPanel'
import CompanyList from '@/components/common/CompanyList'
import BuildingDetailModal from '@/components/common/BuildingDetailModal'
import { useMapStore } from '@/stores/mapStore'
import { useCompaniesStore } from '@/stores/companiesStore'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Download, Layers, Filter, Building2, MapPin, Search as SearchIcon, RefreshCw } from 'lucide-react'
import { areaAnalyzerService } from '@/services/areaAnalyzerService'
import { toast } from 'sonner'

export default function AreaAnalyzer() {
  const { loadGeoJSON, bounds, filters, toggleOSMBuildingsLayer, toggleGoogleBuildingsLayer } = useMapStore()
  const { setCompanies, detectCompaniesInArea } = useCompaniesStore()
  const [isLoading, setIsLoading] = useState(false)
  const [showLayersMenu, setShowLayersMenu] = useState(false)
  const [dataLoaded, setDataLoaded] = useState(false)
  const loadingRef = useRef(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Function to load area data
  const loadAreaData = useCallback(async () => {
    if (!bounds || loadingRef.current) return;
    
    loadingRef.current = true;
    setIsLoading(true);
    
    try {
      const boundsData = {
        swLng: bounds.southwest.lng,
        swLat: bounds.southwest.lat,
        neLng: bounds.northeast.lng,
        neLat: bounds.northeast.lat
      };

      // Load buildings data
      const buildingsData = await areaAnalyzerService.getBuildingsInArea(boundsData);
      if (buildingsData && buildingsData.features) {
        loadGeoJSON(buildingsData);
      }

      // Load companies data  
      const companiesData = await areaAnalyzerService.getCompaniesInArea(boundsData);
      if (companiesData && companiesData.companies) {
        // Map CompanyData to Company format
        const mappedCompanies = companiesData.companies.map((company: any) => ({
          id: company.id,
          name: company.name,
          address: company.address,
          category: company.category || company.type || 'Unknown',
          size: 'medium' as const,
          employees: 0,
          founded: new Date().getFullYear(),
          contact: {
            email: '',
            phone: company.phone || ''
          },
          location: company.location,
          buildings: []
        }));
        setCompanies(mappedCompanies);
      }
      
      setDataLoaded(true);
    } catch (error: any) {
      console.error('Error loading area data:', error);
      // Only show error if it's not an auth error (user might not be logged in)
      if (error?.response?.status !== 401) {
        toast.error('Errore nel caricamento dei dati dell\'area');
      }
    } finally {
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, [bounds, loadGeoJSON, setCompanies]);

  // Load data with debounce when bounds change
  useEffect(() => {
    if (!bounds) return;
    
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Set new timeout with debounce
    timeoutRef.current = setTimeout(() => {
      loadAreaData();
    }, 1000); // Wait 1 second after map stops moving
    
    // Cleanup
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [bounds?.northeast.lat, bounds?.northeast.lng, bounds?.southwest.lat, bounds?.southwest.lng, loadAreaData])

  // Filtra le aziende quando cambiano i bounds della mappa
  // Utilizziamo useCallback per memoizzare la funzione e prevenire loop infiniti
  const { filterCompaniesByBounds } = useCompaniesStore();
  
  // Utilizziamo un riferimento per tenere traccia dell'ultimo bounds processato
  // e prevenire aggiornamenti non necessari
  const lastBoundsRef = useRef<LatLngBounds | null>(null);
  
  useEffect(() => {
    // Verifichiamo se i bounds sono cambiati significativamente per evitare aggiornamenti inutili
    if (bounds && (!lastBoundsRef.current ||
        bounds.northeast.lat !== lastBoundsRef.current.northeast.lat ||
        bounds.northeast.lng !== lastBoundsRef.current.northeast.lng ||
        bounds.southwest.lat !== lastBoundsRef.current.southwest.lat ||
        bounds.southwest.lng !== lastBoundsRef.current.southwest.lng)) {
      
      // Aggiorniamo il riferimento
      lastBoundsRef.current = bounds;
      
      // Filtriamo le aziende con i nuovi bounds
      filterCompaniesByBounds(bounds);
    }
  }, [bounds, filterCompaniesByBounds])

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Analisi Area</h1>
          <div className='flex items-center space-x-2'>
            <Button
              variant="outline"
              onClick={() => {
                loadAreaData().then(() => {
                  toast.success('Dati aggiornati');
                });
              }}
              disabled={isLoading}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Aggiorna
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowLayersMenu(!showLayersMenu)}
            >
              <Layers className="mr-2 h-4 w-4" />
              Layers
            </Button>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filtri
            </Button>
            <Button>
              <Download className="mr-2 h-4 w-4" />
              Esporta
            </Button>
          </div>
        </div>

        {showLayersMenu && (
          <div className="mb-6 bg-card p-4 rounded-md shadow-sm">
            <h2 className="text-lg font-semibold mb-3">Layers</h2>
            <div className="flex flex-col space-y-2">
              <Button
                variant={filters.showOSMBuildingsLayer ? "default" : "outline"}
                className="justify-start"
                onClick={toggleOSMBuildingsLayer}
              >
                <Building2 className="mr-2 h-4 w-4" />
                Edifici OpenStreetMap
              </Button>
              <Button
                variant={filters.showGoogleBuildingsLayer ? "default" : "outline"}
                className="justify-start"
                onClick={toggleGoogleBuildingsLayer}
              >
                <MapPin className="mr-2 h-4 w-4" />
                Edifici Google Maps
              </Button>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Colonna sinistra - Mappa e ricerca */}
          <div className="lg:col-span-2">
            <Card className="mb-6">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle>Mappa Satellitare</CardTitle>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      className="flex items-center space-x-1"
                      onClick={() => detectCompaniesInArea()}
                    >
                      <SearchIcon className="h-4 w-4" />
                      <span>Rileva aziende nell'area</span>
                    </Button>
                    <div className="w-48">
                      <SearchBox placeholder="Cerca indirizzo..." />
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[600px] w-full">
                  {isLoading ? (
                    <div className="flex h-full w-full items-center justify-center">
                      <p>Caricamento mappa...</p>
                    </div>
                  ) : (
                    <Map />
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Statistiche Area</CardTitle>
              </CardHeader>
              <CardContent>
                <StatsPanel />
              </CardContent>
            </Card>
          </div>

          {/* Colonna destra - Aziende */}
          <div>
            <Card className="h-full">
              <CardHeader>
                <CardTitle>Aziende nell'Area</CardTitle>
              </CardHeader>
              <CardContent>
                <CompanyList />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Modal per i dettagli dell'edificio */}
        <BuildingDetailModal />
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Mappa',
    href: '/area-analyzer',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Edifici',
    href: '/area-analyzer/edifici',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Tetti',
    href: '/area-analyzer/tetti',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Aziende',
    href: '/area-analyzer/aziende',
    isActive: false,
    disabled: false,
  },
]