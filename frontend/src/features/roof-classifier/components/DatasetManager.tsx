import React, { useState } from 'react';
import { useNavigate } from '@tanstack/react-router';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import {
  Download,
  Upload,
  MapPin,
  Scissors,
  Database,
  AlertCircle,
  CheckCircle,
  Loader2,
  ExternalLink,
  ArrowRight,
} from 'lucide-react';

interface DatasetManagerProps {
  onStatsUpdate: () => void;
  currentStats: {
    total_images: number;
    total_roofs: number;
    annotated: number;
    pending_annotation: number;
  };
}

export const DatasetManager: React.FC<DatasetManagerProps> = ({
  onStatsUpdate,
  currentStats,
}) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [locations, setLocations] = useState<[number, number][]>([]);
  const [locationText, setLocationText] = useState('');
  const [zoom, setZoom] = useState(19);
  const [progress, setProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState('');
  const [error, setError] = useState('');

  // Sample locations for Italy
  const sampleLocations = [
    [45.4408, 12.3155], // Venezia
    [41.9028, 12.4964], // Roma
    [45.0703, 7.6869],  // Torino
    [40.8518, 14.2681], // Napoli
    [45.4642, 9.1900],  // Milano
    [43.7696, 11.2558], // Firenze
    [44.4949, 11.3426], // Bologna
    [45.4064, 11.8768], // Padova
  ];

  const parseLocations = (text: string): [number, number][] => {
    const lines = text.trim().split('\n');
    const parsed: [number, number][] = [];
    
    for (const line of lines) {
      const parts = line.split(',').map(p => p.trim());
      if (parts.length === 2) {
        const lat = parseFloat(parts[0]);
        const lon = parseFloat(parts[1]);
        if (!isNaN(lat) && !isNaN(lon)) {
          parsed.push([lat, lon]);
        }
      }
    }
    
    return parsed;
  };

  const handleDownloadTiles = async () => {
    if (locations.length === 0) {
      setError('Aggiungi prima alcune posizioni');
      return;
    }

    setIsLoading(true);
    setError('');
    setStatusMessage('Download immagini satellitari...');
    setProgress(0);

    try {
      const response = await fetch('http://localhost:8002/api/roof-training/dataset/download-tiles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          locations: JSON.stringify(locations),
          zoom: zoom.toString(),
          size: '640',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to download tiles');
      }

      const data = await response.json();
      setStatusMessage(`Scaricate ${data.total_downloaded} immagini`);
      setProgress(33);
      
      // Auto-proceed to segmentation
      await handleSegmentRoofs(data.downloaded.map((d: any) => d.path));
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setStatusMessage('');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSegmentRoofs = async (imagePaths?: string[]) => {
    setStatusMessage('Segmentazione tetti con AI...');
    setProgress(33);

    try {
      // If no paths provided, get all images
      if (!imagePaths) {
        // This would need an endpoint to list images
        imagePaths = [];
      }

      const response = await fetch('http://localhost:8002/api/roof-training/dataset/segment-roofs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          image_paths: JSON.stringify(imagePaths),
          min_area_ratio: '0.005',
          max_area_ratio: '0.4',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to segment roofs');
      }

      const data = await response.json();
      setStatusMessage(`Trovati ${data.total_roofs_found} tetti`);
      setProgress(66);
      
      // Auto-proceed to extraction
      await handleExtractCrops(data.results.map((r: any) => r.masks_file).filter(Boolean));
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Segmentation failed');
    }
  };

  const handleExtractCrops = async (segmentationFiles?: string[]) => {
    setStatusMessage('Estrazione ritagli tetti...');
    setProgress(66);

    try {
      if (!segmentationFiles || segmentationFiles.length === 0) {
        throw new Error('No segmentation files available');
      }

      const response = await fetch('http://localhost:8002/api/roof-training/dataset/extract-crops', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          segmentation_files: JSON.stringify(segmentationFiles),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to extract crops');
      }

      const data = await response.json();
      setStatusMessage(`Estratte ${data.new_crops} immagini tetti`);
      setProgress(100);
      
      // Update stats
      onStatsUpdate();
      
      setTimeout(() => {
        setProgress(0);
        setStatusMessage('');
      }, 3000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Extraction failed');
    }
  };

  const handleUseSampleLocations = () => {
    setLocations(sampleLocations as [number, number][]);
    setLocationText(sampleLocations.map(l => `${l[0]}, ${l[1]}`).join('\n'));
  };

  const handleProcessDownloadedImages = async () => {
    setIsLoading(true);
    setError('');
    setStatusMessage('Cercando immagini scaricate dal Map Downloader...');
    setProgress(10);

    try {
      // TODO: Implementare logica per trovare e processare immagini dal Map Downloader
      // Per ora simuliamo il processo
      
      setProgress(30);
      setStatusMessage('Segmentazione tetti con AI...');
      
      // Simula elaborazione
      setTimeout(() => {
        setProgress(60);
        setStatusMessage('Estrazione ritagli tetti...');
      }, 1000);
      
      setTimeout(() => {
        setProgress(100);
        setStatusMessage('Elaborazione completata!');
        onStatsUpdate();
        
        setTimeout(() => {
          setProgress(0);
          setStatusMessage('');
        }, 2000);
      }, 2000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore durante l\'elaborazione');
    } finally {
      setTimeout(() => setIsLoading(false), 2500);
    }
  };

  const handlePrepareTrainingData = async () => {
    setIsLoading(true);
    setError('');
    setStatusMessage('Preparazione split training...');

    try {
      const response = await fetch('http://localhost:8002/api/roof-training/dataset/prepare-training', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          train_ratio: '0.7',
          val_ratio: '0.15',
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to prepare training data');
      }

      const data = await response.json();
      setStatusMessage(`Creati split: Train=${data.splits.train}, Val=${data.splits.val}, Test=${data.splits.test}`);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to prepare data');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Step 1: Download Tiles */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Passo 1: Scarica Immagini Satellitari
          </CardTitle>
          <CardDescription>
            Usa il Map Downloader per scaricare immagini satellitari ad alta risoluzione
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Usa il Map Downloader Integrato</AlertTitle>
            <AlertDescription className="mt-2">
              Per scaricare immagini satellitari, utilizza il nostro strumento dedicato Map Downloader 
              che offre funzionalità avanzate come:
              <ul className="mt-2 ml-4 list-disc text-sm">
                <li>Selezione area con coordinate o indirizzo</li>
                <li>Anteprima mappa interattiva</li>
                <li>Diversi stili di mappa (satellite, ibrido, terreno)</li>
                <li>Export in formato JPEG o GeoTIFF</li>
                <li>Gestione download multipli</li>
              </ul>
            </AlertDescription>
          </Alert>
          
          <div className="flex gap-2">
            <Button
              onClick={() => navigate({ to: '/map-downloader' })}
              className="flex-1"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Vai al Map Downloader
            </Button>
            
            <Button
              variant="outline"
              onClick={() => {
                // Apri in nuova tab
                window.open('/map-downloader', '_blank');
              }}
            >
              Apri in Nuova Tab
            </Button>
          </div>
          
          <div className="border-t pt-4">
            <p className="text-sm text-muted-foreground mb-2">
              <strong>Suggerimenti per il download:</strong>
            </p>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Usa <strong>zoom 19-20</strong> per dettagli dei tetti</li>
              <li>• Seleziona <strong>stile Satellite</strong> per migliore visibilità</li>
              <li>• Scarica in <strong>formato JPEG</strong> per elaborazione più veloce</li>
              <li>• Dopo il download, torna qui per elaborare le immagini</li>
            </ul>
          </div>
        </CardContent>
      </Card>
      
      {/* Step 2: Process Downloaded Images */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scissors className="h-5 w-5" />
            Passo 2: Elabora Immagini Scaricate
          </CardTitle>
          <CardDescription>
            Segmenta automaticamente i tetti dalle immagini scaricate con il Map Downloader
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="default">
            <AlertDescription>
              Dopo aver scaricato le immagini con il Map Downloader, 
              clicca qui per avviare la segmentazione automatica dei tetti usando l'AI.
            </AlertDescription>
          </Alert>
          
          <Button
            onClick={handleProcessDownloadedImages}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <ArrowRight className="h-4 w-4 mr-2" />
            )}
            Elabora Immagini dal Map Downloader
          </Button>
        </CardContent>
      </Card>

      {/* Progress */}
      {progress > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{statusMessage}</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {statusMessage && !isLoading && progress === 100 && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{statusMessage}</AlertDescription>
        </Alert>
      )}

      {/* Current Dataset Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Stato Dataset
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Immagini Totali</p>
              <p className="text-2xl font-bold">{currentStats.total_images}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Tetti Rilevati</p>
              <p className="text-2xl font-bold">{currentStats.total_roofs}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Annotati</p>
              <p className="text-2xl font-bold">{currentStats.annotated}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">In Attesa</p>
              <p className="text-2xl font-bold">{currentStats.pending_annotation}</p>
            </div>
          </div>
          
          {currentStats.annotated > 10 && (
            <div className="mt-4 pt-4 border-t">
              <Button
                onClick={handlePrepareTrainingData}
                disabled={isLoading}
                variant="outline"
                className="w-full"
              >
                Prepara Dati Training (Crea Split Train/Val/Test)
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
