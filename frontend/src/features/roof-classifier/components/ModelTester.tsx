import React, { useState, useRef } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Upload,
  Image as ImageIcon,
  Brain,
  BarChart3,
  AlertCircle,
} from 'lucide-react';

interface Prediction {
  material: string;
  confidence: number;
  color: string;
}

const MATERIAL_COLORS: Record<string, string> = {
  terracotta_tiles: 'bg-orange-500',
  cement_tiles: 'bg-gray-500',
  slate_tiles: 'bg-slate-700',
  metal_sheet: 'bg-zinc-400',
  asphalt_shingles: 'bg-stone-800',
  flat_concrete: 'bg-gray-400',
  green_roof: 'bg-green-600',
  solar_panels: 'bg-blue-700',
  mixed_materials: 'bg-purple-500',
  unknown: 'bg-yellow-500',
};

const MATERIAL_LABELS: Record<string, string> = {
  terracotta_tiles: 'Tegole Terracotta',
  cement_tiles: 'Tegole Cemento',
  slate_tiles: 'Ardesia',
  metal_sheet: 'Lamiera Metallica',
  asphalt_shingles: 'Tegole Bituminose',
  flat_concrete: 'Cemento Piano',
  green_roof: 'Tetto Verde',
  solar_panels: 'Pannelli Solari',
  mixed_materials: 'Materiali Misti',
  unknown: 'Sconosciuto',
};

export const ModelTester: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [predictions, setPredictions] = useState<Prediction[]>([]);
  const [error, setError] = useState('');
  const [modelLoaded, setModelLoaded] = useState(true); // Assume model is loaded
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      setError('');
      setPredictions([]);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      setError('');
      setPredictions([]);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleClassify = async () => {
    if (!selectedImage) {
      setError('Seleziona prima un\'immagine');
      return;
    }

    setIsProcessing(true);
    setError('');
    setPredictions([]);

    try {
      // Convert image to base64
      const reader = new FileReader();
      reader.onloadend = async () => {
        const base64 = reader.result as string;
        
        // Call classification API
        const response = await fetch('http://localhost:8002/api/classification/classify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            image: base64.split(',')[1], // Remove data URL prefix
          }),
        });

        if (!response.ok) {
          throw new Error('Classification failed');
        }

        const data = await response.json();
        
        // Process predictions
        const preds: Prediction[] = Object.entries(data.predictions || {})
          .map(([material, confidence]) => ({
            material,
            confidence: confidence as number,
            color: MATERIAL_COLORS[material] || 'bg-gray-500',
          }))
          .sort((a, b) => b.confidence - a.confidence)
          .slice(0, 5); // Top 5 predictions

        setPredictions(preds);
        setIsProcessing(false);
      };
      
      reader.readAsDataURL(selectedImage);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Classification failed');
      setIsProcessing(false);
    }
  };

  const handleReset = () => {
    setSelectedImage(null);
    setImagePreview('');
    setPredictions([]);
    setError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-4">
      {/* Model Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Stato Modello
            </span>
            <Badge variant={modelLoaded ? 'default' : 'secondary'}>
              {modelLoaded ? 'Pronto' : 'Non Caricato'}
            </Badge>
          </CardTitle>
          <CardDescription>
            Testa il modello addestrato con le tue immagini
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid gap-4 md:grid-cols-2">
        {/* Image Upload */}
        <Card>
          <CardHeader>
            <CardTitle>Carica Immagine</CardTitle>
            <CardDescription>
              Seleziona o trascina un'immagine di tetto da classificare
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div
              className="border-2 border-dashed rounded-lg p-6 text-center hover:border-primary transition-colors cursor-pointer"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onClick={() => fileInputRef.current?.click()}
            >
              {imagePreview ? (
                <div className="space-y-4">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="max-h-64 mx-auto rounded-lg"
                  />
                  <p className="text-sm text-muted-foreground">
                    {selectedImage?.name}
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                  <p className="text-sm font-medium">
                    Rilascia immagine qui o clicca per sfogliare
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Supporta JPG, PNG, WebP
                  </p>
                </div>
              )}
            </div>
            
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageSelect}
              className="hidden"
            />
            
            <div className="flex gap-2 mt-4">
              <Button
                onClick={handleClassify}
                disabled={!selectedImage || isProcessing || !modelLoaded}
                className="flex-1"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Elaborazione...
                  </>
                ) : (
                  <>
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Classifica
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={isProcessing}
              >
                Resetta
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        <Card>
          <CardHeader>
            <CardTitle>Risultati Classificazione</CardTitle>
            <CardDescription>
              Predizioni del modello con punteggi di confidenza
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            {predictions.length > 0 ? (
              <div className="space-y-3">
                {predictions.map((pred, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${pred.color}`} />
                        <span className="font-medium">
                          {MATERIAL_LABELS[pred.material] || pred.material}
                        </span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {(pred.confidence * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={pred.confidence * 100} className="h-2" />
                  </div>
                ))}
                
                {predictions[0] && (
                  <div className="mt-6 p-4 bg-muted rounded-lg">
                    <p className="text-sm font-medium mb-1">Predizione Principale:</p>
                    <div className="flex items-center gap-2">
                      <div className={`w-4 h-4 rounded-full ${predictions[0].color}`} />
                      <p className="text-lg font-bold">
                        {MATERIAL_LABELS[predictions[0].material]}
                      </p>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Confidenza: {(predictions[0].confidence * 100).toFixed(1)}%
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4" />
                    <p>Analisi immagine...</p>
                  </>
                ) : (
                  <>
                    <ImageIcon className="h-12 w-12 mx-auto mb-4" />
                    <p>Nessuna predizione ancora</p>
                    <p className="text-sm mt-1">Carica un'immagine e clicca Classifica</p>
                  </>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Sample Images */}
      <Card>
        <CardHeader>
          <CardTitle>Immagini di Esempio</CardTitle>
          <CardDescription>
            Prova questi esempi precaricati
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 md:grid-cols-8 gap-2">
            {/* Placeholder for sample images */}
            <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
              <ImageIcon className="h-6 w-6 text-muted-foreground" />
            </div>
            <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
              <ImageIcon className="h-6 w-6 text-muted-foreground" />
            </div>
            <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
              <ImageIcon className="h-6 w-6 text-muted-foreground" />
            </div>
            <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
              <ImageIcon className="h-6 w-6 text-muted-foreground" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
