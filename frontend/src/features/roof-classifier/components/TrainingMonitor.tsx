import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Play,
  Pause,
  Square,
  Brain,
  TrendingUp,
  TrendingDown,
  Clock,
  AlertCircle,
} from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

interface TrainingMonitorProps {
  trainingStatus: {
    is_training: boolean;
    current_epoch: number;
    total_epochs: number;
    train_loss: number;
    val_loss: number;
    val_accuracy: number;
    status: string;
    started_at: string | null;
    completed_at: string | null;
  };
  datasetStats: {
    annotated: number;
    total_roofs: number;
  };
  onStatusUpdate: () => void;
}

export const TrainingMonitor: React.FC<TrainingMonitorProps> = ({
  trainingStatus,
  datasetStats,
  onStatusUpdate,
}) => {
  const [epochs, setEpochs] = useState(50);
  const [batchSize, setBatchSize] = useState(32);
  const [learningRate, setLearningRate] = useState(0.0001);
  const [modelName, setModelName] = useState('efficientnet_b3');
  const [isStarting, setIsStarting] = useState(false);
  const [error, setError] = useState('');
  const [trainingHistory, setTrainingHistory] = useState<any[]>([]);

  useEffect(() => {
    // Update training history when status changes
    if (trainingStatus.current_epoch > 0 && trainingStatus.is_training) {
      setTrainingHistory(prev => {
        const newHistory = [...prev];
        const existingIndex = newHistory.findIndex(
          h => h.epoch === trainingStatus.current_epoch
        );
        
        const dataPoint = {
          epoch: trainingStatus.current_epoch,
          train_loss: trainingStatus.train_loss,
          val_loss: trainingStatus.val_loss,
          val_accuracy: trainingStatus.val_accuracy,
        };
        
        if (existingIndex >= 0) {
          newHistory[existingIndex] = dataPoint;
        } else {
          newHistory.push(dataPoint);
        }
        
        return newHistory;
      });
    }
  }, [trainingStatus]);

  const handleStartTraining = async () => {
    setIsStarting(true);
    setError('');
    setTrainingHistory([]);

    try {
      const response = await fetch('http://localhost:8002/api/roof-training/training/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          epochs: epochs.toString(),
          batch_size: batchSize.toString(),
          learning_rate: learningRate.toString(),
          model_name: modelName,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to start training');
      }

      onStatusUpdate();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start training');
    } finally {
      setIsStarting(false);
    }
  };

  const handleStopTraining = async () => {
    try {
      const response = await fetch('http://localhost:8002/api/roof-training/training/stop', {
        method: 'POST',
      });

      if (response.ok) {
        onStatusUpdate();
      }
    } catch (err) {
      console.error('Error stopping training:', err);
    }
  };

  const getElapsedTime = () => {
    if (!trainingStatus.started_at) return 'Not started';
    
    const start = new Date(trainingStatus.started_at).getTime();
    const end = trainingStatus.completed_at 
      ? new Date(trainingStatus.completed_at).getTime()
      : Date.now();
    
    const elapsed = Math.floor((end - start) / 1000);
    const hours = Math.floor(elapsed / 3600);
    const minutes = Math.floor((elapsed % 3600) / 60);
    const seconds = elapsed % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const canStartTraining = datasetStats.annotated >= 10 && !trainingStatus.is_training;

  return (
    <div className="space-y-4">
      {/* Training Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Configurazione Training
          </CardTitle>
          <CardDescription>
            Configura gli iperparametri per l'addestramento del modello
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="epochs">Epoche: {epochs}</Label>
              <Slider
                id="epochs"
                min={10}
                max={200}
                step={10}
                value={[epochs]}
                onValueChange={(value) => setEpochs(value[0])}
                disabled={trainingStatus.is_training}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="batch-size">Dimensione Batch: {batchSize}</Label>
              <Slider
                id="batch-size"
                min={8}
                max={128}
                step={8}
                value={[batchSize]}
                onValueChange={(value) => setBatchSize(value[0])}
                disabled={trainingStatus.is_training}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="learning-rate">
                Tasso di Apprendimento: {learningRate.toExponential(1)}
              </Label>
              <Slider
                id="learning-rate"
                min={-5}
                max={-2}
                step={0.5}
                value={[Math.log10(learningRate)]}
                onValueChange={(value) => setLearningRate(Math.pow(10, value[0]))}
                disabled={trainingStatus.is_training}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="model">Architettura Modello</Label>
              <Select
                value={modelName}
                onValueChange={setModelName}
                disabled={trainingStatus.is_training}
              >
                <SelectTrigger id="model">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="efficientnet_b0">EfficientNet-B0 (Veloce)</SelectItem>
                  <SelectItem value="efficientnet_b3">EfficientNet-B3 (Bilanciato)</SelectItem>
                  <SelectItem value="efficientnet_b7">EfficientNet-B7 (Accurato)</SelectItem>
                  <SelectItem value="resnet50">ResNet-50</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {!canStartTraining && !trainingStatus.is_training && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Servono almeno 10 campioni annotati per iniziare il training.
                Attualmente hai {datasetStats.annotated} annotati.
              </AlertDescription>
            </Alert>
          )}
          
          <div className="flex gap-2">
            {!trainingStatus.is_training ? (
              <Button
                onClick={handleStartTraining}
                disabled={!canStartTraining || isStarting}
                className="flex-1"
              >
                <Play className="h-4 w-4 mr-2" />
                Avvia Training
              </Button>
            ) : (
              <Button
                onClick={handleStopTraining}
                variant="destructive"
                className="flex-1"
              >
                <Square className="h-4 w-4 mr-2" />
                Ferma Training
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Training Progress */}
      {(trainingStatus.is_training || trainingStatus.status === 'completed') && (
        <Card>
          <CardHeader>
            <CardTitle>Progresso Training</CardTitle>
            <CardDescription>
              <div className="flex items-center gap-4">
                <Badge variant={trainingStatus.is_training ? 'default' : 'secondary'}>
                  {trainingStatus.status}
                </Badge>
                <span className="text-sm flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {getElapsedTime()}
                </span>
              </div>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {trainingStatus.is_training && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Epoca {trainingStatus.current_epoch} / {trainingStatus.total_epochs}</span>
                  <span>{Math.round((trainingStatus.current_epoch / trainingStatus.total_epochs) * 100)}%</span>
                </div>
                <Progress
                  value={(trainingStatus.current_epoch / trainingStatus.total_epochs) * 100}
                />
              </div>
            )}
            
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Loss Training</p>
                <p className="text-2xl font-bold flex items-center gap-1">
                  {trainingStatus.train_loss.toFixed(4)}
                  {trainingHistory.length > 1 && (
                    trainingStatus.train_loss < trainingHistory[trainingHistory.length - 2]?.train_loss
                      ? <TrendingDown className="h-4 w-4 text-green-500" />
                      : <TrendingUp className="h-4 w-4 text-red-500" />
                  )}
                </p>
              </div>
              
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Loss Validazione</p>
                <p className="text-2xl font-bold flex items-center gap-1">
                  {trainingStatus.val_loss.toFixed(4)}
                  {trainingHistory.length > 1 && (
                    trainingStatus.val_loss < trainingHistory[trainingHistory.length - 2]?.val_loss
                      ? <TrendingDown className="h-4 w-4 text-green-500" />
                      : <TrendingUp className="h-4 w-4 text-red-500" />
                  )}
                </p>
              </div>
              
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Accuratezza Val</p>
                <p className="text-2xl font-bold flex items-center gap-1">
                  {trainingStatus.val_accuracy.toFixed(1)}%
                  {trainingHistory.length > 1 && (
                    trainingStatus.val_accuracy > trainingHistory[trainingHistory.length - 2]?.val_accuracy
                      ? <TrendingUp className="h-4 w-4 text-green-500" />
                      : <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Training Charts */}
      {trainingHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Metriche Training</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={trainingHistory}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="epoch" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="train_loss"
                    stroke="#8884d8"
                    name="Train Loss"
                    strokeWidth={2}
                  />
                  <Line
                    type="monotone"
                    dataKey="val_loss"
                    stroke="#82ca9d"
                    name="Val Loss"
                    strokeWidth={2}
                  />
                  <Line
                    type="monotone"
                    dataKey="val_accuracy"
                    stroke="#ffc658"
                    name="Val Acc %"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
