import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import {
  ChevronLeft,
  ChevronRight,
  Save,
  SkipForward,
  CheckCircle,
  XCircle,
} from 'lucide-react';

interface Annotation {
  id: string;
  filename: string;
  source_image: string;
  bbox: number[];
  mask_file: string;
  area: number;
  material: string;
  needs_annotation: boolean;
  confidence?: number;
}

interface AnnotationToolProps {
  onAnnotationUpdate: () => void;
  pendingCount: number;
}

const MATERIALS = [
  { value: 'terracotta_tiles', label: 'Tegole Terracotta', color: 'bg-orange-500' },
  { value: 'cement_tiles', label: 'Tegole Cemento', color: 'bg-gray-500' },
  { value: 'slate_tiles', label: 'Ardesia', color: 'bg-slate-700' },
  { value: 'metal_sheet', label: 'Lamiera Metallica', color: 'bg-zinc-400' },
  { value: 'asphalt_shingles', label: 'Tegole Bituminose', color: 'bg-stone-800' },
  { value: 'flat_concrete', label: 'Cemento Piano', color: 'bg-gray-400' },
  { value: 'green_roof', label: 'Tetto Verde', color: 'bg-green-600' },
  { value: 'solar_panels', label: 'Pannelli Solari', color: 'bg-blue-700' },
  { value: 'mixed_materials', label: 'Materiali Misti', color: 'bg-purple-500' },
  { value: 'unknown', label: 'Sconosciuto', color: 'bg-yellow-500' },
];

export const AnnotationTool: React.FC<AnnotationToolProps> = ({
  onAnnotationUpdate,
  pendingCount,
}) => {
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedMaterial, setSelectedMaterial] = useState('unknown');
  const [confidence, setConfidence] = useState(1.0);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [maskUrl, setMaskUrl] = useState('');
  const [annotatedCount, setAnnotatedCount] = useState(0);

  const fetchAnnotations = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        'http://localhost:8002/api/roof-training/dataset/annotations?needs_annotation=true&limit=50'
      );
      if (response.ok) {
        const data = await response.json();
        setAnnotations(data.annotations);
        if (data.annotations.length > 0) {
          loadImage(0, data.annotations);
        }
      }
    } catch (error) {
      console.error('Error fetching annotations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadImage = async (index: number, annotationList?: Annotation[]) => {
    const list = annotationList || annotations;
    if (index < 0 || index >= list.length) return;

    const annotation = list[index];
    setSelectedMaterial(annotation.material || 'unknown');
    setConfidence(annotation.confidence || 1.0);

    // Load image
    const imageResponse = await fetch(
      `http://localhost:8002/api/roof-training/dataset/image/${annotation.filename}`
    );
    if (imageResponse.ok) {
      const blob = await imageResponse.blob();
      setImageUrl(URL.createObjectURL(blob));
    }

    // Load mask if available
    if (annotation.mask_file) {
      const maskResponse = await fetch(
        `http://localhost:8002/api/roof-training/dataset/image/${annotation.mask_file}`
      );
      if (maskResponse.ok) {
        const blob = await maskResponse.blob();
        setMaskUrl(URL.createObjectURL(blob));
      }
    }
  };

  useEffect(() => {
    fetchAnnotations();
  }, []);

  useEffect(() => {
    if (annotations.length > 0) {
      loadImage(currentIndex);
    }
  }, [currentIndex]);

  const handleSave = async () => {
    if (currentIndex >= annotations.length) return;

    const annotation = annotations[currentIndex];
    setIsSaving(true);

    try {
      const response = await fetch(
        `http://localhost:8002/api/roof-training/dataset/annotations/${annotation.id}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            material: selectedMaterial,
            confidence: confidence.toString(),
          }),
        }
      );

      if (response.ok) {
        // Update local state
        const updated = [...annotations];
        updated[currentIndex] = {
          ...updated[currentIndex],
          material: selectedMaterial,
          confidence: confidence,
          needs_annotation: false,
        };
        setAnnotations(updated);
        setAnnotatedCount(annotatedCount + 1);

        // Move to next
        if (currentIndex < annotations.length - 1) {
          setCurrentIndex(currentIndex + 1);
        }

        // Update parent
        onAnnotationUpdate();
      }
    } catch (error) {
      console.error('Error saving annotation:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSkip = () => {
    if (currentIndex < annotations.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < annotations.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const currentAnnotation = annotations[currentIndex];

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Caricamento annotazioni...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (annotations.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <p className="text-lg font-semibold">Tutto aggiornato!</p>
            <p className="text-muted-foreground">Nessuna annotazione in attesa</p>
            <Button onClick={fetchAnnotations} className="mt-4">
              Aggiorna
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Progress Header */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">
              Progresso Annotazioni ({currentIndex + 1} / {annotations.length})
            </span>
            <Badge variant="secondary">
              {annotatedCount} annotate in questa sessione
            </Badge>
          </div>
          <Progress value={((currentIndex + 1) / annotations.length) * 100} />
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2">
        {/* Image Display */}
        <Card>
          <CardHeader>
            <CardTitle>Immagine Tetto</CardTitle>
            <CardDescription>
              {currentAnnotation?.filename || 'No image'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative bg-muted rounded-lg overflow-hidden" style={{ height: '400px' }}>
              {imageUrl && (
                <img
                  src={imageUrl}
                  alt="Roof"
                  className="w-full h-full object-contain"
                />
              )}
              {maskUrl && (
                <img
                  src={maskUrl}
                  alt="Mask"
                  className="absolute inset-0 w-full h-full object-contain opacity-30"
                />
              )}
            </div>
            
            {currentAnnotation && (
              <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-muted-foreground">Area:</span>{' '}
                  <span className="font-medium">{currentAnnotation.area} px²</span>
                </div>
                <div>
                  <span className="text-muted-foreground">ID:</span>{' '}
                  <span className="font-medium">{currentAnnotation.id}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Annotation Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Classifica Materiale</CardTitle>
            <CardDescription>
              Seleziona il materiale di copertura principale visibile nell'immagine
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="material">Tipo Materiale</Label>
              <Select value={selectedMaterial} onValueChange={setSelectedMaterial}>
                <SelectTrigger id="material">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {MATERIALS.map((material) => (
                    <SelectItem key={material.value} value={material.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${material.color}`} />
                        {material.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confidence">
                Confidenza: {(confidence * 100).toFixed(0)}%
              </Label>
              <Slider
                id="confidence"
                min={0}
                max={1}
                step={0.1}
                value={[confidence]}
                onValueChange={(value) => setConfidence(value[0])}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">
                Quanto sei sicuro di questa classificazione?
              </p>
            </div>

            {/* Quick Select Buttons */}
            <div className="space-y-2">
              <Label>Selezione Rapida</Label>
              <div className="grid grid-cols-2 gap-2">
                {MATERIALS.slice(0, 6).map((material) => (
                  <Button
                    key={material.value}
                    variant={selectedMaterial === material.value ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedMaterial(material.value)}
                    className="justify-start"
                  >
                    <div className={`w-3 h-3 rounded-full ${material.color} mr-2`} />
                    {material.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevious}
                disabled={currentIndex === 0}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="default"
                className="flex-1"
                onClick={handleSave}
                disabled={isSaving}
              >
                <Save className="h-4 w-4 mr-2" />
                Salva e Avanti
              </Button>
              
              <Button
                variant="outline"
                onClick={handleSkip}
              >
                <SkipForward className="h-4 w-4 mr-2" />
                Salta
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleNext}
                disabled={currentIndex === annotations.length - 1}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
