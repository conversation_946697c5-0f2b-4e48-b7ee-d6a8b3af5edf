import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Download,
  Upload,
  Play,
  Pause,
  RefreshCw,
  Database,
  Brain,
  Image as ImageIcon,
  MapPin,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react';
import { DatasetManager } from './components/DatasetManager';
import { AnnotationTool } from './components/AnnotationTool';
import { TrainingMonitor } from './components/TrainingMonitor';
import { ModelTester } from './components/ModelTester';

interface DatasetStats {
  total_images: number;
  total_roofs: number;
  annotated: number;
  pending_annotation: number;
  last_updated: string | null;
}

interface TrainingStatus {
  is_training: boolean;
  current_epoch: number;
  total_epochs: number;
  train_loss: number;
  val_loss: number;
  val_accuracy: number;
  status: string;
  started_at: string | null;
  completed_at: string | null;
}

export const RoofClassifierDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dataset');
  const [datasetStats, setDatasetStats] = useState<DatasetStats>({
    total_images: 0,
    total_roofs: 0,
    annotated: 0,
    pending_annotation: 0,
    last_updated: null,
  });
  const [trainingStatus, setTrainingStatus] = useState<TrainingStatus>({
    is_training: false,
    current_epoch: 0,
    total_epochs: 0,
    train_loss: 0,
    val_loss: 0,
    val_accuracy: 0,
    status: 'idle',
    started_at: null,
    completed_at: null,
  });
  const [isLoading, setIsLoading] = useState(false);

  // Fetch dataset stats
  const fetchDatasetStats = async () => {
    try {
      const response = await fetch('http://localhost:8002/api/roof-training/dataset/status');
      if (response.ok) {
        const data = await response.json();
        setDatasetStats(data);
      }
    } catch (error) {
      console.error('Error fetching dataset stats:', error);
    }
  };

  // Fetch training status
  const fetchTrainingStatus = async () => {
    try {
      const response = await fetch('http://localhost:8002/api/roof-training/training/status');
      if (response.ok) {
        const data = await response.json();
        setTrainingStatus(data);
      }
    } catch (error) {
      console.error('Error fetching training status:', error);
    }
  };

  useEffect(() => {
    fetchDatasetStats();
    fetchTrainingStatus();

    // Poll for updates
    const interval = setInterval(() => {
      fetchDatasetStats();
      if (trainingStatus.is_training) {
        fetchTrainingStatus();
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [trainingStatus.is_training]);

  const getStatusIcon = (status: string) => {
    if (status === 'completed') return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (status.includes('error')) return <XCircle className="h-4 w-4 text-red-500" />;
    if (status === 'idle') return <AlertCircle className="h-4 w-4 text-gray-400" />;
    return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Classificatore Materiali Tetti</h1>
          <p className="text-muted-foreground">
            Addestra modelli AI per classificare materiali di copertura da immagini satellitari
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => {
              fetchDatasetStats();
              fetchTrainingStatus();
            }}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Aggiorna
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Immagini Totali</CardTitle>
            <ImageIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{datasetStats.total_images}</div>
            <p className="text-xs text-muted-foreground">
              Immagini satellitari scaricate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tetti Rilevati</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{datasetStats.total_roofs}</div>
            <p className="text-xs text-muted-foreground">
              {datasetStats.annotated} annotati
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stato Training</CardTitle>
            {getStatusIcon(trainingStatus.status)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {trainingStatus.is_training ? (
                `${trainingStatus.current_epoch}/${trainingStatus.total_epochs}`
              ) : (
                <Badge variant={trainingStatus.status === 'completed' ? 'default' : 'secondary'}>
                  {trainingStatus.status === 'completed' ? 'Completato' : 
                   trainingStatus.status === 'idle' ? 'Inattivo' : trainingStatus.status}
                </Badge>
              )}
            </div>
            {trainingStatus.is_training && (
              <Progress 
                value={(trainingStatus.current_epoch / trainingStatus.total_epochs) * 100} 
                className="mt-2"
              />
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Accuratezza Modello</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {trainingStatus.val_accuracy > 0 
                ? `${trainingStatus.val_accuracy.toFixed(1)}%`
                : 'N/D'}
            </div>
            <p className="text-xs text-muted-foreground">
              Accuratezza validazione
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dataset">Dataset</TabsTrigger>
          <TabsTrigger value="annotation">Annotazione</TabsTrigger>
          <TabsTrigger value="training">Training</TabsTrigger>
          <TabsTrigger value="testing">Test</TabsTrigger>
        </TabsList>

        <TabsContent value="dataset" className="space-y-4">
          <DatasetManager 
            onStatsUpdate={fetchDatasetStats}
            currentStats={datasetStats}
          />
        </TabsContent>

        <TabsContent value="annotation" className="space-y-4">
          <AnnotationTool
            onAnnotationUpdate={fetchDatasetStats}
            pendingCount={datasetStats.pending_annotation}
          />
        </TabsContent>

        <TabsContent value="training" className="space-y-4">
          <TrainingMonitor
            trainingStatus={trainingStatus}
            datasetStats={datasetStats}
            onStatusUpdate={fetchTrainingStatus}
          />
        </TabsContent>

        <TabsContent value="testing" className="space-y-4">
          <ModelTester />
        </TabsContent>
      </Tabs>
    </div>
  );
};