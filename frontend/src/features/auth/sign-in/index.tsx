import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import AuthLayout from '../auth-layout'
import { UserAuthForm } from './components/user-auth-form'
import { Navigate } from '@tanstack/react-router'
import { authService } from '@/services/authService'

export default function SignIn() {
  // Redirect if already authenticated
  if (authService.isAuthenticated()) {
    return <Navigate to="/" />
  }
  return (
    <AuthLayout>
      <Card className='gap-4'>
        <CardHeader>
          <CardTitle className='text-lg tracking-tight'>Accedi</CardTitle>
          <CardDescription>
            Inserisci email e password per accedere <br />
            al tuo account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UserAuthForm />
        </CardContent>
      </Card>
    </AuthLayout>
  )
}
