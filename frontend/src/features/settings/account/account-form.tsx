import { useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { PasswordInput } from '@/components/password-input'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import { authService } from '@/services/authService'
import { userService } from '@/services/userService'
import { Shield, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

const passwordFormSchema = z.object({
  currentPassword: z.string().min(1, 'Password attuale richiesta'),
  newPassword: z
    .string()
    .min(7, 'La nuova password deve essere di almeno 7 caratteri')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'La password deve contenere almeno una lettera maiuscola, una minuscola e un numero'
    ),
  confirmPassword: z.string().min(1, 'Conferma password richiesta'),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: 'Le password non corrispondono',
  path: ['confirmPassword'],
})

type PasswordFormValues = z.infer<typeof passwordFormSchema>

export function AccountForm() {
  const [isLoading, setIsLoading] = useState(false)
  const currentUser = authService.getUser()
  
  const form = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  })

  async function onSubmit(data: PasswordFormValues) {
    if (!currentUser?.id) return
    
    setIsLoading(true)
    try {
      // Verifica prima la password attuale facendo login
      const loginResult = await authService.login({
        email: currentUser.email,
        password: data.currentPassword
      })
      
      if (!loginResult.success) {
        form.setError('currentPassword', {
          type: 'manual',
          message: 'Password attuale non corretta'
        })
        return
      }
      
      // Aggiorna la password
      await userService.updateUser(currentUser.id, {
        password: data.newPassword
      })
      
      toast.success('Password aggiornata con successo')
      form.reset()
    } catch (error) {
      toast.error('Errore nell\'aggiornamento della password')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Sicurezza Account</h3>
        <p className="text-sm text-muted-foreground">
          Gestisci le impostazioni di sicurezza del tuo account
        </p>
      </div>
      
      <Separator />
      
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <strong>Informazioni Account</strong>
          <div className="mt-2 space-y-1 text-sm">
            <p>Email: {currentUser?.email}</p>
            <p>Ruolo: {currentUser?.role === 'admin' ? 'Amministratore' : 'Utente'}</p>
            <p>ID: {currentUser?.id}</p>
          </div>
        </AlertDescription>
      </Alert>

      <div>
        <h4 className="text-md font-medium mb-4">Cambia Password</h4>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="currentPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password Attuale</FormLabel>
                  <FormControl>
                    <PasswordInput 
                      placeholder="Inserisci la password attuale" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nuova Password</FormLabel>
                  <FormControl>
                    <PasswordInput 
                      placeholder="Inserisci la nuova password" 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Minimo 7 caratteri, deve contenere maiuscole, minuscole e numeri
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Conferma Nuova Password</FormLabel>
                  <FormControl>
                    <PasswordInput 
                      placeholder="Conferma la nuova password" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="flex gap-4 pt-4">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Aggiornamento...' : 'Aggiorna Password'}
              </Button>
              <Button 
                type="button" 
                variant="outline"
                onClick={() => form.reset()}
                disabled={isLoading}
              >
                Annulla
              </Button>
            </div>
          </form>
        </Form>
      </div>
      
      <Separator />
      
      <div className="pt-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Eliminazione Account</strong>
            <p className="mt-1">
              Per eliminare il tuo account, contatta l'amministratore del sistema.
            </p>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}