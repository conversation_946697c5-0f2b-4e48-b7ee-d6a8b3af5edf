import { useState, useEffect } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { authService } from '@/services/authService'
import { userService } from '@/services/userService'
import { toast } from 'sonner'

const profileFormSchema = z.object({
  name: z
    .string()
    .min(2, {
      message: 'Il nome deve essere di almeno 2 caratteri.',
    })
    .max(50, {
      message: 'Il nome non può superare i 50 caratteri.',
    }),
  email: z
    .string({
      required_error: 'Email richiesta.',
    })
    .email('Inserisci un indirizzo email valido'),
  bio: z.string().max(500).optional(),
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

export default function ProfileForm() {
  const [isLoading, setIsLoading] = useState(false)
  const currentUser = authService.getUser()
  
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: currentUser?.name || '',
      email: currentUser?.email || '',
      bio: '',
    },
    mode: 'onChange',
  })

  useEffect(() => {
    // Carica i dati dell'utente dal backend
    const loadUserData = async () => {
      if (currentUser?.id) {
        try {
          const userData = await userService.getUserById(currentUser.id)
          form.reset({
            name: userData.name,
            email: userData.email,
            bio: '', // TODO: Aggiungere campo bio nel backend
          })
        } catch (error) {
          console.error('Errore nel caricamento dati utente:', error)
        }
      }
    }
    loadUserData()
  }, [currentUser?.id, form])

  const onSubmit = async (data: ProfileFormValues) => {
    if (!currentUser?.id) return
    
    setIsLoading(true)
    try {
      const updatedUser = await userService.updateUser(currentUser.id, {
        name: data.name,
        // L'email non può essere modificata per ora
      })
      
      // Aggiorna i dati nel localStorage
      const token = authService.getToken()
      if (token) {
        localStorage.setItem('astrameccanica_user', JSON.stringify({
          ...currentUser,
          name: updatedUser.name,
        }))
      }
      
      toast.success('Profilo aggiornato con successo')
    } catch (error) {
      toast.error('Errore nell\'aggiornamento del profilo')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className='space-y-8'
      >
        <FormField
          control={form.control}
          name='name'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input placeholder='Il tuo nome' {...field} />
              </FormControl>
              <FormDescription>
                Questo è il tuo nome pubblico. Verrà mostrato agli altri utenti.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input 
                  type="email" 
                  {...field} 
                  disabled 
                  className="opacity-60"
                />
              </FormControl>
              <FormDescription>
                L'indirizzo email non può essere modificato. Contatta l'amministratore per cambiarlo.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='bio'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Biografia</FormLabel>
              <FormControl>
                <Textarea
                  placeholder='Raccontaci qualcosa di te...'
                  className='resize-none'
                  rows={4}
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Puoi scrivere una breve descrizione di te stesso. Massimo 500 caratteri.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex gap-4">
          <Button type='submit' disabled={isLoading}>
            {isLoading ? 'Salvataggio...' : 'Aggiorna profilo'}
          </Button>
          <Button 
            type='button' 
            variant='outline'
            onClick={() => form.reset()}
            disabled={isLoading}
          >
            Annulla
          </Button>
        </div>
      </form>
    </Form>
  )
}