import { useState, useEffect } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { toast } from 'sonner'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Loader2, UserPlus, Trash2, Shield, User, Crown, Send, Settings } from 'lucide-react'
import { organizationService, type Organization } from '@/services/organizationService'
import axios from 'axios'

const organizationFormSchema = z.object({
  name: z.string().min(2, {
    message: 'Il nome deve essere di almeno 2 caratteri.',
  }),
  plan: z.enum(['base', 'professional', 'premium', 'enterprise']),
})

type OrganizationFormValues = z.infer<typeof organizationFormSchema>

const smtpConfigSchema = z.object({
  host: z.string().min(1, 'Host richiesto'),
  port: z.string().min(1, 'Porta richiesta'),
  secure: z.boolean(),
  from: z.string().email('Email non valida'),
  fromName: z.string().optional(),
  auth: z.object({
    user: z.string().min(1, 'Username richiesto'),
    pass: z.string().optional(),
  }),
})

type SmtpConfigValues = z.infer<typeof smtpConfigSchema>

export default function OrganizationForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [newUserEmail, setNewUserEmail] = useState('')
  const [newUserRole, setNewUserRole] = useState<'admin' | 'member'>('member')
  const [isAddingUser, setIsAddingUser] = useState(false)
  const [smtpConfig, setSmtpConfig] = useState<SmtpConfigValues | null>(null)
  const [isTestingSmtp, setIsTestingSmtp] = useState(false)
  const [isSavingSmtp, setIsSavingSmtp] = useState(false)
  const [testEmail, setTestEmail] = useState('')
  const [hasExistingPassword, setHasExistingPassword] = useState(false)

  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationFormSchema),
    defaultValues: {
      name: '',
      plan: 'base',
    },
  })

  const smtpForm = useForm<SmtpConfigValues>({
    resolver: zodResolver(smtpConfigSchema),
    defaultValues: {
      host: '',
      port: '587',
      secure: false,
      from: '',
      fromName: '',
      auth: {
        user: '',
        pass: '',
      },
    },
  })

  useEffect(() => {
    loadOrganization()
    loadSmtpConfig()
  }, [])

  const loadOrganization = async () => {
    try {
      const orgId = organizationService.getCurrentOrganizationId()
      if (!orgId) {
        toast.error('Nessuna organizzazione selezionata')
        return
      }

      const org = await organizationService.getOrganizationById(orgId)
      setOrganization(org)
      
      form.reset({
        name: org.name,
        plan: org.plan as 'base' | 'professional' | 'premium' | 'enterprise',
      })
    } catch (error) {
      console.error('Error loading organization:', error)
      toast.error('Errore nel caricamento dell\'organizzazione')
    }
  }

  const loadSmtpConfig = async () => {
    try {
      const orgId = organizationService.getCurrentOrganizationId()
      if (!orgId) return

      const token = localStorage.getItem('astrameccanica_token')
      const response = await axios.get('/api/email/smtp-config', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-organization-id': orgId
        }
      })

      if (response.data.config) {
        const config = response.data.config
        smtpForm.reset({
          host: config.host || '',
          port: config.port?.toString() || '587',
          secure: config.secure || false,
          from: config.from || '',
          fromName: config.fromName || '',
          auth: {
            user: config.auth?.user || '',
            pass: '', // Lascia vuota, non mostrare password mascherata nel form
          },
        })
        setSmtpConfig(config)
        setHasExistingPassword(true) // Indica che esiste già una password salvata
      }
    } catch (error) {
      console.error('Error loading SMTP config:', error)
    }
  }

  async function onSubmit(data: OrganizationFormValues) {
    setIsLoading(true)
    
    try {
      if (!organization) {
        toast.error('Nessuna organizzazione selezionata')
        return
      }

      await organizationService.updateOrganization(organization.id, {
        name: data.name,
        plan: data.plan,
      })
      
      toast.success('Organizzazione aggiornata con successo')
      await loadOrganization()
    } catch (error) {
      console.error('Error updating organization:', error)
      toast.error('Errore nell\'aggiornamento dell\'organizzazione')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddUser = async () => {
    if (!newUserEmail || !organization) return
    
    setIsAddingUser(true)
    try {
      await organizationService.addUserToOrganization(organization.id, {
        email: newUserEmail,
        role: newUserRole,
      })
      
      toast.success('Utente aggiunto con successo')
      setIsAddUserDialogOpen(false)
      setNewUserEmail('')
      setNewUserRole('member')
      await loadOrganization()
    } catch (error: any) {
      console.error('Error adding user:', error)
      if (error.response?.status === 404) {
        toast.error('Utente non trovato. L\'utente deve prima registrarsi.')
      } else if (error.response?.status === 400) {
        toast.error('L\'utente è già membro dell\'organizzazione')
      } else {
        toast.error('Errore nell\'aggiunta dell\'utente')
      }
    } finally {
      setIsAddingUser(false)
    }
  }

  const handleRemoveUser = async (userId: string) => {
    if (!organization) return
    
    if (!confirm('Sei sicuro di voler rimuovere questo utente?')) return
    
    try {
      await organizationService.removeUserFromOrganization(organization.id, userId)
      toast.success('Utente rimosso con successo')
      await loadOrganization()
    } catch (error) {
      console.error('Error removing user:', error)
      toast.error('Errore nella rimozione dell\'utente')
    }
  }

  const handleUpdateUserRole = async (userId: string, newRole: 'owner' | 'admin' | 'member') => {
    if (!organization) return
    
    try {
      await organizationService.updateUserRole(organization.id, userId, newRole)
      toast.success('Ruolo aggiornato con successo')
      await loadOrganization()
    } catch (error) {
      console.error('Error updating user role:', error)
      toast.error('Errore nell\'aggiornamento del ruolo')
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4" />
      case 'admin':
        return <Shield className="h-4 w-4" />
      default:
        return <User className="h-4 w-4" />
    }
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'owner':
        return 'default' as const
      case 'admin':
        return 'secondary' as const
      default:
        return 'outline' as const
    }
  }

  const handleSaveSmtpConfig = async (data: SmtpConfigValues) => {
    setIsSavingSmtp(true)
    try {
      const orgId = organizationService.getCurrentOrganizationId()
      if (!orgId) {
        toast.error('Nessuna organizzazione selezionata')
        return
      }

      const token = localStorage.getItem('astrameccanica_token')
      
      // Determina se aggiornare la password
      const updatePassword = !!data.auth.pass // Aggiorna solo se c'è una nuova password
      
      // Se non c'è password e non esiste una password salvata, errore
      if (!data.auth.pass && !hasExistingPassword) {
        toast.error('La password è richiesta per la prima configurazione')
        setIsSavingSmtp(false)
        return
      }
      
      await axios.post('/api/email/smtp-config', {
        ...data,
        port: parseInt(data.port),
        updatePassword,
        auth: {
          user: data.auth.user,
          pass: updatePassword ? data.auth.pass : undefined // Invia password solo se da aggiornare
        }
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-organization-id': orgId
        }
      })

      toast.success('Configurazione SMTP salvata con successo')
      await loadSmtpConfig()
    } catch (error: any) {
      console.error('Error saving SMTP config:', error)
      toast.error(error.response?.data?.error || 'Errore nel salvataggio della configurazione SMTP')
    } finally {
      setIsSavingSmtp(false)
    }
  }

  const handleTestSmtp = async () => {
    setIsTestingSmtp(true)
    try {
      const values = smtpForm.getValues()
      const orgId = organizationService.getCurrentOrganizationId()
      
      // Se c'è una configurazione esistente ma nessuna nuova password, usa la configurazione del server
      if (hasExistingPassword && !values.auth.pass) {
        // Testa usando la configurazione salvata sul server
        const token = localStorage.getItem('astrameccanica_token')
        
        // Prima ottieni la configurazione dal server con la password
        await axios.get('/api/email/smtp-config', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'x-organization-id': orgId
          }
        })
        
        // Usa i valori del form ma recupera la password dal backend tramite un endpoint dedicato
        const testConfig = {
          host: values.host,
          port: parseInt(values.port),
          secure: values.secure,
          from: values.from,
          fromName: values.fromName,
          auth: {
            user: values.auth.user,
            pass: 'USE_EXISTING' // Indica al backend di usare la password esistente
          },
          testEmail: testEmail || values.from,
          useExistingPassword: true,
          organizationId: orgId
        }
        
        const response = await axios.post('/api/email/test-smtp-existing', testConfig, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'x-organization-id': orgId
          }
        })
        
        toast.success(response.data.message || 'Email di test inviata con successo!')
      } else if (values.auth.pass) {
        // Testa con la nuova password inserita
        const token = localStorage.getItem('astrameccanica_token')
        
        const response = await axios.post('/api/email/test-smtp', {
          ...values,
          port: parseInt(values.port),
          testEmail: testEmail || values.from
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        toast.success(response.data.message || 'Email di test inviata con successo!')
      } else {
        toast.error('Inserisci la password per testare la configurazione')
        setIsTestingSmtp(false)
        return
      }
    } catch (error: any) {
      console.error('Error testing SMTP:', error)
      toast.error(error.response?.data?.error || 'Errore nel test della configurazione')
    } finally {
      setIsTestingSmtp(false)
    }
  }

  const currentUser = JSON.parse(localStorage.getItem('astrameccanica_user') || '{}')
  const currentUserRole = organization?.users?.find(u => u.userId === currentUser.id)?.role

  return (
    <Tabs defaultValue="general" className="space-y-4">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="general">Generale</TabsTrigger>
        <TabsTrigger value="members">Membri</TabsTrigger>
        <TabsTrigger value="email">Email</TabsTrigger>
      </TabsList>

      <TabsContent value="general" className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
          <FormField
            control={form.control}
            name='name'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome Organizzazione</FormLabel>
                <FormControl>
                  <Input
                    placeholder='Astra Meccanica'
                    {...field}
                    disabled={currentUserRole !== 'owner' && currentUserRole !== 'admin'}
                  />
                </FormControl>
                <FormDescription>
                  Il nome della tua organizzazione come apparirà in tutta l'applicazione
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='plan'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Piano</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                  disabled={currentUserRole !== 'owner'}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona un piano" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="base">Base</SelectItem>
                    <SelectItem value="professional">Professional</SelectItem>
                    <SelectItem value="premium">Premium</SelectItem>
                    <SelectItem value="enterprise">Enterprise</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Il piano determina le funzionalità disponibili per la tua organizzazione
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {(currentUserRole === 'owner' || currentUserRole === 'admin') && (
            <Button type='submit' disabled={isLoading}>
              {isLoading && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
              Salva Modifiche
            </Button>
          )}
        </form>
      </Form>
    </TabsContent>

    <TabsContent value="members" className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Membri dell'Organizzazione</CardTitle>
              <CardDescription>
                Gestisci gli utenti che hanno accesso a questa organizzazione
              </CardDescription>
            </div>
            {(currentUserRole === 'owner' || currentUserRole === 'admin') && (
              <Button onClick={() => setIsAddUserDialogOpen(true)} size="sm">
                <UserPlus className="mr-2 h-4 w-4" />
                Aggiungi Utente
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nome</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Ruolo</TableHead>
                {(currentUserRole === 'owner' || currentUserRole === 'admin') && (
                  <TableHead>Azioni</TableHead>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {organization?.users?.map((userOrg) => (
                <TableRow key={userOrg.userId}>
                  <TableCell>{userOrg.user?.name || 'N/A'}</TableCell>
                  <TableCell>{userOrg.user?.email || 'N/A'}</TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(userOrg.role)}>
                      <span className="flex items-center gap-1">
                        {getRoleIcon(userOrg.role)}
                        {userOrg.role === 'owner' ? 'Proprietario' :
                         userOrg.role === 'admin' ? 'Amministratore' : 'Membro'}
                      </span>
                    </Badge>
                  </TableCell>
                  {(currentUserRole === 'owner' || currentUserRole === 'admin') && (
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {currentUserRole === 'owner' && userOrg.role !== 'owner' && (
                          <Select
                            value={userOrg.role}
                            onValueChange={(value) => 
                              handleUpdateUserRole(userOrg.userId, value as 'owner' | 'admin' | 'member')
                            }
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="admin">Admin</SelectItem>
                              <SelectItem value="member">Membro</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                        {userOrg.role !== 'owner' && userOrg.userId !== currentUser.id && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveUser(userOrg.userId)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </TabsContent>

    <TabsContent value="email" className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Configurazione SMTP</CardTitle>
          <CardDescription>
            Configura il server SMTP per l'invio delle email dall'applicazione
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...smtpForm}>
            <form onSubmit={smtpForm.handleSubmit(handleSaveSmtpConfig)} className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={smtpForm.control}
                  name="host"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Host SMTP</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="smtp.gmail.com"
                          {...field}
                          disabled={currentUserRole !== 'owner' && currentUserRole !== 'admin'}
                        />
                      </FormControl>
                      <FormDescription>
                        Server SMTP (es. smtp.gmail.com)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="port"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Porta</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="587"
                          {...field}
                          disabled={currentUserRole !== 'owner' && currentUserRole !== 'admin'}
                        />
                      </FormControl>
                      <FormDescription>
                        Porta SMTP (es. 587 per TLS, 465 per SSL)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={smtpForm.control}
                name="secure"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Connessione Sicura (SSL/TLS)</FormLabel>
                      <FormDescription>
                        Attiva per usare SSL (porta 465), disattiva per TLS/STARTTLS (porta 587)
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={currentUserRole !== 'owner' && currentUserRole !== 'admin'}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={smtpForm.control}
                  name="from"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Mittente</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          disabled={currentUserRole !== 'owner' && currentUserRole !== 'admin'}
                        />
                      </FormControl>
                      <FormDescription>
                        Indirizzo email del mittente
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="fromName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome Mittente</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="AstraMeccanica"
                          {...field}
                          disabled={currentUserRole !== 'owner' && currentUserRole !== 'admin'}
                        />
                      </FormControl>
                      <FormDescription>
                        Nome visualizzato del mittente
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={smtpForm.control}
                  name="auth.user"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          {...field}
                          disabled={currentUserRole !== 'owner' && currentUserRole !== 'admin'}
                        />
                      </FormControl>
                      <FormDescription>
                        Username per l'autenticazione SMTP
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="auth.pass"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder={hasExistingPassword ? "Lascia vuoto per mantenere la password attuale" : "Inserisci password"}
                          {...field}
                          disabled={currentUserRole !== 'owner' && currentUserRole !== 'admin'}
                        />
                      </FormControl>
                      <FormDescription>
                        {hasExistingPassword 
                          ? "Lascia vuoto per mantenere la password salvata" 
                          : "Password per l'autenticazione SMTP"}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {(currentUserRole === 'owner' || currentUserRole === 'admin') && (
                <div className="flex gap-4">
                  <Button type="submit" disabled={isSavingSmtp}>
                    {isSavingSmtp && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    <Settings className="mr-2 h-4 w-4" />
                    Salva Configurazione
                  </Button>
                </div>
              )}
            </form>
          </Form>

          {smtpConfig && (currentUserRole === 'owner' || currentUserRole === 'admin') && (
            <div className="mt-6 pt-6 border-t">
              <h4 className="text-sm font-medium mb-4">Test Configurazione</h4>
              <div className="flex gap-4 items-end">
                <div className="flex-1">
                  <Label htmlFor="testEmail">Email di Test (opzionale)</Label>
                  <Input
                    id="testEmail"
                    type="email"
                    placeholder="<EMAIL>"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    className="mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Lascia vuoto per inviare all'email del mittente
                  </p>
                </div>
                <Button
                  onClick={handleTestSmtp}
                  disabled={isTestingSmtp}
                  variant="outline"
                >
                  {isTestingSmtp && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Send className="mr-2 h-4 w-4" />
                  Invia Email di Test
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </TabsContent>

      <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Aggiungi Nuovo Utente</DialogTitle>
            <DialogDescription>
              Invita un utente a far parte della tua organizzazione
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={newUserEmail}
                onChange={(e) => setNewUserEmail(e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="role">Ruolo</Label>
              <Select
                value={newUserRole}
                onValueChange={(value) => setNewUserRole(value as 'admin' | 'member')}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Amministratore</SelectItem>
                  <SelectItem value="member">Membro</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsAddUserDialogOpen(false)}
              disabled={isAddingUser}
            >
              Annulla
            </Button>
            <Button 
              onClick={handleAddUser}
              disabled={isAddingUser || !newUserEmail}
            >
              {isAddingUser && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Aggiungi Utente
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Tabs>
  )
}