import { useState, useEffect } from 'react'
import { userPreferencesService, MapDownloaderPreferences } from '@/services/userPreferencesService'

export type MapSettings = MapDownloaderPreferences

const DEFAULT_SETTINGS: MapSettings = {
  defaultStyle: 's',
  defaultFormat: 'jpeg',
  defaultZoom: '18',
  autoDownload: false,
  saveHistory: true,
  cacheEnabled: true,
  cacheSize: '500',
  compressionLevel: '80',
  maxConcurrentDownloads: '3',
  notifyOnComplete: true,
  notifyOnError: true,
  emailNotifications: false,
  notificationEmail: '',
  apiKey: '',
  apiEndpoint: 'http://localhost:3000/api'
}

export function useMapSettings() {
  const [settings, setSettings] = useState<MapSettings>(DEFAULT_SETTINGS)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Carica le preferenze dal database
    const loadPreferences = async () => {
      try {
        const preferences = await userPreferencesService.getPreferences()
        setSettings(preferences.mapDownloader)
      } catch (error) {
        console.error('Error loading map settings:', error)
        // Usa i valori predefiniti in caso di errore
        setSettings(DEFAULT_SETTINGS)
      } finally {
        setIsLoading(false)
      }
    }

    loadPreferences()

    // Opzionale: ricarica le preferenze quando la finestra torna attiva
    const handleFocus = () => {
      loadPreferences()
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [])

  return { settings, isLoading }
}