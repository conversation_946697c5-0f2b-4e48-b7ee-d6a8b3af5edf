// Service per SAM su GPU locale
import axios from 'axios';

// Configura l'URL del tuo server SAM (container LXC)
const SAM_SERVER_URL = import.meta.env.VITE_SAM_SERVER_URL || 'http://192.168.0.110:8080';

export interface Point {
  x: number;
  y: number;
  label: number; // 1 = foreground, 0 = background
}

export interface BoundingBox {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

export interface SegmentationResult {
  masks?: string[]; // Base64 encoded masks
  scores?: number[];
  processing_time: number;
}

class SAMLocalService {

  /**
   * Check server health
   */
  async checkHealth(): Promise<any> {
    try {
      const response = await axios.get(`${SAM_SERVER_URL}/health`);
      return response.data;
    } catch (error) {
      console.error('SAM server health check failed:', error);
      throw error;
    }
  }

  /**
   * Convert image file to base64 for SAM server
   */
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (reader.result && typeof reader.result === 'string') {
          // Remove the data:image/...;base64, prefix
          const base64 = reader.result.split(',')[1];
          resolve(base64);
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Segment with points using the new SAM server API
   */
  async segmentWithPoints(
    imageFile: File,
    points: Point[]
  ): Promise<SegmentationResult> {
    try {
      const base64Image = await this.fileToBase64(imageFile);
      const pointCoords = points.map(p => [p.x, p.y]);
      
      // Create a temporary image to get dimensions
      const img = new Image();
      img.src = `data:image/jpeg;base64,${base64Image}`;
      await new Promise((resolve) => img.onload = resolve);
      
      console.log('Sending segmentation request:', {
        imageSize: { width: img.width, height: img.height },
        points: pointCoords,
        pointsWithLabels: points
      });

      const response = await axios.post(
        `${SAM_SERVER_URL}/segment`,
        {
          image: base64Image,
          points: pointCoords
        }
      );
      
      console.log('Segmentation response:', {
        masksCount: response.data.masks?.length,
        scores: response.data.scores
      });

      return {
        masks: response.data.masks,
        scores: response.data.scores,
        processing_time: 0 // SAM server doesn't return this
      };
    } catch (error) {
      console.error('Segmentation with points failed:', error);
      throw error;
    }
  }

  /**
   * Segment with bounding box using the new SAM server API
   * Note: The new SAM server only supports points, so this converts box to corner points
   */
  async segmentWithBox(
    imageFile: File,
    box: BoundingBox
  ): Promise<SegmentationResult> {
    try {
      // Convert bounding box to points (corners)
      const points: Point[] = [
        { x: box.x1, y: box.y1, label: 1 }, // top-left
        { x: box.x2, y: box.y2, label: 1 }, // bottom-right
      ];
      
      return this.segmentWithPoints(imageFile, points);
    } catch (error) {
      console.error('Segmentation with box failed:', error);
      throw error;
    }
  }

  /**
   * Automatic segmentation (generates all masks)
   * Note: The new SAM server doesn't have auto mode, so this uses a center point as default
   */
  async segmentAutomatic(imageFile: File, imageWidth: number = 512, imageHeight: number = 512): Promise<SegmentationResult> {
    try {
      // Use center point as default for "automatic" segmentation
      const centerPoint: Point = {
        x: imageWidth / 2,
        y: imageHeight / 2,
        label: 1
      };
      
      return this.segmentWithPoints(imageFile, [centerPoint]);
    } catch (error) {
      console.error('Automatic segmentation failed:', error);
      throw error;
    }
  }





}

export const samLocalService = new SAMLocalService();