import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'

interface Organization {
  id: string
  name: string
  slug: string
  logo?: string
  plan: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  users?: Array<{
    userId: string
    role: string
    user?: {
      id: string
      email: string
      name: string
    }
  }>
}

interface CreateOrganizationDto {
  name: string
  logo?: string
  plan?: string
}

interface UpdateOrganizationDto {
  name?: string
  logo?: string
  plan?: string
  isActive?: boolean
}

interface AddUserDto {
  email: string
  role: 'owner' | 'admin' | 'member'
}

class OrganizationService {
  private apiUrl = import.meta.env.DEV ? '/api/organizations' : `${API_BASE_URL}/api/organizations`

  private getAuthHeader() {
    const token = localStorage.getItem('astrameccanica_token')
    return token ? { Authorization: `Bearer ${token}` } : {}
  }

  async getOrganizations(): Promise<Organization[]> {
    try {
      const response = await axios.get(this.apiUrl, {
        headers: this.getAuthHeader()
      })
      return response.data
    } catch (error) {
      console.error('Error fetching organizations:', error)
      throw error
    }
  }

  async getOrganizationById(id: string): Promise<Organization> {
    try {
      const response = await axios.get(`${this.apiUrl}/${id}`, {
        headers: this.getAuthHeader()
      })
      return response.data
    } catch (error) {
      console.error('Error fetching organization:', error)
      throw error
    }
  }

  async createOrganization(data: CreateOrganizationDto): Promise<Organization> {
    try {
      const response = await axios.post(this.apiUrl, data, {
        headers: {
          ...this.getAuthHeader(),
          'Content-Type': 'application/json'
        }
      })
      return response.data
    } catch (error) {
      console.error('Error creating organization:', error)
      throw error
    }
  }

  async updateOrganization(id: string, data: UpdateOrganizationDto): Promise<Organization> {
    try {
      const response = await axios.put(`${this.apiUrl}/${id}`, data, {
        headers: {
          ...this.getAuthHeader(),
          'Content-Type': 'application/json'
        }
      })
      return response.data
    } catch (error) {
      console.error('Error updating organization:', error)
      throw error
    }
  }

  async deleteOrganization(id: string): Promise<void> {
    try {
      await axios.delete(`${this.apiUrl}/${id}`, {
        headers: this.getAuthHeader()
      })
    } catch (error) {
      console.error('Error deleting organization:', error)
      throw error
    }
  }

  async addUserToOrganization(orgId: string, userData: AddUserDto): Promise<any> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/${orgId}/users`,
        userData,
        {
          headers: {
            ...this.getAuthHeader(),
            'Content-Type': 'application/json'
          }
        }
      )
      return response.data
    } catch (error) {
      console.error('Error adding user to organization:', error)
      throw error
    }
  }

  async removeUserFromOrganization(orgId: string, userId: string): Promise<void> {
    try {
      await axios.delete(`${this.apiUrl}/${orgId}/users/${userId}`, {
        headers: this.getAuthHeader()
      })
    } catch (error) {
      console.error('Error removing user from organization:', error)
      throw error
    }
  }

  async updateUserRole(
    orgId: string,
    userId: string,
    role: 'owner' | 'admin' | 'member'
  ): Promise<any> {
    try {
      const response = await axios.put(
        `${this.apiUrl}/${orgId}/users/${userId}/role`,
        { role },
        {
          headers: {
            ...this.getAuthHeader(),
            'Content-Type': 'application/json'
          }
        }
      )
      return response.data
    } catch (error) {
      console.error('Error updating user role:', error)
      throw error
    }
  }

  getCurrentOrganizationId(): string | null {
    return localStorage.getItem('selectedOrgId')
  }

  setCurrentOrganizationId(orgId: string): void {
    localStorage.setItem('selectedOrgId', orgId)
  }
}

export const organizationService = new OrganizationService()
export type { Organization, CreateOrganizationDto, UpdateOrganizationDto, AddUserDto }