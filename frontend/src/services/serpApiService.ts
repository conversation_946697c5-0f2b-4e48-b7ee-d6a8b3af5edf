import axios from 'axios';

// URL del backend locale che gestirà le richieste a SerpAPI
const BACKEND_URL = 'http://localhost:3000/api/serpapi';

/**
 * Interfaccia per i risultati della ricerca di Google Maps
 */
export interface GoogleMapsSearchResult {
  search_metadata?: {
    id: string;
    status: string;
    json_endpoint: string;
    created_at: string;
    processed_at: string;
    google_maps_url: string;
    raw_html_file: string;
    total_time_taken: number;
  };
  search_parameters?: {
    engine: string;
    type: string;
    q: string;
    google_domain: string;
    hl: string;
    gl: string;
  };
  search_information?: {
    local_results_state: string;
    query_displayed: string;
  };
  place_results?: {
    title: string;
    place_id: string;
    data_id: string;
    data_cid: string;
    reviews_link: string;
    photos_link: string;
    gps_coordinates: {
      latitude: number;
      longitude: number;
    };
    place_id_search: string;
    provider_id: string;
    thumbnail: string;
    serpapi_thumbnail: string;
    rating_summary?: Array<{
      stars: number;
      amount: number;
    }>;
    rating: number;
    reviews: number;
    type: string[];
    type_ids?: string[];
    extensions?: Array<{
      accessibility?: string[];
      payments?: string[];
    }>;
    address: string;
    website?: string;
    phone?: string;
    open_state?: string;
    plus_code?: string;
    hours?: Array<{
      [key: string]: string;
    }>;
    images?: Array<{
      title: string;
      thumbnail: string;
      serpapi_thumbnail: string;
    }>;
    user_reviews?: {
      summary: Array<{
        snippet: string;
      }>;
      most_relevant: Array<{
        username: string;
        rating: number;
        contributor_id: string;
        description: string;
        link: string;
        date: string;
      }>;
    };
  };
  local_results?: Array<{
    position: number;
    title: string;
    place_id: string;
    data_id: string;
    data_cid: string;
    reviews_link: string;
    photos_link: string;
    gps_coordinates: {
      latitude: number;
      longitude: number;
    };
    place_id_search: string;
    rating: number;
    reviews: number;
    price: string;
    type: string;
    address: string;
    open_state: string;
    hours: string;
    operating_hours: {
      thursday: string;
      friday: string;
      saturday: string;
      sunday: string;
      monday: string;
      tuesday: string;
      wednesday: string;
    };
    phone: string;
    website: string;
    description: string;
    service_options: {
      dine_in: boolean;
      takeout: boolean;
      delivery: boolean;
    };
    thumbnail: string;
  }>;
  error?: string;
}

/**
 * Cerca un'azienda su Google Maps utilizzando SerpApi
 * @param companyName Nome dell'azienda da cercare
 * @param location Coordinate geografiche (lat,lng) o indirizzo
 * @returns Promise con i risultati della ricerca
 */
export const searchCompanyOnGoogleMaps = async (
  companyName: string,
  _location: string // Parametro non utilizzato ma mantenuto per compatibilità
): Promise<GoogleMapsSearchResult> => {
  try {
    // Costruisci i parametri della query
    const params = {
      q: companyName
    };
    
    // Esegui la richiesta HTTP al backend locale
    const response = await axios.get(BACKEND_URL, { params });
    
    // Restituisci i dati della risposta
    return response.data as GoogleMapsSearchResult;
  } catch (_error) {
    // Gestione silenziosa dell'errore
    return { error: 'Errore durante la ricerca. Riprova più tardi.' };
  }
};

/**
 * Formatta le coordinate per l'API di Google Maps
 * @param lat Latitudine
 * @param lng Longitudine
 * @returns Stringa formattata per l'API
 */
export const formatCoordinates = (lat: number, lng: number): string => {
  return `@${lat},${lng},14z`;
};