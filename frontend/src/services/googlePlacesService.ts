import { Company, PlacesSearchRequest, PlaceDetailsRequest } from '../types/company';
import { LatLngBounds } from '../stores/mapStore';
import { 
  mapPlaceTypeToCategory, 
  getCompanySize, 
  generateEmployeesCount, 
  generateFoundedYear, 
  generateEmail 
} from '../utils/companyUtils';

/**
 * Servizio per l'interazione con Google Places API
 */
export class GooglePlacesService {
  private placesService: google.maps.places.PlacesService;

  /**
   * Costruttore
   * @param mapInstance Istanza della mappa Google
   */
  constructor(mapInstance: google.maps.Map) {
    this.placesService = new google.maps.places.PlacesService(mapInstance);
  }

  /**
   * Calcola il centro di un'area definita da bounds
   * @param bounds Confini dell'area
   * @returns Coordinate del centro
   */
  private calculateCenter(bounds: LatLngBounds): google.maps.LatLng {
    return new google.maps.LatLng(
      (bounds.northeast.lat + bounds.southwest.lat) / 2,
      (bounds.northeast.lng + bounds.southwest.lng) / 2
    );
  }

  /**
   * Calcola il raggio di ricerca in metri
   * @param center Centro dell'area
   * @param bounds Confini dell'area
   * @returns Raggio in metri
   */
  private calculateRadius(center: google.maps.LatLng, bounds: LatLngBounds): number {
    const northeast = new google.maps.LatLng(bounds.northeast.lat, bounds.northeast.lng);
    return Math.max(
      google.maps.geometry.spherical.computeDistanceBetween(center, northeast),
      500 // Raggio minimo di 500 metri
    );
  }

  /**
   * Crea i parametri di ricerca per Google Places
   * @param bounds Confini dell'area
   * @returns Parametri di ricerca
   */
  private createSearchRequest(bounds: LatLngBounds): PlacesSearchRequest {
    const center = this.calculateCenter(bounds);
    const radius = this.calculateRadius(center, bounds);
    
    return {
      location: center,
      radius,
      type: 'establishment' // Cerchiamo tutti i tipi di attività commerciali
    };
  }

  /**
   * Ottiene i dettagli di un luogo
   * @param placeId ID del luogo
   * @returns Promise con i dettagli del luogo
   */
  private getPlaceDetails(placeId: string): Promise<google.maps.places.PlaceResult> {
    const request: PlaceDetailsRequest = {
      placeId,
      fields: [
        'name', 'place_id', 'formatted_address', 'geometry', 'types',
        'business_status', 'formatted_phone_number', 'website', 'rating'
      ]
    };

    return new Promise((resolve, reject) => {
      this.placesService.getDetails(
        request,
        (placeDetails, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && placeDetails) {
            resolve(placeDetails);
          } else {
            reject(new Error(`Errore nel recupero dei dettagli: ${status}`));
          }
        }
      );
    });
  }

  /**
   * Converte un PlaceResult in un oggetto Company
   * @param placeDetails Dettagli del luogo
   * @param index Indice del risultato (usato come fallback per l'ID)
   * @returns Oggetto Company
   */
  private convertToCompany(placeDetails: google.maps.places.PlaceResult, index: number): Company {
    const types = placeDetails.types || [];
    const category = mapPlaceTypeToCategory(types);
    const size = getCompanySize(types);
    const employees = generateEmployeesCount(size);
    
    return {
      id: placeDetails.place_id || `place-${index}`,
      name: placeDetails.name || 'Azienda sconosciuta',
      address: placeDetails.formatted_address || '',
      category,
      size,
      employees,
      founded: generateFoundedYear(),
      contact: {
        email: generateEmail(placeDetails.name || 'azienda'),
        phone: placeDetails.formatted_phone_number || '+39 000 0000000'
      },
      location: {
        lat: placeDetails.geometry?.location?.lat() || 0,
        lng: placeDetails.geometry?.location?.lng() || 0
      },
      buildings: [] // Non abbiamo informazioni sui buildings
    };
  }

  /**
   * Rileva le aziende nell'area specificata
   * @param bounds Confini dell'area
   * @param maxResults Numero massimo di risultati da elaborare (opzionale)
   * @returns Promise con un array di aziende
   */
  public detectCompaniesInArea(bounds: LatLngBounds, maxResults: number = 20): Promise<Company[]> {
    const request = this.createSearchRequest(bounds);
    
    return new Promise((resolve, reject) => {
      this.placesService.nearbySearch(
        request,
        async (results, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && results) {
            try {
              // Limitiamo il numero di risultati per migliorare le performance
              const limitedResults = results.slice(0, maxResults);
              const companies: Company[] = [];
              
              // Elaboriamo i risultati in modo asincrono
              for (let i = 0; i < limitedResults.length; i++) {
                const place = limitedResults[i];
                if (!place.place_id) continue;
                
                try {
                  const placeDetails = await this.getPlaceDetails(place.place_id);
                  const company = this.convertToCompany(placeDetails, i);
                  companies.push(company);
                } catch (error) {
                  // Registriamo l'errore ma continuiamo con il prossimo risultato
                  // eslint-disable-next-line no-console
                  console.warn(`Impossibile ottenere dettagli per ${place.name}:`, error);
                  // Continuiamo con il prossimo risultato
                }
              }
              
              resolve(companies);
            } catch (error) {
              reject(error);
            }
          } else {
            reject(new Error(`Errore nella ricerca: ${status}`));
          }
        }
      );
    });
  }
}

/**
 * Crea un'istanza del servizio Google Places
 * @param mapInstance Istanza della mappa Google
 * @returns Istanza del servizio o null se Google Maps non è disponibile
 */
export function createGooglePlacesService(mapInstance: google.maps.Map | null): GooglePlacesService | null {
  if (!mapInstance || !window.google?.maps?.places) {
    return null;
  }
  
  return new GooglePlacesService(mapInstance);
}