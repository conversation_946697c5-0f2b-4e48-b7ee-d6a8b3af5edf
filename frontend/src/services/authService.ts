import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'

interface LoginData {
  email: string
  password: string
}

interface AuthResponse {
  success: boolean
  user?: {
    id: string
    email: string
    name: string
    role: string
  }
  token?: string
  error?: string
}

class AuthService {
  private apiUrl = import.meta.env.DEV ? '/api' : `${API_BASE_URL}/api`
  private tokenKey = 'astrameccanica_token'
  private userKey = 'astrameccanica_user'

  constructor() {
    // Set default axios authorization header if token exists
    const token = this.getToken()
    if (token) {
      this.setAuthHeader(token)
    }
  }

  private setAuthHeader(token: string) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
  }

  private removeAuthHeader() {
    delete axios.defaults.headers.common['Authorization']
  }

  async login(data: LoginData): Promise<AuthResponse> {
    try {
      const response = await axios.post(`${this.apiUrl}/login`, data)
      const result = response.data
      
      if (result.success && result.token) {
        // Save token and user to localStorage
        localStorage.setItem(this.tokenKey, result.token)
        localStorage.setItem(this.userKey, JSON.stringify(result.user))
        
        // Set axios default header
        this.setAuthHeader(result.token)
      }
      
      return result
    } catch (error: any) {
      console.error('Login error:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Errore di connessione'
      }
    }
  }

  logout() {
    // Remove token and user from localStorage
    localStorage.removeItem(this.tokenKey)
    localStorage.removeItem(this.userKey)
    
    // Remove axios header
    this.removeAuthHeader()
    
    // Redirect to login
    window.location.href = '/sign-in'
  }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey)
  }

  getUser() {
    const userStr = localStorage.getItem(this.userKey)
    return userStr ? JSON.parse(userStr) : null
  }

  isAuthenticated(): boolean {
    return !!this.getToken()
  }

  isAdmin(): boolean {
    const user = this.getUser()
    return user?.role === 'admin'
  }
}

export const authService = new AuthService()