import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';
const API_URL = API_BASE_URL.endsWith('/api') ? API_BASE_URL : `${API_BASE_URL}/api`;

export interface BuildingFeature {
  type: 'Feature';
  id: string | number;
  properties: {
    id: string | number;
    name: string;
    type: string;
    address: string;
    floors?: number;
    height?: number;
    area?: number;
    amenity?: string;
    shop?: string;
    office?: string;
  };
  geometry: any;
}

export interface CompanyData {
  id: string;
  name: string;
  type: string;
  category?: string;
  address: string;
  phone?: string;
  website?: string;
  openingHours?: string;
  location: {
    lat: number;
    lng: number;
  };
  source: string;
}

export interface AreaStatistics {
  totalBuildings: number;
  totalArea: number;
  buildingTypes?: Record<string, {
    count: number;
    area: number;
    avgFloors?: number;
  }>;
  mapsAvailable?: number;
  message?: string;
}

export interface MapData {
  id: string;
  bounds: {
    northwest: number[];
    southeast: number[];
  };
  zoomLevel: number;
  style: string;
  format: string;
  createdAt: string;
  storageKey: string;
  downloadUrl?: string;
}

interface Bounds {
  swLng: number;
  swLat: number;
  neLng: number;
  neLat: number;
}

class AreaAnalyzerService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  /**
   * Get buildings in a specific area
   */
  async getBuildingsInArea(bounds: Bounds, source: 'osm' | 'google' = 'osm') {
    try {
      const boundsStr = `${bounds.swLng},${bounds.swLat},${bounds.neLng},${bounds.neLat}`;
      const response = await axios.get(`${API_URL}/area-analyzer/buildings`, {
        params: { bounds: boundsStr, source },
        headers: this.getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching buildings:', error);
      throw error;
    }
  }

  /**
   * Get companies/businesses in a specific area
   */
  async getCompaniesInArea(bounds: Bounds, types: string = 'all'): Promise<{
    success: boolean;
    companies: CompanyData[];
    total: number;
  }> {
    try {
      const boundsStr = `${bounds.swLng},${bounds.swLat},${bounds.neLng},${bounds.neLat}`;
      const response = await axios.get(`${API_URL}/area-analyzer/companies`, {
        params: { bounds: boundsStr, types },
        headers: this.getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching companies:', error);
      throw error;
    }
  }

  /**
   * Get area statistics
   */
  async getAreaStatistics(bounds: Bounds): Promise<AreaStatistics> {
    try {
      const boundsStr = `${bounds.swLng},${bounds.swLat},${bounds.neLng},${bounds.neLat}`;
      const response = await axios.get(`${API_URL}/area-analyzer/statistics`, {
        params: { bounds: boundsStr },
        headers: this.getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching statistics:', error);
      throw error;
    }
  }

  /**
   * Get available maps for an area
   */
  async getAvailableMaps(bounds?: Bounds): Promise<{
    maps: MapData[];
    total: number;
  }> {
    try {
      const params: any = {};
      if (bounds) {
        params.bounds = `${bounds.swLng},${bounds.swLat},${bounds.neLng},${bounds.neLat}`;
      }
      
      const response = await axios.get(`${API_URL}/area-analyzer/maps`, {
        params,
        headers: this.getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching available maps:', error);
      throw error;
    }
  }
}

export const areaAnalyzerService = new AreaAnalyzerService();