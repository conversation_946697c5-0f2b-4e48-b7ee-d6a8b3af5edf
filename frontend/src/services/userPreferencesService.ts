import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'

export interface MapDownloaderPreferences {
  defaultStyle: string
  defaultFormat: string
  defaultZoom: string
  autoDownload: boolean
  saveHistory: boolean
  cacheEnabled: boolean
  cacheSize: string
  compressionLevel: string
  maxConcurrentDownloads: string
  notifyOnComplete: boolean
  notifyOnError: boolean
  emailNotifications: boolean
  notificationEmail: string
  apiKey: string
  apiEndpoint: string
}

export interface UserPreferences {
  mapDownloader: MapDownloaderPreferences
}

class UserPreferencesService {
  private apiUrl = import.meta.env.DEV ? '/api/user' : `${API_BASE_URL}/api/user`

  async getPreferences(): Promise<UserPreferences> {
    try {
      const response = await axios.get(`${this.apiUrl}/preferences`)
      return response.data.preferences
    } catch (error: any) {
      console.error('Error fetching user preferences:', error)
      // Return default preferences if error
      return this.getDefaultPreferences()
    }
  }

  async updatePreferences(preferences: UserPreferences): Promise<UserPreferences> {
    try {
      const response = await axios.put(`${this.apiUrl}/preferences`, { preferences })
      return response.data.preferences
    } catch (error: any) {
      console.error('Error updating user preferences:', error)
      throw error
    }
  }

  async resetPreferences(): Promise<UserPreferences> {
    try {
      const response = await axios.delete(`${this.apiUrl}/preferences`)
      return response.data.preferences
    } catch (error: any) {
      console.error('Error resetting user preferences:', error)
      throw error
    }
  }

  getDefaultPreferences(): UserPreferences {
    return {
      mapDownloader: {
        defaultStyle: 's',
        defaultFormat: 'jpeg',
        defaultZoom: '18',
        autoDownload: false,
        saveHistory: true,
        cacheEnabled: true,
        cacheSize: '500',
        compressionLevel: '80',
        maxConcurrentDownloads: '3',
        notifyOnComplete: true,
        notifyOnError: true,
        emailNotifications: false,
        notificationEmail: '',
        apiKey: '',
        apiEndpoint: 'http://localhost:3000/api'
      }
    }
  }
}

export const userPreferencesService = new UserPreferencesService()