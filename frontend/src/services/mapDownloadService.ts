import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'

export interface MapDownloadRequest {
  bbox: [number, number, number, number] // [min_lon, min_lat, max_lon, max_lat]
  zoom: number
  style: string
  format: 'jpeg' | 'geotiff'
}

export interface MapDownloadResponse {
  success: boolean
  message: string
  fileUrl?: string
  s3?: {
    bucket: string
    key: string
    location: string
  }
  error?: string
}

export interface MapStyle {
  id: string
  name: string
  description?: string
}

export interface DownloadedArea {
  id: string
  boundingBoxNW: [number, number]
  boundingBoxSE: [number, number]
  zoomLevel: number
  mapStyle: string
  outputFormat: string
  createdAt: string
  storageKey?: string
  metadata?: any
}

export interface GeocodeRequest {
  address: string
  radius: number
}

export interface GeocodeResponse {
  success: boolean
  location?: {
    lat: number
    lng: number
    formatted_address: string
  }
  bbox?: [number, number, number, number]
  radius?: number
  error?: string
}

class MapDownloadService {
  private apiUrl = import.meta.env.DEV ? '/api' : `${API_BASE_URL}/api`

  async generateMap(request: MapDownloadRequest): Promise<MapDownloadResponse> {
    try {
      const response = await axios.post(`${this.apiUrl}/generate-map`, request)
      return response.data
    } catch (error: any) {
      console.error('Error generating map:', error)
      return {
        success: false,
        message: 'Errore nella generazione della mappa',
        error: error.response?.data?.error || error.message
      }
    }
  }

  async generateMapAsync(request: MapDownloadRequest): Promise<MapDownloadResponse> {
    try {
      const response = await axios.post(`${this.apiUrl}/generate-map-async`, request)
      return response.data
    } catch (error: any) {
      console.error('Error generating map async:', error)
      return {
        success: false,
        message: 'Errore nella generazione asincrona della mappa',
        error: error.response?.data?.error || error.message
      }
    }
  }

  async getMapStyles(): Promise<MapStyle[]> {
    try {
      const response = await axios.get(`${this.apiUrl}/map-styles`)
      return response.data
    } catch (error) {
      console.error('Error fetching map styles:', error)
      return [
        { id: 's', name: 'Satellite', description: 'Immagini satellitari' },
        { id: 'm', name: 'Stradale', description: 'Mappa stradale standard' },
        { id: 'y', name: 'Ibrido', description: 'Satellite con etichette' },
        { id: 't', name: 'Terreno', description: 'Terreno' },
        { id: 'p', name: 'Terreno con etichette', description: 'Terreno con etichette' },
        { id: 'h', name: 'Solo etichette', description: 'Solo etichette' }
      ]
    }
  }

  async getDownloadedAreas(): Promise<DownloadedArea[]> {
    try {
      const response = await axios.get(`${this.apiUrl}/downloaded-areas`)
      return response.data.areas || []
    } catch (error) {
      console.error('Error fetching downloaded areas:', error)
      return []
    }
  }

  async downloadMap(key: string): Promise<string> {
    try {
      const response = await axios.get(`${this.apiUrl}/map-presigned-url/${encodeURIComponent(key)}`)
      return response.data.url
    } catch (error) {
      console.error('Error getting download URL:', error)
      throw error
    }
  }

  async geocodeAddress(request: GeocodeRequest): Promise<GeocodeResponse> {
    try {
      const response = await axios.post(`${this.apiUrl}/geocode-address`, request)
      return response.data
    } catch (error: any) {
      console.error('Error geocoding address:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Errore nella geocodifica dell\'indirizzo'
      }
    }
  }
}

export const mapDownloadService = new MapDownloadService()