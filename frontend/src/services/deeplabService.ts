/**
 * DeepLab Segmentation Service
 * Optimized for satellite imagery segmentation using DeepLabV3+
 */

import axios from 'axios';

// Configuration
const DEEPLAB_SERVER_URL = 'http://192.168.0.110:8081';

export interface DeeplabSegmentationResult {
  masks: string[];  // Base64 encoded PNG masks
  scores: number[];
  shape?: number[];
  processing_time?: number;
}

export interface DeeplabServerInfo {
  service: string;
  version: string;
  model: string;
  device: string;
  optimized_for: string;
}

export interface DeeplabGPUInfo {
  gpu_available: boolean;
  gpu_count?: number;
  gpu_name?: string;
  cuda_version?: string;
  memory_allocated?: number;
  memory_reserved?: number;
}

export interface Point {
  x: number;
  y: number;
  label?: number;
}

class DeeplabService {
  private serverUrl: string;

  constructor() {
    this.serverUrl = DEEPLAB_SERVER_URL;
  }

  /**
   * Check if DeepLab server is healthy
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.serverUrl}/health`);
      return response.data.status === 'healthy';
    } catch (error) {
      console.error('DeepLab health check failed:', error);
      return false;
    }
  }

  /**
   * Get server information
   */
  async getServerInfo(): Promise<DeeplabServerInfo> {
    try {
      const response = await axios.get(`${this.serverUrl}/`);
      return response.data;
    } catch (error) {
      console.error('Failed to get DeepLab server info:', error);
      throw error;
    }
  }

  /**
   * Get GPU information
   */
  async getGPUInfo(): Promise<DeeplabGPUInfo> {
    try {
      const response = await axios.get(`${this.serverUrl}/gpu-info`);
      return response.data;
    } catch (error) {
      console.error('Failed to get GPU info:', error);
      throw error;
    }
  }

  /**
   * Perform semantic segmentation on an image
   */
  async segment(imageFile: File): Promise<DeeplabSegmentationResult> {
    try {
      const formData = new FormData();
      formData.append('file', imageFile);

      console.log('Sending segmentation request to DeepLab...');
      
      const response = await axios.post(
        `${this.serverUrl}/segment`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 30000, // 30 second timeout
        }
      );

      console.log('DeepLab segmentation response:', {
        masksCount: response.data.masks?.length,
        scores: response.data.scores,
        shape: response.data.shape
      });

      return response.data;
    } catch (error) {
      console.error('DeepLab segmentation failed:', error);
      throw error;
    }
  }

  /**
   * Segment with point prompts (compatibility layer with SAM interface)
   * DeepLab doesn't natively support point prompts, so this converts to regular segmentation
   */
  async segmentWithPoints(
    imageFile: File,
    points: Point[]
  ): Promise<DeeplabSegmentationResult> {
    try {
      // For now, DeepLab doesn't use points, just perform regular segmentation
      // In the future, we could implement attention mechanisms based on points
      console.log('DeepLab: Performing segmentation (point hints not yet supported)');
      
      return await this.segment(imageFile);
    } catch (error) {
      console.error('DeepLab segmentation with points failed:', error);
      throw error;
    }
  }

  /**
   * Convert base64 mask to blob URL for display
   */
  maskToImageUrl(maskBase64: string): string {
    try {
      const byteCharacters = atob(maskBase64);
      const byteNumbers = new Array(byteCharacters.length);
      
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: 'image/png' });
      
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('Failed to convert mask to image URL:', error);
      throw error;
    }
  }

  /**
   * Apply color overlay to mask
   */
  async applyColorToMask(
    maskBase64: string,
    color: string,
    opacity: number = 0.5
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }

        // Draw the mask
        ctx.drawImage(img, 0, 0);
        
        // Get image data
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // Parse color
        const r = parseInt(color.slice(1, 3), 16);
        const g = parseInt(color.slice(3, 5), 16);
        const b = parseInt(color.slice(5, 7), 16);
        
        // Apply color to non-transparent pixels
        for (let i = 0; i < data.length; i += 4) {
          if (data[i + 3] > 0) { // If pixel is not transparent
            data[i] = r;     // Red
            data[i + 1] = g; // Green
            data[i + 2] = b; // Blue
            data[i + 3] = Math.floor(data[i + 3] * opacity); // Apply opacity
          }
        }
        
        // Put the colored image data back
        ctx.putImageData(imageData, 0, 0);
        
        // Convert to base64
        canvas.toBlob((blob) => {
          if (blob) {
            const reader = new FileReader();
            reader.onloadend = () => {
              const base64 = reader.result?.toString().split(',')[1];
              resolve(base64 || '');
            };
            reader.readAsDataURL(blob);
          } else {
            reject(new Error('Failed to create blob'));
          }
        }, 'image/png');
      };
      
      img.onerror = () => reject(new Error('Failed to load mask image'));
      img.src = `data:image/png;base64,${maskBase64}`;
    });
  }

  /**
   * Get class label for satellite imagery
   */
  getClassLabel(classId: number): string {
    const labels: { [key: number]: string } = {
      1: 'Building',
      2: 'Road',
      3: 'Vegetation',
      4: 'Water',
      5: 'Parking',
      6: 'Roof',
      7: 'Pavement',
      8: 'Bare Soil'
    };
    
    return labels[classId] || `Class ${classId}`;
  }
}

// Export singleton instance
export const deeplabService = new DeeplabService();