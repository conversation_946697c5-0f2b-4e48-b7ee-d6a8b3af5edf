import axios from 'axios';

// URL del backend locale che gestirà le richieste di analisi del tetto
const API_URL = 'http://localhost:5001/api';

/**
 * Interfaccia per i risultati dell'analisi del tetto
 */
export interface RoofAnalysisResult {
  materiale: string;
  confidenza: number;
  pannelli_solari: boolean;
  probabilita_amianto: number;
}

/**
 * Interfaccia per la risposta dell'API
 */
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Cattura un'immagine del tetto dalle coordinate geografiche
 * @param lat Latitudine
 * @param lng Longitudine
 * @param zoom Livello di zoom (opzionale, default 19)
 * @returns Promise con l'URL dell'immagine
 */
export const captureRoofImage = async (
  lat: number,
  lng: number,
  zoom: number = 19
): Promise<string> => {
  try {
    const response = await axios.post<ApiResponse<{ imageUrl: string }>>(`${API_URL}/capture-roof-image`, {
      lat,
      lng,
      zoom
    });
    
    if (response.data.success && response.data.data?.imageUrl) {
      return response.data.data.imageUrl;
    } else {
      throw new Error(response.data.error || 'Errore durante la cattura dell\'immagine');
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'Errore durante la richiesta');
    }
    throw error;
  }
};

/**
 * Analizza un'immagine di un tetto
 * @param imageUrl URL dell'immagine da analizzare
 * @returns Promise con i risultati dell'analisi
 */
export const analyzeRoofImage = async (
  imageUrl: string
): Promise<RoofAnalysisResult> => {
  try {
    const response = await axios.post<ApiResponse<RoofAnalysisResult>>(`${API_URL}/analyze-roof`, {
      imageUrl
    });
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    } else {
      throw new Error(response.data.error || 'Errore durante l\'analisi dell\'immagine');
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'Errore durante la richiesta');
    }
    throw error;
  }
};

/**
 * Cattura e analizza un'immagine del tetto dalle coordinate geografiche
 * @param lat Latitudine
 * @param lng Longitudine
 * @param zoom Livello di zoom (opzionale, default 19)
 * @returns Promise con i risultati dell'analisi
 */
export const captureAndAnalyzeRoof = async (
  lat: number,
  lng: number,
  zoom: number = 19
): Promise<{ imageUrl: string; analysis: RoofAnalysisResult }> => {
  // Prima cattura l'immagine
  const imageUrl = await captureRoofImage(lat, lng, zoom);
  
  // Poi analizza l'immagine
  const analysis = await analyzeRoofImage(imageUrl);
  
  return {
    imageUrl,
    analysis
  };
};