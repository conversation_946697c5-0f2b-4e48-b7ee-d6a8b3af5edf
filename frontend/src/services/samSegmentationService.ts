import axios from 'axios';

const API_BASE_URL = import.meta.env.DEV ? '/api' : `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/api`;

export interface SegmentationPoint {
  x: number;
  y: number;
  label: number; // 1 = foreground, 0 = background
}

export interface SegmentationBox {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

export interface SegmentationOptions {
  pointsPerSide?: number;
  predIouThresh?: number;
  stabilityScoreThresh?: number;
  minMaskRegionArea?: number;
  multimaskOutput?: boolean;
  returnLogits?: boolean;
}

export interface SegmentationResult {
  success: boolean;
  masks?: any;
  metadata?: {
    model: string;
    mode: string;
    timestamp: string;
  };
  roofAnalysis?: {
    totalSegments: number;
    largestSegment: any;
    possiblePanels: any[];
    roofArea: {
      pixelArea: number;
      estimatedM2: number;
      confidence: number;
    };
  };
  error?: string;
  details?: any;
}

class SAMSegmentationService {
  private apiUrl = `${API_BASE_URL}/sam`;

  /**
   * Automatic segmentation
   */
  async segmentAuto(
    image: File | string,
    options: SegmentationOptions = {}
  ): Promise<SegmentationResult> {
    try {
      const formData = new FormData();
      
      if (image instanceof File) {
        formData.append('image', image);
      } else {
        formData.append('imageUrl', image);
      }
      
      // Add options to form data
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      const response = await axios.post(
        `${this.apiUrl}/segment-auto`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data.data;
    } catch (error: any) {
      console.error('Auto segmentation error:', error);
      throw error;
    }
  }

  /**
   * Segmentation with point prompts
   */
  async segmentWithPoints(
    image: File | string,
    points: SegmentationPoint[],
    options: SegmentationOptions = {}
  ): Promise<SegmentationResult> {
    try {
      const formData = new FormData();
      
      if (image instanceof File) {
        formData.append('image', image);
      } else {
        formData.append('imageUrl', image);
      }
      
      formData.append('points', JSON.stringify(points));
      
      // Add options
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      const response = await axios.post(
        `${this.apiUrl}/segment-points`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data.data;
    } catch (error: any) {
      console.error('Point segmentation error:', error);
      throw error;
    }
  }

  /**
   * Segmentation with bounding box
   */
  async segmentWithBox(
    image: File | string,
    box: SegmentationBox,
    options: SegmentationOptions = {}
  ): Promise<SegmentationResult> {
    try {
      const formData = new FormData();
      
      if (image instanceof File) {
        formData.append('image', image);
      } else {
        formData.append('imageUrl', image);
      }
      
      // Convert box to array format [x1, y1, x2, y2]
      const boxArray = [box.x1, box.y1, box.x2, box.y2];
      formData.append('box', JSON.stringify(boxArray));
      
      // Add options
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      const response = await axios.post(
        `${this.apiUrl}/segment-box`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data.data;
    } catch (error: any) {
      console.error('Box segmentation error:', error);
      throw error;
    }
  }

  /**
   * Specialized roof segmentation
   */
  async segmentRoof(
    image: File | string,
    options: SegmentationOptions = {}
  ): Promise<SegmentationResult> {
    try {
      const formData = new FormData();
      
      if (image instanceof File) {
        formData.append('image', image);
      } else {
        formData.append('imageUrl', image);
      }
      
      // Add options optimized for roof detection
      const roofOptions = {
        pointsPerSide: 64,
        predIouThresh: 0.90,
        stabilityScoreThresh: 0.92,
        minMaskRegionArea: 100,
        ...options
      };
      
      Object.entries(roofOptions).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      const response = await axios.post(
        `${this.apiUrl}/segment-roof`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data.data;
    } catch (error: any) {
      console.error('Roof segmentation error:', error);
      throw error;
    }
  }

  /**
   * Batch segmentation for multiple images
   */
  async batchSegment(
    images: string[],
    mode: 'auto' | 'roof' = 'auto',
    options: SegmentationOptions = {}
  ): Promise<any> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/batch-segment`,
        {
          images,
          mode,
          options
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Batch segmentation error:', error);
      throw error;
    }
  }

  /**
   * Check SAM service status
   */
  async checkStatus(): Promise<any> {
    try {
      const response = await axios.get(`${this.apiUrl}/status`);
      return response.data;
    } catch (error: any) {
      console.error('Status check error:', error);
      throw error;
    }
  }
}

export const samSegmentationService = new SAMSegmentationService();