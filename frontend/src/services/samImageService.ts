import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';
const API_URL = API_BASE_URL.endsWith('/api') ? API_BASE_URL : `${API_BASE_URL}/api`;

export interface SamImage {
  id: string;
  name: string;
  description?: string;
  url: string;
  signedUrl?: string;
  thumbnailUrl?: string;
  size: number;
  mimeType: string;
  width?: number;
  height?: number;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
  _count?: {
    segmentations: number;
  };
}

export interface SamSegmentation {
  id: string;
  imageId: string;
  masks: any[];
  points: any[];
  metadata?: any;
  createdAt: string;
}

export interface ImageHistoryResponse {
  success: boolean;
  images: SamImage[];
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

class SamImageService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  /**
   * Upload an image to S3 and save metadata
   */
  async uploadImage(file: File, metadata?: { name?: string; description?: string; [key: string]: any }) {
    try {
      const formData = new FormData();
      formData.append('image', file);
      
      if (metadata?.name) {
        formData.append('name', metadata.name);
      }
      if (metadata?.description) {
        formData.append('description', metadata.description);
      }
      if (metadata) {
        const { name, description, ...rest } = metadata;
        if (Object.keys(rest).length > 0) {
          formData.append('metadata', JSON.stringify(rest));
        }
      }

      const response = await axios.post(
        `${API_URL}/sam-images/upload`,
        formData,
        {
          headers: {
            ...this.getAuthHeaders(),
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  }

  /**
   * Get user's image history
   */
  async getImageHistory(params?: {
    limit?: number;
    offset?: number;
    sortBy?: string;
    order?: 'asc' | 'desc';
  }): Promise<ImageHistoryResponse> {
    try {
      const response = await axios.get(
        `${API_URL}/sam-images/history`,
        {
          params: params || { limit: 20, offset: 0 },
          headers: this.getAuthHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error fetching image history:', error);
      throw error;
    }
  }

  /**
   * Get a specific image by ID
   */
  async getImage(imageId: string): Promise<{ success: boolean; image: SamImage }> {
    try {
      const response = await axios.get(
        `${API_URL}/sam-images/${imageId}`,
        {
          headers: this.getAuthHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error fetching image:', error);
      throw error;
    }
  }

  /**
   * Delete an image
   */
  async deleteImage(imageId: string) {
    try {
      const response = await axios.delete(
        `${API_URL}/sam-images/${imageId}`,
        {
          headers: this.getAuthHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error deleting image:', error);
      throw error;
    }
  }

  /**
   * Update image metadata
   */
  async updateImage(imageId: string, updates: {
    name?: string;
    description?: string;
    metadata?: any;
  }) {
    try {
      const response = await axios.patch(
        `${API_URL}/sam-images/${imageId}`,
        updates,
        {
          headers: this.getAuthHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error updating image:', error);
      throw error;
    }
  }

  /**
   * Save segmentation results for an image
   */
  async saveSegmentationResults(imageId: string, data: {
    masks: any[];
    points: any[];
    metadata?: any;
  }) {
    try {
      const response = await axios.post(
        `${API_URL}/sam-images/${imageId}/segmentation`,
        data,
        {
          headers: this.getAuthHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error saving segmentation results:', error);
      throw error;
    }
  }

  /**
   * Get segmentation results for an image
   */
  async getSegmentationResults(imageId: string): Promise<{
    success: boolean;
    segmentations: SamSegmentation[];
  }> {
    try {
      const response = await axios.get(
        `${API_URL}/sam-images/${imageId}/segmentation`,
        {
          headers: this.getAuthHeaders()
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error fetching segmentation results:', error);
      throw error;
    }
  }
}

export const samImageService = new SamImageService();