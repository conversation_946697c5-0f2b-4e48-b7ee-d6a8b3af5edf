import axios from 'axios'
import { authService } from './authService'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'

// Configura axios per includere il token in tutte le richieste
axios.interceptors.request.use(
  (config) => {
    const token = authService.getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Intercetta le risposte 401 e reindirizza al login
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      authService.logout()
      window.location.href = '/sign-in'
    }
    return Promise.reject(error)
  }
)

export interface User {
  id: string
  email: string
  name: string
  role: string
  createdAt: string
  lastLogin?: string | null
}

export interface CreateUserData {
  email: string
  name: string
  password: string
  role?: string
}

export interface UpdateUserData {
  email?: string
  name?: string
  password?: string
  role?: string
}

class UserService {
  private apiUrl = import.meta.env.DEV ? '/api' : `${API_BASE_URL}/api`

  async getAllUsers(): Promise<User[]> {
    try {
      const response = await axios.get(`${this.apiUrl}/users`)
      return response.data.users || []
    } catch (error) {
      console.error('Error fetching users:', error)
      throw error
    }
  }

  async getUserById(id: string): Promise<User> {
    try {
      const response = await axios.get(`${this.apiUrl}/users/${id}`)
      return response.data.user
    } catch (error) {
      console.error('Error fetching user:', error)
      throw error
    }
  }

  async createUser(userData: CreateUserData): Promise<User> {
    try {
      const response = await axios.post(`${this.apiUrl}/users`, userData)
      return response.data.user
    } catch (error) {
      console.error('Error creating user:', error)
      throw error
    }
  }

  async updateUser(id: string, userData: UpdateUserData): Promise<User> {
    try {
      const response = await axios.put(`${this.apiUrl}/users/${id}`, userData)
      return response.data.user
    } catch (error) {
      console.error('Error updating user:', error)
      throw error
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      await axios.delete(`${this.apiUrl}/users/${id}`)
    } catch (error) {
      console.error('Error deleting user:', error)
      throw error
    }
  }
}

export const userService = new UserService()