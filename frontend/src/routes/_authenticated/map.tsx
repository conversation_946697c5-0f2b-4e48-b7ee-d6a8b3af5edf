import { createFileRoute } from '@tanstack/react-router'
import { MapView } from '@/components/MapView'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export const Route = createFileRoute('/_authenticated/map')({
  component: MapPage
})

function MapPage() {
  return (
    <div className="flex flex-col h-screen p-4 md:p-8 pt-6 pb-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-3xl font-bold tracking-tight">Mappa</h2>
      </div>
      
      <Card className="flex-1 flex flex-col overflow-hidden">
        <CardHeader className="pb-3 shrink-0">
          <CardTitle>Visualizzazione Mappa</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 p-0 overflow-hidden">
          <MapView />
        </CardContent>
      </Card>
    </div>
  )
}