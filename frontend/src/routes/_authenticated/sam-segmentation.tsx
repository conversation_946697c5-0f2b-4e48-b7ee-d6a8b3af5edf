import { createFileRoute } from '@tanstack/react-router'
import { SAMAdvancedSegmentation } from '@/components/sam-segmentation/SAMAdvancedSegmentation'
import { BuildingSegmentationHelper } from '@/components/sam-segmentation/BuildingSegmentationHelper'
import { ImageHistory } from '@/components/sam-segmentation/ImageHistory'
import { useState } from 'react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { samImageService, SamImage } from '@/services/samImageService'

export const Route = createFileRoute('/_authenticated/sam-segmentation')({
  component: SAMSegmentationPage,
})

function SAMSegmentationPage() {
  const [segmentationMode, setSegmentationMode] = useState<'point' | 'box'>('point');
  const [selectedImage, setSelectedImage] = useState<SamImage | null>(null);
  const [currentImageId, setCurrentImageId] = useState<string | undefined>();

  const handleSelectImage = (image: SamImage) => {
    setSelectedImage(image);
    setCurrentImageId(image.id);
    // The SAMAdvancedSegmentation component will be updated to accept selectedImage prop
  };

  return (
    <div className="w-full h-full">
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 p-6 h-full">
        <div className="lg:col-span-8">
          <SAMAdvancedSegmentation 
            selectedImage={selectedImage}
            onImageUploaded={(imageId) => setCurrentImageId(imageId)}
          />
        </div>
        <div className="lg:col-span-4 space-y-4">
          <Tabs defaultValue="helper" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="helper">Aiuto</TabsTrigger>
              <TabsTrigger value="history">Storico</TabsTrigger>
            </TabsList>
            <TabsContent value="helper">
              <BuildingSegmentationHelper onModeChange={setSegmentationMode} />
            </TabsContent>
            <TabsContent value="history" className="h-[600px]">
              <ImageHistory 
                onSelectImage={handleSelectImage}
                currentImageId={currentImageId}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}