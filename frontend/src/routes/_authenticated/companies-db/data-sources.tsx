import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { PlusCircle, Pencil, Trash2, Building2, FolderTree, Upload, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'

export const Route = createFileRoute('/_authenticated/companies-db/data-sources')({
  component: DataSourcesPage
})

interface DataSource {
  id: string
  name: string
  type: string
  url?: string
  apiKey?: string
  schedule?: string
  lastSyncAt?: string
  config?: any
  isActive: boolean
  createdAt: string
  updatedAt: string
  _count?: {
    companies: number
    categories: number
    importBatches: number
  }
}

function DataSourcesPage() {
  const [dataSources, setDataSources] = useState<DataSource[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedDataSource, setSelectedDataSource] = useState<DataSource | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    type: 'manual',
    url: '',
    apiKey: '',
    schedule: '',
    config: '{}'
  })

  // Load data sources
  const loadDataSources = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/companies-db/data-sources')
      
      if (!response.ok) {
        throw new Error('Failed to load data sources')
      }
      
      const data = await response.json()
      setDataSources(data)
    } catch (error) {
      console.error('Error loading data sources:', error)
      toast.error('Errore nel caricamento delle fonti dati')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadDataSources()
  }, [])

  const handleCreate = () => {
    setIsEditing(false)
    setFormData({
      name: '',
      type: 'manual',
      url: '',
      apiKey: '',
      schedule: '',
      config: '{}'
    })
    setIsDialogOpen(true)
  }

  const handleEdit = (dataSource: DataSource) => {
    setIsEditing(true)
    setSelectedDataSource(dataSource)
    setFormData({
      name: dataSource.name,
      type: dataSource.type,
      url: dataSource.url || '',
      apiKey: dataSource.apiKey || '',
      schedule: dataSource.schedule || '',
      config: dataSource.config ? JSON.stringify(dataSource.config, null, 2) : '{}'
    })
    setIsDialogOpen(true)
  }

  const handleSubmit = async () => {
    try {
      let config = null
      try {
        config = formData.config ? JSON.parse(formData.config) : null
      } catch {
        toast.error('Configurazione JSON non valida')
        return
      }

      const url = isEditing 
        ? `/api/companies-db/data-sources/${selectedDataSource?.id}`
        : '/api/companies-db/data-sources'
      
      const method = isEditing ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('astrameccanica_token')}`
        },
        body: JSON.stringify({
          ...formData,
          config
        })
      })

      if (!response.ok) throw new Error('Failed to save data source')
      
      toast.success(isEditing ? 'Fonte dati aggiornata' : 'Fonte dati creata')
      setIsDialogOpen(false)
      loadDataSources()
    } catch (error) {
      console.error('Error saving data source:', error)
      toast.error('Errore nel salvataggio della fonte dati')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Sei sicuro di voler eliminare questa fonte dati?')) return
    
    try {
      const response = await fetch(`/api/companies-db/data-sources/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('astrameccanica_token')}`
        }
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete data source')
      }
      
      toast.success('Fonte dati eliminata')
      loadDataSources()
    } catch (error: any) {
      console.error('Error deleting data source:', error)
      toast.error(error.message || 'Errore nell\'eliminazione della fonte dati')
    }
  }

  const handleSync = async (id: string) => {
    try {
      const response = await fetch(`/api/companies-db/data-sources/${id}/sync`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('astrameccanica_token')}`
        }
      })

      if (!response.ok) throw new Error('Failed to sync data source')
      
      await response.json()
      toast.success('Sincronizzazione avviata')
      loadDataSources()
    } catch (error) {
      console.error('Error syncing data source:', error)
      toast.error('Errore nella sincronizzazione')
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'scraping': return 'destructive'
      case 'api': return 'default'
      case 'import': return 'secondary'
      case 'manual': return 'outline'
      default: return 'outline'
    }
  }

  const topNav = [
    {
      title: 'Dashboard',
      href: '/companies-db/dashboard',
      isActive: false,
      disabled: false,
    },
    {
      title: 'Aziende',
      href: '/companies-db',
      isActive: false,
      disabled: false,
    },
    {
      title: 'Categorie',
      href: '/companies-db/categories',
      isActive: false,
      disabled: false,
    },
    {
      title: 'Sorgenti Dati',
      href: '/companies-db/data-sources',
      isActive: true,
      disabled: false,
    },
  ]

  return (
    <>
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Fonti Dati</h1>
          <Button onClick={handleCreate}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Nuova Fonte
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Fonti Dati Configurate</CardTitle>
            <CardDescription>
              {dataSources.length} fonti dati disponibili
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>URL</TableHead>
                  <TableHead>Ultima Sync</TableHead>
                  <TableHead>Stato</TableHead>
                  <TableHead>Statistiche</TableHead>
                  <TableHead>Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center">
                      Caricamento...
                    </TableCell>
                  </TableRow>
                ) : dataSources.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center">
                      Nessuna fonte dati configurata
                    </TableCell>
                  </TableRow>
                ) : (
                  dataSources.map(source => (
                    <TableRow key={source.id}>
                      <TableCell className="font-medium">{source.name}</TableCell>
                      <TableCell>
                        <Badge variant={getTypeColor(source.type)}>
                          {source.type}
                        </Badge>
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {source.url || '-'}
                      </TableCell>
                      <TableCell>
                        {source.lastSyncAt 
                          ? new Date(source.lastSyncAt).toLocaleString('it-IT')
                          : 'Mai sincronizzato'}
                      </TableCell>
                      <TableCell>
                        <Badge variant={source.isActive ? 'default' : 'secondary'}>
                          {source.isActive ? 'Attivo' : 'Inattivo'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1 text-sm">
                          <div className="flex items-center gap-1">
                            <Building2 className="h-3 w-3" />
                            <span>{source._count?.companies || 0} aziende</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <FolderTree className="h-3 w-3" />
                            <span>{source._count?.categories || 0} categorie</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Upload className="h-3 w-3" />
                            <span>{source._count?.importBatches || 0} import</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {source.isActive && (
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleSync(source.id)}
                              title="Sincronizza"
                            >
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEdit(source)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDelete(source.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {isEditing ? 'Modifica Fonte Dati' : 'Nuova Fonte Dati'}
              </DialogTitle>
              <DialogDescription>
                {isEditing 
                  ? 'Modifica la configurazione della fonte dati'
                  : 'Configura una nuova fonte per importare aziende'}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Nome</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Es. Google Places API"
                />
              </div>
              
              <div>
                <Label htmlFor="type">Tipo</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData({ ...formData, type: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="manual">Manuale</SelectItem>
                    <SelectItem value="api">API</SelectItem>
                    <SelectItem value="scraping">Scraping</SelectItem>
                    <SelectItem value="import">Import File</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="url">URL</Label>
                <Input
                  id="url"
                  value={formData.url}
                  onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                  placeholder="https://api.example.com/companies"
                />
              </div>
              
              <div>
                <Label htmlFor="apiKey">API Key</Label>
                <Input
                  id="apiKey"
                  type="password"
                  value={formData.apiKey}
                  onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
                  placeholder="Chiave API (opzionale)"
                />
              </div>
              
              <div>
                <Label htmlFor="schedule">Schedule (Cron)</Label>
                <Input
                  id="schedule"
                  value={formData.schedule}
                  onChange={(e) => setFormData({ ...formData, schedule: e.target.value })}
                  placeholder="0 0 * * * (ogni giorno a mezzanotte)"
                />
              </div>
              
              <div>
                <Label htmlFor="config">Configurazione (JSON)</Label>
                <Textarea
                  id="config"
                  value={formData.config}
                  onChange={(e) => setFormData({ ...formData, config: e.target.value })}
                  placeholder="{}"
                  className="font-mono"
                  rows={6}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Annulla
              </Button>
              <Button onClick={handleSubmit}>
                {isEditing ? 'Salva Modifiche' : 'Crea Fonte'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </Main>
    </>
  )
}