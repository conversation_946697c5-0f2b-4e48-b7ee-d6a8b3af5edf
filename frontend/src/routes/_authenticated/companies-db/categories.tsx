import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { PlusCircle, Pencil, Trash2, Building2, Database, ChevronRight } from 'lucide-react'
import { toast } from 'sonner'

export const Route = createFileRoute('/_authenticated/companies-db/categories')({
  component: CategoriesPage
})

interface Category {
  id: string
  name: string
  slug: string
  description?: string
  parentId?: string
  dataSourceId?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  dataSource?: {
    id: string
    name: string
    type: string
  }
  parent?: {
    id: string
    name: string
  }
  children?: Category[]
  _count?: {
    companies: number
  }
}

interface DataSource {
  id: string
  name: string
  type: string
}

function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [dataSources, setDataSources] = useState<DataSource[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    parentId: '',
    dataSourceId: ''
  })

  // Load categories
  const loadCategories = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/companies-db/categories')
      if (!response.ok) throw new Error('Failed to load categories')
      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('Error loading categories:', error)
      toast.error('Errore nel caricamento delle categorie')
    } finally {
      setIsLoading(false)
    }
  }

  // Load data sources
  const loadDataSources = async () => {
    try {
      const response = await fetch('/api/companies-db/data-sources', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('astrameccanica_token')}`
        }
      })
      if (!response.ok) throw new Error('Failed to load data sources')
      const data = await response.json()
      setDataSources(data)
    } catch (error) {
      console.error('Error loading data sources:', error)
    }
  }

  useEffect(() => {
    loadCategories()
    loadDataSources()
  }, [])

  const handleCreate = () => {
    setIsEditing(false)
    setFormData({
      name: '',
      description: '',
      parentId: '',
      dataSourceId: ''
    })
    setIsDialogOpen(true)
  }

  const handleEdit = (category: Category) => {
    setIsEditing(true)
    setSelectedCategory(category)
    setFormData({
      name: category.name,
      description: category.description || '',
      parentId: category.parentId || '',
      dataSourceId: category.dataSourceId || ''
    })
    setIsDialogOpen(true)
  }

  const handleSubmit = async () => {
    try {
      const url = isEditing 
        ? `/api/companies-db/categories/${selectedCategory?.id}`
        : '/api/companies-db/categories'
      
      const method = isEditing ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('astrameccanica_token')}`
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) throw new Error('Failed to save category')
      
      toast.success(isEditing ? 'Categoria aggiornata' : 'Categoria creata')
      setIsDialogOpen(false)
      loadCategories()
    } catch (error) {
      console.error('Error saving category:', error)
      toast.error('Errore nel salvataggio della categoria')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Sei sicuro di voler eliminare questa categoria?')) return
    
    try {
      const response = await fetch(`/api/companies-db/categories/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('astrameccanica_token')}`
        }
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete category')
      }
      
      toast.success('Categoria eliminata')
      loadCategories()
    } catch (error: any) {
      console.error('Error deleting category:', error)
      toast.error(error.message || 'Errore nell\'eliminazione della categoria')
    }
  }

  const renderCategoryRow = (category: Category, level = 0) => {
    const rows = []
    
    rows.push(
      <TableRow key={category.id}>
        <TableCell>
          <div className="flex items-center gap-2" style={{ paddingLeft: `${level * 20}px` }}>
            {category.children && category.children.length > 0 && (
              <ChevronRight className="h-4 w-4" />
            )}
            <span className="font-medium">{category.name}</span>
          </div>
        </TableCell>
        <TableCell>{category.description || '-'}</TableCell>
        <TableCell>
          {category.dataSource ? (
            <Badge variant="outline">
              <Database className="h-3 w-3 mr-1" />
              {category.dataSource.name}
            </Badge>
          ) : (
            '-'
          )}
        </TableCell>
        <TableCell>
          <Badge variant={category.isActive ? 'default' : 'secondary'}>
            {category.isActive ? 'Attiva' : 'Inattiva'}
          </Badge>
        </TableCell>
        <TableCell>
          <div className="flex items-center gap-1">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <span>{category._count?.companies || 0}</span>
          </div>
        </TableCell>
        <TableCell>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleEdit(category)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleDelete(category.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
    )
    
    // Render children recursively
    if (category.children) {
      category.children.forEach(child => {
        rows.push(...renderCategoryRow(child, level + 1))
      })
    }
    
    return rows
  }

  const topNav = [
    {
      title: 'Dashboard',
      href: '/companies-db/dashboard',
      isActive: false,
      disabled: false,
    },
    {
      title: 'Aziende',
      href: '/companies-db',
      isActive: false,
      disabled: false,
    },
    {
      title: 'Categorie',
      href: '/companies-db/categories',
      isActive: true,
      disabled: false,
    },
    {
      title: 'Sorgenti Dati',
      href: '/companies-db/data-sources',
      isActive: false,
      disabled: false,
    },
  ]

  return (
    <>
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Gestione Categorie</h1>
          <Button onClick={handleCreate}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Nuova Categoria
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Categorie Aziende</CardTitle>
            <CardDescription>
              {categories.length} categorie totali
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Descrizione</TableHead>
                  <TableHead>Fonte Dati</TableHead>
                  <TableHead>Stato</TableHead>
                  <TableHead>Aziende</TableHead>
                  <TableHead>Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">
                      Caricamento...
                    </TableCell>
                  </TableRow>
                ) : categories.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">
                      Nessuna categoria trovata
                    </TableCell>
                  </TableRow>
                ) : (
                  categories
                    .filter(cat => !cat.parentId) // Show only root categories
                    .map(category => renderCategoryRow(category))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {isEditing ? 'Modifica Categoria' : 'Nuova Categoria'}
              </DialogTitle>
              <DialogDescription>
                {isEditing 
                  ? 'Modifica i dettagli della categoria'
                  : 'Crea una nuova categoria per organizzare le aziende'}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Nome</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Es. Imprese Edili Milano"
                />
              </div>
              
              <div>
                <Label htmlFor="description">Descrizione</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Descrizione della categoria"
                />
              </div>
              
              <div>
                <Label htmlFor="parent">Categoria Padre</Label>
                <Select
                  value={formData.parentId}
                  onValueChange={(value) => setFormData({ ...formData, parentId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona categoria padre (opzionale)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Nessuna</SelectItem>
                    {categories
                      .filter(cat => cat.id !== selectedCategory?.id)
                      .map(cat => (
                        <SelectItem key={cat.id} value={cat.id}>
                          {cat.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="dataSource">Fonte Dati</Label>
                <Select
                  value={formData.dataSourceId}
                  onValueChange={(value) => setFormData({ ...formData, dataSourceId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona fonte dati (opzionale)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Nessuna</SelectItem>
                    {dataSources.map(ds => (
                      <SelectItem key={ds.id} value={ds.id}>
                        {ds.name} ({ds.type})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Annulla
              </Button>
              <Button onClick={handleSubmit}>
                {isEditing ? 'Salva Modifiche' : 'Crea Categoria'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </Main>
    </>
  )
}