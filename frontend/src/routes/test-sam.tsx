import { createFileRoute } from '@tanstack/react-router'
import { SAMRealTimeSegmentation } from '@/components/sam-segmentation/SAMRealTimeSegmentation'

export const Route = createFileRoute('/test-sam')({
  component: TestSAMPage,
})

function TestSAMPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold text-center mb-8">
          SAM Segmentation Test (Local GPU Server)
        </h1>
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="mb-4 p-4 bg-blue-50 rounded">
            <p className="text-sm text-blue-800">
              <strong>Server:</strong> http://192.168.0.110:8080 | 
              <strong> GPU:</strong> RTX 5080 | 
              <strong> Model:</strong> SAM ViT-H
            </p>
          </div>
          <SAMRealTimeSegmentation />
        </div>
      </div>
    </div>
  )
}