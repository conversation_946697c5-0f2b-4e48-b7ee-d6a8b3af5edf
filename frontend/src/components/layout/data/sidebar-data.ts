import {
  IconLayoutDashboard,
  IconSettings,
  IconUsers,
  IconMap,
  IconDownload,
  IconFileAnalytics,
  IconBuildingFactory2,
  IconChartBar,
  IconBrain,
  IconWand,
  IconMapPin,
} from '@tabler/icons-react'
import { Command } from 'lucide-react'
import { type SidebarData } from '../types'
import { authService } from '@/services/authService'

// Ottieni i dati dell'utente corrente
const currentUser = authService.getUser()

export const sidebarData: SidebarData = {
  user: {
    name: currentUser?.name || 'Utente',
    email: currentUser?.email || '<EMAIL>',
    avatar: '',  // Lasciamo vuoto per mostrare sempre le iniziali
  },
  teams: [
    {
      name: 'Astrameccanica',
      logo: Command,
      plan: 'Ana<PERSON>i <PERSON>',
    },
  ],
  navGroups: [
    {
      title: '<PERSON><PERSON><PERSON>',
      items: [
        {
          title: 'Dashboard',
          url: '/',
          icon: IconLayoutDashboard,
        },
        {
          title: 'Analisi Area',
          url: '/area-analyzer',
          icon: IconMap,
        },
        {
          title: 'Download Mappe',
          url: '/map-downloader',
          icon: IconDownload,
        },
        {
          title: 'Analisi Bollette',
          url: '/bills-analyzer',
          icon: IconFileAnalytics,
        },
        {
          title: 'Classificatore Tetti',
          url: '/roof-classifier',
          icon: IconBrain,
        },
        {
          title: 'SAM Segmentazione',
          url: '/sam-segmentation',
          icon: IconWand,
        },
        {
          title: 'Mappa',
          url: '/map',
          icon: IconMapPin,
        },
      ],
    },
    {
      title: 'Gestione',
      items: [
        {
          title: 'Database Aziende',
          icon: IconBuildingFactory2,
          items: [
            {
              title: 'Dashboard',
              url: '/companies-db/dashboard',
            },
            {
              title: 'Lista Aziende',
              url: '/companies-db',
            },
            {
              title: 'Categorie',
              url: '/companies-db/categories',
            },
            {
              title: 'Sorgenti Dati',
              url: '/companies-db/data-sources',
            },
          ],
        },
        {
          title: 'Valutazione CRM',
          url: '/crm-evaluation',
          icon: IconChartBar,
        },
        {
          title: 'Utenti',
          url: '/users',
          icon: IconUsers,
        },
      ],
    },
    // Sezione Pages rimossa per non essere visibile pubblicamente
    {
      title: 'Other',
      items: [
        {
          title: 'Impostazioni',
          url: '/settings',
          icon: IconSettings,
        },
      ],
    },
  ],
}
