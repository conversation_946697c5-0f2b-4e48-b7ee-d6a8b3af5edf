import { useEffect, useRef } from 'react'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

interface MapPreviewProps {
  bbox?: [number, number, number, number] // [min_lon, min_lat, max_lon, max_lat]
  center?: { lat: number; lng: number }
  radius?: number // in km
  style?: string
  height?: string
}

export function MapPreview({ bbox, center, radius, style = 's', height = '400px' }: MapPreviewProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<L.Map | null>(null)
  const rectangleRef = useRef<L.Rectangle | null>(null)
  const circleRef = useRef<L.Circle | null>(null)

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return

    // Initialize map
    const map = L.map(mapRef.current).setView([45.4642, 9.1900], 10) // Milano default
    mapInstanceRef.current = map

    // Add Google Maps tiles    
    L.tileLayer(`https://mt1.google.com/vt/lyrs=${style}&x={x}&y={y}&z={z}`, {
      attribution: '© Google Maps',
      maxZoom: 22,
      subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
    }).addTo(map)

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [style])

  useEffect(() => {
    if (!mapInstanceRef.current) return

    // Clear previous overlays
    if (rectangleRef.current) {
      rectangleRef.current.remove()
      rectangleRef.current = null
    }
    if (circleRef.current) {
      circleRef.current.remove()
      circleRef.current = null
    }

    if (bbox) {
      // Draw rectangle for bbox
      const bounds = L.latLngBounds(
        [bbox[1], bbox[0]], // SW corner
        [bbox[3], bbox[2]]  // NE corner
      )
      
      rectangleRef.current = L.rectangle(bounds, {
        color: '#ff0000',
        weight: 3,
        fillOpacity: 0.1,
        fillColor: '#ff0000'
      }).addTo(mapInstanceRef.current)
      
      // Fit map to bounds with padding
      mapInstanceRef.current.fitBounds(bounds, { padding: [50, 50] })
      
    } else if (center && radius) {
      // Draw circle for center + radius
      const centerLatLng = L.latLng(center.lat, center.lng)
      
      circleRef.current = L.circle(centerLatLng, {
        radius: radius * 1000, // Convert km to meters
        color: '#ff0000',
        weight: 3,
        fillOpacity: 0.1,
        fillColor: '#ff0000'
      }).addTo(mapInstanceRef.current)
      
      // Fit map to circle bounds
      mapInstanceRef.current.fitBounds(circleRef.current.getBounds(), { padding: [50, 50] })
    }
  }, [bbox, center, radius])

  return (
    <div 
      ref={mapRef} 
      style={{ 
        height, 
        width: '100%',
        borderRadius: '8px',
        overflow: 'hidden',
        border: '1px solid #e5e7eb'
      }} 
    />
  )
}