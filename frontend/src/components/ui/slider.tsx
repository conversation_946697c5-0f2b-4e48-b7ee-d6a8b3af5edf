import React, { forwardRef, useState, useEffect } from "react"
import { cn } from "@/lib/utils"

interface SliderProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'onChange'> {
  value?: number[]
  onValueChange?: (value: number[]) => void
  min?: number
  max?: number
  step?: number
  className?: string
}

const Slider = forwardRef<HTMLInputElement, SliderProps>(
  ({ className, value, onValueChange, min = 0, max = 100, step = 1, ...props }, ref) => {
    const [internalValue, setInternalValue] = useState<number>(
      Array.isArray(value) && value.length > 0 ? value[0] : min
    )

    useEffect(() => {
      if (Array.isArray(value) && value.length > 0 && value[0] !== internalValue) {
        setInternalValue(value[0])
      }
    }, [value, internalValue])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = parseFloat(e.target.value)
      setInternalValue(newValue)
      
      if (onValueChange) {
        onValueChange([newValue])
      }
    }

    const percentage = ((internalValue - min) / (max - min)) * 100

    return (
      <div className={cn("relative w-full", className)}>
        <input
          type="range"
          ref={ref}
          min={min}
          max={max}
          step={step}
          value={internalValue}
          onChange={handleChange}
          className="w-full h-2 appearance-none bg-secondary rounded-full outline-none"
          style={{
            background: `linear-gradient(to right, var(--color-primary) 0%, var(--color-primary) ${percentage}%, var(--secondary) ${percentage}%, var(--secondary) 100%)`
          }}
          {...props}
        />
      </div>
    )
  }
)

Slider.displayName = "Slider"

export { Slider }