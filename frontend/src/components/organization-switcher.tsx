import { useState, useEffect } from 'react'
import { Check, ChevronsUpDown, PlusCircle, Building2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useSidebar } from '@/components/ui/sidebar'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { toast } from 'sonner'

interface Organization {
  id: string
  name: string
  slug: string
  logo?: string
  plan: string
  users?: Array<{
    role: string
  }>
}

export function OrganizationSwitcher() {
  const [open, setOpen] = useState(false)
  const [showNewOrgDialog, setShowNewOrgDialog] = useState(false)
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null)
  const [newOrgName, setNewOrgName] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { state } = useSidebar()

  // Carica le organizzazioni
  const loadOrganizations = async () => {
    try {
      const token = localStorage.getItem('astrameccanica_token')
      const response = await fetch('/api/organizations', {
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      })
      
      if (response.ok) {
        const data = await response.json()
        setOrganizations(data)
        
        // Seleziona la prima organizzazione o quella salvata
        const savedOrgId = localStorage.getItem('selectedOrgId')
        const orgToSelect = savedOrgId 
          ? data.find((o: Organization) => o.id === savedOrgId) 
          : data[0]
        
        if (orgToSelect) {
          setSelectedOrg(orgToSelect)
          localStorage.setItem('selectedOrgId', orgToSelect.id)
        }
      }
    } catch (error) {
      console.error('Errore caricamento organizzazioni:', error)
    }
  }

  useEffect(() => {
    loadOrganizations()
  }, [])

  // Crea nuova organizzazione
  const createOrganization = async () => {
    if (!newOrgName.trim()) {
      toast.error('Inserisci il nome dell\'azienda')
      return
    }

    setIsLoading(true)
    try {
      const token = localStorage.getItem('astrameccanica_token')
      const response = await fetch('/api/organizations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        },
        body: JSON.stringify({ name: newOrgName })
      })

      if (response.ok) {
        const newOrg = await response.json()
        setOrganizations([...organizations, newOrg])
        setSelectedOrg(newOrg)
        localStorage.setItem('selectedOrgId', newOrg.id)
        setShowNewOrgDialog(false)
        setNewOrgName('')
        toast.success('Azienda creata con successo')
        
        // Ricarica la pagina per aggiornare il contesto
        window.location.reload()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Errore nella creazione dell\'azienda')
      }
    } catch (error) {
      console.error('Errore creazione organizzazione:', error)
      toast.error('Errore nella creazione dell\'azienda')
    } finally {
      setIsLoading(false)
    }
  }

  // Cambia organizzazione
  const selectOrganization = (org: Organization) => {
    setSelectedOrg(org)
    localStorage.setItem('selectedOrgId', org.id)
    setOpen(false)
    
    // Ricarica la pagina per aggiornare il contesto
    window.location.reload()
  }

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            aria-label="Seleziona azienda"
            className={cn(
              "w-full",
              state === 'collapsed' ? "justify-center" : "justify-between"
            )}
          >
            <Building2 className={cn(
              "h-4 w-4 shrink-0",
              state === 'collapsed' ? "" : "mr-2"
            )} />
            {state !== 'collapsed' && (
              <>
                {selectedOrg?.name || 'Seleziona azienda'}
                <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
              </>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Cerca azienda..." />
            <CommandList>
              <CommandEmpty>Nessuna azienda trovata.</CommandEmpty>
              <CommandGroup heading="Aziende">
                {organizations.map((org) => (
                  <CommandItem
                    key={org.id}
                    onSelect={() => selectOrganization(org)}
                    className="text-sm"
                  >
                    <Building2 className="mr-2 h-4 w-4" />
                    <div className="flex flex-col">
                      <span>{org.name}</span>
                      {org.users?.[0]?.role && (
                        <span className="text-xs text-muted-foreground">
                          {org.users[0].role === 'owner' ? 'Proprietario' : 
                           org.users[0].role === 'admin' ? 'Amministratore' : 'Membro'}
                        </span>
                      )}
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedOrg?.id === org.id
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={() => {
                    setOpen(false)
                    setShowNewOrgDialog(true)
                  }}
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Aggiungi Azienda
                </CommandItem>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <Dialog open={showNewOrgDialog} onOpenChange={setShowNewOrgDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Crea Nuova Azienda</DialogTitle>
            <DialogDescription>
              Aggiungi una nuova azienda per gestire i suoi dati separatamente.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Nome Azienda</Label>
              <Input
                id="name"
                placeholder="Es. Astra Meccanica"
                value={newOrgName}
                onChange={(e) => setNewOrgName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !isLoading) {
                    createOrganization()
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowNewOrgDialog(false)}
              disabled={isLoading}
            >
              Annulla
            </Button>
            <Button 
              onClick={createOrganization}
              disabled={isLoading}
            >
              {isLoading ? 'Creazione...' : 'Crea Azienda'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}