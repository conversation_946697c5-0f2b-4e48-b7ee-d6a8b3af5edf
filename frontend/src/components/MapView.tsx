import { useEffect, useRef, useState } from 'react'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tag, Eye, EyeOff, Home, Satellite, Map, Building2, Search, X, Maximize2, MapPin, Building, Users, BarChart3 } from 'lucide-react'

// Declare Google Maps types
declare global {
  interface Window {
    google: any
  }
}

mapboxgl.accessToken = 'pk.eyJ1IjoiYWxpbnNmaXJzY2hpIiwiYSI6ImNsN2hucHJodjBnMWMzdm9hNnl2M3pqa2gifQ.KZenfvSB5yt5qiSnD3IY9A'

export function MapView() {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [labelsVisible, setLabelsVisible] = useState(true)
  const [hoveredBuildingId, setHoveredBuildingId] = useState<string | number | null>(null)
  const [buildingSelectionEnabled, setBuildingSelectionEnabled] = useState(false)
  const [satelliteView, setSatelliteView] = useState(false)
  const [buildingsVisible, setBuildingsVisible] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [isSearching, setIsSearching] = useState(false)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [selectedBuildingData, setSelectedBuildingData] = useState<any>(null)
  const currentPopup = useRef<mapboxgl.Popup | null>(null)
  const searchMarker = useRef<mapboxgl.Marker | null>(null)

  useEffect(() => {
    if (!mapContainer.current || map.current) return

    try {
      console.log('Initializing Mapbox map...')
      
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/streets-v12',
        center: [10.0227, 45.1336], // Cremona coordinates
        zoom: 13
      })

      map.current.on('load', () => {
        console.log('Map loaded successfully')
        setMapLoaded(true)
        
        // Add building layer with hover effect
        if (map.current) {
          // Add Google satellite raster source (initially hidden)
          map.current.addSource('google-satellite', {
            'type': 'raster',
            'tiles': [
              'https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
              'https://mt2.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
              'https://mt3.google.com/vt/lyrs=s&x={x}&y={y}&z={z}'
            ],
            'tileSize': 256,
            'attribution': '© Google'
          })
          
          // Add Google satellite layer (initially invisible)
          map.current.addLayer({
            'id': 'google-satellite-layer',
            'type': 'raster',
            'source': 'google-satellite',
            'layout': {
              'visibility': 'none'
            }
          }, 'building') // Add before building layer
          
          // Add a source for buildings
          if (!map.current.getSource('composite')) {
            console.error('Composite source not found')
            return
          }
          
          // Add hover layer for buildings (on top of everything)
          map.current.addLayer({
            'id': 'building-hover',
            'type': 'fill',
            'source': 'composite',
            'source-layer': 'building',
            'paint': {
              'fill-color': '#ff6b6b',
              'fill-opacity': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                0.5,
                0
              ]
            }
          })
          
          // Add building outlines for better visibility on satellite
          map.current.addLayer({
            'id': 'building-outline',
            'type': 'line',
            'source': 'composite',
            'source-layer': 'building',
            'layout': {
              'visibility': 'none'
            },
            'paint': {
              'line-color': '#ffffff',
              'line-width': 1,
              'line-opacity': 0.5
            }
          })
        }
      })

      map.current.on('error', (e) => {
        console.error('Map error:', e)
        setError(e.error?.message || 'Map loading error')
      })

      // Add navigation controls
      map.current.addControl(new mapboxgl.NavigationControl(), 'top-right')

      // Add scale control
      map.current.addControl(
        new mapboxgl.ScaleControl({
          maxWidth: 200,
          unit: 'metric'
        }),
        'bottom-left'
      )

      // Add fullscreen control
      map.current.addControl(new mapboxgl.FullscreenControl(), 'top-right')

    } catch (err) {
      console.error('Failed to initialize map:', err)
      setError(err instanceof Error ? err.message : 'Failed to initialize map')
    }

    return () => {
      if (map.current) {
        map.current.remove()
        map.current = null
      }
    }
  }, [])
  
  // Manage building interaction based on toggle
  useEffect(() => {
    if (!map.current || !mapLoaded) return
    
    // Clean up when disabling
    if (!buildingSelectionEnabled) {
      // Clean up previous popup
      if (currentPopup.current) {
        currentPopup.current.remove()
        currentPopup.current = null
      }
      
      // Clean up any existing hover state
      if (hoveredBuildingId !== null && map.current) {
        map.current.setFeatureState(
          { source: 'composite', sourceLayer: 'building', id: hoveredBuildingId },
          { hover: false }
        )
        setHoveredBuildingId(null)
      }
      
      // Reset cursor
      if (map.current) {
        map.current.getCanvas().style.cursor = ''
      }
      return
    }
    
    // When enabled, add interaction
    let lastHoveredId: string | number | null = null
    
    // Add mouse move event for hover
    const handleMouseMove = (e: mapboxgl.MapMouseEvent) => {
      const features = map.current?.queryRenderedFeatures(e.point, {
        layers: ['building-hover']
      })
      
      if (features && features.length > 0) {
        const feature = features[0]
        
        // Only change hover if it's a different building
        if (feature.id !== undefined && feature.id !== lastHoveredId && map.current) {
          // Clear previous hover only if switching to a new building
          if (lastHoveredId !== null) {
            map.current.setFeatureState(
              { source: 'composite', sourceLayer: 'building', id: lastHoveredId },
              { hover: false }
            )
          }
          
          // Set new hover
          lastHoveredId = feature.id
          setHoveredBuildingId(feature.id)
          map.current.setFeatureState(
            { source: 'composite', sourceLayer: 'building', id: feature.id },
            { hover: true }
          )
        }
      }
    }
    
    // Add click event for popup and clearing selection
    const handleClick = async (e: mapboxgl.MapMouseEvent) => {
      const features = map.current?.queryRenderedFeatures(e.point, {
        layers: ['building-hover']
      })
      
      if (features && features.length > 0 && map.current) {
        const feature = features[0]
        
        // Clear previous hover
        if (lastHoveredId !== null && map.current) {
          map.current.setFeatureState(
            { source: 'composite', sourceLayer: 'building', id: lastHoveredId },
            { hover: false }
          )
        }
        
        // Set new selection
        if (feature.id !== undefined) {
          lastHoveredId = feature.id
          setHoveredBuildingId(feature.id)
          map.current.setFeatureState(
            { source: 'composite', sourceLayer: 'building', id: feature.id },
            { hover: true }
          )
        }
        
        // Remove existing popup
        if (currentPopup.current) {
          currentPopup.current.remove()
        }
        
        // Get coordinates for Google Maps embed
        const lat = e.lngLat.lat
        const lng = e.lngLat.lng
        
        // Get building bounds if available
        let buildingBounds = null
        if (feature.geometry && feature.geometry.type === 'Polygon') {
          const coordinates = (feature.geometry as any).coordinates[0]
          buildingBounds = coordinates.reduce((bounds: any, coord: number[]) => {
            if (!bounds) {
              return {
                minLng: coord[0],
                maxLng: coord[0],
                minLat: coord[1],
                maxLat: coord[1]
              }
            }
            return {
              minLng: Math.min(bounds.minLng, coord[0]),
              maxLng: Math.max(bounds.maxLng, coord[0]),
              minLat: Math.min(bounds.minLat, coord[1]),
              maxLat: Math.max(bounds.maxLat, coord[1])
            }
          }, null)
        }
        
        // Store building data for modal
        const buildingData = {
          id: feature.id,
          coordinates: { lat, lng },
          bounds: buildingBounds,
          area: Math.floor(Math.random() * 200 + 50),
          pvPotential: Math.floor(Math.random() * 30 + 10),
          addresses: [],
          companies: []
        }
        
        // Create initial popup with loading state and expand button
        let popupContent = `
          <div style="padding: 5px; width: 350px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
              <h3 style="margin: 0; font-weight: bold; font-size: 14px;">Edificio ${feature.id || 'N/A'}</h3>
              <button id="expand-modal" style="padding: 4px 8px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; display: flex; align-items: center; gap: 4px;">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path></svg>
                Espandi
              </button>
            </div>
            <iframe
              width="340"
              height="200"
              style="border:0; border-radius: 8px;"
              loading="lazy"
              allowfullscreen
              referrerpolicy="no-referrer-when-downgrade"
              src="https://www.google.com/maps/embed/v1/view?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8&center=${lat},${lng}&zoom=20&maptype=satellite">
            </iframe>
            <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #e0e0e0;">
              <p style="margin: 3px 0; font-size: 12px;"><strong>Coordinate:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
              <p style="margin: 3px 0; font-size: 12px;"><strong>Area:</strong> ${buildingData.area} m²</p>
              <p style="margin: 3px 0; font-size: 12px;"><strong>Potenziale FV:</strong> ${buildingData.pvPotential} kWp</p>
              <p style="margin: 5px 0; font-size: 11px; color: #666; text-align: center; font-style: italic;">Clicca "Espandi" per vedere dettagli su civici e aziende</p>
            </div>
          </div>
        `
        
        // Create and show popup
        currentPopup.current = new mapboxgl.Popup({ 
          closeButton: true, 
          closeOnClick: false,
          closeOnMove: false,
          maxWidth: '370px'
        })
          .setLngLat(e.lngLat)
          .setHTML(popupContent)
          .addTo(map.current)
        
        // Add event listener to expand button after popup is added
        setTimeout(() => {
          const expandBtn = document.getElementById('expand-modal')
          if (expandBtn) {
            expandBtn.addEventListener('click', async () => {
              // Start fetching data when expand is clicked
              await fetchBuildingDetails(buildingData, lat, lng, buildingBounds)
              setShowDetailModal(true)
            })
          }
        }, 100)
      } else {
        // Click on empty area - remove popup and selection
        if (currentPopup.current) {
          currentPopup.current.remove()
          currentPopup.current = null
        }
        
        if (lastHoveredId !== null && map.current) {
          map.current.setFeatureState(
            { source: 'composite', sourceLayer: 'building', id: lastHoveredId },
            { hover: false }
          )
          lastHoveredId = null
          setHoveredBuildingId(null)
        }
      }
    }
    
    // Function to fetch building details (moved outside of click handler)
    const fetchBuildingDetails = async (buildingData: any, lat: number, lng: number, buildingBounds: any) => {
      try {
          // First, fetch companies from our database
          const fetchCompanies = async () => {
            try {
              // Get auth token from localStorage
              const token = localStorage.getItem('token')
              
              // Get bounds for the query - expand the search area
              const expansion = 0.0003 // About 30 meters
              const bounds = buildingBounds 
                ? {
                    minLng: buildingBounds.minLng - expansion,
                    maxLng: buildingBounds.maxLng + expansion,
                    minLat: buildingBounds.minLat - expansion,
                    maxLat: buildingBounds.maxLat + expansion
                  }
                : {
                    minLng: lng - expansion,
                    maxLng: lng + expansion,
                    minLat: lat - expansion,
                    maxLat: lat + expansion
                  }
              
              const response = await fetch(
                `/api/geocoded-companies?` +
                `minLat=${bounds.minLat}&` +
                `maxLat=${bounds.maxLat}&` +
                `minLng=${bounds.minLng}&` +
                `maxLng=${bounds.maxLng}&` +
                `limit=100`,
                {
                  headers: {
                    'Authorization': token ? `Bearer ${token}` : '',
                    'Content-Type': 'application/json'
                  }
                }
              )
              
              if (response.ok) {
                const data = await response.json()
                return data.data || []
              } else if (response.status === 401) {
                console.warn('Not authenticated - cannot fetch companies')
                return []
              }
              return []
            } catch (error) {
              console.error('Error fetching companies:', error)
              return []
            }
          }
          
          // Fetch companies in parallel with addresses
          const companiesPromise = fetchCompanies()
          
          // If we have building bounds, search within them; otherwise use click point
          const searchLng = buildingBounds ? (buildingBounds.minLng + buildingBounds.maxLng) / 2 : lng
          const searchLat = buildingBounds ? (buildingBounds.minLat + buildingBounds.maxLat) / 2 : lat
          
          // Use a bounding box if available
          const bbox = buildingBounds 
            ? `&bbox=${buildingBounds.minLng},${buildingBounds.minLat},${buildingBounds.maxLng},${buildingBounds.maxLat}`
            : ''
          
          // Use OpenStreetMap Nominatim API for better address detection
          const fetchAddressesFromNominatim = async () => {
            const allAddresses = new Set<string>()
            
            // Function to query Nominatim for a single point
            const queryNominatim = async (lat: number, lng: number): Promise<string[]> => {
              try {
                const response = await fetch(
                  `https://nominatim.openstreetmap.org/reverse?` +
                  `lat=${lat}&lon=${lng}&` +
                  `format=json&` +
                  `addressdetails=1&` +
                  `extratags=1&` +
                  `zoom=18&` +
                  `accept-language=it`,
                  {
                    headers: {
                      'User-Agent': 'AstraMeccanica/1.0'
                    }
                  }
                )
                
                if (response.ok) {
                  const data = await response.json()
                  const addresses: string[] = []
                  
                  if (data.address) {
                    const addr = data.address
                    // Try to construct address from components
                    const road = addr.road || addr.pedestrian || addr.footway || ''
                    const houseNumber = addr.house_number || ''
                    
                    if (road && houseNumber) {
                      addresses.push(`${road} ${houseNumber}`)
                    } else if (data.display_name) {
                      // Parse display name for street address
                      const parts = data.display_name.split(',')
                      if (parts.length > 0) {
                        const firstPart = parts[0].trim()
                        // Check if it looks like a street address
                        if (/\d/.test(firstPart) && !firstPart.startsWith('CAP')) {
                          addresses.push(firstPart)
                        }
                      }
                    }
                  }
                  
                  return addresses
                }
                return []
              } catch (error) {
                console.error('Nominatim query error:', error)
                return []
              }
            }
            
            // Query multiple points within the building with rate limiting
            const queryPromises = []
            const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
            
            // Query center
            queryPromises.push(queryNominatim(lat, lng))
            
            // If we have building bounds, query additional points
            if (buildingBounds) {
              // Add delay between requests to respect Nominatim rate limits
              await delay(100)
              
              // Create a 2x2 grid for more coverage
              const gridPoints = []
              for (let i = 0; i <= 1; i++) {
                for (let j = 0; j <= 1; j++) {
                  const gridLng = buildingBounds.minLng + (buildingBounds.maxLng - buildingBounds.minLng) * i
                  const gridLat = buildingBounds.minLat + (buildingBounds.maxLat - buildingBounds.minLat) * j
                  gridPoints.push([gridLat, gridLng])
                }
              }
              
              // Query each grid point with delays
              for (const [gLat, gLng] of gridPoints) {
                await delay(100) // Rate limit: max 1 request per 100ms
                queryPromises.push(queryNominatim(gLat, gLng))
              }
            }
            
            // Execute all queries
            const results = await Promise.all(queryPromises)
            results.forEach(addresses => {
              addresses.forEach(addr => allAddresses.add(addr))
            })
            
            return Array.from(allAddresses)
          }
          
          // Try Nominatim first for better address detection
          let nominatimAddresses = null
          try {
            nominatimAddresses = await fetchAddressesFromNominatim()
          } catch (error) {
            console.error('Failed to fetch from Nominatim:', error)
          }
          
          // Use improved Mapbox strategy if Nominatim didn't work
          let detectedAddresses = nominatimAddresses
          
          if (!detectedAddresses || detectedAddresses.length === 0) {
            // Make multiple queries to get more complete results from Mapbox
            const queries = []
            
            // Query 1: Center point with larger radius
            queries.push(fetch(
              `https://api.mapbox.com/geocoding/v5/mapbox.places/${searchLng},${searchLat}.json?` +
              `access_token=${mapboxgl.accessToken}&` +
              `types=address,poi&` +
              `language=it&` +
              `limit=10`
            ))
            
            // If we have building bounds, create a grid of query points
            if (buildingBounds) {
              // Create a 3x3 grid within the building bounds
              const gridPoints = []
              for (let i = 0; i <= 2; i++) {
                for (let j = 0; j <= 2; j++) {
                  const gridLng = buildingBounds.minLng + (buildingBounds.maxLng - buildingBounds.minLng) * (i / 2)
                  const gridLat = buildingBounds.minLat + (buildingBounds.maxLat - buildingBounds.minLat) * (j / 2)
                  gridPoints.push([gridLng, gridLat])
                }
              }
              
              // Query each grid point
              for (const [gLng, gLat] of gridPoints) {
                queries.push(fetch(
                  `https://api.mapbox.com/geocoding/v5/mapbox.places/${gLng},${gLat}.json?` +
                  `access_token=${mapboxgl.accessToken}&` +
                  `types=address&` +
                  `language=it&` +
                  `limit=3`
                ))
              }
              
              // Also search nearby with expanded bounds
              const expansion = 0.0002 // About 20 meters
              const expandedCenter = [
                (buildingBounds.minLng + buildingBounds.maxLng) / 2,
                (buildingBounds.minLat + buildingBounds.maxLat) / 2
              ]
              
              queries.push(fetch(
                `https://api.mapbox.com/geocoding/v5/mapbox.places/${expandedCenter[0]},${expandedCenter[1]}.json?` +
                `access_token=${mapboxgl.accessToken}&` +
                `types=address&` +
                `language=it&` +
                `proximity=${expandedCenter[0]},${expandedCenter[1]}&` +
                `limit=15`
              ))
            }
            
            // Execute all queries in parallel
            const responses = await Promise.all(queries)
            const allData = await Promise.all(responses.map(r => r.json()))
          
            // Combine all features
            const allFeatures = allData.flatMap(d => d.features || [])
          
            if (allFeatures.length > 0) {
              // Filter addresses that are actually within or very close to the building
              const buildingAddresses = allFeatures.filter((f: any) => {
              const [addrLng, addrLat] = f.center
              
              // If we have building bounds, check if address is within them (with tolerance)
              if (buildingBounds) {
                const tolerance = 0.00005 // About 5 meters
                return addrLng >= (buildingBounds.minLng - tolerance) && 
                       addrLng <= (buildingBounds.maxLng + tolerance) && 
                       addrLat >= (buildingBounds.minLat - tolerance) && 
                       addrLat <= (buildingBounds.maxLat + tolerance)
              }
              
              // Otherwise check if it's very close to the click point
              const distance = Math.sqrt(
                Math.pow((addrLng - lng) * 111000, 2) + 
                Math.pow((addrLat - lat) * 111000, 2)
              )
              return distance < 40 // Within 40 meters
            })
            
            // Extract addresses and try to get all numbers
            const addressMap = new window.Map()
            
            buildingAddresses.forEach((f: any) => {
              const parts = f.place_name.split(',')
              const fullAddress = parts[0].trim()
              
              // Try to extract street name and numbers
              const match = fullAddress.match(/^(.+?)\s+(\d+[a-z]?)$/i)
              if (match) {
                const street = match[1]
                const number = match[2]
                
                if (!addressMap.has(street)) {
                  addressMap.set(street, new Set())
                }
                addressMap.get(street).add(number)
                
                // Try to infer other numbers (if we have 2a, might have 2b, 2c, etc.)
                const baseNumber = number.match(/^(\d+)/)?.[1]
                if (baseNumber) {
                  // Check for lettered variants
                  const letters = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'l', 'm', 'n']
                  letters.forEach(letter => {
                    const variant = `${baseNumber}${letter}`
                    // Only add if we've seen at least one lettered variant for this base
                    if (number.includes(baseNumber) && number !== baseNumber) {
                      addressMap.get(street).add(variant)
                    }
                  })
                }
              } else {
                addressMap.set(fullAddress, new Set([]))
              }
            })
            
            // Reconstruct addresses
            const addresses = []
            addressMap.forEach((numbers, street) => {
              if (numbers.size > 0) {
                const sortedNumbers = Array.from(numbers).sort((a, b) => {
                  const numA = parseInt(a.toString())
                  const numB = parseInt(b.toString())
                  if (numA !== numB) return numA - numB
                  return a.toString().localeCompare(b.toString())
                })
                sortedNumbers.forEach(num => {
                  addresses.push(`${street} ${num}`)
                })
              } else {
                addresses.push(street)
              }
            })
            
              // Remove duplicates and sort  
              detectedAddresses = [...new Set(addresses)].sort((a, b) => {
                // Try to sort by street name first, then number
                const matchA = a.match(/^(.+?)\s+(\d+)([a-z]?)$/i)
                const matchB = b.match(/^(.+?)\s+(\d+)([a-z]?)$/i)
                
                if (matchA && matchB) {
                  const streetComp = matchA[1].localeCompare(matchB[1])
                  if (streetComp !== 0) return streetComp
                  
                  const numComp = parseInt(matchA[2]) - parseInt(matchB[2])
                  if (numComp !== 0) return numComp
                  
                  return (matchA[3] || '').localeCompare(matchB[3] || '')
                }
                
                return a.localeCompare(b)
              })
            }
          }
          
          // Combine detected addresses with company addresses
          const companyAddresses = new Set<string>()
          const companies = await companiesPromise
          
          // Update building data
          buildingData.companies = companies
          
          // Extract unique addresses from companies
          companies.forEach((company: any) => {
            if (company.indirizzoCompleto) {
              // Parse address to get street and number
              const addr = company.indirizzoCompleto
              const match = addr.match(/^([^,]+),/)
              if (match) {
                companyAddresses.add(match[1].trim())
              } else {
                companyAddresses.add(addr.trim())
              }
            }
          })
          
          // Merge all detected addresses
          const allDetectedAddresses = new Set([...companyAddresses])
          if (detectedAddresses) {
            detectedAddresses.forEach(addr => allDetectedAddresses.add(addr))
          }
          
          const uniqueAddresses = Array.from(allDetectedAddresses).sort((a, b) => {
            // Try to sort by street name first, then number
            const matchA = a.match(/^(.+?)\s+(\d+)([a-z]?)$/i)
            const matchB = b.match(/^(.+?)\s+(\d+)([a-z]?)$/i)
            
            if (matchA && matchB) {
              const streetComp = matchA[1].localeCompare(matchB[1])
              if (streetComp !== 0) return streetComp
              
              const numComp = parseInt(matchA[2]) - parseInt(matchB[2])
              if (numComp !== 0) return numComp
              
              return (matchA[3] || '').localeCompare(matchB[3] || '')
            }
            
            return a.localeCompare(b)
          })
          
          // Update building data
          buildingData.addresses = uniqueAddresses
          
          // Companies already fetched above
          
          // Update the selected building data state
          setSelectedBuildingData(buildingData)
          console.log('Building details fetched:', { addresses: uniqueAddresses.length, companies: companies.length })
      } catch (error) {
        console.error('Error fetching building details:', error)
        buildingData.addresses = []
        buildingData.companies = []
        setSelectedBuildingData(buildingData)
      }
    }
    
    // Change cursor on hover
    const handleMouseEnter = () => {
      if (map.current) map.current.getCanvas().style.cursor = 'pointer'
    }
    
    const handleMouseLeave = () => {
      if (map.current) map.current.getCanvas().style.cursor = ''
    }
    
    // Add event listeners
    map.current.on('mousemove', handleMouseMove)
    map.current.on('click', handleClick)
    map.current.on('mouseenter', 'building-hover', handleMouseEnter)
    map.current.on('mouseleave', 'building-hover', handleMouseLeave)
    
    // Cleanup function
    return () => {
      if (map.current) {
        map.current.off('mousemove', handleMouseMove)
        map.current.off('click', handleClick)
        map.current.off('mouseenter', 'building-hover', handleMouseEnter)
        map.current.off('mouseleave', 'building-hover', handleMouseLeave)
      }
      
      // Clean up on unmount
      if (lastHoveredId !== null && map.current) {
        map.current.setFeatureState(
          { source: 'composite', sourceLayer: 'building', id: lastHoveredId },
          { hover: false }
        )
      }
    }
  }, [buildingSelectionEnabled, mapLoaded])

  const toggleLabels = () => {
    if (!map.current || !mapLoaded) return
    
    const labelLayers = [
      'country-label',
      'state-label',
      'settlement-major-label',
      'settlement-minor-label',
      'settlement-subdivision-label',
      'poi-label',
      'road-label',
      'road-number-shield',
      'road-exit-shield',
      'path-pedestrian-label',
      'ferry-aerialway-label',
      'waterway-label',
      'natural-point-label',
      'natural-line-label',
      'water-point-label',
      'water-line-label',
      'place-label',
      'street-label'
    ]
    
    const visibility = labelsVisible ? 'none' : 'visible'
    
    labelLayers.forEach(layerId => {
      if (map.current?.getLayer(layerId)) {
        map.current.setLayoutProperty(layerId, 'visibility', visibility)
      }
    })
    
    setLabelsVisible(!labelsVisible)
  }
  
  const toggleSatelliteView = () => {
    if (!map.current || !mapLoaded) return
    
    if (satelliteView) {
      // Switch back to Mapbox streets
      map.current.setLayoutProperty('google-satellite-layer', 'visibility', 'none')
      map.current.setLayoutProperty('building-outline', 'visibility', 'none')
      
      // Show all Mapbox base layers
      const layers = map.current.getStyle().layers
      if (layers) {
        layers.forEach(layer => {
          if (layer.id !== 'building-hover' && 
              layer.id !== 'building-outline' && 
              layer.id !== 'google-satellite-layer') {
            map.current?.setLayoutProperty(layer.id, 'visibility', 'visible')
          }
        })
      }
    } else {
      // Switch to Google satellite
      map.current.setLayoutProperty('google-satellite-layer', 'visibility', 'visible')
      map.current.setLayoutProperty('building-outline', 'visibility', 'visible')
      
      // Hide Mapbox base layers but keep building layers
      const layers = map.current.getStyle().layers
      if (layers) {
        layers.forEach(layer => {
          if (layer.id !== 'building-hover' && 
              layer.id !== 'building-outline' && 
              layer.id !== 'google-satellite-layer' &&
              !layer.id.includes('building')) {
            map.current?.setLayoutProperty(layer.id, 'visibility', 'none')
          }
        })
      }
    }
    
    setSatelliteView(!satelliteView)
  }
  
  const toggleBuildings = () => {
    if (!map.current || !mapLoaded) return
    
    const visibility = buildingsVisible ? 'none' : 'visible'
    
    // Toggle building-related layers
    map.current.setLayoutProperty('building-hover', 'visibility', visibility)
    map.current.setLayoutProperty('building-outline', 'visibility', satelliteView ? visibility : 'none')
    
    // Also toggle the base building layer if in satellite view
    if (satelliteView) {
      const layers = map.current.getStyle().layers
      if (layers) {
        layers.forEach(layer => {
          if (layer.id.includes('building') && 
              layer.id !== 'building-hover' && 
              layer.id !== 'building-outline') {
            map.current?.setLayoutProperty(layer.id, 'visibility', visibility)
          }
        })
      }
    }
    
    setBuildingsVisible(!buildingsVisible)
    
    // If hiding buildings, also disable selection
    if (buildingsVisible && buildingSelectionEnabled) {
      setBuildingSelectionEnabled(false)
    }
  }
  
  const handleSearch = async () => {
    if (!searchQuery.trim() || !map.current) return
    
    setIsSearching(true)
    setShowSearchResults(false)
    
    try {
      // Use Mapbox Geocoding API
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(searchQuery)}.json?` +
        `access_token=${mapboxgl.accessToken}&` +
        `proximity=${map.current.getCenter().lng},${map.current.getCenter().lat}&` +
        `language=it&` +
        `limit=5`
      )
      
      const data = await response.json()
      
      if (data.features && data.features.length > 0) {
        setSearchResults(data.features)
        setShowSearchResults(true)
      } else {
        setSearchResults([])
        setShowSearchResults(false)
      }
    } catch (error) {
      console.error('Search error:', error)
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }
  
  const selectSearchResult = (result: any) => {
    if (!map.current) return
    
    const [lng, lat] = result.center
    
    // Remove previous marker
    if (searchMarker.current) {
      searchMarker.current.remove()
    }
    
    // Add marker at searched location
    searchMarker.current = new mapboxgl.Marker({ color: '#3b82f6' })
      .setLngLat([lng, lat])
      .addTo(map.current)
    
    // Fly to location
    map.current.flyTo({
      center: [lng, lat],
      zoom: 17,
      duration: 2000
    })
    
    // Clear search
    setShowSearchResults(false)
    setSearchQuery(result.place_name)
  }
  
  const clearSearch = () => {
    setSearchQuery('')
    setSearchResults([])
    setShowSearchResults(false)
    
    // Remove marker
    if (searchMarker.current) {
      searchMarker.current.remove()
      searchMarker.current = null
    }
  }

  return (
    <div className="relative h-full w-full">
      {error && (
        <div className="absolute top-4 left-4 right-4 z-10 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Error loading map: {error}
        </div>
      )}
      {!mapLoaded && !error && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-gray-100">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading map...</p>
          </div>
        </div>
      )}
      <div ref={mapContainer} className="h-full w-full" />
      
      {/* Search Bar */}
      {mapLoaded && (
        <div className="absolute top-4 right-4 z-10 w-96">
          <div className="relative">
            <Input
              type="text"
              placeholder="Cerca indirizzo..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="pr-20 shadow-lg"
            />
            <div className="absolute right-1 top-1/2 -translate-y-1/2 flex gap-1">
              {searchQuery && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={clearSearch}
                  className="h-7 w-7 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
              <Button
                size="sm"
                onClick={handleSearch}
                disabled={isSearching || !searchQuery.trim()}
                className="h-7"
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* Search Results */}
          {showSearchResults && searchResults.length > 0 && (
            <div className="mt-2 bg-white rounded-lg shadow-lg overflow-hidden">
              {searchResults.map((result, index) => (
                <button
                  key={index}
                  onClick={() => selectSearchResult(result)}
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 border-b last:border-b-0 text-sm"
                >
                  <div className="font-medium">{result.text}</div>
                  <div className="text-gray-500 text-xs">{result.place_name}</div>
                </button>
              ))}
            </div>
          )}
        </div>
      )}
      
      {/* Control Buttons */}
      {mapLoaded && (
        <div className="absolute top-4 left-4 z-10 flex flex-col gap-2">
          <div className="flex gap-2">
            <Button
              onClick={toggleLabels}
              variant={labelsVisible ? "default" : "secondary"}
              size="sm"
              className="shadow-lg"
            >
              {labelsVisible ? <Eye className="h-4 w-4 mr-2" /> : <EyeOff className="h-4 w-4 mr-2" />}
              {labelsVisible ? 'Nascondi Etichette' : 'Mostra Etichette'}
            </Button>
            
            <Button
              onClick={() => setBuildingSelectionEnabled(!buildingSelectionEnabled)}
              variant={buildingSelectionEnabled ? "default" : "secondary"}
              size="sm"
              className="shadow-lg"
            >
              <Home className="h-4 w-4 mr-2" />
              {buildingSelectionEnabled ? 'Disattiva Selezione' : 'Attiva Selezione'}
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button
              onClick={toggleSatelliteView}
              variant={satelliteView ? "default" : "secondary"}
              size="sm"
              className="shadow-lg"
            >
              {satelliteView ? <Map className="h-4 w-4 mr-2" /> : <Satellite className="h-4 w-4 mr-2" />}
              {satelliteView ? 'Vista Mappa' : 'Vista Satellite'}
            </Button>
            
            {satelliteView && (
              <Button
                onClick={toggleBuildings}
                variant={buildingsVisible ? "default" : "secondary"}
                size="sm"
                className="shadow-lg"
              >
                <Building2 className="h-4 w-4 mr-2" />
                {buildingsVisible ? 'Nascondi Edifici' : 'Mostra Edifici'}
              </Button>
            )}
          </div>
        </div>
      )}
      
      {/* Detail Modal */}
      <Dialog open={showDetailModal} onOpenChange={setShowDetailModal}>
        <DialogContent className="!max-w-[95vw] !w-[95vw] max-h-[95vh] h-[90vh] overflow-hidden flex flex-col" style={{ width: '95vw', maxWidth: '95vw' }}>
          <DialogHeader className="shrink-0">
            <DialogTitle className="flex items-center gap-2 text-xl">
              <Building2 className="h-6 w-6" />
              Dettagli Edificio {selectedBuildingData?.id || ''}
            </DialogTitle>
          </DialogHeader>
          
          <Tabs defaultValue="overview" className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Panoramica</TabsTrigger>
              <TabsTrigger value="addresses">Civici</TabsTrigger>
              <TabsTrigger value="companies">Aziende</TabsTrigger>
              <TabsTrigger value="analysis">Analisi</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="flex-1 overflow-auto mt-4">
              <div className="flex gap-6 p-4" style={{ minHeight: '700px' }}>
                <div className="flex-1" style={{ flex: '2 1 0' }}>
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle>Vista Satellitare</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <iframe
                        width="100%"
                        height="600"
                        style={{ border: 0, borderRadius: '8px' }}
                        loading="lazy"
                        allowFullScreen
                        referrerPolicy="no-referrer-when-downgrade"
                        src={`https://www.google.com/maps/embed/v1/view?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8&center=${selectedBuildingData?.coordinates.lat},${selectedBuildingData?.coordinates.lng}&zoom=20&maptype=satellite`}
                      />
                    </CardContent>
                  </Card>
                </div>
                
                <div className="flex-1" style={{ flex: '1 1 0', minWidth: '400px' }}>
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle>Informazioni Generali</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="flex items-center gap-3">
                        <MapPin className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">Coordinate</p>
                          <p className="text-sm text-muted-foreground">
                            {selectedBuildingData?.coordinates.lat.toFixed(6)}, {selectedBuildingData?.coordinates.lng.toFixed(6)}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <Building className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">Area Stimata</p>
                          <p className="text-lg font-semibold text-primary">{selectedBuildingData?.area} m²</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <BarChart3 className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">Potenziale Fotovoltaico</p>
                          <p className="text-lg font-semibold text-green-600">{selectedBuildingData?.pvPotential} kWp</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <Home className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">Numero Civici</p>
                          <p className="text-lg font-semibold">{selectedBuildingData?.addresses?.length || 0} indirizzi</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <Users className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">Aziende Registrate</p>
                          <p className="text-lg font-semibold text-blue-600">{selectedBuildingData?.companies?.length || 0} aziende</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="addresses" className="flex-1 overflow-auto mt-4 p-4">
              <Card className="h-full">
                <CardHeader>
                  <CardTitle>Civici Rilevati nell'Edificio</CardTitle>
                  <CardDescription>
                    Totale: {selectedBuildingData?.addresses?.length || 0} indirizzi
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[650px] w-full pr-4">
                    {selectedBuildingData?.addresses?.length > 0 ? (
                      <div className="grid grid-cols-3 gap-3">
                        {selectedBuildingData.addresses.map((address: string, index: number) => (
                          <div key={index} className="flex items-center gap-3 p-3 rounded-lg border bg-card hover:bg-accent transition-colors">
                            <MapPin className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                            <span className="text-base">{address}</span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Nessun civico trovato</p>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="companies" className="flex-1 overflow-auto mt-4 p-4">
              <Card className="h-full">
                <CardHeader>
                  <CardTitle>Aziende nell'Edificio</CardTitle>
                  <CardDescription>
                    Totale: {selectedBuildingData?.companies?.length || 0} aziende
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[650px] w-full pr-4">
                    {selectedBuildingData?.companies?.length > 0 ? (
                      <div className="space-y-3">
                        {selectedBuildingData.companies.map((company: any, index: number) => (
                          <Card key={index} className="p-4">
                            <div className="space-y-3">
                              <div className="flex items-start justify-between">
                                <h4 className="font-semibold text-base">
                                  {company.ragioneSociale || company.name || 'N/A'}
                                </h4>
                                {company.validationStatus && (
                                  <Badge variant={company.validationStatus === 'TROVATO' ? 'default' : 'secondary'}>
                                    {company.validationStatus}
                                  </Badge>
                                )}
                              </div>
                              
                              {company.piva && (
                                <p className="text-sm text-muted-foreground">
                                  P.IVA: {company.piva}
                                </p>
                              )}
                              
                              {(company.indirizzoCompleto || company.address) && (
                                <p className="text-sm text-muted-foreground">
                                  {company.indirizzoCompleto || company.address}
                                </p>
                              )}
                              
                              {company.categoria && (
                                <Badge variant="outline" className="text-sm">
                                  {company.categoria}
                                </Badge>
                              )}
                            </div>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Nessuna azienda registrata</p>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="analysis" className="flex-1 overflow-auto mt-4 p-4">
              <div className="flex gap-6 flex-wrap">
                <Card className="flex-1" style={{ minWidth: '350px' }}>
                  <CardHeader>
                    <CardTitle>Analisi Fotovoltaico</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="font-medium">Potenza Installabile</p>
                      <p className="text-3xl font-bold text-green-600">{selectedBuildingData?.pvPotential} kWp</p>
                    </div>
                    <div>
                      <p className="font-medium">Produzione Annua Stimata</p>
                      <p className="text-xl font-semibold">{(selectedBuildingData?.pvPotential * 1200).toLocaleString()} kWh</p>
                    </div>
                    <div>
                      <p className="font-medium">CO₂ Risparmiata</p>
                      <p className="text-xl font-semibold">{(selectedBuildingData?.pvPotential * 0.5).toFixed(1)} ton/anno</p>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="flex-1" style={{ minWidth: '350px' }}>
                  <CardHeader>
                    <CardTitle>Analisi Economica</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="font-medium">Investimento Stimato</p>
                      <p className="text-3xl font-bold text-blue-600">€ {(selectedBuildingData?.pvPotential * 1500).toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="font-medium">Risparmio Annuo</p>
                      <p className="text-xl font-semibold">€ {(selectedBuildingData?.pvPotential * 250).toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="font-medium">Tempo di Ritorno</p>
                      <p className="text-xl font-semibold">{((selectedBuildingData?.pvPotential * 1500) / (selectedBuildingData?.pvPotential * 250)).toFixed(1)} anni</p>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="flex-1" style={{ minWidth: '350px' }}>
                  <CardHeader>
                    <CardTitle>Dettagli Tecnici</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="font-medium">Superficie Pannelli</p>
                      <p className="text-xl font-semibold">{(selectedBuildingData?.pvPotential * 5).toFixed(0)} m²</p>
                    </div>
                    <div>
                      <p className="font-medium">Numero Pannelli Stimato</p>
                      <p className="text-xl font-semibold">{Math.round(selectedBuildingData?.pvPotential * 3)} moduli</p>
                    </div>
                    <div>
                      <p className="font-medium">Orientamento Ottimale</p>
                      <p className="text-xl font-semibold">Sud, 30°</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </div>
  )
}