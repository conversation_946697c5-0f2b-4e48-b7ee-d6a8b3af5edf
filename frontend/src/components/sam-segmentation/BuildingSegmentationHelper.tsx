import React from 'react';
import { Info, Home, MousePointer, Square } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface BuildingSegmentationHelperProps {
  onModeChange?: (mode: 'point' | 'box') => void;
}

export const BuildingSegmentationHelper: React.FC<BuildingSegmentationHelperProps> = ({ 
  onModeChange 
}) => {
  return (
    <div className="space-y-4">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Suggerimenti per segmentare edifici</AlertTitle>
        <AlertDescription className="mt-2 space-y-2">
          <p className="text-sm">
            Per ottenere migliori risultati con immagini satellitari:
          </p>
          <ul className="list-disc pl-5 space-y-1 text-sm">
            <li>
              <strong>Usa più punti:</strong> Clicca almeno 3-4 punti sull'edificio (angoli e centro)
            </li>
            <li>
              <strong>Punti negativi:</strong> Clicca con tasto destro fuori dall'edificio per escludere aree
            </li>
            <li>
              <strong>Box mode:</strong> Disegna un rettangolo attorno all'edificio per selezione rapida
            </li>
            <li>
              <strong>Zoom:</strong> Ingrandisci l'immagine per maggiore precisione
            </li>
            <li>
              <strong>Contrasto:</strong> Se possibile, usa immagini con buon contrasto tra edifici e sfondo
            </li>
          </ul>
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <Home className="h-4 w-4" />
            Modalità ottimizzate per edifici
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <button
            onClick={() => onModeChange?.('box')}
            className="w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            <div className="flex items-center gap-2">
              <Square className="h-4 w-4 text-blue-500" />
              <div>
                <p className="font-medium text-sm">Selezione rettangolare</p>
                <p className="text-xs text-gray-500">Ideale per edifici con forma regolare</p>
              </div>
            </div>
          </button>

          <button
            onClick={() => onModeChange?.('point')}
            className="w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            <div className="flex items-center gap-2">
              <MousePointer className="h-4 w-4 text-green-500" />
              <div>
                <p className="font-medium text-sm">Multi-punto preciso</p>
                <p className="text-xs text-gray-500">Per edifici complessi o irregolari</p>
              </div>
            </div>
          </button>
        </CardContent>
      </Card>

      <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
        <CardContent className="pt-4">
          <p className="text-xs text-orange-700 dark:text-orange-400">
            <strong>Nota:</strong> SAM è ottimizzato per immagini naturali. 
            Per risultati professionali su mappe satellitari, considera l'uso di modelli 
            specializzati per remote sensing come DeepLab o U-Net addestrati su dataset satellitari.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default BuildingSegmentationHelper;