import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Upload, 
  Wand2, 
  <PERSON><PERSON>ointer, 
  Square,
  Loader2, 
  AlertCircle, 
  Download,
  Trash2,
  ZoomIn,
  ZoomOut,
  Move,
  Layers,
  Eye,
  EyeOff,
  Settings,
  Server,
  Zap,
  Image as ImageIcon,
  Maximize2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { samLocalService, Point } from '@/services/samLocalService';
import { samImageService } from '@/services/samImageService';
import type { SamImage } from '@/services/samImageService';
import { deeplabService } from '@/services/deeplabService';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

type Tool = 'point' | 'box' | 'polygon' | 'pan' | 'zoom';
type MaskMode = 'single' | 'multi' | 'all';

interface Box {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

interface Mask {
  id: string;
  data: string;
  score: number;
  color: string;
  visible: boolean;
  name: string;
  model?: 'sam' | 'deeplab';
}

interface SAMAdvancedSegmentationProps {
  selectedImage?: SamImage | null;
  onImageUploaded?: (imageId: string) => void;
}

export const SAMAdvancedSegmentation: React.FC<SAMAdvancedSegmentationProps> = ({ 
  selectedImage,
  onImageUploaded 
}) => {
  // State management
  const [image, setImage] = useState<File | null>(null);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string>('');
  const [serverStatus, setServerStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [gpuInfo, setGpuInfo] = useState<any>(null);
  const [segmentationModel, setSegmentationModel] = useState<'sam' | 'deeplab'>('sam');
  
  // Segmentation state
  const [points, setPoints] = useState<Point[]>([]);
  const [boxes, setBoxes] = useState<Box[]>([]);
  const [masks, setMasks] = useState<Mask[]>([]);
  const [activeTool, setActiveTool] = useState<Tool>('point');
  const [maskMode, setMaskMode] = useState<MaskMode>('single');
  const [currentBox, setCurrentBox] = useState<Box | null>(null);
  const [isDrawingBox, setIsDrawingBox] = useState(false);
  
  // View state
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [showGrid, setShowGrid] = useState(false);
  const [maskOpacity, setMaskOpacity] = useState(0.5);
  const [autoSegment, setAutoSegment] = useState(true);
  const [showMasks] = useState(true);
  
  // Performance metrics
  const [lastSegmentTime, setLastSegmentTime] = useState<number>(0);
  const [totalSegments, setTotalSegments] = useState<number>(0);
  
  // Refs
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const maskCanvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Check server status
  useEffect(() => {
    checkServerStatus();
    const interval = setInterval(checkServerStatus, 30000); // Check every 30s
    return () => clearInterval(interval);
  }, [segmentationModel]); // Re-check when model changes

  // Handle selected image from history
  useEffect(() => {
    if (selectedImage) {
      // Load image from URL
      const loadImageFromHistory = async () => {
        try {
          // Use signed URL if available, otherwise regular URL
          const imageUrl = selectedImage.signedUrl || selectedImage.url;
          setImageUrl(imageUrl);
          
          // Create a File object from the URL for processing
          const response = await fetch(imageUrl);
          const blob = await response.blob();
          const file = new File([blob], selectedImage.name || 'image.jpg', {
            type: selectedImage.mimeType || 'image/jpeg'
          });
          
          setImage(file);
          resetSegmentation();
          onImageUploaded?.(selectedImage.id);
          
          toast.success('Immagine caricata dallo storico');
        } catch (error) {
          console.error('Error loading image from history:', error);
          toast.error('Errore nel caricamento dell\'immagine');
        }
      };
      
      loadImageFromHistory();
    }
  }, [selectedImage]);

  const checkServerStatus = async () => {
    try {
      // Check the selected model's server
      if (segmentationModel === 'deeplab') {
        const isHealthy = await deeplabService.checkHealth();
        setServerStatus(isHealthy ? 'online' : 'offline');
        
        if (isHealthy) {
          try {
            const gpuData = await deeplabService.getGPUInfo();
            setGpuInfo(gpuData);
          } catch (e) {
            console.log('GPU info not available from DeepLab');
          }
        }
      } else {
        // Check SAM server
        await samLocalService.checkHealth();
        setServerStatus('online');
        
        // Try to get GPU info from SAM
        try {
          const response = await fetch('http://192.168.0.110:8080/gpu-info');
          if (response.ok) {
            const gpuData = await response.json();
            setGpuInfo(gpuData);
          }
        } catch (e) {
          console.log('GPU info not available from SAM');
        }
      }
    } catch (error) {
      setServerStatus('offline');
      setError(`${segmentationModel.toUpperCase()} Server non raggiungibile`);
    }
  };

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setImage(file);
    resetSegmentation();
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImageUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    setError('');
    
    // Auto-save to S3
    try {
      const result = await samImageService.uploadImage(file, {
        name: file.name,
        description: `Uploaded on ${new Date().toLocaleDateString()}`,
        source: 'sam-segmentation'
      });
      
      if (result.success && result.image) {
        toast.success('Immagine salvata nello storico');
        onImageUploaded?.(result.image.id);
      }
    } catch (error) {
      console.error('Error saving image to history:', error);
      // Non mostriamo errore perché il salvataggio è opzionale
    }
  };

  // Reset segmentation
  const resetSegmentation = () => {
    setPoints([]);
    setBoxes([]);
    setMasks([]);
    setCurrentBox(null);
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  // Draw everything
  const draw = useCallback(() => {
    if (!canvasRef.current || !imageRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    ctx.save();
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Apply transformations
    ctx.translate(pan.x, pan.y);
    ctx.scale(zoom, zoom);
    
    // Draw image
    ctx.drawImage(imageRef.current, 0, 0);
    
    // Draw grid if enabled
    if (showGrid) {
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
      ctx.lineWidth = 1 / zoom;
      for (let i = 0; i <= canvas.width; i += 50) {
        ctx.beginPath();
        ctx.moveTo(i, 0);
        ctx.lineTo(i, canvas.height);
        ctx.stroke();
      }
      for (let i = 0; i <= canvas.height; i += 50) {
        ctx.beginPath();
        ctx.moveTo(0, i);
        ctx.lineTo(canvas.width, i);
        ctx.stroke();
      }
    }
    
    // Draw boxes
    boxes.forEach(box => {
      ctx.strokeStyle = '#3b82f6';
      ctx.lineWidth = 2 / zoom;
      ctx.strokeRect(box.x1, box.y1, box.x2 - box.x1, box.y2 - box.y1);
    });
    
    // Draw current box being drawn
    if (currentBox && isDrawingBox) {
      ctx.strokeStyle = '#ef4444';
      ctx.lineWidth = 2 / zoom;
      ctx.setLineDash([5 / zoom, 5 / zoom]);
      ctx.strokeRect(
        currentBox.x1, 
        currentBox.y1, 
        currentBox.x2 - currentBox.x1, 
        currentBox.y2 - currentBox.y1
      );
      ctx.setLineDash([]);
    }
    
    // Draw points
    points.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, 6 / zoom, 0, 2 * Math.PI);
      ctx.fillStyle = point.label === 1 ? '#10b981' : '#ef4444';
      ctx.fill();
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2 / zoom;
      ctx.stroke();
      
      // Draw + or - symbol
      ctx.fillStyle = '#ffffff';
      ctx.font = `bold ${12 / zoom}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(point.label === 1 ? '+' : '-', point.x, point.y);
    });
    
    ctx.restore();
  }, [points, boxes, currentBox, isDrawingBox, zoom, pan, showGrid]);

  // Draw masks
  const drawMasks = useCallback(async () => {
    if (!maskCanvasRef.current || !imageRef.current) return;
    
    const ctx = maskCanvasRef.current.getContext('2d');
    if (!ctx) return;
    
    // Set canvas size to match the container
    const canvas = maskCanvasRef.current;
    const imageCanvas = canvasRef.current;
    if (imageCanvas) {
      canvas.width = imageCanvas.width;
      canvas.height = imageCanvas.height;
    }
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    if (!showMasks || masks.length === 0) {
      console.log('Not drawing masks:', { showMasks, masksLength: masks.length });
      return;
    }
    
    ctx.save();
    ctx.translate(pan.x, pan.y);
    ctx.scale(zoom, zoom);
    
    // Process each visible mask
    const visibleMasks = masks.filter(m => m.visible);
    console.log('Drawing masks:', { 
      total: masks.length, 
      visible: visibleMasks.length,
      showMasks,
      maskOpacity 
    });
    
    // Load all mask images first
    const loadedImages = await Promise.all(
      visibleMasks.map((mask) => {
        return new Promise<HTMLImageElement>((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve(img);
          img.onerror = reject;
          img.src = `data:image/png;base64,${mask.data}`;
        });
      })
    );
    
    // Now draw all masks synchronously
    loadedImages.forEach((img, index) => {
      const mask = visibleMasks[index];
      console.log(`Drawing mask ${index}...`);
      
      // Create a temporary canvas for the mask
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = imageRef.current!.width;
      tempCanvas.height = imageRef.current!.height;
      const tempCtx = tempCanvas.getContext('2d');
      
      if (!tempCtx) return;
      
      // Draw the mask image to temp canvas
      tempCtx.drawImage(img, 0, 0, imageRef.current!.width, imageRef.current!.height);
      
      // Get the image data to create colored overlay
      const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
      const data = imageData.data;
      
      // Parse the color (assuming hex format like #3b82f6)
      const color = mask.color;
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      
      // Apply color to non-transparent pixels
      for (let i = 0; i < data.length; i += 4) {
        if (data[i + 3] > 0) { // If pixel is not transparent
          data[i] = r;     // Red
          data[i + 1] = g; // Green
          data[i + 2] = b; // Blue
          data[i + 3] = Math.floor(data[i + 3] * maskOpacity); // Apply opacity to alpha
        }
      }
      
      // Put the colored image data back
      tempCtx.putImageData(imageData, 0, 0);
      
      // Draw to main canvas
      ctx.drawImage(tempCanvas, 0, 0);
    });
    
    ctx.restore();
  }, [masks, showMasks, maskOpacity, zoom, pan]);

  // Update canvas when image loads
  useEffect(() => {
    if (imageUrl && canvasRef.current && maskCanvasRef.current && imageRef.current) {
      const img = imageRef.current;
      
      img.onload = () => {
        const canvas = canvasRef.current!;
        const maskCanvas = maskCanvasRef.current!;
        
        canvas.width = img.width;
        canvas.height = img.height;
        maskCanvas.width = img.width;
        maskCanvas.height = img.height;
        
        draw();
      };
    }
  }, [imageUrl, draw]);

  // Redraw when state changes
  useEffect(() => {
    draw();
  }, [draw]);

  useEffect(() => {
    drawMasks().catch(console.error);
  }, [drawMasks]);

  // Handle canvas interaction
  const getCanvasCoordinates = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !imageRef.current) return { x: 0, y: 0 };
    
    const rect = canvasRef.current.getBoundingClientRect();
    const canvas = canvasRef.current;
    const img = imageRef.current;
    
    // Get click position relative to canvas
    const canvasX = e.clientX - rect.left;
    const canvasY = e.clientY - rect.top;
    
    // Convert to image coordinates considering zoom and pan
    // The image is drawn at (0,0) with its natural dimensions
    const x = (canvasX - pan.x) / zoom;
    const y = (canvasY - pan.y) / zoom;
    
    // Ensure coordinates are within image bounds
    const finalX = Math.max(0, Math.min(img.width, Math.round(x)));
    const finalY = Math.max(0, Math.min(img.height, Math.round(y)));
    
    console.log('Click coordinates:', {
      canvas: { x: canvasX, y: canvasY },
      image: { x: finalX, y: finalY },
      imageDimensions: { width: img.width, height: img.height }
    });
    
    return { x: finalX, y: finalY };
  };

  const handleCanvasMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const coords = getCanvasCoordinates(e);
    
    if (activeTool === 'point') {
      handlePointClick(coords, e.button === 0 ? 1 : 0);
    } else if (activeTool === 'box') {
      setIsDrawingBox(true);
      setCurrentBox({ x1: coords.x, y1: coords.y, x2: coords.x, y2: coords.y });
    }
  };

  const handleCanvasMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (activeTool === 'box' && isDrawingBox && currentBox) {
      const coords = getCanvasCoordinates(e);
      setCurrentBox({ ...currentBox, x2: coords.x, y2: coords.y });
    }
  };

  const handleCanvasMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (activeTool === 'box' && isDrawingBox && currentBox) {
      const coords = getCanvasCoordinates(e);
      const finalBox = { ...currentBox, x2: coords.x, y2: coords.y };
      
      // Normalize box coordinates
      const normalizedBox: Box = {
        x1: Math.min(finalBox.x1, finalBox.x2),
        y1: Math.min(finalBox.y1, finalBox.y2),
        x2: Math.max(finalBox.x1, finalBox.x2),
        y2: Math.max(finalBox.y1, finalBox.y2)
      };
      
      setBoxes([...boxes, normalizedBox]);
      setIsDrawingBox(false);
      setCurrentBox(null);
      
      if (autoSegment) {
        performSegmentation();
      }
    }
  };

  const handlePointClick = (coords: { x: number, y: number }, label: number) => {
    if (!image) return;
    
    const newPoint: Point = { x: coords.x, y: coords.y, label };
    const newPoints = [...points, newPoint];
    setPoints(newPoints);
    
    if (autoSegment) {
      performSegmentation();
    }
  };

  // Perform segmentation
  const performSegmentation = async () => {
    if (!image || isProcessing) return;
    
    setIsProcessing(true);
    const startTime = performance.now();
    
    try {
      let result;
      
      // Use selected model
      if (segmentationModel === 'deeplab') {
        console.log('Using DeepLab for segmentation...');
        result = await deeplabService.segmentWithPoints(image, points);
      } else {
        console.log('Using SAM for segmentation...');
        result = await samLocalService.segmentWithPoints(image, points);
      }
      
      const endTime = performance.now();
      setLastSegmentTime(endTime - startTime);
      setTotalSegments(totalSegments + 1);
      
      if (result.masks && result.masks.length > 0) {
        console.log(`Received masks from ${segmentationModel}:`, {
          count: result.masks.length,
          firstMaskSample: result.masks[0]?.substring(0, 50), // Log first 50 chars
          scores: result.scores
        });
        
        const newMasks = result.masks.slice(0, maskMode === 'single' ? 1 : 3).map((maskData, idx) => ({
          id: `mask-${Date.now()}-${idx}`,
          data: maskData,
          score: result.scores?.[idx] || 0,
          color: ['#3b82f6', '#10b981', '#f59e0b'][idx] || '#3b82f6',
          visible: true,
          name: `Mask ${masks.length + idx + 1}`,
          model: segmentationModel
        }));
        
        console.log('Created new masks:', newMasks);
        
        if (maskMode === 'single') {
          setMasks(newMasks);
        } else {
          setMasks([...masks, ...newMasks]);
        }
      }
    } catch (error) {
      console.error('Segmentation error:', error);
      setError('Errore nella segmentazione');
    } finally {
      setIsProcessing(false);
    }
  };

  // Export functions
  const exportMask = (mask: Mask) => {
    const link = document.createElement('a');
    link.download = `${mask.name}.png`;
    link.href = `data:image/png;base64,${mask.data}`;
    link.click();
  };

  const exportComposite = () => {
    if (!canvasRef.current || !maskCanvasRef.current) return;
    
    const compositeCanvas = document.createElement('canvas');
    compositeCanvas.width = canvasRef.current.width;
    compositeCanvas.height = canvasRef.current.height;
    const ctx = compositeCanvas.getContext('2d');
    
    if (ctx) {
      ctx.drawImage(canvasRef.current, 0, 0);
      ctx.globalAlpha = maskOpacity;
      ctx.drawImage(maskCanvasRef.current, 0, 0);
      
      compositeCanvas.toBlob(blob => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.download = 'segmentation-result.png';
          link.href = url;
          link.click();
          URL.revokeObjectURL(url);
        }
      });
    }
  };

  // Zoom controls
  const handleZoom = (delta: number) => {
    setZoom(prev => Math.max(0.1, Math.min(5, prev + delta)));
  };

  const resetView = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  return (
    <div className="w-full max-w-full mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Wand2 className="w-6 h-6" />
              AI Segmentation - RTX 5080 GPU
            </CardTitle>
            <div className="flex items-center gap-4">
              <Select value={segmentationModel} onValueChange={(value: 'sam' | 'deeplab') => setSegmentationModel(value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sam">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">SAM</Badge>
                      <span className="text-sm">Segment Anything</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="deeplab">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">DeepLab</Badge>
                      <span className="text-sm">Satellite Optimized</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <Badge variant={serverStatus === 'online' ? 'default' : 'destructive'}>
                <Server className="w-3 h-3 mr-1" />
                {serverStatus === 'checking' ? 'Checking...' : serverStatus}
              </Badge>
              {gpuInfo && (
                <Badge variant="secondary">
                  <Zap className="w-3 h-3 mr-1" />
                  {gpuInfo.name || 'RTX 5080'}
                </Badge>
              )}
              {totalSegments > 0 && (
                <Badge variant="outline">
                  {totalSegments} segmentazioni • {lastSegmentTime.toFixed(0)}ms
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Toolbar */}
          <div className="flex flex-wrap gap-2">
            {/* File Upload */}
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={serverStatus !== 'online'}
            >
              <Upload className="w-4 h-4 mr-2" />
              Carica Immagine
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
            
            {/* Tool Selection */}
            <div className="flex gap-1 border rounded-lg p-1">
              <Button
                size="sm"
                variant={activeTool === 'point' ? 'default' : 'ghost'}
                onClick={() => setActiveTool('point')}
              >
                <MousePointer className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant={activeTool === 'box' ? 'default' : 'ghost'}
                onClick={() => setActiveTool('box')}
              >
                <Square className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant={activeTool === 'pan' ? 'default' : 'ghost'}
                onClick={() => setActiveTool('pan')}
              >
                <Move className="w-4 h-4" />
              </Button>
            </div>
            
            {/* View Controls */}
            <div className="flex gap-1">
              <Button size="sm" variant="outline" onClick={() => handleZoom(0.1)}>
                <ZoomIn className="w-4 h-4" />
              </Button>
              <Button size="sm" variant="outline" onClick={() => handleZoom(-0.1)}>
                <ZoomOut className="w-4 h-4" />
              </Button>
              <Button size="sm" variant="outline" onClick={resetView}>
                <Maximize2 className="w-4 h-4" />
              </Button>
            </div>
            
            {/* Actions */}
            {image && (
              <>
                <Button
                  variant="outline"
                  onClick={performSegmentation}
                  disabled={isProcessing || points.length === 0}
                >
                  <Wand2 className="w-4 h-4 mr-2" />
                  Segmenta
                </Button>
                <Button
                  variant="outline"
                  onClick={resetSegmentation}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Reset
                </Button>
                <Button
                  variant="outline"
                  onClick={exportComposite}
                  disabled={masks.length === 0}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Esporta
                </Button>
              </>
            )}
          </div>

          {/* Main Content Area */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Canvas Area */}
            <div className="lg:col-span-3">
              <div className="border rounded-lg overflow-hidden bg-gray-50" ref={containerRef}>
                {imageUrl ? (
                  <div className="relative">
                    <canvas
                      ref={canvasRef}
                      className={cn(
                        "max-w-full",
                        activeTool === 'point' && "cursor-crosshair",
                        activeTool === 'box' && "cursor-crosshair",
                        activeTool === 'pan' && "cursor-move"
                      )}
                      onMouseDown={handleCanvasMouseDown}
                      onMouseMove={handleCanvasMouseMove}
                      onMouseUp={handleCanvasMouseUp}
                      onContextMenu={(e) => {
                        e.preventDefault();
                        if (activeTool === 'point') {
                          const coords = getCanvasCoordinates(e);
                          handlePointClick(coords, 0);
                        }
                      }}
                    />
                    
                    <canvas
                      ref={maskCanvasRef}
                      className="absolute top-0 left-0 max-w-full pointer-events-none"
                      style={{ opacity: showMasks ? 1 : 0 }}
                    />
                    
                    <img
                      ref={imageRef}
                      src={imageUrl}
                      className="hidden"
                      alt="Source"
                    />
                    
                    {isProcessing && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div className="bg-white rounded-lg p-4 flex items-center gap-2">
                          <Loader2 className="w-5 h-5 animate-spin" />
                          <span>Segmentazione...</span>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="h-96 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <ImageIcon className="w-12 h-12 mx-auto mb-2" />
                      <p>Carica un'immagine per iniziare</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Side Panel */}
            <div className="space-y-4">
              {/* Settings */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Settings className="w-4 h-4" />
                    Impostazioni
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    <Label className="text-xs">Modalità Maschera</Label>
                    <Select value={maskMode} onValueChange={(v) => setMaskMode(v as MaskMode)}>
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="single">Singola</SelectItem>
                        <SelectItem value="multi">Multiple</SelectItem>
                        <SelectItem value="all">Tutte</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Auto-segmenta</Label>
                    <Switch
                      checked={autoSegment}
                      onCheckedChange={setAutoSegment}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Mostra griglia</Label>
                    <Switch
                      checked={showGrid}
                      onCheckedChange={setShowGrid}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-xs">Opacità maschere: {Math.round(maskOpacity * 100)}%</Label>
                    <Slider
                      value={[maskOpacity]}
                      onValueChange={([v]) => setMaskOpacity(v)}
                      min={0}
                      max={1}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Masks List */}
              {masks.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Layers className="w-4 h-4" />
                      Maschere ({masks.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {masks.map(mask => (
                      <div key={mask.id} className="flex items-center gap-2 p-2 border rounded">
                        <button
                          onClick={() => {
                            setMasks(masks.map(m => 
                              m.id === mask.id ? { ...m, visible: !m.visible } : m
                            ));
                          }}
                          className="p-1"
                        >
                          {mask.visible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                        </button>
                        <div
                          className="w-4 h-4 rounded"
                          style={{ backgroundColor: mask.color }}
                        />
                        <span className="text-xs flex-1">{mask.name}</span>
                        <span className="text-xs text-gray-500">
                          {(mask.score * 100).toFixed(1)}%
                        </span>
                        <button
                          onClick={() => exportMask(mask)}
                          className="p-1"
                        >
                          <Download className="w-3 h-3" />
                        </button>
                        <button
                          onClick={() => setMasks(masks.filter(m => m.id !== mask.id))}
                          className="p-1"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* Instructions */}
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-xs">
                  <strong>Modello {segmentationModel.toUpperCase()}:</strong> {
                    segmentationModel === 'sam' 
                      ? 'Ottimizzato per immagini naturali e oggetti generici'
                      : 'Ottimizzato per immagini satellitari e remote sensing'
                  }<br/>
                  <strong>Punto:</strong> Click sinistro (foreground), destro (background)<br/>
                  <strong>Box:</strong> Trascina per selezionare area<br/>
                  <strong>Zoom:</strong> Usa i controlli o rotella mouse
                </AlertDescription>
              </Alert>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};