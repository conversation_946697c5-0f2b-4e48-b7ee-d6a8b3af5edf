import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  Trash2, 
  Download, 
  Eye, 
  Image as ImageIcon,
  Loader2,
  Search,
  X
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { samImageService, SamImage } from '@/services/samImageService';
import { formatDistanceToNow } from 'date-fns';
import { it } from 'date-fns/locale';
import { toast } from 'sonner';

interface ImageHistoryProps {
  onSelectImage: (image: SamImage) => void;
  currentImageId?: string;
}

export const ImageHistory: React.FC<ImageHistoryProps> = ({ 
  onSelectImage,
  currentImageId 
}) => {
  const [images, setImages] = useState<SamImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [hasMore, setHasMore] = useState(false);
  const [offset, setOffset] = useState(0);
  const limit = 20;

  useEffect(() => {
    loadImages();
  }, []);

  const loadImages = async (reset = false) => {
    try {
      setLoading(true);
      const response = await samImageService.getImageHistory({
        limit,
        offset: reset ? 0 : offset,
        sortBy: 'createdAt',
        order: 'desc'
      });

      if (reset) {
        setImages(response.images);
        setOffset(limit);
      } else {
        setImages(prev => [...prev, ...response.images]);
        setOffset(prev => prev + limit);
      }
      
      setHasMore(response.hasMore);
    } catch (error) {
      console.error('Error loading images:', error);
      toast.error('Errore nel caricamento delle immagini');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (imageId: string) => {
    if (!confirm('Sei sicuro di voler eliminare questa immagine?')) return;

    try {
      await samImageService.deleteImage(imageId);
      setImages(prev => prev.filter(img => img.id !== imageId));
      toast.success('Immagine eliminata');
    } catch (error) {
      console.error('Error deleting image:', error);
      toast.error('Errore nell\'eliminazione dell\'immagine');
    }
  };

  const handleDownload = (image: SamImage) => {
    const link = document.createElement('a');
    link.href = image.url;
    link.download = image.name || `image-${image.id}.png`;
    link.click();
  };

  const filteredImages = images.filter(img => 
    img.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    img.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Storico Immagini
          </div>
          <Button
            size="sm"
            variant="outline"
            onClick={() => loadImages(true)}
          >
            Aggiorna
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col p-4">
        {/* Search bar */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Cerca immagini..."
            className="pl-9 pr-9"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2"
            >
              <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
            </button>
          )}
        </div>

        {/* Images list */}
        <ScrollArea className="flex-1">
          {loading && images.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : filteredImages.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Nessuna immagine trovata</p>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredImages.map((image) => (
                <div
                  key={image.id}
                  className={`
                    group relative rounded-lg border p-3 hover:bg-gray-50 dark:hover:bg-gray-800 
                    transition-colors cursor-pointer
                    ${currentImageId === image.id ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' : ''}
                  `}
                  onClick={() => onSelectImage(image)}
                >
                  <div className="flex gap-3">
                    {/* Thumbnail */}
                    <div className="flex-shrink-0 w-16 h-16 rounded overflow-hidden bg-gray-100">
                      {image.thumbnailUrl ? (
                        <img
                          src={image.thumbnailUrl}
                          alt={image.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <ImageIcon className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Info */}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm truncate">
                        {image.name || `Immagine ${image.id.slice(0, 8)}`}
                      </h4>
                      {image.description && (
                        <p className="text-xs text-gray-500 truncate">
                          {image.description}
                        </p>
                      )}
                      <div className="flex items-center gap-3 mt-1 text-xs text-gray-400">
                        <span>{formatFileSize(image.size)}</span>
                        {image.width && image.height && (
                          <span>{image.width}x{image.height}</span>
                        )}
                        {image._count?.segmentations ? (
                          <span>{image._count.segmentations} segm.</span>
                        ) : null}
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        {formatDistanceToNow(new Date(image.createdAt), { 
                          addSuffix: true,
                          locale: it 
                        })}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-7 w-7 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          onSelectImage(image);
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-7 w-7 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownload(image);
                        }}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-7 w-7 p-0 text-red-500 hover:text-red-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(image.id);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}

              {/* Load more button */}
              {hasMore && !loading && (
                <Button
                  variant="outline"
                  className="w-full mt-4"
                  onClick={() => loadImages()}
                >
                  Carica altre
                </Button>
              )}

              {loading && images.length > 0 && (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-5 w-5 animate-spin" />
                </div>
              )}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default ImageHistory;