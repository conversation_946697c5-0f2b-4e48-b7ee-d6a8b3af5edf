import React, { useState, useRef, useEffect } from 'react';
import { Upload, Wand2, <PERSON>, MousePointer, Loader2, Download, AlertCircle, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  samSegmentationService, 
  SegmentationPoint, 
  SegmentationBox,
  SegmentationOptions 
} from '@/services/samSegmentationService';

type SegmentationMode = 'auto' | 'points' | 'box' | 'roof';

interface Point {
  x: number;
  y: number;
  label: number;
}

export const SAMSegmentationTool: React.FC = () => {
  const [mode, setMode] = useState<SegmentationMode>('auto');
  const [image, setImage] = useState<File | null>(null);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [result, setResult] = useState<any>(null);
  
  // Interactive segmentation state
  const [points, setPoints] = useState<Point[]>([]);
  const [box, setBox] = useState<SegmentationBox | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  
  // Options
  const [options, setOptions] = useState<SegmentationOptions>({
    pointsPerSide: 32,
    predIouThresh: 0.88,
    stabilityScoreThresh: 0.95,
    minMaskRegionArea: 0,
    multimaskOutput: true
  });
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImageUrl(e.target?.result as string);
        setResult(null);
        setPoints([]);
        setBox(null);
      };
      reader.readAsDataURL(file);
    }
  };

  // Draw image on canvas
  useEffect(() => {
    if (imageUrl && canvasRef.current && imageRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      const img = imageRef.current;
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        
        // Redraw points and boxes
        drawAnnotations();
      };
    }
  }, [imageUrl]);

  // Draw annotations (points and boxes)
  const drawAnnotations = () => {
    if (!canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Clear and redraw image
    if (imageRef.current) {
      ctx.drawImage(imageRef.current, 0, 0);
    }
    
    // Draw points
    points.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, 5, 0, 2 * Math.PI);
      ctx.fillStyle = point.label === 1 ? '#00ff00' : '#ff0000';
      ctx.fill();
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();
    });
    
    // Draw box
    if (box) {
      ctx.strokeStyle = '#0080ff';
      ctx.lineWidth = 2;
      ctx.strokeRect(
        box.x1,
        box.y1,
        box.x2 - box.x1,
        box.y2 - box.y1
      );
    }
  };

  // Handle canvas click for point mode
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (mode !== 'points' || !canvasRef.current) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Left click = foreground (1), right click = background (0)
    const label = e.button === 0 ? 1 : 0;
    
    const newPoint = { x, y, label };
    setPoints([...points, newPoint]);
    
    // Redraw
    setTimeout(drawAnnotations, 0);
  };

  // Handle box drawing
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (mode !== 'box' || !canvasRef.current) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setStartPoint({ x, y });
    setIsDrawing(true);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !startPoint || !canvasRef.current) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setBox({
      x1: Math.min(startPoint.x, x),
      y1: Math.min(startPoint.y, y),
      x2: Math.max(startPoint.x, x),
      y2: Math.max(startPoint.y, y)
    });
    
    drawAnnotations();
  };

  const handleMouseUp = () => {
    setIsDrawing(false);
  };

  // Run segmentation
  const runSegmentation = async () => {
    if (!image && !imageUrl) {
      setError('Please upload an image first');
      return;
    }
    
    setIsLoading(true);
    setError('');
    
    try {
      let result;
      
      switch (mode) {
        case 'auto':
          result = await samSegmentationService.segmentAuto(image || imageUrl, options);
          break;
        case 'points':
          if (points.length === 0) {
            throw new Error('Please add at least one point');
          }
          result = await samSegmentationService.segmentWithPoints(
            image || imageUrl,
            points,
            options
          );
          break;
        case 'box':
          if (!box) {
            throw new Error('Please draw a bounding box');
          }
          result = await samSegmentationService.segmentWithBox(
            image || imageUrl,
            box,
            options
          );
          break;
        case 'roof':
          result = await samSegmentationService.segmentRoof(image || imageUrl, options);
          break;
      }
      
      setResult(result);
      
      // Draw masks on canvas
      if (result.masks && canvasRef.current) {
        drawMasks(result.masks);
      }
    } catch (error: any) {
      setError(error.message || 'Segmentation failed');
      console.error('Segmentation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Draw segmentation masks
  const drawMasks = (masks: any) => {
    if (!canvasRef.current) return;
    
    const ctx = canvasRef.current.getContext('2d');
    if (!ctx) return;
    
    // This is a placeholder - actual implementation depends on mask format from Replicate
    // Masks might be base64 encoded images or arrays
    console.log('Drawing masks:', masks);
    
    // If masks are image URLs/base64
    if (typeof masks === 'string') {
      const maskImg = new Image();
      maskImg.onload = () => {
        ctx.globalAlpha = 0.5;
        ctx.drawImage(maskImg, 0, 0);
        ctx.globalAlpha = 1.0;
      };
      maskImg.src = masks;
    }
  };

  // Clear all annotations
  const clearAnnotations = () => {
    setPoints([]);
    setBox(null);
    setResult(null);
    drawAnnotations();
  };

  // Download result
  const downloadResult = () => {
    if (!result) return;
    
    const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `segmentation-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="w-6 h-6" />
            SAM Segmentation Tool (GPU Cloud)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Mode Selection */}
          <Tabs value={mode} onValueChange={(v) => setMode(v as SegmentationMode)}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="auto">Auto</TabsTrigger>
              <TabsTrigger value="points">Points</TabsTrigger>
              <TabsTrigger value="box">Box</TabsTrigger>
              <TabsTrigger value="roof">Roof</TabsTrigger>
            </TabsList>
            
            <TabsContent value="auto" className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Automatic segmentation will identify all objects in the image
              </p>
            </TabsContent>
            
            <TabsContent value="points" className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Click to add foreground points (green), right-click for background points (red)
              </p>
            </TabsContent>
            
            <TabsContent value="box" className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Draw a bounding box around the object you want to segment
              </p>
            </TabsContent>
            
            <TabsContent value="roof" className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Specialized segmentation optimized for roof and solar panel detection
              </p>
            </TabsContent>
          </Tabs>

          {/* Image Upload */}
          <div className="space-y-2">
            <Label>Upload Image</Label>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center gap-2"
              >
                <Upload className="w-4 h-4" />
                Choose Image
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
              {image && (
                <span className="text-sm text-muted-foreground self-center">
                  {image.name}
                </span>
              )}
            </div>
          </div>

          {/* Canvas for image and annotations */}
          {imageUrl && (
            <div className="relative border rounded-lg overflow-hidden">
              <canvas
                ref={canvasRef}
                className="max-w-full cursor-crosshair"
                onClick={handleCanvasClick}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onContextMenu={(e) => {
                  e.preventDefault();
                  handleCanvasClick(e as any);
                }}
              />
              <img
                ref={imageRef}
                src={imageUrl}
                className="hidden"
                alt="Source"
              />
            </div>
          )}

          {/* Options */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Advanced Options</Label>
              <Settings className="w-4 h-4 text-muted-foreground" />
            </div>
            
            {(mode === 'auto' || mode === 'roof') && (
              <>
                <div className="space-y-2">
                  <Label>Points Per Side: {options.pointsPerSide}</Label>
                  <Slider
                    value={[options.pointsPerSide || 32]}
                    onValueChange={([v]) => setOptions({ ...options, pointsPerSide: v })}
                    min={8}
                    max={128}
                    step={8}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>IoU Threshold: {options.predIouThresh}</Label>
                  <Slider
                    value={[options.predIouThresh || 0.88]}
                    onValueChange={([v]) => setOptions({ ...options, predIouThresh: v })}
                    min={0.5}
                    max={1}
                    step={0.01}
                  />
                </div>
              </>
            )}
            
            {(mode === 'points' || mode === 'box') && (
              <div className="flex items-center space-x-2">
                <Switch
                  checked={options.multimaskOutput}
                  onCheckedChange={(v) => setOptions({ ...options, multimaskOutput: v })}
                />
                <Label>Multiple Masks Output</Label>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={runSegmentation}
              disabled={isLoading || !imageUrl}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Wand2 className="w-4 h-4" />
                  Run Segmentation
                </>
              )}
            </Button>
            
            {(points.length > 0 || box) && (
              <Button
                variant="outline"
                onClick={clearAnnotations}
              >
                Clear Annotations
              </Button>
            )}
            
            {result && (
              <Button
                variant="outline"
                onClick={downloadResult}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Download Result
              </Button>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Results Display */}
          {result && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Segmentation Results</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {result.metadata && (
                  <div className="text-sm space-y-1">
                    <p><strong>Model:</strong> {result.metadata.model}</p>
                    <p><strong>Mode:</strong> {result.metadata.mode}</p>
                    <p><strong>Timestamp:</strong> {new Date(result.metadata.timestamp).toLocaleString()}</p>
                  </div>
                )}
                
                {result.roofAnalysis && (
                  <div className="text-sm space-y-1 border-t pt-2">
                    <p className="font-semibold">Roof Analysis:</p>
                    <p><strong>Total Segments:</strong> {result.roofAnalysis.totalSegments}</p>
                    <p><strong>Possible Panels:</strong> {result.roofAnalysis.possiblePanels.length}</p>
                    <p><strong>Estimated Area:</strong> {result.roofAnalysis.roofArea.estimatedM2} m²</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
};