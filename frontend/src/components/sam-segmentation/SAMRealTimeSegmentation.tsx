import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Upload, 
  Wand2, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>ader2, 
  <PERSON>ertCircle, 
  Wifi, 
  WifiOff,
  Server,
  Zap
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { samLocalService, Point } from '@/services/samLocalService';

export const SAMRealTimeSegmentation: React.FC = () => {
  const [image, setImage] = useState<File | null>(null);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string>('');
  const [serverStatus, setServerStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [imageReady, setImageReady] = useState(false);
  const [points, setPoints] = useState<Point[]>([]);
  const [currentMask, setCurrentMask] = useState<string | null>(null);
  const [maskScore, setMaskScore] = useState<number>(0);
  const [lastSegmentTime, setLastSegmentTime] = useState<number>(0);
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const maskCanvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check server status on mount
  useEffect(() => {
    checkServerStatus();
  }, []);

  const checkServerStatus = async () => {
    try {
      const health = await samLocalService.checkHealth();
      setServerStatus('online');
      console.log('SAM Server status:', health);
    } catch (error) {
      setServerStatus('offline');
      setError('SAM GPU Server non raggiungibile. Verifica che sia avviato.');
    }
  };

  // No WebSocket setup needed for the new simple API


  const segmentStartTime = useRef<number>(0);

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setImage(file);
    setPoints([]);
    setCurrentMask(null);
    setImageReady(true);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImageUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Image is ready for segmentation
    setIsLoading(false);
    setError('');
  };


  // Draw image on canvas
  useEffect(() => {
    if (imageUrl && canvasRef.current && imageRef.current) {
      const canvas = canvasRef.current;
      const maskCanvas = maskCanvasRef.current!;
      const ctx = canvas.getContext('2d');
      const img = imageRef.current;
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        maskCanvas.width = img.width;
        maskCanvas.height = img.height;
        
        ctx?.drawImage(img, 0, 0);
        drawPoints();
      };
    }
  }, [imageUrl]);

  // Draw points on canvas
  const drawPoints = useCallback(() => {
    if (!canvasRef.current || !imageRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Redraw image
    ctx.drawImage(imageRef.current, 0, 0);
    
    // Draw points
    points.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, 6, 0, 2 * Math.PI);
      ctx.fillStyle = point.label === 1 ? '#10b981' : '#ef4444';
      ctx.fill();
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // Draw + or - symbol
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 12px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(point.label === 1 ? '+' : '-', point.x, point.y);
    });
  }, [points]);

  useEffect(() => {
    drawPoints();
  }, [points, drawPoints]);

  // Handle canvas click
  const handleCanvasClick = async (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!image || !imageReady || isProcessing) return;
    
    const rect = canvasRef.current!.getBoundingClientRect();
    const x = Math.round(e.clientX - rect.left);
    const y = Math.round(e.clientY - rect.top);
    
    // Left click = foreground (1), right click = background (0)
    const label = e.button === 0 ? 1 : 0;
    
    const newPoint: Point = { x, y, label };
    const newPoints = [...points, newPoint];
    setPoints(newPoints);
    
    // Segment with new points
    setIsProcessing(true);
    segmentStartTime.current = performance.now();
    
    try {
      const result = await samLocalService.segmentWithPoints(image, newPoints);
      const endTime = performance.now();
      setLastSegmentTime(endTime - segmentStartTime.current);
      
      if (result.masks && result.masks.length > 0) {
        setCurrentMask(result.masks[0]);
        setMaskScore(result.scores?.[0] || 0);
        drawMask(result.masks[0]);
      }
      setIsProcessing(false);
    } catch (error) {
      setError('Errore nella segmentazione');
      setIsProcessing(false);
    }
  };

  // Draw mask
  const drawMask = (maskBase64: string) => {
    if (!maskCanvasRef.current) return;
    
    const ctx = maskCanvasRef.current.getContext('2d');
    if (!ctx) return;
    
    const img = new Image();
    img.onload = () => {
      ctx.clearRect(0, 0, maskCanvasRef.current!.width, maskCanvasRef.current!.height);
      
      // Draw mask with transparency
      ctx.globalAlpha = 0.5;
      ctx.fillStyle = '#3b82f6';
      ctx.fillRect(0, 0, maskCanvasRef.current!.width, maskCanvasRef.current!.height);
      
      ctx.globalCompositeOperation = 'destination-in';
      ctx.globalAlpha = 1.0;
      ctx.drawImage(img, 0, 0);
      
      ctx.globalCompositeOperation = 'source-over';
    };
    img.src = `data:image/png;base64,${maskBase64}`;
  };

  // Clear all
  const clearAll = () => {
    setPoints([]);
    setCurrentMask(null);
    
    if (maskCanvasRef.current) {
      const ctx = maskCanvasRef.current.getContext('2d');
      ctx?.clearRect(0, 0, maskCanvasRef.current.width, maskCanvasRef.current.height);
    }
    
    drawPoints();
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Wand2 className="w-6 h-6" />
              SAM Real-Time Segmentation (GPU Local)
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant={serverStatus === 'online' ? 'default' : 'destructive'}>
                <Server className="w-3 h-3 mr-1" />
                {serverStatus === 'checking' ? 'Checking...' : serverStatus}
              </Badge>
              <Badge variant={imageReady ? 'default' : 'outline'}>
                {imageReady ? <Wifi className="w-3 h-3 mr-1" /> : <WifiOff className="w-3 h-3 mr-1" />}
                Image Ready
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Performance Metrics */}
          {imageReady && (
            <div className="flex gap-4 text-sm">
              {lastSegmentTime > 0 && (
                <div className="flex items-center gap-1">
                  <MousePointer className="w-4 h-4 text-blue-500" />
                  <span>Ultimo click: {lastSegmentTime.toFixed(0)}ms</span>
                </div>
              )}
              {maskScore > 0 && (
                <div className="flex items-center gap-1">
                  <span>Score: {(maskScore * 100).toFixed(1)}%</span>
                </div>
              )}
            </div>
          )}

          {/* Upload */}
          <div className="space-y-2">
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={serverStatus !== 'online'}
              className="flex items-center gap-2"
            >
              <Upload className="w-4 h-4" />
              Carica Immagine
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
            {image && (
              <span className="text-sm text-muted-foreground">
                {image.name} • {imageReady ? 'Pronto per segmentazione' : 'Processing...'}
              </span>
            )}
          </div>

          {/* Instructions */}
          {imageReady && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Click sinistro</strong>: Aggiungi punto foreground (verde) • 
                <strong> Click destro</strong>: Aggiungi punto background (rosso)
              </AlertDescription>
            </Alert>
          )}

          {/* Canvas Container */}
          {imageUrl && (
            <div className="relative border rounded-lg overflow-hidden bg-gray-50">
              {/* Main Canvas */}
              <canvas
                ref={canvasRef}
                className="max-w-full cursor-crosshair relative z-10"
                onClick={handleCanvasClick}
                onContextMenu={(e) => {
                  e.preventDefault();
                  handleCanvasClick(e as any);
                }}
              />
              
              {/* Mask Canvas (overlay) */}
              <canvas
                ref={maskCanvasRef}
                className="absolute top-0 left-0 max-w-full pointer-events-none z-20"
              />
              
              {/* Hidden Image */}
              <img
                ref={imageRef}
                src={imageUrl}
                className="hidden"
                alt="Source"
              />
              
              {/* Loading Overlay */}
              {(isLoading || isProcessing) && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30">
                  <div className="bg-white rounded-lg p-4 flex items-center gap-2">
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>{isLoading ? 'Creazione embedding...' : 'Segmentazione...'}</span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          {imageReady && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={clearAll}
                disabled={points.length === 0}
              >
                Cancella Punti
              </Button>
              <Button
                variant="outline"
                onClick={checkServerStatus}
              >
                Check Server
              </Button>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};