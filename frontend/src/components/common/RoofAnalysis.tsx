import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '@/components/ui/progress';
import { analyzeRoofImage, type RoofAnalysisResult } from '@/services/roofAnalysisService';

interface RoofAnalysisProps {
  imageUrl: string;
  isLoading?: boolean;
}

const RoofAnalysis: React.FC<RoofAnalysisProps> = ({ imageUrl, isLoading = false }) => {
  const [result, setResult] = useState<RoofAnalysisResult | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const analyzeRoof = async () => {
      if (!imageUrl) return;
      
      setLoading(true);
      setError(null);
      
      try {
        // Utilizza il servizio per l'analisi del tetto
        const analysisResult = await analyzeRoofImage(imageUrl);
        setResult(analysisResult);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Errore durante l\'analisi del tetto');
      } finally {
        setLoading(false);
      }
    };
    
    analyzeRoof();
  }, [imageUrl]);

  // Funzione per determinare il colore della probabilità di amianto
  const getAmiantoColor = (probability: number): string => {
    if (probability < 0.3) return 'bg-green-500';
    if (probability < 0.7) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  // Funzione per formattare la percentuale
  const formatPercentage = (value: number): string => {
    return `${Math.round(value * 100)}%`;
  };

  if (isLoading || loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Analisi del tetto</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center p-6">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="mt-2 text-sm text-muted-foreground">Analisi in corso...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Analisi del tetto</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-red-500">
            <p>{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Analisi del tetto</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">Nessun dato disponibile</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Analisi del tetto</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium">Materiale</p>
            <p className="text-lg">{result.materiale}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Confidenza</p>
            <div className="flex items-center">
              <Progress value={result.confidenza * 100} className="h-2 w-full" />
              <span className="ml-2 text-sm">{formatPercentage(result.confidenza)}</span>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium">Pannelli solari</p>
            <Badge variant={result.pannelli_solari ? "default" : "outline"}>
              {result.pannelli_solari ? "Presenti" : "Assenti"}
            </Badge>
          </div>
          <div>
            <p className="text-sm font-medium">Probabilità amianto</p>
            <div className="flex items-center">
              <Progress 
                value={result.probabilita_amianto * 100} 
                className={`h-2 w-full ${getAmiantoColor(result.probabilita_amianto)}`} 
              />
              <span className="ml-2 text-sm">{formatPercentage(result.probabilita_amianto)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RoofAnalysis;