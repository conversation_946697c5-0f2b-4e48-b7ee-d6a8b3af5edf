import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Company } from '../../stores/companiesStore';
import { Building, Users, Calendar, Mail, Phone, Briefcase } from 'lucide-react';

interface CompanyAccordionProps {
  companies: Company[];
}

const CompanyAccordion: React.FC<CompanyAccordionProps> = ({ companies }) => {
  // Stato per tenere traccia degli elementi aperti
  const [openItems, setOpenItems] = useState<Record<string, boolean>>({});
  
  // Funzione per gestire il toggle di un elemento
  const toggleItem = (companyId: string) => {
    setOpenItems(prev => ({
      ...prev,
      [companyId]: !prev[companyId]
    }));
  };

  // Traduce la dimensione dell'azienda
  const translateSize = (size: string): string => {
    switch (size) {
      case 'small': return 'Piccola';
      case 'medium': return 'Media';
      case 'large': return 'Grande';
      default: return size;
    }
  };

  // Genera un codice fiscale/P.IVA fittizio per le aziende
  const generateVatNumber = (companyId: string): string => {
    // Usa l'ID dell'azienda per generare un numero costante ma unico
    const base = 'IT';
    const numbers = companyId
      .split('')
      .map(char => char.charCodeAt(0))
      .reduce((acc, val) => acc + val, 0);
    
    // Genera un numero di 11 cifre
    const vatNumber = String(numbers).padStart(11, '0').slice(0, 11);
    
    return `${base}${vatNumber}`;
  };

  // Genera un codice ATECO fittizio
  const generateAtecoCode = (category: string): string => {
    switch (category) {
      case 'Bonifica Amianto': return '39.00.01';
      case 'Installazione Pannelli Solari': return '43.21.01';
      case 'Ristrutturazione Tetti': return '43.91.00';
      default: return '00.00.00';
    }
  };

  // Genera un fatturato fittizio basato sul numero di dipendenti
  const generateRevenue = (employees: number): number => {
    const baseRevenue = 50000; // 50k per dipendente
    return employees * baseRevenue;
  };

  // Formatta il fatturato in Euro
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Genera un consumo energetico fittizio
  const generateEnergyConsumption = (employees: number, size: string): number => {
    const baseConsumption = 2000; // kWh base
    const sizeMultiplier = size === 'small' ? 1 : size === 'medium' ? 1.5 : 2;
    return Math.round(baseConsumption * employees * sizeMultiplier / 10);
  };

  return (
    <Card className="my-4">
      <CardHeader>
        <CardTitle className="text-lg">Aziende nell'Edificio</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="w-full space-y-2">
          {companies.map((company) => {
            const vatNumber = generateVatNumber(company.id);
            const atecoCode = generateAtecoCode(company.category);
            const revenue = generateRevenue(company.employees);
            const energyConsumption = generateEnergyConsumption(company.employees, company.size);
            const isOpen = !!openItems[company.id];
            
            return (
              <div key={company.id} className="border rounded-md overflow-hidden">
                {/* Header dell'accordion */}
                <div 
                  className="hover:bg-gray-50 px-4 py-3 cursor-pointer flex items-center justify-between"
                  onClick={() => toggleItem(company.id)}
                >
                  <div className="flex items-center text-left">
                    <Briefcase className="mr-2 h-5 w-5 text-gray-500" />
                    <div>
                      <div className="font-medium">{company.name}</div>
                      <div className="text-sm text-gray-500">{company.category}</div>
                    </div>
                  </div>
                  <div className={`transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}>
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M2 4L6 8L10 4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                </div>
                
                {/* Contenuto dell'accordion */}
                {isOpen && (
                  <div className="px-4 pt-2 pb-4 border-t">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <Building className="mr-2 h-4 w-4 text-gray-500" />
                          <span className="text-sm">{company.address}</span>
                        </div>
                        <div className="flex items-center">
                          <Users className="mr-2 h-4 w-4 text-gray-500" />
                          <span className="text-sm">
                            {company.employees} dipendenti ({translateSize(company.size)})
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                          <span className="text-sm">Fondata nel {company.founded}</span>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <Mail className="mr-2 h-4 w-4 text-gray-500" />
                          <span className="text-sm">{company.contact.email}</span>
                        </div>
                        <div className="flex items-center">
                          <Phone className="mr-2 h-4 w-4 text-gray-500" />
                          <span className="text-sm">{company.contact.phone}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <h4 className="text-sm font-medium mb-2">Informazioni Fiscali e Consumi</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="text-sm">
                            <span className="font-medium">P.IVA:</span> {vatNumber}
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Codice ATECO:</span> {atecoCode}
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="text-sm">
                            <span className="font-medium">Fatturato annuo:</span> {formatCurrency(revenue)}
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Consumo energetico:</span> {energyConsumption} kWh/mese
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default CompanyAccordion;