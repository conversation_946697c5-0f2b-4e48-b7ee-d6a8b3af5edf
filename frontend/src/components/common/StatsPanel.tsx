import React, { useMemo, useEffect, useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { useMapStore } from '../../stores/mapStore';
import { areaAnalyzerService } from '@/services/areaAnalyzerService';

interface StatCardProps {
  title: string;
  value: number | string;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  description,
  icon,
  className = '',
}) => (
  <Card className={`${className}`}>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon && <div className="h-4 w-4 text-muted-foreground">{icon}</div>}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
    </CardContent>
  </Card>
);

const StatsPanel: React.FC = () => {
  // Accesso agli store
  const { filteredBuildings, bounds } = useMapStore();
  const [realStats, setRealStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Formatta i numeri con separatore delle migliaia
  const formatNumber = (num: number): string => {
    return num.toLocaleString('it-IT');
  };

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const loadingRef = useRef(false);

  // Load real statistics from API with debounce
  useEffect(() => {
    if (!bounds) return;
    
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Set new timeout with debounce
    timeoutRef.current = setTimeout(async () => {
      if (loadingRef.current) return;
      
      loadingRef.current = true;
      setIsLoading(true);
      
      try {
        const boundsData = {
          swLng: bounds.southwest.lng,
          swLat: bounds.southwest.lat,
          neLng: bounds.northeast.lng,
          neLat: bounds.northeast.lat
        };
        const stats = await areaAnalyzerService.getAreaStatistics(boundsData);
        setRealStats(stats);
      } catch (error: any) {
        // Only log error if it's not an auth error
        if (error?.response?.status !== 401) {
          console.error('Error loading statistics:', error);
        }
      } finally {
        setIsLoading(false);
        loadingRef.current = false;
      }
    }, 1500); // Wait 1.5 seconds after map stops moving
    
    // Cleanup
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [bounds?.northeast.lat, bounds?.northeast.lng, bounds?.southwest.lat, bounds?.southwest.lng]);

  // Calcola statistiche basate sui dati reali o sui dati filtrati
  const osmStats = useMemo(() => {
    if (realStats) {
      // Use real statistics from API
      const totalOSMBuildings = realStats.totalBuildings || filteredBuildings.length;
      const totalBuildingArea = realStats.totalArea || 0;
      
      // Estimate asbestos buildings (10% if not provided)
      const asbestosBuildings = realStats.buildingTypes?.asbestos?.count || 
                                Math.round(totalOSMBuildings * 0.1);
      
      // Estimate solar roofs (15% if not provided)
      const solarRoofs = realStats.buildingTypes?.solar?.count || 
                        Math.round(totalOSMBuildings * 0.15);
      
      return {
        totalOSMBuildings,
        totalBuildingArea,
        asbestosBuildings,
        solarRoofs,
        mapsAvailable: realStats.mapsAvailable || 0
      };
    } else {
      // Fallback to filtered buildings
      const totalOSMBuildings = filteredBuildings.length;
      let totalBuildingArea = 0;
      filteredBuildings.forEach(building => {
        totalBuildingArea += building.properties.area || 0;
      });
      
      const asbestosBuildings = Math.round(totalOSMBuildings * 0.1);
      const solarRoofs = Math.round(totalOSMBuildings * 0.15);
      
      return {
        totalOSMBuildings,
        totalBuildingArea,
        asbestosBuildings,
        solarRoofs,
        mapsAvailable: 0
      };
    }
  }, [filteredBuildings, realStats]);

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard
        title="Edifici Totali"
        value={isLoading ? '...' : formatNumber(osmStats.totalOSMBuildings)}
        description={realStats?.message || 'Edifici nell\'area'}
        className="col-span-1"
      />
      
      <StatCard
        title="Tetti in Amianto"
        value={formatNumber(osmStats.asbestosBuildings)}
        description="10% del totale edifici"
        className="col-span-1"
      />
      
      <StatCard
        title="Potenziale Solare"
        value="15.0%"
        description="Percentuale tetti con potenziale"
        className="col-span-1"
      />

      {/* Nuove statistiche richieste */}

      <StatCard
        title="Area Totale"
        value={isLoading ? '...' : `${formatNumber(osmStats.totalBuildingArea)} m²`}
        description="Superficie totale edifici"
        className="col-span-1"
      />


      <StatCard
        title="Mappe Disponibili"
        value={isLoading ? '...' : formatNumber(osmStats.mapsAvailable)}
        description="Mappe scaricate per l'area"
        className="col-span-1"
      />
    </div>
  );
};

export default StatsPanel;