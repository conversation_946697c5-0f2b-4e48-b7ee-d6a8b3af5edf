import React from 'react';
import { Button } from '../ui/button';
import { FileDown } from 'lucide-react';
import { BuildingFeature } from '../../stores/mapStore';
import { Roof } from '../../stores/statsStore';
import { Company } from '../../stores/companiesStore';

interface PdfButtonProps {
  building: BuildingFeature;
  roof: Roof | null;
  companies: Company[];
}

const PdfButton: React.FC<PdfButtonProps> = ({ building, roof, companies }) => {
  // Funzione per generare il PDF
  const generatePdf = () => {
    // In un'implementazione reale, qui utilizzeremmo una libreria come jsPDF o pdfmake
    // per generare un PDF con i dati dell'edificio, del tetto e delle aziende.
    // Per questa demo, simuliamo la generazione del PDF con un alert.
    
    // Prepara un riepilogo dei dati per l'alert
    let message = `PDF generato per l'edificio: ${building.properties.name}\n`;
    message += `Indirizzo: ${building.properties.address}\n`;
    message += `Tipo: ${building.properties.type}, Anno: ${building.properties.year_built}\n\n`;
    
    if (roof) {
      message += `Tetto: ${roof.type}, Condizione: ${roof.condition}\n`;
      message += `Potenziale solare: ${(roof.solar_potential * 100).toFixed(1)}%\n\n`;
    }
    
    if (companies.length > 0) {
      message += `Aziende nell'edificio (${companies.length}):\n`;
      companies.forEach((company, index) => {
        message += `${index + 1}. ${company.name} - ${company.category}\n`;
      });
    }
    
    alert(message);
    
    // Esempio di come potrebbe essere implementato con jsPDF:
    /*
    import { jsPDF } from 'jspdf';
    import 'jspdf-autotable';

    const doc = new jsPDF();
    
    // Aggiungi titolo
    doc.setFontSize(20);
    doc.text(`Scheda Edificio: ${building.properties.name}`, 14, 22);
    
    // Aggiungi indirizzo
    doc.setFontSize(12);
    doc.text(`Indirizzo: ${building.properties.address}`, 14, 32);
    
    // Aggiungi dettagli edificio
    doc.setFontSize(16);
    doc.text('Dettagli Edificio', 14, 45);
    
    const buildingDetails = [
      ['Tipo', translateBuildingType(building.properties.type)],
      ['Anno di costruzione', building.properties.year_built.toString()],
      ['Piani', building.properties.floors.toString()],
      ['Superficie', `${building.properties.area} m²`],
    ];
    
    doc.autoTable({
      startY: 50,
      head: [['Proprietà', 'Valore']],
      body: buildingDetails,
    });
    
    // Aggiungi dettagli tetto se disponibili
    if (roof) {
      doc.setFontSize(16);
      doc.text('Dettagli Tetto', 14, doc.lastAutoTable.finalY + 15);
      
      const roofDetails = [
        ['Tipo', translateRoofType(roof.type)],
        ['Condizione', translateRoofCondition(roof.condition)],
        ['Anno installazione', roof.installation_year.toString()],
        ['Ultima ispezione', formatDate(roof.last_inspection)],
        ['Superficie', `${roof.area} m²`],
        ['Potenziale solare', `${(roof.solar_potential * 100).toFixed(1)}%`],
      ];
      
      doc.autoTable({
        startY: doc.lastAutoTable.finalY + 20,
        head: [['Proprietà', 'Valore']],
        body: roofDetails,
      });
    }
    
    // Aggiungi aziende
    if (companies.length > 0) {
      doc.setFontSize(16);
      doc.text('Aziende nell\'Edificio', 14, doc.lastAutoTable.finalY + 15);
      
      const companiesData = companies.map(company => [
        company.name,
        company.category,
        translateSize(company.size),
        company.employees.toString(),
        company.contact.email,
      ]);
      
      doc.autoTable({
        startY: doc.lastAutoTable.finalY + 20,
        head: [['Nome', 'Categoria', 'Dimensione', 'Dipendenti', 'Contatto']],
        body: companiesData,
      });
    }
    
    // Aggiungi mappa statica
    // In un'implementazione reale, qui aggiungeremmo un'immagine della mappa
    // utilizzando l'API di Google Maps Static Maps o simili
    
    // Salva il PDF
    doc.save(`edificio_${building.id}.pdf`);
    */
  };

  return (
    <Button onClick={generatePdf} className="flex items-center">
      <FileDown className="mr-2 h-4 w-4" />
      Genera PDF
    </Button>
  );
};

export default PdfButton;