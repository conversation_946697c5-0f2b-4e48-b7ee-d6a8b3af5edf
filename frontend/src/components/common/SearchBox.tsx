import React, { useEffect, useRef, useState } from 'react';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Search } from 'lucide-react';
import { useMapStore } from '../../stores/mapStore';

interface SearchBoxProps {
  placeholder?: string;
  className?: string;
}

const SearchBox: React.FC<SearchBoxProps> = ({
  placeholder = 'Cerca un indirizzo...',
  className = '',
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [predictions, setPredictions] = useState<google.maps.places.AutocompletePrediction[]>([]);
  const [showPredictions, setShowPredictions] = useState(false);
  
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);
  const searchBoxRef = useRef<HTMLDivElement>(null);
  
  const { setCenter, setZoom } = useMapStore();

  // Inizializza i servizi di Google Places
  useEffect(() => {
    if (window.google && window.google.maps && window.google.maps.places) {
      autocompleteService.current = new google.maps.places.AutocompleteService();
      
      // Crea un elemento div temporaneo per il PlacesService
      const tempDiv = document.createElement('div');
      placesService.current = new google.maps.places.PlacesService(tempDiv);
    }
  }, []);

  // Gestisce il click all'esterno del componente per chiudere le previsioni
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchBoxRef.current && !searchBoxRef.current.contains(event.target as Node)) {
        setShowPredictions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Ottiene le previsioni di autocompletamento
  const getAutocomplete = (input: string) => {
    if (!autocompleteService.current || input.length < 3) {
      setPredictions([]);
      return;
    }

    const request = {
      input,
      componentRestrictions: { country: 'it' }, // Limita i risultati all'Italia
      types: ['geocode', 'establishment']
    };

    autocompleteService.current.getPlacePredictions(
      request,
      (predictions, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
          setPredictions(predictions);
          setShowPredictions(true);
        } else {
          setPredictions([]);
          setShowPredictions(false);
        }
      }
    );
  };

  // Gestisce il cambio del valore di ricerca
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    getAutocomplete(value);
  };

  // Gestisce la selezione di una previsione
  const handlePredictionSelect = (placeId: string, description: string) => {
    setSearchValue(description);
    setShowPredictions(false);

    if (!placesService.current) return;

    const request = {
      placeId,
      fields: ['geometry']
    };

    placesService.current.getDetails(request, (place, status) => {
      if (status === google.maps.places.PlacesServiceStatus.OK && place && place.geometry && place.geometry.location) {
        // Aggiorna il centro della mappa
        setCenter({
          lat: place.geometry.location.lat(),
          lng: place.geometry.location.lng()
        });
        
        // Imposta lo zoom appropriato
        setZoom(16);
      }
    });
  };

  // Gestisce la ricerca
  const handleSearch = () => {
    if (searchValue.trim() && predictions.length > 0) {
      handlePredictionSelect(predictions[0].place_id, predictions[0].description);
    }
  };

  // Gestisce il tasto Invio
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div ref={searchBoxRef} className={`relative ${className}`}>
      <div className="flex">
        <Input
          type="text"
          placeholder={placeholder}
          value={searchValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          className="w-full"
        />
        <Button 
          variant="ghost" 
          size="icon" 
          className="ml-2" 
          onClick={handleSearch}
        >
          <Search className="h-4 w-4" />
        </Button>
      </div>

      {showPredictions && predictions.length > 0 && (
        <div className="absolute z-50 mt-1 w-full bg-white rounded-md shadow-lg max-h-60 overflow-auto">
          <ul className="py-1">
            {predictions.map((prediction) => (
              <li
                key={prediction.place_id}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={() => handlePredictionSelect(prediction.place_id, prediction.description)}
              >
                {prediction.description}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default SearchBox;