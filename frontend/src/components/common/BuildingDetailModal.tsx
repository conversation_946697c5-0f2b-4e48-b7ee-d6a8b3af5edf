import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { useMapStore, type BuildingFeature } from '../../stores/mapStore';
import { Roof } from '../../stores/statsStore';
import { Company } from '../../stores/companiesStore';
import RoofHistoryTimeline from './RoofHistoryTimeline';
import CompanyAccordion from './CompanyAccordion';
import PdfButton from './PdfButton';
import { X } from 'lucide-react';

const BuildingDetailModal: React.FC = () => {
  // Stato locale
  const [building, setBuilding] = useState<BuildingFeature | null>(null);
  const [roof, setRoof] = useState<Roof | null>(null);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Accesso allo store della mappa
  const { selectedBuildingId, selectBuilding } = useMapStore();

  // Carica i dati dell'edificio selezionato
  useEffect(() => {
    const loadBuildingData = async () => {
      if (!selectedBuildingId) {
        setBuilding(null);
        setRoof(null);
        setCompanies([]);
        return;
      }

      setIsLoading(true);

      try {
        // Carica i dati dell'edificio
        const buildingResponse = await fetch('/src/dummy-data/footprints.geojson');
        const buildingData = await buildingResponse.json();
        const selectedBuilding = buildingData.features.find(
          (feature: BuildingFeature) => feature.id === selectedBuildingId
        );
        setBuilding(selectedBuilding || null);

        // Carica i dati del tetto
        const roofResponse = await fetch('/src/dummy-data/roofsClass.json');
        const roofData: Roof[] = await roofResponse.json();
        const selectedRoof = roofData.find(
          (r) => r.building_id === selectedBuildingId
        );
        setRoof(selectedRoof || null);

        // Carica i dati delle aziende
        const companiesResponse = await fetch('/src/dummy-data/companies.json');
        const companiesData: Company[] = await companiesResponse.json();
        const buildingCompanies = companiesData.filter((company) =>
          company.buildings.includes(selectedBuildingId)
        );
        setCompanies(buildingCompanies);
      } catch (error) {
        console.error('Errore nel caricamento dei dati dell\'edificio:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadBuildingData();
  }, [selectedBuildingId]);

  // Gestisce la chiusura del modale
  const handleClose = () => {
    selectBuilding(null);
  };

  // Formatta la data
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // Traduce il tipo di edificio
  const translateBuildingType = (type: string): string => {
    switch (type) {
      case 'residential': return 'Residenziale';
      case 'commercial': return 'Commerciale';
      case 'industrial': return 'Industriale';
      default: return type;
    }
  };

  // Traduce il tipo di tetto
  const translateRoofType = (type: string): string => {
    switch (type) {
      case 'normal': return 'Normale';
      case 'asbestos': return 'Amianto';
      case 'solar': return 'Solare';
      default: return type;
    }
  };

  // Traduce la condizione del tetto
  const translateRoofCondition = (condition: string): string => {
    switch (condition) {
      case 'excellent': return 'Eccellente';
      case 'good': return 'Buona';
      case 'fair': return 'Discreta';
      case 'poor': return 'Scarsa';
      default: return condition;
    }
  };

  return (
    <Dialog open={!!selectedBuildingId} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <p>Caricamento dati...</p>
          </div>
        ) : building ? (
          <>
            <DialogHeader>
              <div className="flex justify-between items-center">
                <DialogTitle className="text-xl">{building.properties.name}</DialogTitle>
                <Button variant="ghost" size="icon" onClick={handleClose}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <DialogDescription>{building.properties.address}</DialogDescription>
            </DialogHeader>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-4">
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Dettagli Edificio</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-sm font-medium">Tipo:</div>
                  <div className="text-sm">{translateBuildingType(building.properties.type)}</div>
                  
                  <div className="text-sm font-medium">Anno di costruzione:</div>
                  <div className="text-sm">{building.properties.year_built}</div>
                  
                  <div className="text-sm font-medium">Piani:</div>
                  <div className="text-sm">{building.properties.floors}</div>
                  
                  <div className="text-sm font-medium">Superficie:</div>
                  <div className="text-sm">{building.properties.area} m²</div>
                </div>
              </div>

              {roof && (
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Dettagli Tetto</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Tipo:</div>
                    <div className="text-sm">{translateRoofType(roof.type)}</div>
                    
                    <div className="text-sm font-medium">Condizione:</div>
                    <div className="text-sm">{translateRoofCondition(roof.condition)}</div>
                    
                    <div className="text-sm font-medium">Anno installazione:</div>
                    <div className="text-sm">{roof.installation_year}</div>
                    
                    <div className="text-sm font-medium">Ultima ispezione:</div>
                    <div className="text-sm">{formatDate(roof.last_inspection)}</div>
                    
                    <div className="text-sm font-medium">Superficie:</div>
                    <div className="text-sm">{roof.area} m²</div>
                    
                    <div className="text-sm font-medium">Potenziale solare:</div>
                    <div className="text-sm">{(roof.solar_potential * 100).toFixed(1)}%</div>
                  </div>
                </div>
              )}
            </div>

            {roof && <RoofHistoryTimeline roof={roof} />}
            
            {companies.length > 0 && <CompanyAccordion companies={companies} />}

            <DialogFooter className="mt-4">
              <PdfButton building={building} roof={roof} companies={companies} />
            </DialogFooter>
          </>
        ) : (
          <div className="flex justify-center items-center h-40">
            <p>Nessun edificio selezionato</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default BuildingDetailModal;