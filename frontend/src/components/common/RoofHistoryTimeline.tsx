import React from 'react';
import { Roof } from '../../stores/statsStore';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { CalendarIcon, ClipboardCheck, Construction, AlertTriangle, SunIcon } from 'lucide-react';

interface RoofHistoryTimelineProps {
  roof: Roof;
}

interface TimelineEvent {
  id: string;
  date: Date;
  title: string;
  description: string;
  icon: React.ReactNode;
  type: 'installation' | 'inspection' | 'maintenance' | 'warning' | 'solar';
}

const RoofHistoryTimeline: React.FC<RoofHistoryTimelineProps> = ({ roof }) => {
  // Genera eventi di timeline basati sui dati del tetto
  const generateTimelineEvents = (roof: Roof): TimelineEvent[] => {
    const events: TimelineEvent[] = [];

    // Evento di installazione
    events.push({
      id: `installation-${roof.id}`,
      date: new Date(roof.installation_year, 0, 1), // 1 gennaio dell'anno di installazione
      title: 'Installazione tetto',
      description: `Installazione del tetto di tipo ${translateRoofType(roof.type)}`,
      icon: <Construction className="h-5 w-5" />,
      type: 'installation'
    });

    // Evento dell'ultima ispezione
    events.push({
      id: `inspection-${roof.id}`,
      date: new Date(roof.last_inspection),
      title: 'Ultima ispezione',
      description: `Ispezione del tetto. Condizione: ${translateRoofCondition(roof.condition)}`,
      icon: <ClipboardCheck className="h-5 w-5" />,
      type: 'inspection'
    });

    // Se è un tetto in amianto, aggiungi un avviso
    if (roof.type === 'asbestos') {
      events.push({
        id: `warning-${roof.id}`,
        date: new Date(), // Data corrente
        title: 'Attenzione: Amianto',
        description: 'Questo tetto contiene amianto e richiede monitoraggio regolare',
        icon: <AlertTriangle className="h-5 w-5" />,
        type: 'warning'
      });
    }

    // Se è un tetto solare, aggiungi info sul potenziale
    if (roof.type === 'solar') {
      events.push({
        id: `solar-${roof.id}`,
        date: new Date(), // Data corrente
        title: 'Impianto solare attivo',
        description: `Potenziale solare: ${(roof.solar_potential * 100).toFixed(1)}%`,
        icon: <SunIcon className="h-5 w-5" />,
        type: 'solar'
      });
    }

    // Aggiungi eventi di manutenzione simulati
    const currentYear = new Date().getFullYear();
    if (roof.installation_year < currentYear - 5) {
      events.push({
        id: `maintenance-${roof.id}`,
        date: new Date(currentYear - 3, 5, 15), // 15 giugno di 3 anni fa
        title: 'Manutenzione ordinaria',
        description: 'Intervento di manutenzione ordinaria sul tetto',
        icon: <Construction className="h-5 w-5" />,
        type: 'maintenance'
      });
    }

    // Ordina gli eventi per data (dal più recente al più vecchio)
    return events.sort((a, b) => b.date.getTime() - a.date.getTime());
  };

  // Traduce il tipo di tetto
  const translateRoofType = (type: string): string => {
    switch (type) {
      case 'normal': return 'Normale';
      case 'asbestos': return 'Amianto';
      case 'solar': return 'Solare';
      default: return type;
    }
  };

  // Traduce la condizione del tetto
  const translateRoofCondition = (condition: string): string => {
    switch (condition) {
      case 'excellent': return 'Eccellente';
      case 'good': return 'Buona';
      case 'fair': return 'Discreta';
      case 'poor': return 'Scarsa';
      default: return condition;
    }
  };

  // Formatta la data
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // Ottiene il colore in base al tipo di evento
  const getEventColor = (type: string): string => {
    switch (type) {
      case 'installation': return 'bg-blue-100 border-blue-500 text-blue-700';
      case 'inspection': return 'bg-green-100 border-green-500 text-green-700';
      case 'maintenance': return 'bg-yellow-100 border-yellow-500 text-yellow-700';
      case 'warning': return 'bg-red-100 border-red-500 text-red-700';
      case 'solar': return 'bg-purple-100 border-purple-500 text-purple-700';
      default: return 'bg-gray-100 border-gray-500 text-gray-700';
    }
  };

  // Genera gli eventi della timeline
  const timelineEvents = generateTimelineEvents(roof);

  return (
    <Card className="my-4">
      <CardHeader>
        <CardTitle className="text-lg">Cronologia del Tetto</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          {/* Linea verticale della timeline */}
          <div className="absolute left-9 top-0 h-full w-0.5 bg-gray-200"></div>
          
          <div className="space-y-6">
            {timelineEvents.map((event) => (
              <div key={event.id} className="relative flex items-start">
                {/* Icona dell'evento */}
                <div className={`absolute left-0 flex h-9 w-9 items-center justify-center rounded-full border-2 ${getEventColor(event.type)}`}>
                  {event.icon}
                </div>
                
                {/* Contenuto dell'evento */}
                <div className="ml-12">
                  <div className="flex items-center text-sm">
                    <CalendarIcon className="mr-1 h-4 w-4 text-gray-500" />
                    <span className="text-gray-500">{formatDate(event.date)}</span>
                  </div>
                  <h4 className="font-medium">{event.title}</h4>
                  <p className="text-sm text-gray-600">{event.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RoofHistoryTimeline;