import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Loader2, ExternalLink } from 'lucide-react';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { searchCompanyOnGoogleMaps, formatCoordinates, type GoogleMapsSearchResult } from '@/services/serpApiService';
import { captureAndAnalyzeRoof } from '@/services/roofAnalysisService';
import { type Company } from '@/stores/companiesStore';
import CompanyMapView from './CompanyMapView';
import RoofAnalysis from './RoofAnalysis';

interface CompanySearchModalProps {
  company: Company | null;
  isOpen: boolean;
  onClose: () => void;
}

const CompanySearchModal: React.FC<CompanySearchModalProps> = ({
  company,
  isOpen,
  onClose,
}) => {
  const [searchResult, setSearchResult] = useState<GoogleMapsSearchResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('info');
  const [roofImageUrl, setRoofImageUrl] = useState<string | null>(null);
  const [roofAnalysisLoading, setRoofAnalysisLoading] = useState(false);
  const [roofAnalysisError, setRoofAnalysisError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCompanyInfo = async () => {
      if (!company || !isOpen) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const location = formatCoordinates(company.location.lat, company.location.lng);
        const result = await searchCompanyOnGoogleMaps(company.name, location);
        
        if (result.error) {
          setError(result.error);
        } else {
          setSearchResult(result);
        }
      } catch (_error) {
        setError('Si è verificato un errore durante la ricerca. Riprova più tardi.');
      } finally {
        setLoading(false);
      }
    };

    fetchCompanyInfo();
  }, [company, isOpen]);

  // Effetto per caricare l'analisi del tetto quando si passa alla tab "tetto"
  useEffect(() => {
    const loadRoofAnalysis = async () => {
      if (!company || activeTab !== 'tetto' || roofImageUrl) return;
      
      setRoofAnalysisLoading(true);
      setRoofAnalysisError(null);
      
      try {
        // Cattura e analizza l'immagine del tetto
        const { imageUrl } = await captureAndAnalyzeRoof(
          company.location.lat,
          company.location.lng,
          19 // Zoom ottimale per vedere i tetti
        );
        
        setRoofImageUrl(imageUrl);
      } catch (err) {
        setRoofAnalysisError(err instanceof Error ? err.message : 'Errore durante l\'analisi del tetto');
      } finally {
        setRoofAnalysisLoading(false);
      }
    };
    
    loadRoofAnalysis();
  }, [company, activeTab, roofImageUrl]);

  const handleOpenGoogleMaps = () => {
    if (!company) return;
    
    const placeId = searchResult?.place_results?.place_id || '';
    const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(company.name)}&query_place_id=${placeId}`;
    window.open(url, '_blank');
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{company?.name || 'Dettagli azienda'}</DialogTitle>
          <DialogDescription>
            Informazioni dettagliate sull'azienda
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="info">Informazioni</TabsTrigger>
            <TabsTrigger value="tetto">Analisi Tetto</TabsTrigger>
          </TabsList>
          
          <TabsContent value="info" className="py-2">
            {loading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Ricerca informazioni...</span>
              </div>
            ) : error ? (
              <div className="text-center text-red-500 p-4">
                <p>{error}</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={onClose}
                >
                  Chiudi
                </Button>
              </div>
            ) : searchResult?.place_results ? (
              <div className="space-y-4">
                <div className="border rounded-md p-4">
                  <div className="flex items-start space-x-4">
                    {searchResult.place_results.thumbnail && (
                      <img
                        src={searchResult.place_results.thumbnail}
                        alt={searchResult.place_results.title}
                        className="w-24 h-24 object-cover rounded-md"
                      />
                    )}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold">{searchResult.place_results.title}</h3>
                      {searchResult.place_results.address && (
                        <p className="text-sm text-gray-500">{searchResult.place_results.address}</p>
                      )}
                      {searchResult.place_results.rating && (
                        <div className="flex items-center mt-1">
                          <span className="text-yellow-500">★</span>
                          <span className="ml-1">{searchResult.place_results.rating}</span>
                          {searchResult.place_results.reviews && (
                            <span className="text-sm text-gray-500 ml-1">
                              ({searchResult.place_results.reviews} recensioni)
                            </span>
                          )}
                        </div>
                      )}
                      {searchResult.place_results.type && (
                        <div className="mt-1">
                          {searchResult.place_results.type.map((type: string, idx: number) => (
                            <p key={idx} className="text-sm text-gray-500">{type}</p>
                          ))}
                        </div>
                      )}
                      {searchResult.place_results.phone && (
                        <p className="text-sm mt-1">{searchResult.place_results.phone}</p>
                      )}
                      {searchResult.place_results.open_state && (
                        <p className="text-sm mt-1">{searchResult.place_results.open_state}</p>
                      )}
                      {searchResult.place_results.website && (
                        <a
                          href={searchResult.place_results.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:underline flex items-center mt-2"
                        >
                          Visita il sito web
                          <ExternalLink className="ml-1 h-4 w-4" />
                        </a>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-2 mt-4">
                  <Button variant="outline" onClick={onClose}>
                    Chiudi
                  </Button>
                  <Button onClick={handleOpenGoogleMaps}>
                    Apri in Google Maps
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center p-4">
                <p>Nessun risultato trovato per questa azienda.</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={onClose}
                >
                  Chiudi
                </Button>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="tetto" className="py-2">
            <div className="space-y-4">
              {company && (
                <CompanyMapView
                  location={company.location}
                  name={company.name}
                />
              )}
              
              {roofAnalysisLoading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Analisi del tetto in corso...</span>
                </div>
              ) : roofAnalysisError ? (
                <div className="text-center text-red-500 p-4">
                  <p>{roofAnalysisError}</p>
                </div>
              ) : roofImageUrl ? (
                <div className="space-y-4">
                  <div className="border rounded-md p-4">
                    <h3 className="text-lg font-semibold mb-2">Immagine del tetto</h3>
                    <img
                      src={roofImageUrl}
                      alt="Tetto dell'edificio"
                      className="w-full h-auto rounded-md"
                    />
                  </div>
                  
                  <RoofAnalysis imageUrl={roofImageUrl} />
                </div>
              ) : (
                <div className="text-center p-4">
                  <p>Nessuna immagine del tetto disponibile.</p>
                </div>
              )}
              
              <div className="flex justify-end space-x-2 mt-4">
                <Button variant="outline" onClick={onClose}>
                  Chiudi
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default CompanySearchModal;