import React, { useState, useEffect } from 'react';
import CompanySearchModal from './CompanySearchModal';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import { Input } from '../ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Button } from '../ui/button';
import { useCompaniesStore, type Company } from '../../stores/companiesStore';
import { useMapStore } from '../../stores/mapStore';
import { ArrowUpDown, Search, Loader2 } from 'lucide-react';

type SortField = 'name' | 'category' | 'size' | 'employees' | 'founded';
type SortDirection = 'asc' | 'desc';

const CompanyList: React.FC = () => {
  // Accesso agli store
  const { filteredCompanies, setFilters, applyFilters, loading, showCompanies } = useCompaniesStore();
  const { selectCompany, setCenter } = useMapStore();

  // Stato locale per la ricerca e l'ordinamento
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<SortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [displayedCompanies, setDisplayedCompanies] = useState<Company[]>([]);
  
  // Stato per il modale di ricerca
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);

  // Estrai le categorie uniche dalle aziende
  const categories = React.useMemo(() => {
    const uniqueCategories = new Set<string>();
    filteredCompanies.forEach(company => uniqueCategories.add(company.category));
    return Array.from(uniqueCategories);
  }, [filteredCompanies]);

  // Applica i filtri quando cambiano i valori
  useEffect(() => {
    setFilters({
      name: searchTerm || null,
      category: selectedCategory ? [selectedCategory] : null,
      size: selectedSize ? [selectedSize as 'small' | 'medium' | 'large'] : null,
    });
    applyFilters();
  }, [searchTerm, selectedCategory, selectedSize, setFilters, applyFilters]);

  // Ordina e filtra le aziende
  useEffect(() => {
    const sorted = [...filteredCompanies];
    // Definisci sizeOrder fuori dallo switch
    const sizeOrder: Record<string, number> = { small: 1, medium: 2, large: 3 };

    // Ordinamento
    sorted.sort((a, b) => {
      let valueA: string | number = '';
      let valueB: string | number = '';

      switch (sortField) {
        case 'name':
          valueA = a.name;
          valueB = b.name;
          break;
        case 'category':
          valueA = a.category;
          valueB = b.category;
          break;
        case 'size':
          // Converti size in valore numerico per l'ordinamento
          valueA = sizeOrder[a.size];
          valueB = sizeOrder[b.size];
          break;
        case 'employees':
          valueA = a.employees;
          valueB = b.employees;
          break;
        case 'founded':
          valueA = a.founded;
          valueB = b.founded;
          break;
      }

      if (valueA < valueB) return sortDirection === 'asc' ? -1 : 1;
      if (valueA > valueB) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    setDisplayedCompanies(sorted);
  }, [filteredCompanies, sortField, sortDirection]);

  // Gestisce il cambio dell'ordinamento
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Gestisce il click su una riga
  const handleRowClick = (company: Company) => {
    selectCompany(company.id);
    setCenter(company.location);
  };
  
  // Gestisce il click sul nome dell'azienda
  const handleCompanyNameClick = (e: React.MouseEvent, company: Company) => {
    e.stopPropagation(); // Previene il trigger del click sulla riga
    setSelectedCompany(company);
    setIsModalOpen(true);
  };


  // Se le aziende non devono essere visualizzate, mostriamo un messaggio
  if (!showCompanies) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-8 text-center">
        <p className="text-muted-foreground mb-4">
          Clicca sul pulsante "Rileva aziende nell'area" sulla mappa per visualizzare le aziende presenti nell'area corrente.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
        <div className="flex-1 relative">
          <Input
            placeholder="Cerca azienda..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-8"
          />
          <Search className="h-4 w-4 absolute left-2 top-3 text-gray-400" />
        </div>
        
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="Categoria" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tutte le categorie</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={selectedSize} onValueChange={setSelectedSize}>
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="Dimensione" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tutte le dimensioni</SelectItem>
            <SelectItem value="small">Piccola</SelectItem>
            <SelectItem value="medium">Media</SelectItem>
            <SelectItem value="large">Grande</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {loading ? (
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Caricamento aziende...</span>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
            <TableRow>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('name')}
                  className="flex items-center"
                >
                  Nome Azienda
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('founded')}
                  className="flex items-center"
                >
                  Anno
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayedCompanies.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center">
                  Nessuna azienda trovata
                </TableCell>
              </TableRow>
            ) : (
              displayedCompanies.map((company) => (
                <TableRow
                  key={company.id}
                  onClick={() => handleRowClick(company)}
                  className="cursor-pointer hover:bg-muted"
                >
                  <TableCell>
                    <span
                      className="font-medium text-blue-600 hover:underline cursor-pointer"
                      onClick={(e) => handleCompanyNameClick(e, company)}
                    >
                      {company.name}
                    </span>
                  </TableCell>
                  <TableCell>{company.founded}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
          </Table>
        </div>
      )}
      
      {/* Modale per la ricerca dell'azienda */}
      <CompanySearchModal
        company={selectedCompany}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
};

export default CompanyList;