import React, { useState, useEffect } from 'react';
import { GoogleMap, useJsApi<PERSON><PERSON><PERSON>, Marker, Libraries } from '@react-google-maps/api';
import { Loader2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import type { LatLngLiteral } from '@/stores/mapStore';

// Chiave API Google Maps (in un'applicazione reale, questa dovrebbe essere in un file .env)
const GOOGLE_MAPS_API_KEY = 'AIzaSyBxpMSp5E3JtWbe7TsPDm7mHR6u2O_gXYY';

// Definiamo le librerie come costante statica per evitare ricaricamenti non intenzionali
const GOOGLE_MAPS_LIBRARIES: Libraries = ['places', 'geometry'];

// Stile della mappa
const containerStyle = {
  width: '100%',
  height: '300px'
};

// Opzioni della mappa
const defaultOptions = {
  disableDefaultUI: false,
  zoomControl: true,
  mapTypeControl: false,
  scaleControl: true,
  streetViewControl: false,
  rotateControl: false,
  clickableIcons: false,
  keyboardShortcuts: true,
  scrollwheel: true,
  disableDoubleClickZoom: false,
  fullscreenControl: false,
};

interface CompanyMapViewProps {
  location: LatLngLiteral;
  name: string;
  onMapLoaded?: (map: google.maps.Map) => void;
}

const CompanyMapView: React.FC<CompanyMapViewProps> = ({ 
  location, 
  name,
  onMapLoaded 
}) => {
  const [map, setMap] = useState<google.maps.Map | null>(null);
  
  // Caricamento dell'API Google Maps
  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: GOOGLE_MAPS_API_KEY,
    libraries: GOOGLE_MAPS_LIBRARIES
  });

  // Callback quando la mappa viene caricata
  const onLoad = React.useCallback((map: google.maps.Map) => {
    setMap(map);
    if (onMapLoaded) {
      onMapLoaded(map);
    }
  }, [onMapLoaded]);

  // Callback quando la mappa viene smontata
  const onUnmount = React.useCallback(() => {
    setMap(null);
  }, []);

  // Effetto per centrare la mappa quando cambiano le coordinate
  useEffect(() => {
    if (map && location) {
      map.panTo(location);
    }
  }, [map, location]);

  if (!isLoaded) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Visualizzazione edificio</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Caricamento mappa...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Visualizzazione edificio</CardTitle>
      </CardHeader>
      <CardContent>
        <GoogleMap
          mapContainerStyle={containerStyle}
          center={location}
          zoom={18}
          onLoad={onLoad}
          onUnmount={onUnmount}
          options={{
            ...defaultOptions,
            mapTypeId: google.maps.MapTypeId.SATELLITE
          }}
        >
          <Marker
            position={location}
            title={name}
          />
        </GoogleMap>
      </CardContent>
    </Card>
  );
};

export default CompanyMapView;