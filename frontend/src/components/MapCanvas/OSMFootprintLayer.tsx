import React, { useCallback, useMemo } from 'react';

// Estendi l'interfaccia Window per i nostri timeout
declare global {
  interface Window {
    buildingSelectionTimeout?: number;
  }
}
import { Polygon } from '@react-google-maps/api';
import { useMapStore } from '@/stores/mapStore';

// Stili per i poligoni in base al tipo di edificio
const buildingStyles = {
  residential: {
    fillColor: '#4285F4',
    fillOpacity: 0.3,
    strokeColor: '#4285F4',
    strokeOpacity: 0.8,
    strokeWeight: 1,
  },
  commercial: {
    fillColor: '#34A853',
    fillOpacity: 0.3,
    strokeColor: '#34A853',
    strokeOpacity: 0.8,
    strokeWeight: 1,
  },
  industrial: {
    fillColor: '#FBBC05',
    fillOpacity: 0.3,
    strokeColor: '#FBBC05',
    strokeOpacity: 0.8,
    strokeWeight: 1,
  },
};

// Stili per i poligoni selezionati
const selectedStyle = {
  fillOpacity: 0.5,
  strokeOpacity: 1,
  strokeWeight: 2,
};

const OSMFootprintLayer: React.FC = () => {
  // Accesso allo store della mappa
  const {
    filteredBuildings,
    selectedBuildingId,
    selectBuilding
  } = useMapStore();

  // Gestisce il click su un poligono
  const handlePolygonClick = useCallback((buildingId: string) => {
    // Preveniamo selezioni multiple ravvicinate
    if (window.buildingSelectionTimeout) {
      clearTimeout(window.buildingSelectionTimeout);
    }
    
    window.buildingSelectionTimeout = setTimeout(() => {
      selectBuilding(buildingId);
    }, 50) as unknown as number;
  }, [selectBuilding]);

  // Converte le coordinate GeoJSON in coordinate LatLng per Google Maps
  const convertCoordinates = useCallback((coordinates: number[][][]): google.maps.LatLngLiteral[] => {
    return coordinates[0].map(coord => ({
      lat: coord[1],
      lng: coord[0]
    }));
  }, []);
  
  // Memoizziamo la lista degli edifici filtrati per evitare ricalcoli inutili
  const memoizedFilteredBuildings = useMemo(() => filteredBuildings, [filteredBuildings]);

  return (
    <>
      {memoizedFilteredBuildings.map((building) => {
        const isSelected = building.id === selectedBuildingId;
        const buildingType = building.properties.type as 'residential' | 'commercial' | 'industrial';
        const baseStyle = buildingStyles[buildingType];
        
        // Combina gli stili di base con gli stili di selezione se l'edificio è selezionato
        const polygonOptions = {
          ...baseStyle,
          ...(isSelected ? selectedStyle : {})
        };

        return (
          <Polygon
            key={building.id}
            paths={convertCoordinates(building.geometry.coordinates as number[][][])}
            onClick={() => handlePolygonClick(building.id)}
            options={{
              ...polygonOptions,
              zIndex: isSelected ? 2 : 1
            }}
          />
        );
      })}
    </>
  );
};

export default OSMFootprintLayer;