import React, { useCallback, useEffect, useState } from 'react';
import { Polygon } from '@react-google-maps/api';
import { useMapStore } from '@/stores/mapStore';
import type { BuildingFeature } from '@/stores/mapStore';

// Stile per i contorni degli edifici di OpenStreetMap
const osmBuildingStyle = {
  fillColor: '#FF0000',
  fillOpacity: 0.3,
  strokeColor: '#FF0000',
  strokeOpacity: 0.8,
  strokeWeight: 1,
};

// Interfaccia per i dati di OpenStreetMap
interface OSMNode {
  id: number;
  lat: number;
  lon: number;
}

interface OSMWay {
  id: number;
  nodes: number[];
  tags: Record<string, string>;
}

interface OSMRelation {
  id: number;
  members: Array<{
    type: string;
    ref: number;
    role: string;
  }>;
  tags: Record<string, string>;
}

interface OSMResponse {
  elements: Array<OSMNode | OSMWay | OSMRelation>;
}

const OSMBuildingsLayer: React.FC = () => {
  // Stato locale per tracciare se il componente è montato
  const [isMounted, setIsMounted] = useState(false);
  
  // Stato locale per i buildings da renderizzare
  const [buildings, setBuildings] = useState<BuildingFeature[]>([]);
  
  // Stato per tracciare se è in corso un caricamento
  const [isLoading, setIsLoading] = useState(false);
  
  // Accesso allo store della mappa
  const {
    filters,
    isMapLoaded,
    bounds
  } = useMapStore();

  // Aggiorniamo lo stato locale solo quando il componente è montato
  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Funzione per convertire i dati di OpenStreetMap in GeoJSON
  const convertOSMToGeoJSON = useCallback((data: OSMResponse): BuildingFeature[] => {
    const nodes: Record<number, OSMNode> = {};
    const ways: Record<number, OSMWay> = {};
    const buildings: BuildingFeature[] = [];
    
    // Prima passiamo attraverso tutti gli elementi per organizzarli
    data.elements.forEach(element => {
      if ('lat' in element && 'lon' in element) {
        // È un nodo
        nodes[element.id] = element as OSMNode;
      } else if ('nodes' in element) {
        // È un way
        const way = element as OSMWay;
        if (way.tags && (way.tags.building || way.tags.amenity)) {
          ways[way.id] = way;
        }
      }
    });
    
    // Ora convertiamo i ways in features GeoJSON
    Object.values(ways).forEach(way => {
      const coordinates: number[][] = [];
      
      // Raccogliamo le coordinate dei nodi
      way.nodes.forEach(nodeId => {
        const node = nodes[nodeId];
        if (node) {
          coordinates.push([node.lon, node.lat]);
        }
      });
      
      // Chiudiamo il poligono se necessario
      if (coordinates.length > 0 && 
          (coordinates[0][0] !== coordinates[coordinates.length - 1][0] || 
           coordinates[0][1] !== coordinates[coordinates.length - 1][1])) {
        coordinates.push([...coordinates[0]]);
      }
      
      if (coordinates.length > 3) { // Un poligono valido ha almeno 4 punti (incluso il punto di chiusura)
        const buildingType = way.tags.amenity === 'school' || way.tags.amenity === 'university' ? 
                            'educational' : 
                            way.tags.amenity === 'hospital' ? 
                            'healthcare' : 
                            way.tags.building === 'commercial' || way.tags.building === 'retail' ? 
                            'commercial' : 
                            way.tags.building === 'industrial' || way.tags.building === 'warehouse' ? 
                            'industrial' : 
                            'residential';
        
        const feature: BuildingFeature = {
          type: "Feature",
          id: `osm-${way.id}`,
          geometry: {
            type: "Polygon",
            coordinates: [coordinates]
          },
          properties: {
            id: `osm-${way.id}`,
            name: way.tags.name || '',
            address: `${way.tags['addr:street'] || ''} ${way.tags['addr:housenumber'] || ''}`.trim(),
            type: buildingType as 'residential' | 'commercial' | 'industrial',
            floors: parseInt(way.tags.building_levels || '1', 10),
            year_built: parseInt(way.tags.start_date || '2000', 10),
            area: 0 // Calcoleremo l'area in un secondo momento se necessario
          }
        };
        
        buildings.push(feature);
      }
    });
    
    return buildings;
  }, []);

  // Funzione per caricare i dati degli edifici da OpenStreetMap
  const fetchOSMBuildings = useCallback(async () => {
    if (!bounds || isLoading) return;
    
    setIsLoading(true);
    
    try {
      // Costruiamo la query Overpass per ottenere gli edifici nell'area visualizzata
      const bbox = `${bounds.southwest.lat},${bounds.southwest.lng},${bounds.northeast.lat},${bounds.northeast.lng}`;
      const query = `
        [out:json];
        (
          way["building"]
            (${bbox});
          relation["building"]
            (${bbox});
        );
        out body;
        >;
        out skel qt;
      `;
      
      // Facciamo la richiesta all'API di Overpass
      const response = await fetch('https://overpass-api.de/api/interpreter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `data=${encodeURIComponent(query)}`
      });
      
      if (!response.ok) {
        throw new Error(`Errore nella richiesta: ${response.status} ${response.statusText}`);
      }
      
      const data: OSMResponse = await response.json();
      
      // Convertiamo i dati in GeoJSON
      const buildings = convertOSMToGeoJSON(data);
      
      // Aggiorniamo lo stato
      setBuildings(buildings);
    } catch (_error) {
      // Gestione silenziosa dell'errore
    } finally {
      setIsLoading(false);
    }
  }, [bounds, isLoading, convertOSMToGeoJSON]);

  // Carichiamo i dati quando cambiano i bounds
  useEffect(() => {
    if (isMounted && isMapLoaded && filters.showOSMBuildingsLayer && bounds) {
      fetchOSMBuildings();
    }
  }, [isMounted, isMapLoaded, filters.showOSMBuildingsLayer, bounds, fetchOSMBuildings]);

  // Converte le coordinate GeoJSON in coordinate LatLng per Google Maps
  const convertCoordinates = useCallback((coordinates: number[][][]): google.maps.LatLngLiteral[] => {
    try {
      return coordinates[0].map(coord => ({
        lat: coord[1],
        lng: coord[0]
      }));
    } catch (_error) {
      // Gestione silenziosa dell'errore
      return [];
    }
  }, []);

  // Se il layer non è attivo o la mappa non è caricata, non renderizziamo nulla
  if (!filters.showOSMBuildingsLayer || !isMapLoaded || !isMounted) {
    return null;
  }

  return (
    <>
      {buildings.map((building) => {
        try {
          return (
            <Polygon
              key={`osm-building-${building.id}`}
              paths={convertCoordinates(building.geometry.coordinates as number[][][])}
              options={{
                ...osmBuildingStyle,
                zIndex: 5 // Mettiamo questo layer sopra gli altri
              }}
            />
          );
        } catch (_error) {
          // Gestione silenziosa dell'errore
          return null;
        }
      })}
    </>
  );
};

export default OSMBuildingsLayer;