import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Polygon } from '@react-google-maps/api';
import { useMapStore } from '@/stores/mapStore';
import type { BuildingFeature } from '@/stores/mapStore';
import type { Roof } from '@/stores/statsStore';

// Stili per i tetti in base al tipo
const roofStyles = {
  normal: {
    fillColor: '#CCCCCC',
    fillOpacity: 0.5,
    strokeColor: '#999999',
    strokeOpacity: 0.8,
    strokeWeight: 1,
  },
  asbestos: {
    fillColor: '#FF0000',
    fillOpacity: 0.5,
    strokeColor: '#CC0000',
    strokeOpacity: 0.8,
    strokeWeight: 1,
  },
  solar: {
    fillColor: '#0000FF',
    fillOpacity: 0.5,
    strokeColor: '#0000CC',
    strokeOpacity: 0.8,
    strokeWeight: 1,
  },
};

// Stili per i tetti selezionati
const selectedStyle = {
  fillOpacity: 0.7,
  strokeOpacity: 1,
  strokeWeight: 2,
};

const RoofOverlay: React.FC = () => {
  // Stato locale per i dati dei tetti
  const [roofs, setRoofs] = useState<Roof[]>([]);
  const [_error, setError] = useState<string | null>(null);
  
  // Accesso allo store della mappa
  const { 
    filteredBuildings, 
    selectedRoofId, 
    selectRoof,
    isMapLoaded,
    filters
  } = useMapStore();

  // Carica i dati dei tetti all'avvio
  useEffect(() => {
    // Utilizziamo un flag per evitare caricamenti multipli
    let isLoading = false;
    
    const loadRoofs = async () => {
      // Se è già in corso un caricamento, usciamo
      if (isLoading || roofs.length > 0) return;
      
      isLoading = true;
      
      try {
        const response = await fetch('/dummy-data/roofsClass.json');
        const data: Roof[] = await response.json();
        setRoofs(data);
        setError(null);
      } catch (_error) {
        setError('Errore nel caricamento dei dati dei tetti');
      } finally {
        isLoading = false;
      }
    };

    if (isMapLoaded) {
      loadRoofs();
    }
  }, [isMapLoaded, roofs.length]);

  // Filtra i tetti in base ai filtri applicati
  const filteredRoofs = useMemo(() => {
    if (!filters.roofType || filters.roofType.length === 0) {
      return roofs;
    }
    return roofs.filter(roof => filters.roofType!.includes(roof.type));
  }, [roofs, filters.roofType]);

  // Gestisce il click su un tetto
  const handleRoofClick = useCallback((roofId: string) => {
    selectRoof(roofId);
  }, [selectRoof]);

  // Trova l'edificio corrispondente a un tetto
  // Memoizziamo questa funzione per evitare ricalcoli inutili
  const findBuildingForRoof = useCallback((buildingId: string): BuildingFeature | undefined => {
    return filteredBuildings.find(building => building.id === buildingId);
  }, [filteredBuildings]);
  
  // Memoizziamo la lista dei tetti filtrati per evitare ricalcoli inutili
  const memoizedFilteredRoofs = useMemo(() => filteredRoofs, [filteredRoofs]);

  // Converte le coordinate GeoJSON in coordinate LatLng per Google Maps
  const convertCoordinates = useCallback((coordinates: number[][][]): google.maps.LatLngLiteral[] => {
    return coordinates[0].map(coord => ({
      lat: coord[1],
      lng: coord[0]
    }));
  }, []);

  return (
    <>
      {memoizedFilteredRoofs.map((roof) => {
        const building = findBuildingForRoof(roof.building_id);
        if (!building) return null;

        const isSelected = roof.id === selectedRoofId;
        const roofType = roof.type;
        const baseStyle = roofStyles[roofType];
        
        // Combina gli stili di base con gli stili di selezione se il tetto è selezionato
        const polygonOptions = {
          ...baseStyle,
          ...(isSelected ? selectedStyle : {})
        };

        return (
          <Polygon
            key={roof.id}
            paths={convertCoordinates(building.geometry.coordinates as number[][][])}
            onClick={() => handleRoofClick(roof.id)}
            options={{
              ...polygonOptions,
              zIndex: isSelected ? 4 : 3
            }}
          />
        );
      })}
    </>
  );
};

export default RoofOverlay;