import React, { useEffect, useRef } from 'react';
import { useGoogleMap } from '@react-google-maps/api';

interface AdvancedMarkerProps {
  position: google.maps.LatLngLiteral;
  onClick?: () => void;
  icon?: {
    url: string;
  };
  zIndex?: number;
  animation?: google.maps.Animation;
}

const AdvancedMarker: React.FC<AdvancedMarkerProps> = ({
  position,
  onClick,
  icon,
  zIndex,
  animation
}) => {
  const map = useGoogleMap();
  const markerRef = useRef<google.maps.marker.AdvancedMarkerElement | null>(null);

  useEffect(() => {
    if (!map || !window.google) return;

    // Assicuriamoci che il namespace marker esista
    if (!google.maps.marker || !google.maps.marker.AdvancedMarkerElement) {
      // Utilizziamo un approccio silenzioso invece di console.error
      return;
    }

    // Creiamo l'elemento del marker
    const markerElement = document.createElement('div');
    markerElement.className = 'custom-marker';
    
    // Se è fornita un'icona, la utilizziamo
    if (icon) {
      const imgElement = document.createElement('img');
      imgElement.src = icon.url;
      imgElement.style.width = '32px';
      imgElement.style.height = '32px';
      markerElement.appendChild(imgElement);
    } else {
      // Altrimenti utilizziamo un marker predefinito
      markerElement.innerHTML = `
        <div style="
          width: 24px;
          height: 24px;
          background-color: #4285F4;
          border-radius: 50%;
          border: 2px solid white;
        "></div>
      `;
    }

    // Creiamo il marker avanzato
    const advancedMarker = new google.maps.marker.AdvancedMarkerElement({
      map,
      position,
      content: markerElement,
      zIndex: zIndex || undefined,
      title: 'Marker'
    });

    // Aggiungiamo l'evento click
    if (onClick) {
      advancedMarker.addListener('click', onClick);
    }

    // Aggiungiamo l'animazione se specificata
    if (animation) {
      // Nota: AdvancedMarkerElement non supporta direttamente l'animazione come Marker
      // Implementiamo una semplice animazione di rimbalzo se richiesto
      // Utilizziamo un confronto numerico sicuro per evitare problemi di tipo
      if (animation && animation.toString() === '1') { // BOUNCE è 1, DROP è 2
        const bounceAnimation = () => {
          markerElement.animate(
            [
              { transform: 'translateY(0)' },
              { transform: 'translateY(-10px)' },
              { transform: 'translateY(0)' }
            ],
            {
              duration: 500,
              iterations: Infinity
            }
          );
        };
        
        bounceAnimation();
      }
    }

    // Salviamo il riferimento al marker
    markerRef.current = advancedMarker;

    // Cleanup quando il componente viene smontato
    return () => {
      if (markerRef.current) {
        markerRef.current.map = null;
        if (onClick) {
          google.maps.event.clearListeners(markerRef.current, 'click');
        }
      }
    };
  }, [map, position, onClick, icon, zIndex, animation]);

  // Aggiorniamo la posizione quando cambia
  useEffect(() => {
    if (markerRef.current) {
      markerRef.current.position = position;
    }
  }, [position]);

  // Aggiorniamo lo zIndex quando cambia
  useEffect(() => {
    if (markerRef.current && zIndex !== undefined) {
      markerRef.current.zIndex = zIndex;
    }
  }, [zIndex]);

  // Questo componente non renderizza nulla direttamente nel DOM
  return null;
};

export default AdvancedMarker;