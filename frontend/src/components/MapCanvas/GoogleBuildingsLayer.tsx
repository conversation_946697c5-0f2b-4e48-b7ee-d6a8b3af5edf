import React, { useEffect, useState } from 'react';
import { useMapStore } from '@/stores/mapStore';

// Stile alternativo che utilizza un approccio diverso
const alternativeBuildingStyle = [
  // Manteniamo tutto normale
  {
    featureType: "all",
    elementType: "all",
    stylers: [{ visibility: "on" }]
  },
  // Coloriamo solo gli edifici
  {
    featureType: "poi",
    elementType: "geometry.fill",
    stylers: [
      { color: "#ff0000" },
      { visibility: "on" }
    ]
  },
  {
    featureType: "poi",
    elementType: "geometry.stroke",
    stylers: [
      { color: "#ff0000" },
      { visibility: "on" }
    ]
  },
  // Nascondiamo le etichette dei POI (nomi delle aziende)
  {
    featureType: "poi",
    elementType: "labels",
    stylers: [
      { visibility: "off" }
    ]
  },
  // Nascondiamo anche le etichette delle strade per maggiore pulizia
  {
    featureType: "road",
    elementType: "labels",
    stylers: [
      { visibility: "off" }
    ]
  }
];

const GoogleBuildingsLayer: React.FC = () => {
  // Stato locale per tracciare se il componente è montato
  const [isMounted, setIsMounted] = useState(false);
  
  // Accesso allo store della mappa
  const { 
    filters,
    isMapLoaded,
    mapInstance
  } = useMapStore();
  
  // Stato per tenere traccia dello stile precedente
  const [previousStyle, setPreviousStyle] = useState<google.maps.MapTypeStyle[]>([]);
  
  // Stato per tenere traccia del tipo di mappa precedente
  const [previousMapType, setPreviousMapType] = useState<string>('');

  // Aggiorniamo lo stato locale solo quando il componente è montato
  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Applichiamo lo stile quando cambia il filtro
  useEffect(() => {
    if (!isMounted || !isMapLoaded || !mapInstance) return;
    
    try {
      if (filters.showGoogleBuildingsLayer) {
        // Salviamo lo stile e il tipo di mappa correnti
        const currentStyle = mapInstance.get('styles') || [];
        setPreviousStyle(currentStyle);
        setPreviousMapType(mapInstance.getMapTypeId() || google.maps.MapTypeId.ROADMAP);
        
        // Proviamo un approccio diverso: cambiamo il tipo di mappa
        // Creiamo un nuovo tipo di mappa con lo stile personalizzato
        const styledMapType = new google.maps.StyledMapType(
          alternativeBuildingStyle,
          { name: "Edifici Rossi" }
        );
        
        // Registriamo il nuovo tipo di mappa
        mapInstance.mapTypes.set('styled_buildings', styledMapType);
        
        // Cambiamo il tipo di mappa
        mapInstance.setMapTypeId('styled_buildings');
      } else {
        // Ripristiniamo il tipo di mappa precedente
        if (previousMapType) {
          mapInstance.setMapTypeId(previousMapType);
        } else {
          mapInstance.setMapTypeId(google.maps.MapTypeId.ROADMAP);
        }
        
        // Ripristiniamo lo stile precedente
        mapInstance.setOptions({
          styles: previousStyle
        });
      }
    } catch (_error) {
      // Gestione silenziosa dell'errore
    }
    
    // Cleanup
    return () => {
      if (mapInstance) {
        try {
          // Ripristiniamo il tipo di mappa precedente
          if (previousMapType) {
            mapInstance.setMapTypeId(previousMapType);
          } else {
            mapInstance.setMapTypeId(google.maps.MapTypeId.ROADMAP);
          }
          
          // Ripristiniamo lo stile precedente
          mapInstance.setOptions({
            styles: previousStyle
          });
        } catch (_error) {
          // Gestione silenziosa dell'errore
        }
      }
    };
  }, [isMounted, isMapLoaded, mapInstance, filters.showGoogleBuildingsLayer, previousStyle, previousMapType]);

  // Questo componente non renderizza nulla direttamente nel DOM
  return null;
};

export default GoogleBuildingsLayer;