import React, { useState, useCallback, useMemo } from 'react';

// Estendi l'interfaccia Window per i nostri timeout
declare global {
  interface Window {
    companySelectionTimeout?: number;
  }
}
import { InfoWindow } from '@react-google-maps/api';
import AdvancedMarker from './AdvancedMarker';
import { useMapStore } from '@/stores/mapStore';
import { useCompaniesStore, type Company } from '@/stores/companiesStore';

// Icone personalizzate per i marker in base alla categoria
const markerIcons = {
  'Ristorazione': {
    url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
  },
  'Commercio': {
    url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',
  },
  'Istruzione': {
    url: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',
  },
  'Sanità': {
    url: 'https://maps.google.com/mapfiles/ms/icons/purple-dot.png',
  },
  'Ospitalità': {
    url: 'https://maps.google.com/mapfiles/ms/icons/orange-dot.png',
  },
  'Uffici': {
    url: 'https://maps.google.com/mapfiles/ms/icons/pink-dot.png',
  },
  'default': {
    url: 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png',
  }
};

const CompanyMarkers: React.FC = () => {
  // Stato locale per la finestra informativa
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  
  // Accesso agli store
  const { selectedCompanyId, selectCompany } = useMapStore();
  const {
    filteredCompanies,
    showCompanies
  } = useCompaniesStore();

  // Non carichiamo più i dati qui, poiché vengono già caricati nel componente AreaAnalyzer

  // Non filtriamo più le aziende qui, poiché viene già fatto nel componente AreaAnalyzer

  // Gestisce il click su un marker
  const handleMarkerClick = useCallback((company: Company) => {
    // Preveniamo selezioni multiple ravvicinate
    if (window.companySelectionTimeout) {
      clearTimeout(window.companySelectionTimeout);
    }
    
    window.companySelectionTimeout = setTimeout(() => {
      selectCompany(company.id);
      setSelectedCompany(company);
    }, 50) as unknown as number;
  }, [selectCompany]);

  // Gestisce la chiusura della finestra informativa
  const handleInfoWindowClose = useCallback(() => {
    selectCompany(null);
    setSelectedCompany(null);
  }, [selectCompany]);

  // Ottiene l'icona appropriata per la categoria dell'azienda
  const getMarkerIcon = useCallback((category: string) => {
    return markerIcons[category as keyof typeof markerIcons] || markerIcons.default;
  }, []);
  
  // Memoizziamo la lista delle aziende filtrate per evitare ricalcoli inutili
  const memoizedFilteredCompanies = useMemo(() => filteredCompanies, [filteredCompanies]);

  // Se le aziende non devono essere visualizzate, non mostriamo i marker
  if (!showCompanies) {
    return null;
  }

  return (
    <>
      {memoizedFilteredCompanies.map((company) => {
        const isSelected = company.id === selectedCompanyId;
        
        return (
          <AdvancedMarker
            key={company.id}
            position={company.location}
            onClick={() => handleMarkerClick(company)}
            icon={getMarkerIcon(company.category)}
            zIndex={isSelected ? 1000 : undefined}
            animation={isSelected ? google.maps.Animation.BOUNCE : undefined}
          />
        );
      })}

      {selectedCompany && (
        <InfoWindow
          position={selectedCompany.location}
          onCloseClick={handleInfoWindowClose}
        >
          <div className="p-2">
            <h3 className="text-lg font-bold">{selectedCompany.name}</h3>
            <p className="text-sm">{selectedCompany.address}</p>
            <p className="text-sm">
              <span className="font-semibold">Categoria:</span> {selectedCompany.category}
            </p>
            <p className="text-sm">
              <span className="font-semibold">Dipendenti:</span> {selectedCompany.employees}
            </p>
            <p className="text-sm">
              <span className="font-semibold">Contatto:</span> {selectedCompany.contact.email}
            </p>
          </div>
        </InfoWindow>
      )}
    </>
  );
};

export default CompanyMarkers;