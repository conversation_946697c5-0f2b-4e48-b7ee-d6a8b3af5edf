import React, { useCallback, useEffect } from 'react';

// Estendi l'interfaccia Window per i nostri timeout
declare global {
  interface Window {
    centerChangeTimeout?: number;
    zoomChangeTimeout?: number;
  }
}
import { GoogleMap, useJsApiLoader, Libraries } from '@react-google-maps/api';
import { useMapStore } from '@/stores/mapStore';
import OSMFootprintLayer from '@/components/MapCanvas/OSMFootprintLayer';
import OSMBuildingsLayer from '@/components/MapCanvas/OSMBuildingsLayer';
import GoogleBuildingsLayer from '@/components/MapCanvas/GoogleBuildingsLayer';
import GooglePlacesLayer from '@/components/MapCanvas/GooglePlacesLayer';
import RoofOverlay from '@/components/MapCanvas/RoofOverlay';
import CompanyMarkers from '@/components/MapCanvas/CompanyMarkers';

// Stile della mappa
const containerStyle = {
  width: '100%',
  height: '100%'
};

// Opzioni della mappa
const defaultOptions = {
  disableDefaultUI: false,
  zoomControl: true,
  mapTypeControl: true,
  scaleControl: true,
  streetViewControl: true,
  rotateControl: true,
  clickableIcons: false,
  keyboardShortcuts: true,
  scrollwheel: true,
  disableDoubleClickZoom: false,
  fullscreenControl: true,
};

// Chiave API Google Maps (in un'applicazione reale, questa dovrebbe essere in un file .env)
// Utilizziamo una stringa vuota per evitare errori di caricamento in modalità di sviluppo
// In produzione, questa dovrebbe essere sostituita con una chiave API valida
const GOOGLE_MAPS_API_KEY = 'AIzaSyBxpMSp5E3JtWbe7TsPDm7mHR6u2O_gXYY';

// Definiamo le librerie come costante statica per evitare ricaricamenti non intenzionali
const GOOGLE_MAPS_LIBRARIES: Libraries = ['places', 'geometry'];

const Map: React.FC = () => {
  // Caricamento dell'API Google Maps
  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: GOOGLE_MAPS_API_KEY,
    libraries: GOOGLE_MAPS_LIBRARIES
  });

  // Accesso allo store della mappa
  const {
    center,
    zoom,
    setCenter,
    setZoom,
    setBounds,
    setMapLoaded,
    setMapInstance
  } = useMapStore();

  // Riferimento alla mappa
  const [map, setMap] = React.useState<google.maps.Map | null>(null);

  // Callback quando la mappa viene caricata
  const onLoad = useCallback((map: google.maps.Map) => {
    setMap(map);
    setMapInstance(map);
    setMapLoaded(true);
  }, [setMapLoaded, setMapInstance]);

  // Callback quando la mappa viene smontata
  const onUnmount = useCallback(() => {
    setMap(null);
    setMapInstance(null);
    setMapLoaded(false);
  }, [setMapLoaded, setMapInstance]);

  // Aggiorna i bounds quando la mappa cambia
  useEffect(() => {
    if (map) {
      // Utilizziamo un flag per evitare aggiornamenti multipli ravvicinati
      let isUpdating = false;
      
      const updateBounds = () => {
        // Se è già in corso un aggiornamento, usciamo
        if (isUpdating) return;
        
        isUpdating = true;
        
        const bounds = map.getBounds();
        if (bounds) {
          const newBounds = {
            northeast: {
              lat: bounds.getNorthEast().lat(),
              lng: bounds.getNorthEast().lng()
            },
            southwest: {
              lat: bounds.getSouthWest().lat(),
              lng: bounds.getSouthWest().lng()
            }
          };
          
          // Aggiorniamo i bounds solo se sono effettivamente cambiati
          setBounds(newBounds);
        }
        
        // Resettiamo il flag dopo un breve ritardo
        setTimeout(() => {
          isUpdating = false;
        }, 100);
      };

      // Aggiungi listener per gli eventi di zoom e drag
      map.addListener('idle', updateBounds);
      
      // Cleanup
      return () => {
        if (map) {
          google.maps.event.clearListeners(map, 'idle');
        }
      };
    }
  }, [map, setBounds]);

  // Callback quando il centro della mappa cambia
  // Utilizziamo un debounce per evitare troppi aggiornamenti ravvicinati
  const onCenterChanged = useCallback(() => {
    if (map) {
      // Utilizziamo un timeout per debounce
      if (window.centerChangeTimeout) {
        clearTimeout(window.centerChangeTimeout);
      }
      
      window.centerChangeTimeout = setTimeout(() => {
        const newCenter = map.getCenter();
        if (newCenter) {
          setCenter({
            lat: newCenter.lat(),
            lng: newCenter.lng()
          });
        }
      }, 100) as unknown as number;
    }
  }, [map, setCenter]);

  // Callback quando lo zoom della mappa cambia
  // Utilizziamo un debounce per evitare troppi aggiornamenti ravvicinati
  const onZoomChanged = useCallback(() => {
    if (map) {
      // Utilizziamo un timeout per debounce
      if (window.zoomChangeTimeout) {
        clearTimeout(window.zoomChangeTimeout);
      }
      
      window.zoomChangeTimeout = setTimeout(() => {
        const newZoom = map.getZoom();
        if (newZoom) {
          setZoom(newZoom);
        }
      }, 100) as unknown as number;
    }
  }, [map, setZoom]);

  if (!isLoaded) return <div>Caricamento mappa...</div>;

  return (
    <GoogleMap
      mapContainerStyle={containerStyle}
      center={center}
      zoom={zoom}
      onLoad={onLoad}
      onUnmount={onUnmount}
      onCenterChanged={onCenterChanged}
      onZoomChanged={onZoomChanged}
      options={defaultOptions}
    >
      {/* Layers della mappa */}
      <OSMFootprintLayer />
      <OSMBuildingsLayer />
      <GoogleBuildingsLayer />
      <GooglePlacesLayer />
      <RoofOverlay />
      <CompanyMarkers />
    </GoogleMap>
  );
};

export default Map;