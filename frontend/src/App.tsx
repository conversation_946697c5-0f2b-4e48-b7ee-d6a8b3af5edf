import { Suspense } from 'react'
import { RouterProvider, createRouter } from '@tanstack/react-router'
import { ThemeProvider } from './context/theme-context'
import { FontProvider } from './context/font-context'
import { NavigationProgress } from './components/navigation-progress'
import { routeTree } from './routeTree.gen'
import { QueryClient } from '@tanstack/react-query'

/**
 * App principale di Astrameccanica
 * 
 * Questo componente è il punto di ingresso dell'applicazione e configura:
 * - Provider per il tema
 * - Provider per i font
 * - Router principale
 * - Componenti globali come il Toaster per le notifiche
 */
// Crea un'istanza di QueryClient per React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 1000, // 10 secondi
      refetchOnWindowFocus: import.meta.env.PROD,
    },
  },
})

// Crea un'istanza del router
const router = createRouter({
  routeTree,
  context: { queryClient },
  defaultPreload: 'intent',
  defaultPreloadStaleTime: 0,
})

// Registra il router per il type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

export function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="astrameccanica-theme">
      <FontProvider>
        <Suspense fallback={<NavigationProgress />}>
          <RouterProvider router={router} />
        </Suspense>
      </FontProvider>
    </ThemeProvider>
  )
}

export default App