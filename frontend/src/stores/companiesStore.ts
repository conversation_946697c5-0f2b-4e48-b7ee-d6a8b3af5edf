import { create } from 'zustand';
import { useMapStore } from './mapStore';
import type { CompaniesState, Company } from '../types/company';
import { createGooglePlacesService } from '../services/googlePlacesService';

// Re-export Company type
export type { Company };

// Store Zustand per la gestione delle aziende
export const useCompaniesStore = create<CompaniesState>((set, get) => ({
  // Stato iniziale
  companies: [],
  filteredCompanies: [],
  selectedCompanyId: null,
  loading: false,
  error: null,
  showCompanies: false, // Inizialmente le aziende non sono visualizzate
  filters: {
    name: null,
    category: null,
    size: null,
    employeesRange: null
  },
  
  // Azioni
  setCompanies: (companies) => set({ 
    companies, 
    filteredCompanies: companies 
  }),
  
  filterCompaniesByBounds: (bounds) => {
    if (!bounds) {
      set({ filteredCompanies: get().companies });
      return;
    }
    
    const filtered = get().companies.filter(company => {
      const { lat, lng } = company.location;
      return (
        lat >= bounds.southwest.lat &&
        lat <= bounds.northeast.lat &&
        lng >= bounds.southwest.lng &&
        lng <= bounds.northeast.lng
      );
    });
    
    set({ filteredCompanies: filtered });
  },
  
  filterCompaniesByBuilding: (buildingId) => {
    if (!buildingId) {
      set({ filteredCompanies: get().companies });
      return;
    }
    
    const filtered = get().companies.filter(company => 
      company.buildings.includes(buildingId)
    );
    
    set({ filteredCompanies: filtered });
  },
  
  setFilters: (filters) => set({ 
    filters: { 
      ...get().filters, 
      ...filters 
    } 
  }),
  
  applyFilters: () => {
    const { companies, filters } = get();
    let filtered = [...companies];
    
    // Filtra per nome
    if (filters.name) {
      filtered = filtered.filter(company => 
        company.name.toLowerCase().includes(filters.name!.toLowerCase())
      );
    }
    
    // Filtra per categoria
    if (filters.category && filters.category.length > 0) {
      filtered = filtered.filter(company => 
        filters.category!.some(cat => 
          company.category.toLowerCase().includes(cat.toLowerCase())
        )
      );
    }
    
    // Filtra per dimensione
    if (filters.size && filters.size.length > 0) {
      filtered = filtered.filter(company => 
        filters.size!.includes(company.size)
      );
    }
    
    // Filtra per numero di dipendenti
    if (filters.employeesRange) {
      const [min, max] = filters.employeesRange;
      filtered = filtered.filter(company => 
        company.employees >= min && company.employees <= max
      );
    }
    
    set({ filteredCompanies: filtered });
  },
  
  resetFilters: () => set({ 
    filters: {
      name: null,
      category: null,
      size: null,
      employeesRange: null
    },
    filteredCompanies: get().companies
  }),
  
  selectCompany: (companyId) => set({ selectedCompanyId: companyId }),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  
  // Azione per impostare lo stato di visualizzazione delle aziende
  setShowCompanies: (show) => set({ showCompanies: show }),
  
  // Azione per rilevare le aziende nell'area corrente utilizzando Google Places API
  detectCompaniesInArea: async () => {
    const { setCompanies, setError } = get();
    const mapStore = useMapStore.getState();
    const { bounds, mapInstance } = mapStore;
    
    // Verifichiamo che ci siano tutti i prerequisiti
    if (!mapInstance || !bounds || !window.google?.maps?.places) {
      setError('Mappa o API Google Places non disponibili');
      return;
    }
    
    set({ loading: true, error: null });
    
    try {
      // Creiamo il servizio Google Places
      const placesService = createGooglePlacesService(mapInstance);
      
      if (!placesService) {
        throw new Error('Impossibile creare il servizio Google Places');
      }
      
      // Rileviamo le aziende nell'area
      const companies = await placesService.detectCompaniesInArea(bounds);
      
      if (companies.length > 0) {
        setCompanies(companies);
        set({ showCompanies: true });
      } else {
        setError('Nessuna azienda trovata nell\'area selezionata');
      }
    } catch (error) {
      // Gestiamo l'errore
      const errorMessage = error instanceof Error ? error.message : 'Errore sconosciuto';
      setError(`Errore durante il rilevamento delle aziende: ${errorMessage}`);
    } finally {
      set({ loading: false });
    }
  }
}));