import { create } from 'zustand'
import type { BuildingFeature } from './mapStore'
import type { Company } from './companiesStore'

// Definizione dei tipi
export interface Roof {
  id: string
  building_id: string
  type: 'normal' | 'asbestos' | 'solar'
  area: number
  condition: string
  installation_year: number
  last_inspection: string
  solar_potential: number
}

export interface BuildingStats {
  total: number
  byType: {
    residential: number
    commercial: number
    industrial: number
  }
  byYear: Record<string, number> // Decade -> count (es. "1980-1989" -> 15)
  averageFloors: number
  totalArea: number
}

export interface RoofStats {
  total: number
  byType: {
    normal: number
    asbestos: number
    solar: number
  }
  asbestosPercentage: number
  solarPercentage: number
  averageSolarPotential: number
  totalArea: number
}

export interface CompanyStats {
  total: number
  byCategory: Record<string, number> // Category -> count
  bySize: {
    small: number
    medium: number
    large: number
  }
  averageEmployees: number
  totalEmployees: number
  oldestCompany: number
  newestCompany: number
}

export interface GlobalStats {
  buildings: BuildingStats
  roofs: RoofStats
  companies: CompanyStats
  lastUpdated: Date | null
}

interface StatsState {
  // Stato
  stats: GlobalStats
  isLoading: boolean
  error: string | null
  
  // Azioni
  calculateBuildingStats: (buildings: BuildingFeature[]) => void
  calculateRoofStats: (roofs: Roof[]) => void
  calculateCompanyStats: (companies: Company[]) => void
  calculateAllStats: (buildings: BuildingFeature[], roofs: Roof[], companies: Company[]) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

// Funzioni di utilità per il calcolo delle statistiche
const calculateBuildingStatistics = (buildings: BuildingFeature[]): BuildingStats => {
  // Conteggio per tipo
  const byType = {
    residential: 0,
    commercial: 0,
    industrial: 0
  };
  
  // Conteggio per decade
  const byYear: Record<string, number> = {};
  
  // Calcolo totali
  let totalFloors = 0;
  let totalArea = 0;
  
  buildings.forEach(building => {
    // Conteggio per tipo
    byType[building.properties.type]++;
    
    // Conteggio per decade
    const year = building.properties.year_built;
    const decade = `${Math.floor(year / 10) * 10}-${Math.floor(year / 10) * 10 + 9}`;
    byYear[decade] = (byYear[decade] || 0) + 1;
    
    // Calcolo totali
    totalFloors += building.properties.floors;
    totalArea += building.properties.area;
  });
  
  return {
    total: buildings.length,
    byType,
    byYear,
    averageFloors: buildings.length > 0 ? totalFloors / buildings.length : 0,
    totalArea
  };
};

const calculateRoofStatistics = (roofs: Roof[]): RoofStats => {
  // Conteggio per tipo
  const byType = {
    normal: 0,
    asbestos: 0,
    solar: 0
  };
  
  // Calcolo totali
  let totalArea = 0;
  let totalSolarPotential = 0;
  
  roofs.forEach(roof => {
    // Conteggio per tipo
    byType[roof.type]++;
    
    // Calcolo totali
    totalArea += roof.area;
    totalSolarPotential += roof.solar_potential;
  });
  
  return {
    total: roofs.length,
    byType,
    asbestosPercentage: roofs.length > 0 ? (byType.asbestos / roofs.length) * 100 : 0,
    solarPercentage: roofs.length > 0 ? (byType.solar / roofs.length) * 100 : 0,
    averageSolarPotential: roofs.length > 0 ? totalSolarPotential / roofs.length : 0,
    totalArea
  };
};

const calculateCompanyStatistics = (companies: Company[]): CompanyStats => {
  // Conteggio per categoria
  const byCategory: Record<string, number> = {};
  
  // Conteggio per dimensione
  const bySize = {
    small: 0,
    medium: 0,
    large: 0
  };
  
  // Calcolo totali
  let totalEmployees = 0;
  let oldestCompany = new Date().getFullYear();
  let newestCompany = 0;
  
  companies.forEach(company => {
    // Conteggio per categoria
    byCategory[company.category] = (byCategory[company.category] || 0) + 1;
    
    // Conteggio per dimensione
    bySize[company.size]++;
    
    // Calcolo totali
    totalEmployees += company.employees;
    
    // Anno di fondazione
    if (company.founded < oldestCompany) {
      oldestCompany = company.founded;
    }
    
    if (company.founded > newestCompany) {
      newestCompany = company.founded;
    }
  });
  
  return {
    total: companies.length,
    byCategory,
    bySize,
    averageEmployees: companies.length > 0 ? totalEmployees / companies.length : 0,
    totalEmployees,
    oldestCompany,
    newestCompany
  };
};

// Store Zustand per la gestione delle statistiche
export const useStatsStore = create<StatsState>((set) => ({
  // Stato iniziale
  stats: {
    buildings: {
      total: 0,
      byType: {
        residential: 0,
        commercial: 0,
        industrial: 0
      },
      byYear: {},
      averageFloors: 0,
      totalArea: 0
    },
    roofs: {
      total: 0,
      byType: {
        normal: 0,
        asbestos: 0,
        solar: 0
      },
      asbestosPercentage: 0,
      solarPercentage: 0,
      averageSolarPotential: 0,
      totalArea: 0
    },
    companies: {
      total: 0,
      byCategory: {},
      bySize: {
        small: 0,
        medium: 0,
        large: 0
      },
      averageEmployees: 0,
      totalEmployees: 0,
      oldestCompany: 0,
      newestCompany: 0
    },
    lastUpdated: null
  },
  isLoading: false,
  error: null,
  
  // Azioni
  calculateBuildingStats: (buildings) => {
    set(state => ({
      stats: {
        ...state.stats,
        buildings: calculateBuildingStatistics(buildings),
        lastUpdated: new Date()
      }
    }));
  },
  
  calculateRoofStats: (roofs) => {
    set(state => ({
      stats: {
        ...state.stats,
        roofs: calculateRoofStatistics(roofs),
        lastUpdated: new Date()
      }
    }));
  },
  
  calculateCompanyStats: (companies) => {
    set(state => ({
      stats: {
        ...state.stats,
        companies: calculateCompanyStatistics(companies),
        lastUpdated: new Date()
      }
    }));
  },
  
  calculateAllStats: (buildings, roofs, companies) => {
    set({
      stats: {
        buildings: calculateBuildingStatistics(buildings),
        roofs: calculateRoofStatistics(roofs),
        companies: calculateCompanyStatistics(companies),
        lastUpdated: new Date()
      }
    });
  },
  
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
}));