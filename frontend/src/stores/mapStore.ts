import { create } from 'zustand'

// Definizione dei tipi GeoJSON
export type GeoJsonCoordinates = number[] | number[][] | number[][][] | number[][][][];

export interface Geometry {
  type: string;
  coordinates: GeoJsonCoordinates;
}

export interface Feature {
  type: "Feature";
  geometry: Geometry;
  properties: Record<string, unknown>;
  id?: string;
}

export interface FeatureCollection {
  type: "FeatureCollection";
  features: Feature[];
}

// Definizione dei tipi
export interface LatLngLiteral {
  lat: number
  lng: number
}

export interface LatLngBounds {
  northeast: LatLngLiteral
  southwest: LatLngLiteral
}

export interface BuildingFeature extends Feature {
  id: string
  properties: {
    id: string
    name: string
    address: string
    type: 'residential' | 'commercial' | 'industrial'
    floors: number
    year_built: number
    area: number
  }
}

export interface MapFilters {
  buildingType: ('residential' | 'commercial' | 'industrial')[] | null
  roofType: ('normal' | 'asbestos' | 'solar')[] | null
  yearBuiltRange: [number, number] | null
  showOSMBuildingsLayer: boolean
  showGoogleBuildingsLayer: boolean
}

interface MapState {
  // Stato
  center: LatLngLiteral
  zoom: number
  bounds: LatLngBounds | null
  selectedBuildingId: string | null
  selectedRoofId: string | null
  selectedCompanyId: string | null
  isMapLoaded: boolean
  buildings: BuildingFeature[]
  filteredBuildings: BuildingFeature[]
  filters: MapFilters
  mapInstance: google.maps.Map | null
  
  // Azioni
  setCenter: (center: LatLngLiteral) => void
  setZoom: (zoom: number) => void
  setBounds: (bounds: LatLngBounds | null) => void
  selectBuilding: (buildingId: string | null) => void
  selectRoof: (roofId: string | null) => void
  selectCompany: (companyId: string | null) => void
  setMapLoaded: (loaded: boolean) => void
  setMapInstance: (map: google.maps.Map | null) => void
  loadGeoJSON: (data: FeatureCollection) => void
  setFilters: (filters: Partial<MapFilters>) => void
  applyFilters: () => void
  resetFilters: () => void
  toggleOSMBuildingsLayer: () => void
  toggleGoogleBuildingsLayer: () => void
}

// Store Zustand per la gestione della mappa
export const useMapStore = create<MapState>((set, get) => ({
  // Stato iniziale
  center: {
    lat: 45.0520, // Coordinate di default (Piacenza)
    lng: 9.6950,
  },
  zoom: 15,
  bounds: null,
  selectedBuildingId: null,
  selectedRoofId: null,
  selectedCompanyId: null,
  isMapLoaded: false,
  buildings: [],
  filteredBuildings: [],
  mapInstance: null,
  filters: {
    buildingType: null,
    roofType: null,
    yearBuiltRange: null,
    showOSMBuildingsLayer: false,
    showGoogleBuildingsLayer: false,
  },

  // Azioni
  setCenter: (center) => set({ center }),
  setZoom: (zoom) => set({ zoom }),
  setBounds: (bounds) => set({ bounds }),
  selectBuilding: (buildingId) => set({ selectedBuildingId: buildingId }),
  selectRoof: (roofId) => set({ selectedRoofId: roofId }),
  selectCompany: (companyId) => set({ selectedCompanyId: companyId }),
  setMapLoaded: (loaded) => set({ isMapLoaded: loaded }),
  setMapInstance: (map) => set({ mapInstance: map }),
  
  loadGeoJSON: (data) => {
    const buildings = data.features as BuildingFeature[];
    set({ 
      buildings,
      filteredBuildings: buildings
    });
  },
  
  setFilters: (filters) => set({ 
    filters: { 
      ...get().filters, 
      ...filters 
    } 
  }),
  
  applyFilters: () => {
    const { buildings, filters } = get();
    let filtered = [...buildings];
    
    // Filtra per tipo di edificio
    if (filters.buildingType && filters.buildingType.length > 0) {
      filtered = filtered.filter(building => 
        filters.buildingType!.includes(building.properties.type)
      );
    }
    
    // Filtra per anno di costruzione
    if (filters.yearBuiltRange) {
      const [minYear, maxYear] = filters.yearBuiltRange;
      filtered = filtered.filter(building => 
        building.properties.year_built >= minYear && 
        building.properties.year_built <= maxYear
      );
    }
    
    set({ filteredBuildings: filtered });
  },
  
  resetFilters: () => set({
    filters: {
      buildingType: null,
      roofType: null,
      yearBuiltRange: null,
      showOSMBuildingsLayer: false,
      showGoogleBuildingsLayer: false,
    },
    filteredBuildings: get().buildings
  }),
  
  toggleOSMBuildingsLayer: () => set(state => ({
    filters: {
      ...state.filters,
      showOSMBuildingsLayer: !state.filters.showOSMBuildingsLayer
    }
  })),
  
  toggleGoogleBuildingsLayer: () => set(state => ({
    filters: {
      ...state.filters,
      showGoogleBuildingsLayer: !state.filters.showGoogleBuildingsLayer
    }
  })),
}));