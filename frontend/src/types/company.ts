import type { LatLngLiteral, LatLngBounds } from '../stores/mapStore';

/**
 * Interfaccia che rappresenta un'azienda
 */
export interface Company {
  id: string;
  name: string;
  address: string;
  category: string;
  size: 'small' | 'medium' | 'large';
  employees: number;
  founded: number;
  contact: {
    email: string;
    phone: string;
  };
  location: LatLngLiteral;
  buildings: string[];
}

/**
 * Interfaccia per i filtri delle aziende
 */
export interface CompanyFilters {
  name: string | null;
  category: string[] | null;
  size: ('small' | 'medium' | 'large')[] | null;
  employeesRange: [number, number] | null;
}

/**
 * Interfaccia per lo stato dello store delle aziende
 */
export interface CompaniesState {
  // Stato
  companies: Company[];
  filteredCompanies: Company[];
  selectedCompanyId: string | null;
  loading: boolean;
  error: string | null;
  filters: CompanyFilters;
  showCompanies: boolean;
  
  // Azioni
  setCompanies: (companies: Company[]) => void;
  filterCompaniesByBounds: (bounds: LatLngBounds | null) => void;
  filterCompaniesByBuilding: (buildingId: string | null) => void;
  setFilters: (filters: Partial<CompanyFilters>) => void;
  applyFilters: () => void;
  resetFilters: () => void;
  selectCompany: (companyId: string | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setShowCompanies: (show: boolean) => void;
  detectCompaniesInArea: () => void;
}

/**
 * Interfaccia per i parametri di ricerca di Google Places
 */
export interface PlacesSearchRequest {
  location: google.maps.LatLng;
  radius: number;
  type: string;
}

/**
 * Interfaccia per i campi di dettaglio di Google Places
 */
export interface PlaceDetailsRequest {
  placeId: string;
  fields: string[];
}