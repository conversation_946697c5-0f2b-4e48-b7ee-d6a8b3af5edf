/**
 * Utility per la gestione delle aziende
 */

/**
 * Converte un tipo di attività di Google Places in una categoria
 * @param types Array di tipi di attività di Google Places
 * @returns Categoria dell'azienda
 */
export function mapPlaceTypeToCategory(types: string[] = []): string {
  if (types.includes('restaurant') || types.includes('food') || types.includes('cafe')) {
    return 'Ristorazione';
  } else if (types.includes('store') || types.includes('shop') || types.includes('shopping_mall')) {
    return 'Commercio';
  } else if (types.includes('school') || types.includes('university')) {
    return 'Istruzione';
  } else if (types.includes('hospital') || types.includes('doctor') || types.includes('health')) {
    return 'Sanità';
  } else if (types.includes('lodging') || types.includes('hotel')) {
    return 'Ospitalità';
  } else if (types.includes('office') || types.includes('corporate')) {
    return 'Uffici';
  } else {
    return 'Altro';
  }
}

/**
 * Determina la dimensione dell'azienda in base al tipo
 * @param types Array di tipi di attività di Google Places
 * @returns Dimensione dell'azienda (small, medium, large)
 */
export function getCompanySize(types: string[] = []): 'small' | 'medium' | 'large' {
  if (types.includes('shopping_mall') || types.includes('department_store') ||
  types.includes('supermarket') || types.includes('hospital')) {
    return 'large';
  } else if (types.includes('restaurant') || types.includes('store') ||
  types.includes('school') || types.includes('hotel')) {
    return 'medium';
  } else {
    return 'small';
  }
}

/**
 * Genera un numero di dipendenti in base alla dimensione dell'azienda
 * @param size Dimensione dell'azienda
 * @returns Numero di dipendenti
 */
export function generateEmployeesCount(size: 'small' | 'medium' | 'large'): number {
  switch (size) {
    case 'small':
      return Math.floor(Math.random() * 20) + 5;
    case 'medium':
      return Math.floor(Math.random() * 50) + 20;
    case 'large':
      return Math.floor(Math.random() * 200) + 50;
    default:
      return 10;
  }
}

/**
 * Genera un anno di fondazione casuale
 * @returns Anno di fondazione
 */
export function generateFoundedYear(): number {
  return 2000 + Math.floor(Math.random() * 23);
}

/**
 * Genera un'email basata sul nome dell'azienda
 * @param companyName Nome dell'azienda
 * @returns Email generata
 */
export function generateEmail(companyName: string): string {
  return `info@${(companyName || 'azienda').toLowerCase().replace(/[^a-z0-9]/g, '')}.com`;
}