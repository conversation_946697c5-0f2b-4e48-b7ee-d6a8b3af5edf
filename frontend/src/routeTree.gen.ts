/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as TestSamImport } from './routes/test-sam'
import { Route as TestMapImport } from './routes/test-map'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexImport } from './routes/_authenticated/index'
import { Route as AuthenticatedSamSegmentationImport } from './routes/_authenticated/sam-segmentation'
import { Route as AuthenticatedRoofClassifierImport } from './routes/_authenticated/roof-classifier'
import { Route as AuthenticatedMapImport } from './routes/_authenticated/map'
import { Route as errors503Import } from './routes/(errors)/503'
import { Route as errors500Import } from './routes/(errors)/500'
import { Route as errors404Import } from './routes/(errors)/404'
import { Route as errors403Import } from './routes/(errors)/403'
import { Route as errors401Import } from './routes/(errors)/401'
import { Route as authSignUpImport } from './routes/(auth)/sign-up'
import { Route as authSignIn2Import } from './routes/(auth)/sign-in-2'
import { Route as authSignInImport } from './routes/(auth)/sign-in'
import { Route as authOtpImport } from './routes/(auth)/otp'
import { Route as authForgotPasswordImport } from './routes/(auth)/forgot-password'
import { Route as AuthenticatedSettingsRouteImport } from './routes/_authenticated/settings/route'
import { Route as AuthenticatedUsersIndexImport } from './routes/_authenticated/users/index'
import { Route as AuthenticatedTasksIndexImport } from './routes/_authenticated/tasks/index'
import { Route as AuthenticatedSettingsIndexImport } from './routes/_authenticated/settings/index'
import { Route as AuthenticatedMapDownloaderIndexImport } from './routes/_authenticated/map-downloader/index'
import { Route as AuthenticatedHelpCenterIndexImport } from './routes/_authenticated/help-center/index'
import { Route as AuthenticatedCrmEvaluationIndexImport } from './routes/_authenticated/crm-evaluation/index'
import { Route as AuthenticatedCompaniesDbIndexImport } from './routes/_authenticated/companies-db/index'
import { Route as AuthenticatedChatsIndexImport } from './routes/_authenticated/chats/index'
import { Route as AuthenticatedBillsAnalyzerIndexImport } from './routes/_authenticated/bills-analyzer/index'
import { Route as AuthenticatedAreaAnalyzerIndexImport } from './routes/_authenticated/area-analyzer/index'
import { Route as AuthenticatedAppsIndexImport } from './routes/_authenticated/apps/index'
import { Route as AuthenticatedSettingsOrganizationImport } from './routes/_authenticated/settings/organization'
import { Route as AuthenticatedSettingsNotificationsImport } from './routes/_authenticated/settings/notifications'
import { Route as AuthenticatedSettingsDisplayImport } from './routes/_authenticated/settings/display'
import { Route as AuthenticatedSettingsAppearanceImport } from './routes/_authenticated/settings/appearance'
import { Route as AuthenticatedSettingsAccountImport } from './routes/_authenticated/settings/account'
import { Route as AuthenticatedMapDownloaderSettingsImport } from './routes/_authenticated/map-downloader/settings'
import { Route as AuthenticatedMapDownloaderArchiveImport } from './routes/_authenticated/map-downloader/archive'
import { Route as AuthenticatedCompaniesDbDataSourcesImport } from './routes/_authenticated/companies-db/data-sources'
import { Route as AuthenticatedCompaniesDbDashboardImport } from './routes/_authenticated/companies-db/dashboard'
import { Route as AuthenticatedCompaniesDbCategoriesImport } from './routes/_authenticated/companies-db/categories'

// Create/Update Routes

const TestSamRoute = TestSamImport.update({
  id: '/test-sam',
  path: '/test-sam',
  getParentRoute: () => rootRoute,
} as any)

const TestMapRoute = TestMapImport.update({
  id: '/test-map',
  path: '/test-map',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedRouteRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedIndexRoute = AuthenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSamSegmentationRoute =
  AuthenticatedSamSegmentationImport.update({
    id: '/sam-segmentation',
    path: '/sam-segmentation',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedRoofClassifierRoute =
  AuthenticatedRoofClassifierImport.update({
    id: '/roof-classifier',
    path: '/roof-classifier',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMapRoute = AuthenticatedMapImport.update({
  id: '/map',
  path: '/map',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const errors503Route = errors503Import.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRoute,
} as any)

const errors500Route = errors500Import.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRoute,
} as any)

const errors404Route = errors404Import.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRoute,
} as any)

const errors403Route = errors403Import.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRoute,
} as any)

const errors401Route = errors401Import.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRoute,
} as any)

const authSignUpRoute = authSignUpImport.update({
  id: '/(auth)/sign-up',
  path: '/sign-up',
  getParentRoute: () => rootRoute,
} as any)

const authSignIn2Route = authSignIn2Import.update({
  id: '/(auth)/sign-in-2',
  path: '/sign-in-2',
  getParentRoute: () => rootRoute,
} as any)

const authSignInRoute = authSignInImport.update({
  id: '/(auth)/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRoute,
} as any)

const authOtpRoute = authOtpImport.update({
  id: '/(auth)/otp',
  path: '/otp',
  getParentRoute: () => rootRoute,
} as any)

const authForgotPasswordRoute = authForgotPasswordImport.update({
  id: '/(auth)/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedSettingsRouteRoute = AuthenticatedSettingsRouteImport.update(
  {
    id: '/settings',
    path: '/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedUsersIndexRoute = AuthenticatedUsersIndexImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedTasksIndexRoute = AuthenticatedTasksIndexImport.update({
  id: '/tasks/',
  path: '/tasks/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSettingsIndexRoute = AuthenticatedSettingsIndexImport.update(
  {
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any,
)

const AuthenticatedMapDownloaderIndexRoute =
  AuthenticatedMapDownloaderIndexImport.update({
    id: '/map-downloader/',
    path: '/map-downloader/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedHelpCenterIndexRoute =
  AuthenticatedHelpCenterIndexImport.update({
    id: '/help-center/',
    path: '/help-center/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCrmEvaluationIndexRoute =
  AuthenticatedCrmEvaluationIndexImport.update({
    id: '/crm-evaluation/',
    path: '/crm-evaluation/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCompaniesDbIndexRoute =
  AuthenticatedCompaniesDbIndexImport.update({
    id: '/companies-db/',
    path: '/companies-db/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedChatsIndexRoute = AuthenticatedChatsIndexImport.update({
  id: '/chats/',
  path: '/chats/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedBillsAnalyzerIndexRoute =
  AuthenticatedBillsAnalyzerIndexImport.update({
    id: '/bills-analyzer/',
    path: '/bills-analyzer/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedAreaAnalyzerIndexRoute =
  AuthenticatedAreaAnalyzerIndexImport.update({
    id: '/area-analyzer/',
    path: '/area-analyzer/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedAppsIndexRoute = AuthenticatedAppsIndexImport.update({
  id: '/apps/',
  path: '/apps/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSettingsOrganizationRoute =
  AuthenticatedSettingsOrganizationImport.update({
    id: '/organization',
    path: '/organization',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsNotificationsRoute =
  AuthenticatedSettingsNotificationsImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsDisplayRoute =
  AuthenticatedSettingsDisplayImport.update({
    id: '/display',
    path: '/display',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAppearanceRoute =
  AuthenticatedSettingsAppearanceImport.update({
    id: '/appearance',
    path: '/appearance',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAccountRoute =
  AuthenticatedSettingsAccountImport.update({
    id: '/account',
    path: '/account',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedMapDownloaderSettingsRoute =
  AuthenticatedMapDownloaderSettingsImport.update({
    id: '/map-downloader/settings',
    path: '/map-downloader/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedMapDownloaderArchiveRoute =
  AuthenticatedMapDownloaderArchiveImport.update({
    id: '/map-downloader/archive',
    path: '/map-downloader/archive',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCompaniesDbDataSourcesRoute =
  AuthenticatedCompaniesDbDataSourcesImport.update({
    id: '/companies-db/data-sources',
    path: '/companies-db/data-sources',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCompaniesDbDashboardRoute =
  AuthenticatedCompaniesDbDashboardImport.update({
    id: '/companies-db/dashboard',
    path: '/companies-db/dashboard',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedCompaniesDbCategoriesRoute =
  AuthenticatedCompaniesDbCategoriesImport.update({
    id: '/companies-db/categories',
    path: '/companies-db/categories',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRoute
    }
    '/test-map': {
      id: '/test-map'
      path: '/test-map'
      fullPath: '/test-map'
      preLoaderRoute: typeof TestMapImport
      parentRoute: typeof rootRoute
    }
    '/test-sam': {
      id: '/test-sam'
      path: '/test-sam'
      fullPath: '/test-sam'
      preLoaderRoute: typeof TestSamImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/(auth)/forgot-password': {
      id: '/(auth)/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof authForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/otp': {
      id: '/(auth)/otp'
      path: '/otp'
      fullPath: '/otp'
      preLoaderRoute: typeof authOtpImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in': {
      id: '/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof authSignInImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-in-2': {
      id: '/(auth)/sign-in-2'
      path: '/sign-in-2'
      fullPath: '/sign-in-2'
      preLoaderRoute: typeof authSignIn2Import
      parentRoute: typeof rootRoute
    }
    '/(auth)/sign-up': {
      id: '/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof authSignUpImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503Import
      parentRoute: typeof rootRoute
    }
    '/_authenticated/map': {
      id: '/_authenticated/map'
      path: '/map'
      fullPath: '/map'
      preLoaderRoute: typeof AuthenticatedMapImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/roof-classifier': {
      id: '/_authenticated/roof-classifier'
      path: '/roof-classifier'
      fullPath: '/roof-classifier'
      preLoaderRoute: typeof AuthenticatedRoofClassifierImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/sam-segmentation': {
      id: '/_authenticated/sam-segmentation'
      path: '/sam-segmentation'
      fullPath: '/sam-segmentation'
      preLoaderRoute: typeof AuthenticatedSamSegmentationImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/companies-db/categories': {
      id: '/_authenticated/companies-db/categories'
      path: '/companies-db/categories'
      fullPath: '/companies-db/categories'
      preLoaderRoute: typeof AuthenticatedCompaniesDbCategoriesImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/companies-db/dashboard': {
      id: '/_authenticated/companies-db/dashboard'
      path: '/companies-db/dashboard'
      fullPath: '/companies-db/dashboard'
      preLoaderRoute: typeof AuthenticatedCompaniesDbDashboardImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/companies-db/data-sources': {
      id: '/_authenticated/companies-db/data-sources'
      path: '/companies-db/data-sources'
      fullPath: '/companies-db/data-sources'
      preLoaderRoute: typeof AuthenticatedCompaniesDbDataSourcesImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/map-downloader/archive': {
      id: '/_authenticated/map-downloader/archive'
      path: '/map-downloader/archive'
      fullPath: '/map-downloader/archive'
      preLoaderRoute: typeof AuthenticatedMapDownloaderArchiveImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/map-downloader/settings': {
      id: '/_authenticated/map-downloader/settings'
      path: '/map-downloader/settings'
      fullPath: '/map-downloader/settings'
      preLoaderRoute: typeof AuthenticatedMapDownloaderSettingsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/account': {
      id: '/_authenticated/settings/account'
      path: '/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof AuthenticatedSettingsAccountImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/appearance': {
      id: '/_authenticated/settings/appearance'
      path: '/appearance'
      fullPath: '/settings/appearance'
      preLoaderRoute: typeof AuthenticatedSettingsAppearanceImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/display': {
      id: '/_authenticated/settings/display'
      path: '/display'
      fullPath: '/settings/display'
      preLoaderRoute: typeof AuthenticatedSettingsDisplayImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/notifications': {
      id: '/_authenticated/settings/notifications'
      path: '/notifications'
      fullPath: '/settings/notifications'
      preLoaderRoute: typeof AuthenticatedSettingsNotificationsImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/organization': {
      id: '/_authenticated/settings/organization'
      path: '/organization'
      fullPath: '/settings/organization'
      preLoaderRoute: typeof AuthenticatedSettingsOrganizationImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/apps/': {
      id: '/_authenticated/apps/'
      path: '/apps'
      fullPath: '/apps'
      preLoaderRoute: typeof AuthenticatedAppsIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/area-analyzer/': {
      id: '/_authenticated/area-analyzer/'
      path: '/area-analyzer'
      fullPath: '/area-analyzer'
      preLoaderRoute: typeof AuthenticatedAreaAnalyzerIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/bills-analyzer/': {
      id: '/_authenticated/bills-analyzer/'
      path: '/bills-analyzer'
      fullPath: '/bills-analyzer'
      preLoaderRoute: typeof AuthenticatedBillsAnalyzerIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/chats/': {
      id: '/_authenticated/chats/'
      path: '/chats'
      fullPath: '/chats'
      preLoaderRoute: typeof AuthenticatedChatsIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/companies-db/': {
      id: '/_authenticated/companies-db/'
      path: '/companies-db'
      fullPath: '/companies-db'
      preLoaderRoute: typeof AuthenticatedCompaniesDbIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/crm-evaluation/': {
      id: '/_authenticated/crm-evaluation/'
      path: '/crm-evaluation'
      fullPath: '/crm-evaluation'
      preLoaderRoute: typeof AuthenticatedCrmEvaluationIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/help-center/': {
      id: '/_authenticated/help-center/'
      path: '/help-center'
      fullPath: '/help-center'
      preLoaderRoute: typeof AuthenticatedHelpCenterIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/map-downloader/': {
      id: '/_authenticated/map-downloader/'
      path: '/map-downloader'
      fullPath: '/map-downloader'
      preLoaderRoute: typeof AuthenticatedMapDownloaderIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof AuthenticatedSettingsIndexImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/tasks/': {
      id: '/_authenticated/tasks/'
      path: '/tasks'
      fullPath: '/tasks'
      preLoaderRoute: typeof AuthenticatedTasksIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/users/': {
      id: '/_authenticated/users/'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof AuthenticatedUsersIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
  }
}

// Create and export the route tree

interface AuthenticatedSettingsRouteRouteChildren {
  AuthenticatedSettingsAccountRoute: typeof AuthenticatedSettingsAccountRoute
  AuthenticatedSettingsAppearanceRoute: typeof AuthenticatedSettingsAppearanceRoute
  AuthenticatedSettingsDisplayRoute: typeof AuthenticatedSettingsDisplayRoute
  AuthenticatedSettingsNotificationsRoute: typeof AuthenticatedSettingsNotificationsRoute
  AuthenticatedSettingsOrganizationRoute: typeof AuthenticatedSettingsOrganizationRoute
  AuthenticatedSettingsIndexRoute: typeof AuthenticatedSettingsIndexRoute
}

const AuthenticatedSettingsRouteRouteChildren: AuthenticatedSettingsRouteRouteChildren =
  {
    AuthenticatedSettingsAccountRoute: AuthenticatedSettingsAccountRoute,
    AuthenticatedSettingsAppearanceRoute: AuthenticatedSettingsAppearanceRoute,
    AuthenticatedSettingsDisplayRoute: AuthenticatedSettingsDisplayRoute,
    AuthenticatedSettingsNotificationsRoute:
      AuthenticatedSettingsNotificationsRoute,
    AuthenticatedSettingsOrganizationRoute:
      AuthenticatedSettingsOrganizationRoute,
    AuthenticatedSettingsIndexRoute: AuthenticatedSettingsIndexRoute,
  }

const AuthenticatedSettingsRouteRouteWithChildren =
  AuthenticatedSettingsRouteRoute._addFileChildren(
    AuthenticatedSettingsRouteRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedSettingsRouteRoute: typeof AuthenticatedSettingsRouteRouteWithChildren
  AuthenticatedMapRoute: typeof AuthenticatedMapRoute
  AuthenticatedRoofClassifierRoute: typeof AuthenticatedRoofClassifierRoute
  AuthenticatedSamSegmentationRoute: typeof AuthenticatedSamSegmentationRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedCompaniesDbCategoriesRoute: typeof AuthenticatedCompaniesDbCategoriesRoute
  AuthenticatedCompaniesDbDashboardRoute: typeof AuthenticatedCompaniesDbDashboardRoute
  AuthenticatedCompaniesDbDataSourcesRoute: typeof AuthenticatedCompaniesDbDataSourcesRoute
  AuthenticatedMapDownloaderArchiveRoute: typeof AuthenticatedMapDownloaderArchiveRoute
  AuthenticatedMapDownloaderSettingsRoute: typeof AuthenticatedMapDownloaderSettingsRoute
  AuthenticatedAppsIndexRoute: typeof AuthenticatedAppsIndexRoute
  AuthenticatedAreaAnalyzerIndexRoute: typeof AuthenticatedAreaAnalyzerIndexRoute
  AuthenticatedBillsAnalyzerIndexRoute: typeof AuthenticatedBillsAnalyzerIndexRoute
  AuthenticatedChatsIndexRoute: typeof AuthenticatedChatsIndexRoute
  AuthenticatedCompaniesDbIndexRoute: typeof AuthenticatedCompaniesDbIndexRoute
  AuthenticatedCrmEvaluationIndexRoute: typeof AuthenticatedCrmEvaluationIndexRoute
  AuthenticatedHelpCenterIndexRoute: typeof AuthenticatedHelpCenterIndexRoute
  AuthenticatedMapDownloaderIndexRoute: typeof AuthenticatedMapDownloaderIndexRoute
  AuthenticatedTasksIndexRoute: typeof AuthenticatedTasksIndexRoute
  AuthenticatedUsersIndexRoute: typeof AuthenticatedUsersIndexRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedSettingsRouteRoute: AuthenticatedSettingsRouteRouteWithChildren,
  AuthenticatedMapRoute: AuthenticatedMapRoute,
  AuthenticatedRoofClassifierRoute: AuthenticatedRoofClassifierRoute,
  AuthenticatedSamSegmentationRoute: AuthenticatedSamSegmentationRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedCompaniesDbCategoriesRoute:
    AuthenticatedCompaniesDbCategoriesRoute,
  AuthenticatedCompaniesDbDashboardRoute:
    AuthenticatedCompaniesDbDashboardRoute,
  AuthenticatedCompaniesDbDataSourcesRoute:
    AuthenticatedCompaniesDbDataSourcesRoute,
  AuthenticatedMapDownloaderArchiveRoute:
    AuthenticatedMapDownloaderArchiveRoute,
  AuthenticatedMapDownloaderSettingsRoute:
    AuthenticatedMapDownloaderSettingsRoute,
  AuthenticatedAppsIndexRoute: AuthenticatedAppsIndexRoute,
  AuthenticatedAreaAnalyzerIndexRoute: AuthenticatedAreaAnalyzerIndexRoute,
  AuthenticatedBillsAnalyzerIndexRoute: AuthenticatedBillsAnalyzerIndexRoute,
  AuthenticatedChatsIndexRoute: AuthenticatedChatsIndexRoute,
  AuthenticatedCompaniesDbIndexRoute: AuthenticatedCompaniesDbIndexRoute,
  AuthenticatedCrmEvaluationIndexRoute: AuthenticatedCrmEvaluationIndexRoute,
  AuthenticatedHelpCenterIndexRoute: AuthenticatedHelpCenterIndexRoute,
  AuthenticatedMapDownloaderIndexRoute: AuthenticatedMapDownloaderIndexRoute,
  AuthenticatedTasksIndexRoute: AuthenticatedTasksIndexRoute,
  AuthenticatedUsersIndexRoute: AuthenticatedUsersIndexRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteRouteWithChildren
  '/test-map': typeof TestMapRoute
  '/test-sam': typeof TestSamRoute
  '/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/map': typeof AuthenticatedMapRoute
  '/roof-classifier': typeof AuthenticatedRoofClassifierRoute
  '/sam-segmentation': typeof AuthenticatedSamSegmentationRoute
  '/': typeof AuthenticatedIndexRoute
  '/companies-db/categories': typeof AuthenticatedCompaniesDbCategoriesRoute
  '/companies-db/dashboard': typeof AuthenticatedCompaniesDbDashboardRoute
  '/companies-db/data-sources': typeof AuthenticatedCompaniesDbDataSourcesRoute
  '/map-downloader/archive': typeof AuthenticatedMapDownloaderArchiveRoute
  '/map-downloader/settings': typeof AuthenticatedMapDownloaderSettingsRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/settings/organization': typeof AuthenticatedSettingsOrganizationRoute
  '/apps': typeof AuthenticatedAppsIndexRoute
  '/area-analyzer': typeof AuthenticatedAreaAnalyzerIndexRoute
  '/bills-analyzer': typeof AuthenticatedBillsAnalyzerIndexRoute
  '/chats': typeof AuthenticatedChatsIndexRoute
  '/companies-db': typeof AuthenticatedCompaniesDbIndexRoute
  '/crm-evaluation': typeof AuthenticatedCrmEvaluationIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/map-downloader': typeof AuthenticatedMapDownloaderIndexRoute
  '/settings/': typeof AuthenticatedSettingsIndexRoute
  '/tasks': typeof AuthenticatedTasksIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
}

export interface FileRoutesByTo {
  '/test-map': typeof TestMapRoute
  '/test-sam': typeof TestSamRoute
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/sign-in': typeof authSignInRoute
  '/sign-in-2': typeof authSignIn2Route
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/map': typeof AuthenticatedMapRoute
  '/roof-classifier': typeof AuthenticatedRoofClassifierRoute
  '/sam-segmentation': typeof AuthenticatedSamSegmentationRoute
  '/': typeof AuthenticatedIndexRoute
  '/companies-db/categories': typeof AuthenticatedCompaniesDbCategoriesRoute
  '/companies-db/dashboard': typeof AuthenticatedCompaniesDbDashboardRoute
  '/companies-db/data-sources': typeof AuthenticatedCompaniesDbDataSourcesRoute
  '/map-downloader/archive': typeof AuthenticatedMapDownloaderArchiveRoute
  '/map-downloader/settings': typeof AuthenticatedMapDownloaderSettingsRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/settings/organization': typeof AuthenticatedSettingsOrganizationRoute
  '/apps': typeof AuthenticatedAppsIndexRoute
  '/area-analyzer': typeof AuthenticatedAreaAnalyzerIndexRoute
  '/bills-analyzer': typeof AuthenticatedBillsAnalyzerIndexRoute
  '/chats': typeof AuthenticatedChatsIndexRoute
  '/companies-db': typeof AuthenticatedCompaniesDbIndexRoute
  '/crm-evaluation': typeof AuthenticatedCrmEvaluationIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/map-downloader': typeof AuthenticatedMapDownloaderIndexRoute
  '/settings': typeof AuthenticatedSettingsIndexRoute
  '/tasks': typeof AuthenticatedTasksIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/test-map': typeof TestMapRoute
  '/test-sam': typeof TestSamRoute
  '/_authenticated/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/(auth)/forgot-password': typeof authForgotPasswordRoute
  '/(auth)/otp': typeof authOtpRoute
  '/(auth)/sign-in': typeof authSignInRoute
  '/(auth)/sign-in-2': typeof authSignIn2Route
  '/(auth)/sign-up': typeof authSignUpRoute
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/_authenticated/map': typeof AuthenticatedMapRoute
  '/_authenticated/roof-classifier': typeof AuthenticatedRoofClassifierRoute
  '/_authenticated/sam-segmentation': typeof AuthenticatedSamSegmentationRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/companies-db/categories': typeof AuthenticatedCompaniesDbCategoriesRoute
  '/_authenticated/companies-db/dashboard': typeof AuthenticatedCompaniesDbDashboardRoute
  '/_authenticated/companies-db/data-sources': typeof AuthenticatedCompaniesDbDataSourcesRoute
  '/_authenticated/map-downloader/archive': typeof AuthenticatedMapDownloaderArchiveRoute
  '/_authenticated/map-downloader/settings': typeof AuthenticatedMapDownloaderSettingsRoute
  '/_authenticated/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/_authenticated/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/_authenticated/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/_authenticated/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/_authenticated/settings/organization': typeof AuthenticatedSettingsOrganizationRoute
  '/_authenticated/apps/': typeof AuthenticatedAppsIndexRoute
  '/_authenticated/area-analyzer/': typeof AuthenticatedAreaAnalyzerIndexRoute
  '/_authenticated/bills-analyzer/': typeof AuthenticatedBillsAnalyzerIndexRoute
  '/_authenticated/chats/': typeof AuthenticatedChatsIndexRoute
  '/_authenticated/companies-db/': typeof AuthenticatedCompaniesDbIndexRoute
  '/_authenticated/crm-evaluation/': typeof AuthenticatedCrmEvaluationIndexRoute
  '/_authenticated/help-center/': typeof AuthenticatedHelpCenterIndexRoute
  '/_authenticated/map-downloader/': typeof AuthenticatedMapDownloaderIndexRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexRoute
  '/_authenticated/tasks/': typeof AuthenticatedTasksIndexRoute
  '/_authenticated/users/': typeof AuthenticatedUsersIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/test-map'
    | '/test-sam'
    | '/settings'
    | '/forgot-password'
    | '/otp'
    | '/sign-in'
    | '/sign-in-2'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/map'
    | '/roof-classifier'
    | '/sam-segmentation'
    | '/'
    | '/companies-db/categories'
    | '/companies-db/dashboard'
    | '/companies-db/data-sources'
    | '/map-downloader/archive'
    | '/map-downloader/settings'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/settings/organization'
    | '/apps'
    | '/area-analyzer'
    | '/bills-analyzer'
    | '/chats'
    | '/companies-db'
    | '/crm-evaluation'
    | '/help-center'
    | '/map-downloader'
    | '/settings/'
    | '/tasks'
    | '/users'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/test-map'
    | '/test-sam'
    | '/forgot-password'
    | '/otp'
    | '/sign-in'
    | '/sign-in-2'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/map'
    | '/roof-classifier'
    | '/sam-segmentation'
    | '/'
    | '/companies-db/categories'
    | '/companies-db/dashboard'
    | '/companies-db/data-sources'
    | '/map-downloader/archive'
    | '/map-downloader/settings'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/settings/organization'
    | '/apps'
    | '/area-analyzer'
    | '/bills-analyzer'
    | '/chats'
    | '/companies-db'
    | '/crm-evaluation'
    | '/help-center'
    | '/map-downloader'
    | '/settings'
    | '/tasks'
    | '/users'
  id:
    | '__root__'
    | '/_authenticated'
    | '/test-map'
    | '/test-sam'
    | '/_authenticated/settings'
    | '/(auth)/forgot-password'
    | '/(auth)/otp'
    | '/(auth)/sign-in'
    | '/(auth)/sign-in-2'
    | '/(auth)/sign-up'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/map'
    | '/_authenticated/roof-classifier'
    | '/_authenticated/sam-segmentation'
    | '/_authenticated/'
    | '/_authenticated/companies-db/categories'
    | '/_authenticated/companies-db/dashboard'
    | '/_authenticated/companies-db/data-sources'
    | '/_authenticated/map-downloader/archive'
    | '/_authenticated/map-downloader/settings'
    | '/_authenticated/settings/account'
    | '/_authenticated/settings/appearance'
    | '/_authenticated/settings/display'
    | '/_authenticated/settings/notifications'
    | '/_authenticated/settings/organization'
    | '/_authenticated/apps/'
    | '/_authenticated/area-analyzer/'
    | '/_authenticated/bills-analyzer/'
    | '/_authenticated/chats/'
    | '/_authenticated/companies-db/'
    | '/_authenticated/crm-evaluation/'
    | '/_authenticated/help-center/'
    | '/_authenticated/map-downloader/'
    | '/_authenticated/settings/'
    | '/_authenticated/tasks/'
    | '/_authenticated/users/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  TestMapRoute: typeof TestMapRoute
  TestSamRoute: typeof TestSamRoute
  authForgotPasswordRoute: typeof authForgotPasswordRoute
  authOtpRoute: typeof authOtpRoute
  authSignInRoute: typeof authSignInRoute
  authSignIn2Route: typeof authSignIn2Route
  authSignUpRoute: typeof authSignUpRoute
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
}

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  TestMapRoute: TestMapRoute,
  TestSamRoute: TestSamRoute,
  authForgotPasswordRoute: authForgotPasswordRoute,
  authOtpRoute: authOtpRoute,
  authSignInRoute: authSignInRoute,
  authSignIn2Route: authSignIn2Route,
  authSignUpRoute: authSignUpRoute,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_authenticated",
        "/test-map",
        "/test-sam",
        "/(auth)/forgot-password",
        "/(auth)/otp",
        "/(auth)/sign-in",
        "/(auth)/sign-in-2",
        "/(auth)/sign-up",
        "/(errors)/401",
        "/(errors)/403",
        "/(errors)/404",
        "/(errors)/500",
        "/(errors)/503"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated/route.tsx",
      "children": [
        "/_authenticated/settings",
        "/_authenticated/map",
        "/_authenticated/roof-classifier",
        "/_authenticated/sam-segmentation",
        "/_authenticated/",
        "/_authenticated/companies-db/categories",
        "/_authenticated/companies-db/dashboard",
        "/_authenticated/companies-db/data-sources",
        "/_authenticated/map-downloader/archive",
        "/_authenticated/map-downloader/settings",
        "/_authenticated/apps/",
        "/_authenticated/area-analyzer/",
        "/_authenticated/bills-analyzer/",
        "/_authenticated/chats/",
        "/_authenticated/companies-db/",
        "/_authenticated/crm-evaluation/",
        "/_authenticated/help-center/",
        "/_authenticated/map-downloader/",
        "/_authenticated/tasks/",
        "/_authenticated/users/"
      ]
    },
    "/test-map": {
      "filePath": "test-map.tsx"
    },
    "/test-sam": {
      "filePath": "test-sam.tsx"
    },
    "/_authenticated/settings": {
      "filePath": "_authenticated/settings/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/settings/account",
        "/_authenticated/settings/appearance",
        "/_authenticated/settings/display",
        "/_authenticated/settings/notifications",
        "/_authenticated/settings/organization",
        "/_authenticated/settings/"
      ]
    },
    "/(auth)/forgot-password": {
      "filePath": "(auth)/forgot-password.tsx"
    },
    "/(auth)/otp": {
      "filePath": "(auth)/otp.tsx"
    },
    "/(auth)/sign-in": {
      "filePath": "(auth)/sign-in.tsx"
    },
    "/(auth)/sign-in-2": {
      "filePath": "(auth)/sign-in-2.tsx"
    },
    "/(auth)/sign-up": {
      "filePath": "(auth)/sign-up.tsx"
    },
    "/(errors)/401": {
      "filePath": "(errors)/401.tsx"
    },
    "/(errors)/403": {
      "filePath": "(errors)/403.tsx"
    },
    "/(errors)/404": {
      "filePath": "(errors)/404.tsx"
    },
    "/(errors)/500": {
      "filePath": "(errors)/500.tsx"
    },
    "/(errors)/503": {
      "filePath": "(errors)/503.tsx"
    },
    "/_authenticated/map": {
      "filePath": "_authenticated/map.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/roof-classifier": {
      "filePath": "_authenticated/roof-classifier.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/sam-segmentation": {
      "filePath": "_authenticated/sam-segmentation.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/": {
      "filePath": "_authenticated/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/companies-db/categories": {
      "filePath": "_authenticated/companies-db/categories.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/companies-db/dashboard": {
      "filePath": "_authenticated/companies-db/dashboard.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/companies-db/data-sources": {
      "filePath": "_authenticated/companies-db/data-sources.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/map-downloader/archive": {
      "filePath": "_authenticated/map-downloader/archive.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/map-downloader/settings": {
      "filePath": "_authenticated/map-downloader/settings.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/account": {
      "filePath": "_authenticated/settings/account.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/appearance": {
      "filePath": "_authenticated/settings/appearance.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/display": {
      "filePath": "_authenticated/settings/display.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/notifications": {
      "filePath": "_authenticated/settings/notifications.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/organization": {
      "filePath": "_authenticated/settings/organization.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/apps/": {
      "filePath": "_authenticated/apps/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/area-analyzer/": {
      "filePath": "_authenticated/area-analyzer/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/bills-analyzer/": {
      "filePath": "_authenticated/bills-analyzer/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/chats/": {
      "filePath": "_authenticated/chats/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/companies-db/": {
      "filePath": "_authenticated/companies-db/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/crm-evaluation/": {
      "filePath": "_authenticated/crm-evaluation/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/help-center/": {
      "filePath": "_authenticated/help-center/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/map-downloader/": {
      "filePath": "_authenticated/map-downloader/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/": {
      "filePath": "_authenticated/settings/index.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/tasks/": {
      "filePath": "_authenticated/tasks/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/users/": {
      "filePath": "_authenticated/users/index.tsx",
      "parent": "/_authenticated"
    }
  }
}
ROUTE_MANIFEST_END */
