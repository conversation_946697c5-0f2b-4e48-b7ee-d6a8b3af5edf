qu<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAM Direct Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>SAM Server Direct Test</h1>
    
    <div id="status" class="status info">Ready to test...</div>
    
    <div>
        <button onclick="testHealth()">Test Health</button>
        <button onclick="testSegment()">Test Segmentation</button>
    </div>
    
    <h3>Response:</h3>
    <pre id="response">No response yet...</pre>
    
    <h3>Test Image:</h3>
    <canvas id="canvas" width="400" height="300" style="border: 1px solid #ccc;"></canvas>
    
    <script>
        const SAM_URL = 'http://*************:8080';
        
        // Create a simple test image
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 400, 300);
        ctx.fillStyle = 'blue';
        ctx.fillRect(100, 75, 200, 150);
        ctx.fillStyle = 'red';
        ctx.fillRect(150, 100, 100, 100);
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function updateResponse(data) {
            document.getElementById('response').textContent = JSON.stringify(data, null, 2);
        }
        
        async function testHealth() {
            updateStatus('Testing health endpoint...', 'info');
            try {
                const response = await fetch(`${SAM_URL}/health`);
                const data = await response.json();
                updateStatus('Health check successful!', 'success');
                updateResponse(data);
            } catch (error) {
                updateStatus(`Health check failed: ${error.message}`, 'error');
                updateResponse({ error: error.message });
            }
        }
        
        async function testSegment() {
            updateStatus('Testing segmentation...', 'info');
            try {
                // Convert canvas to base64
                const imageData = canvas.toDataURL('image/png').split(',')[1];
                
                // Send segmentation request with center point
                const response = await fetch(`${SAM_URL}/segment`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image: imageData,
                        points: [[200, 150]]  // Center of canvas
                    })
                });
                
                const data = await response.json();
                updateStatus('Segmentation successful!', 'success');
                updateResponse({
                    masks_count: data.masks ? data.masks.length : 0,
                    scores: data.scores,
                    first_mask_preview: data.masks ? data.masks[0].substring(0, 50) + '...' : null
                });
                
                // Display first mask if available
                if (data.masks && data.masks.length > 0) {
                    const img = new Image();
                    img.onload = function() {
                        ctx.globalAlpha = 0.5;
                        ctx.drawImage(img, 0, 0);
                        ctx.globalAlpha = 1.0;
                    };
                    img.src = 'data:image/png;base64,' + data.masks[0];
                }
            } catch (error) {
                updateStatus(`Segmentation failed: ${error.message}`, 'error');
                updateResponse({ error: error.message });
            }
        }
        
        // Test health on load
        window.onload = () => testHealth();
    </script>
</body>
</html>