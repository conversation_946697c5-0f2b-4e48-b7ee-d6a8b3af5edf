from flask import Flask, request, jsonify
from flask_cors import CORS
from serpapi import GoogleSearch

app = Flask(__name__)
CORS(app)  # Abilita CORS per tutte le rotte

@app.route('/api/serpapi', methods=['GET'])
def search_serpapi():
    # Ottieni i parametri dalla richiesta
    company_name = request.args.get('q', '')
    
    # Configura i parametri per SerpAPI
    params = {
        "api_key": "41b24c1341496b0e9d41dd3871bba79d85dbcd1a56406e9c56e73c8a491a3b10",
        "engine": "google_maps",
        "type": "search",
        "google_domain": "google.it",
        "q": company_name,
        "hl": "it",
        "gl": "it"  # Aggiunto parametro per la regione geografica Italia
    }
    
    try:
        # Esegui la ricerca utilizzando SerpAPI
        search = GoogleSearch(params)
        results = search.get_dict()
        
        # Restituisci i risultati come JSON
        return jsonify(results)
    except Exception as e:
        # Gestisci eventuali errori
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)