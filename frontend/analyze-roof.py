"""
API per l'analisi dei tetti utilizzando ChatGPT 4o
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
import base64
import logging
import os
import sys

# Aggiungi la directory corrente al path per importare il modulo
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importa il modulo per l'analisi dei tetti
from serpapi_backend import analyze_roof_with_chatgpt

# Configura il logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Abilita CORS per tutte le rotte

@app.route('/api/analyze-roof', methods=['POST'])
def analyze_roof():
    """
    Endpoint per l'analisi di un'immagine di un tetto
    
    Richiede un JSON con:
    - imageUrl: URL dell'immagine da analizzare
    
    Restituisce:
    - success: true/false
    - data: risultati dell'analisi o null
    - error: messaggio di errore o null
    """
    try:
        # Ottieni i dati dalla richiesta
        data = request.json
        
        if not data or 'imageUrl' not in data:
            return jsonify({
                'success': False,
                'error': 'Parametri mancanti: imageUrl è richiesto'
            }), 400
        
        image_url = data['imageUrl']
        
        # Verifica che l'URL sia valido
        if not image_url.startswith(('http://', 'https://', 'data:')):
            return jsonify({
                'success': False,
                'error': 'URL immagine non valido'
            }), 400
        
        # Analizza l'immagine
        logger.info(f"Analisi dell'immagine: {image_url[:50]}...")
        result = analyze_roof_with_chatgpt(image_url)
        
        # Restituisci il risultato
        if result.get('success'):
            return jsonify({
                'success': True,
                'data': result.get('data')
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', 'Errore sconosciuto durante l\'analisi')
            }), 500
            
    except Exception as e:
        logger.exception(f"Errore durante l'analisi del tetto: {e}")
        return jsonify({
            'success': False,
            'error': f"Errore durante l'analisi: {str(e)}"
        }), 500

@app.route('/api/capture-roof-image', methods=['POST'])
def capture_roof_image():
    """
    Endpoint per catturare un'immagine di un tetto da Google Maps
    
    Richiede un JSON con:
    - lat: latitudine
    - lng: longitudine
    - zoom: livello di zoom (opzionale, default 19)
    
    Restituisce:
    - success: true/false
    - imageUrl: URL dell'immagine catturata o null
    - error: messaggio di errore o null
    """
    try:
        # Ottieni i dati dalla richiesta
        data = request.json
        
        if not data or 'lat' not in data or 'lng' not in data:
            return jsonify({
                'success': False,
                'error': 'Parametri mancanti: lat e lng sono richiesti'
            }), 400
        
        lat = data['lat']
        lng = data['lng']
        zoom = data.get('zoom', 19)  # Zoom predefinito per vedere bene i tetti
        
        # Costruisci l'URL dell'API di Google Maps Static
        api_key = os.environ.get("GOOGLE_MAPS_API_KEY", "AIzaSyBxpMSp5E3JtWbe7TsPDm7mHR6u2O_gXYY")
        image_url = f"https://maps.googleapis.com/maps/api/staticmap?center={lat},{lng}&zoom={zoom}&size=600x600&maptype=satellite&key={api_key}"
        
        # Scarica l'immagine
        response = requests.get(image_url)
        if response.status_code != 200:
            return jsonify({
                'success': False,
                'error': f'Errore nel download dell\'immagine: {response.status_code}'
            }), 500
        
        # Converti l'immagine in base64
        image_base64 = base64.b64encode(response.content).decode('utf-8')
        data_url = f"data:image/jpeg;base64,{image_base64}"
        
        # Restituisci l'URL dell'immagine
        return jsonify({
            'success': True,
            'imageUrl': data_url
        })
            
    except Exception as e:
        logger.exception(f"Errore durante la cattura dell'immagine: {e}")
        return jsonify({
            'success': False,
            'error': f"Errore durante la cattura dell'immagine: {str(e)}"
        }), 500

if __name__ == '__main__':
    # Avvia il server Flask sulla porta 5001
    port = int(os.environ.get("PORT", 5001))
    app.run(host='0.0.0.0', port=port, debug=True)