#!/usr/bin/env python3
"""
Test script per verificare il server SAM con un'immagine di test
"""
import requests
import base64
import json
from PIL import Image
import io

# Server URL
SAM_SERVER_URL = "http://*************:8080"

def test_sam_server():
    # 1. Check health
    print("Checking server health...")
    try:
        response = requests.get(f"{SAM_SERVER_URL}/health")
        print(f"Health check: {response.json()}")
    except Exception as e:
        print(f"Health check failed: {e}")
        return
    
    # 2. Create a simple test image (500x500 white with a black square in the middle)
    print("\nCreating test image...")
    img = Image.new('RGB', (500, 500), color='white')
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    # Draw a black rectangle in the middle
    draw.rectangle([150, 150, 350, 350], fill='black')
    
    # Convert to base64
    buffered = io.BytesIO()
    img.save(buffered, format="PNG")
    img_base64 = base64.b64encode(buffered.getvalue()).decode()
    
    # 3. Test segmentation with a point in the middle of the black square
    print("\nTesting segmentation...")
    test_point = [250, 250]  # Center of the black square
    
    payload = {
        "image": img_base64,
        "points": [test_point]
    }
    
    try:
        response = requests.post(
            f"{SAM_SERVER_URL}/segment",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"Segmentation successful!")
            print(f"Number of masks: {len(result.get('masks', []))}")
            print(f"Scores: {result.get('scores', [])}")
            
            # Check if the mask makes sense
            if result.get('masks'):
                print(f"First mask sample (first 100 chars): {result['masks'][0][:100]}...")
        else:
            print(f"Segmentation failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Segmentation request failed: {e}")
    
    # 4. Test with multiple points
    print("\n\nTesting with multiple points...")
    test_points = [
        [200, 200],  # Inside black square
        [250, 250],  # Center of black square
        [300, 300],  # Inside black square
    ]
    
    payload = {
        "image": img_base64,
        "points": test_points
    }
    
    try:
        response = requests.post(
            f"{SAM_SERVER_URL}/segment",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"Multi-point segmentation successful!")
            print(f"Number of masks: {len(result.get('masks', []))}")
            print(f"Scores: {result.get('scores', [])}")
        else:
            print(f"Multi-point segmentation failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Multi-point segmentation request failed: {e}")

if __name__ == "__main__":
    test_sam_server()