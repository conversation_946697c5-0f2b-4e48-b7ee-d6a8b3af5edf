{"name": "astrameccanica-monorepo", "version": "1.0.0", "description": "AstraMeccanica Full Stack Application", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run preview", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "prisma:studio": "cd backend && npx prisma studio", "prisma:migrate": "cd backend && npx prisma migrate dev", "prisma:generate": "cd backend && npx prisma generate"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0"}}