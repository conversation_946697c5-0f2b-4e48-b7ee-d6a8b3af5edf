# 🔗 Integrazione Map Downloader con Roof Classifier

## ✅ Modifiche Applicate

### 1. **DatasetManager Modificato**
Il componente ora:
- **Non duplica** la funzionalità di download
- **Rimanda al Map Downloader** esistente con un pulsante chiaro
- **Fornisce suggerimenti** per ottimizzare il download per l'analisi tetti

### 2. **Workflow Integrato**

```mermaid
graph LR
    A[Map Downloader] -->|Scarica Immagini| B[Dataset Manager]
    B -->|Elabora con AI| C[Segmentazione Tetti]
    C -->|Estrai Ritagli| D[Annotazione]
    D -->|Training| E[Modello AI]
```

### 3. **Vantaggi dell'Integrazione**

✅ **Nessuna duplicazione di codice**
- Un solo strumento per scaricare mappe
- Manutenzione semplificata

✅ **Esperienza utente migliorata**
- Workflow chiaro e lineare
- Strumenti specializzati per ogni task

✅ **Funzionalità Map Downloader disponibili**
- Anteprima mappa interattiva
- Selezione con coordinate o indirizzo
- Diversi stili (satellite, ibrido, terreno)
- Export JPEG o GeoTIFF
- Gestione download multipli

## 📋 Come Usare

### Passo 1: Download Immagini
1. Clicca su **"Vai al Map Downloader"** dal Roof Classifier
2. Nel Map Downloader:
   - Seleziona l'area di interesse
   - **Usa zoom 19-20** per dettagli tetti
   - **Scegli stile Satellite** per migliore visibilità
   - **Scarica in JPEG** per elaborazione veloce

### Passo 2: Elaborazione
1. Torna al Roof Classifier
2. Clicca **"Elabora Immagini dal Map Downloader"**
3. Il sistema automaticamente:
   - Trova le immagini scaricate
   - Segmenta i tetti con SAM
   - Estrae i ritagli per annotazione

### Passo 3: Annotazione e Training
- Procedi normalmente con annotazione e training

## 🎯 Suggerimenti Ottimali

| Parametro | Valore Consigliato | Motivo |
|-----------|-------------------|---------|
| **Zoom** | 19-20 | Dettaglio tetti visibile |
| **Stile** | Satellite | Migliore contrasto |
| **Formato** | JPEG | Elaborazione più veloce |
| **Dimensione** | 640x640 px | Ottimale per AI |

## 🚀 Prossimi Sviluppi

- [ ] Passaggio automatico parametri da Map Downloader
- [ ] Coda elaborazione batch
- [ ] Integrazione diretta senza cambio pagina
- [ ] Monitoraggio download in tempo reale

## 📝 Note Tecniche

- Il Map Downloader salva in: `downloads/`
- Il Roof Classifier cerca in: `dataset/roofs/raw_images/`
- Serve un link o copia automatica tra le directory