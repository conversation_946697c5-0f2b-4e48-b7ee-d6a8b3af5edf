// Schema semplificato per SQLite (sviluppo locale)

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  name      String
  password  String
  role      String   @default("user")
  createdAt DateTime @default(now()) @map("created_at")
  lastLogin DateTime? @map("last_login")
  
  @@map("users")
}