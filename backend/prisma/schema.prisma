generator client {
  provider        = "prisma-client-js"
  output          = "../src/generated/prisma"
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  extensions = [postgis]
}

model Organization {
  id               String            @id @default(uuid())
  name             String            @unique
  slug             String            @unique
  logo             String?
  plan             String            @default("base")
  isActive         Boolean           @default(true) @map("is_active")
  smtpConfig       Json?             @map("smtp_config") // Configurazione SMTP criptata
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  
  users            UserOrganization[]
  companies        Company[]
  dataSources      DataSource[]
  categories       Category[]

  @@map("organizations")
}

model User {
  id                String              @id @default(uuid())
  email             String              @unique
  name              String
  password          String
  role              String              @default("user")
  preferences       Json?               @map("preferences") // Preferenze utente per map downloader e altre funzionalità
  createdAt         DateTime            @default(now()) @map("created_at")
  lastLogin         DateTime?           @map("last_login")
  
  organizations     UserOrganization[]
  annotationHistory AnnotationHistory[]
  maps              Map[]
  roofAnnotations   RoofAnnotation[]
  samImages         SamImage[]
  samSegmentations  SamSegmentation[]

  @@map("users")
}

model UserOrganization {
  userId           String            @map("user_id")
  organizationId   String            @map("organization_id")
  role             String            @default("member") // 'owner', 'admin', 'member'
  joinedAt         DateTime          @default(now()) @map("joined_at")
  
  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization     Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@id([userId, organizationId])
  @@map("user_organizations")
}

model Map {
  id              String           @id @default(uuid())
  userId          String?          @map("user_id")
  boundingBoxNW   Float[]          @map("bbox_nw")
  boundingBoxSE   Float[]          @map("bbox_se")
  zoomLevel       Int              @map("zoom_level")
  mapStyle        String           @map("map_style")
  outputFormat    String           @map("output_format")
  storageBucket   String           @map("storage_bucket")
  storageKey      String           @map("storage_key")
  createdAt       DateTime         @default(now()) @map("created_at")
  metadata        Json?
  fileSize        BigInt?          @map("file_size")
  user            User?            @relation(fields: [userId], references: [id])
  roofAnnotations RoofAnnotation[]

  @@map("maps")
}

model Region {
  id             String                  @id @default(uuid())
  codRip         Int                     @map("cod_rip")
  codReg         Int                     @unique @map("cod_reg")
  denReg         String                  @map("den_reg")
  shapeLength    Float                   @map("shape_length")
  shapeArea      Float                   @map("shape_area")
  geometry       Unsupported("geometry")
  createdAt      DateTime                @default(now()) @map("created_at")
  municipalities Municipality[]
  provinces      Province[]

  @@map("regions")
}

model Province {
  id             String                  @id @default(uuid())
  codRip         Int                     @map("cod_rip")
  codReg         Int                     @map("cod_reg")
  codProv        Int                     @map("cod_prov")
  codCm          Int                     @map("cod_cm")
  codUts         Int                     @unique @map("cod_uts")
  denProv        String                  @map("den_prov")
  denCm          String                  @map("den_cm")
  denUts         String                  @map("den_uts")
  sigla          String                  @map("sigla")
  tipoUts        String                  @map("tipo_uts")
  shapeLength    Float                   @map("shape_length")
  shapeArea      Float                   @map("shape_area")
  geometry       Unsupported("geometry")
  createdAt      DateTime                @default(now()) @map("created_at")
  municipalities Municipality[]
  region         Region                  @relation(fields: [codReg], references: [codReg])

  @@map("provinces")
}

model Municipality {
  id          String                  @id @default(uuid())
  codRip      Int                     @map("cod_rip")
  codReg      Int                     @map("cod_reg")
  codProv     Int                     @map("cod_prov")
  codCm       Int                     @map("cod_cm")
  codUts      Int                     @map("cod_uts")
  proCom      Int                     @unique @map("pro_com")
  proComT     String                  @map("pro_com_t")
  comune      String                  @map("comune")
  comuneA     String?                 @map("comune_a")
  ccUts       Int                     @map("cc_uts")
  shapeLength Float                   @map("shape_length")
  shapeArea   Float                   @map("shape_area")
  geometry    Unsupported("geometry")
  createdAt   DateTime                @default(now()) @map("created_at")
  region      Region                  @relation(fields: [codReg], references: [codReg])
  province    Province                @relation(fields: [codUts], references: [codUts])

  @@map("municipalities")
}

model RoofAnnotation {
  id                 String                  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  mapId              String?                 @map("map_id")
  geometry           Unsupported("geometry")
  confidence         Float?
  annotationSource   String?                 @map("annotation_source") @db.VarChar(50)
  roofType           String?                 @map("roof_type") @db.VarChar(50)
  material           String?                 @db.VarChar(50)
  materialConfidence Float?                  @map("material_confidence")
  hasSolarPanels     Boolean?                @default(false) @map("has_solar_panels")
  solarPanelArea     Float?                  @map("solar_panel_area")
  buildingType       String?                 @map("building_type") @db.VarChar(50)
  iouScore           Float?                  @map("iou_score")
  boundaryF1         Float?                  @map("boundary_f1")
  humanVerified      Boolean?                @default(false) @map("human_verified")
  createdAt          DateTime?               @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt          DateTime?               @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  annotatorId        String?                 @map("annotator_id")
  roof_area          Float?
  roof_perimeter     Float?
  qualityMetrics     Json?                   @default("{}") @map("quality_metrics")
  annotationHistory  AnnotationHistory[]
  batchAnnotations   BatchAnnotation[]
  modelPredictions   ModelPrediction[]
  annotator          User?                   @relation(fields: [annotatorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  map                Map?                    @relation(fields: [mapId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([confidence], map: "idx_roof_annotations_confidence")
  @@index([createdAt], map: "idx_roof_annotations_created_at")
  @@index([geometry], map: "idx_roof_annotations_geom", type: Gist)
  @@index([mapId], map: "idx_roof_annotations_map_id")
  @@index([material], map: "idx_roof_annotations_material")
  @@map("roof_annotations")
}

model AnnotationBatch {
  id                         String                       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                       String                       @db.VarChar(255)
  status                     String?                      @default("pending") @db.VarChar(50)
  totalRoofs                 Int                          @map("total_roofs")
  completedRoofs             Int?                         @default(0) @map("completed_roofs")
  iteration                  Int?
  avgConfidence              Float?                       @map("avg_confidence")
  avgIou                     Float?                       @map("avg_iou")
  avgTimePerRoof             Float?                       @map("avg_time_per_roof")
  createdAt                  DateTime?                    @default(now()) @map("created_at") @db.Timestamp(6)
  completedAt                DateTime?                    @map("completed_at") @db.Timestamp(6)
  config                     Json?                        @default("{}")
  active_learning_iterations active_learning_iterations[]
  batchAnnotations           BatchAnnotation[]

  @@index([createdAt], map: "idx_annotation_batches_created_at")
  @@index([status], map: "idx_annotation_batches_status")
  @@map("annotation_batches")
}

model BatchAnnotation {
  batchId      String          @map("batch_id") @db.Uuid
  annotationId String          @map("annotation_id") @db.Uuid
  annotation   RoofAnnotation  @relation(fields: [annotationId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  batch        AnnotationBatch @relation(fields: [batchId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([batchId, annotationId])
  @@map("batch_annotations")
}

model AnnotationHistory {
  id            String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  annotationId  String?         @map("annotation_id") @db.Uuid
  changedBy     String?         @map("changed_by")
  changeType    String?         @map("change_type") @db.VarChar(50)
  oldValues     Json?           @map("old_values")
  newValues     Json?           @map("new_values")
  changedAt     DateTime?       @default(now()) @map("changed_at") @db.Timestamp(6)
  annotation    RoofAnnotation? @relation(fields: [annotationId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  changedByUser User?           @relation(fields: [changedBy], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([annotationId], map: "idx_annotation_history_annotation_id")
  @@index([changedAt], map: "idx_annotation_history_changed_at")
  @@map("annotation_history")
}

model ModelPrediction {
  id              String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  annotationId    String?         @map("annotation_id") @db.Uuid
  modelName       String?         @map("model_name") @db.VarChar(100)
  modelVersion    String?         @map("model_version") @db.VarChar(50)
  prediction      Json?
  confidence      Float?
  inferenceTimeMs Float?          @map("inference_time_ms")
  createdAt       DateTime?       @default(now()) @map("created_at") @db.Timestamp(6)
  annotation      RoofAnnotation? @relation(fields: [annotationId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@map("model_predictions")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model active_learning_iterations {
  id                 String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  iteration_id       String           @unique @db.VarChar(255)
  batch_id           String?          @db.Uuid
  num_samples        Int
  strategy           String           @db.VarChar(50)
  cvat_task_id       String?          @db.VarChar(255)
  status             String?          @default("pending") @db.VarChar(50)
  started_at         DateTime?        @default(now()) @db.Timestamp(6)
  completed_at       DateTime?        @db.Timestamp(6)
  metrics            Json?            @default("{}")
  annotation_batches AnnotationBatch? @relation(fields: [batch_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([batch_id], map: "idx_al_iterations_batch")
  @@index([status], map: "idx_al_iterations_status")
}

model DataSource {
  id               String            @id @default(uuid())
  organizationId   String            @map("organization_id")
  name             String            
  type             String            // 'scraping', 'import', 'manual', 'api'
  url              String?
  apiKey           String?           @map("api_key")
  schedule         String?           // cron expression
  lastSyncAt       DateTime?         @map("last_sync_at")
  config           Json?             // configurazione specifica per fonte
  isActive         Boolean           @default(true) @map("is_active")
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  
  organization     Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  categories       Category[]
  companies        Company[]
  importBatches    ImportBatch[]

  @@unique([organizationId, name])
  @@map("data_sources")
}

model Category {
  id               String            @id @default(uuid())
  organizationId   String            @map("organization_id")
  name             String
  slug             String            
  description      String?
  parentId         String?           @map("parent_id")
  dataSourceId     String?           @map("data_source_id")
  metadata         Json?
  isActive         Boolean           @default(true) @map("is_active")
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  
  organization     Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  parent           Category?         @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children         Category[]        @relation("CategoryHierarchy")
  dataSource       DataSource?       @relation(fields: [dataSourceId], references: [id])
  companies        CompanyCategory[]

  @@unique([organizationId, slug])
  @@index([parentId])
  @@map("categories")
}

model Company {
  id               String            @id @default(uuid())
  organizationId   String            @map("organization_id")
  name             String
  vatNumber        String?           @map("vat_number")
  taxCode          String?           @map("tax_code")
  legalForm        String?           @map("legal_form")
  address          String?
  city             String?
  province         String?
  postalCode       String?           @map("postal_code")
  country          String?           @default("IT")
  email            String?
  phone            String?
  website          String?
  description      String?           @db.Text
  industry         String?
  foundedYear      Int?              @map("founded_year")
  employeesCount   Int?              @map("employees_count")
  annualRevenue    Float?            @map("annual_revenue")
  isActive         Boolean           @default(true) @map("is_active")
  logo             String?
  metadata         Json?
  dataSourceId     String?           @map("data_source_id")
  importBatchId    String?           @map("import_batch_id")
  externalId       String?           @map("external_id")
  latitude         Float?
  longitude        Float?
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  
  organization     Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  dataSource       DataSource?       @relation(fields: [dataSourceId], references: [id])
  importBatch      ImportBatch?      @relation(fields: [importBatchId], references: [id])
  categories       CompanyCategory[]

  @@unique([organizationId, vatNumber])
  @@unique([organizationId, taxCode])
  @@index([name])
  @@index([city])
  @@index([province])
  @@index([dataSourceId])
  @@index([importBatchId])
  @@map("companies")
}

model CompanyCategory {
  companyId        String            @map("company_id")
  categoryId       String            @map("category_id")
  assignedAt       DateTime          @default(now()) @map("assigned_at")
  
  company          Company           @relation(fields: [companyId], references: [id], onDelete: Cascade)
  category         Category          @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([companyId, categoryId])
  @@map("company_categories")
}

model ImportBatch {
  id               String            @id @default(uuid())
  dataSourceId     String            @map("data_source_id")
  status           String            @default("pending") // 'pending', 'processing', 'completed', 'failed'
  totalRecords     Int               @default(0) @map("total_records")
  processedRecords Int               @default(0) @map("processed_records")
  successRecords   Int               @default(0) @map("success_records")
  failedRecords    Int               @default(0) @map("failed_records")
  errors           Json?
  startedAt        DateTime?         @map("started_at")
  completedAt      DateTime?         @map("completed_at")
  createdAt        DateTime          @default(now()) @map("created_at")
  
  dataSource       DataSource        @relation(fields: [dataSourceId], references: [id])
  companies        Company[]

  @@index([dataSourceId])
  @@index([status])
  @@map("import_batches")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model spatial_ref_sys {
  srid      Int     @id
  auth_name String? @db.VarChar(256)
  auth_srid Int?
  srtext    String? @db.VarChar(2048)
  proj4text String? @db.VarChar(2048)
}


model SamImage {
  id               String            @id @default(uuid())
  userId           String            @map("user_id")
  name             String
  description      String?
  s3Key            String            @map("s3_key")
  thumbnailKey     String?           @map("thumbnail_key")
  url              String
  thumbnailUrl     String?           @map("thumbnail_url")
  size             Int
  mimeType         String            @map("mime_type")
  width            Int?
  height           Int?
  metadata         Json?
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  
  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  segmentations    SamSegmentation[]

  @@index([userId])
  @@index([createdAt])
  @@map("sam_images")
}

model SamSegmentation {
  id               String            @id @default(uuid())
  imageId          String            @map("image_id")
  userId           String            @map("user_id")
  masks            Json              // Array of mask objects with s3Key, score, color, etc.
  points           Json              // Array of points used for segmentation
  metadata         Json?             // Additional metadata (model used, processing time, etc.)
  createdAt        DateTime          @default(now()) @map("created_at")
  
  image            SamImage          @relation(fields: [imageId], references: [id], onDelete: Cascade)
  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([imageId])
  @@index([userId])
  @@index([createdAt])
  @@map("sam_segmentations")
}

model GeocodedCompany {
  id                        String    @id @default(uuid())
  piva                      String?   @unique
  ragioneSociale            String    @map("ragione_sociale")
  toponimo                  String?
  indirizzo                 String?
  civico                    String?
  cap                       String?
  citta                     String?
  provincia                 String?
  regione                   String?
  geocodeId                 String?   @map("geocode_id")
  latitudine                Float?
  longitudine               Float?
  indirizzoCompleto         String?   @map("indirizzo_completo")
  geocodingStatus           String?   @map("geocoding_status")
  googlePlaceId             String?   @map("google_place_id")
  googleFormattedAddress    String?   @map("google_formatted_address")
  googlePlaceIdVerified     String?   @map("google_place_id_verified")
  businessNameGoogle        String?   @map("business_name_google")
  businessStatus            String?   @map("business_status")
  businessTypes             String?   @map("business_types")
  addressValidated          String?   @map("address_validated")
  isPermanentlyClosed       Float?    @map("is_permanently_closed")
  hasOpeningHours           Float?    @map("has_opening_hours")
  website                   String?
  phoneNumber               String?   @map("phone_number")
  rating                    Float?
  totalRatings              Float?    @map("total_ratings")
  validationTimestamp       DateTime? @map("validation_timestamp")
  validationStatus          String?   @map("validation_status")
  createdAt                 DateTime  @default(now()) @map("created_at")
  updatedAt                 DateTime  @updatedAt @map("updated_at")

  @@index([piva])
  @@index([citta])
  @@index([provincia])
  @@index([validationStatus])
  @@map("geocoded_companies")
}
