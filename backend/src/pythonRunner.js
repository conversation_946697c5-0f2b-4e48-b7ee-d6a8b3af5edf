const { spawn } = require('child_process');
const path = require('path');

/**
 * Run a Python script with specified arguments
 * 
 * @param {string} scriptPath - Path to the Python script
 * @param {string[]} args - Array of arguments to pass to the script
 * @returns {Promise<{code: number, output: string, error: string}>} - Result of the execution
 */
function runPythonScript(scriptPath, args) {
  return new Promise((resolve, reject) => {
    const pythonPath = process.env.PYTHON_PATH || 'python3';
    const fullScriptPath = path.resolve(scriptPath);
    
    console.log(`Running Python script: ${pythonPath} ${fullScriptPath} ${args.join(' ')}`);
    
    // Pass environment variables to Python process, including GOOGLE_MAPS_API_KEY
    const env = {
      ...process.env,
      GOOGLE_MAPS_API_KEY: process.env.GOOGLE_MAPS_API_KEY
    };
    
    const pyProcess = spawn(pythonPath, [fullScriptPath, ...args], { env });
    
    let output = '';
    let errorOutput = '';
    
    // Capture stdout
    pyProcess.stdout.on('data', (data) => {
      const chunk = data.toString();
      output += chunk;
      console.log(`Python stdout: ${chunk.trim()}`);
    });
    
    // Capture stderr
    pyProcess.stderr.on('data', (data) => {
      const chunk = data.toString();
      errorOutput += chunk;
      console.error(`Python stderr: ${chunk.trim()}`);
    });
    
    // Handle process completion
    pyProcess.on('close', (code) => {
      console.log(`Python process exited with code ${code}`);
      
      if (code === 0) {
        resolve({
          code,
          output,
          error: errorOutput
        });
      } else {
        reject({
          code,
          output,
          error: errorOutput
        });
      }
    });
    
    // Handle spawn errors
    pyProcess.on('error', (err) => {
      console.error('Failed to start Python process:', err);
      reject({
        code: -1,
        output: '',
        error: err.message
      });
    });
  });
}

/**
 * Run a Python script with progress updates via stdout parsing
 * 
 * @param {string} scriptPath - Path to the Python script
 * @param {string[]} args - Array of arguments to pass to the script
 * @param {Function} progressCallback - Callback function for progress updates
 * @returns {Promise<{code: number, output: string, error: string}>} - Result of the execution
 */
function runPythonScriptWithProgress(scriptPath, args, progressCallback) {
  return new Promise((resolve, reject) => {
    const pythonPath = process.env.PYTHON_PATH || 'python3';
    const fullScriptPath = path.resolve(scriptPath);
    
    console.log(`Running Python script with progress: ${pythonPath} ${fullScriptPath} ${args.join(' ')}`);
    
    // Pass environment variables to Python process, including GOOGLE_MAPS_API_KEY
    const env = {
      ...process.env,
      GOOGLE_MAPS_API_KEY: process.env.GOOGLE_MAPS_API_KEY
    };
    
    const pyProcess = spawn(pythonPath, [fullScriptPath, ...args], { env });
    
    let output = '';
    let errorOutput = '';
    
    // Capture stdout and parse progress messages
    pyProcess.stdout.on('data', (data) => {
      const chunk = data.toString();
      output += chunk;
      
      // Split by newlines and process each line
      const lines = chunk.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        try {
          // Try to parse as JSON (progress update)
          const json = JSON.parse(line);
          if (json.type) {
            // This is a progress update
            progressCallback(json);
          }
        } catch (e) {
          // Not JSON, just regular output
          console.log(`Python stdout: ${line}`);
        }
      }
    });
    
    // Capture stderr
    pyProcess.stderr.on('data', (data) => {
      const chunk = data.toString();
      errorOutput += chunk;
      console.error(`Python stderr: ${chunk.trim()}`);
    });
    
    // Handle process completion
    pyProcess.on('close', (code) => {
      console.log(`Python process exited with code ${code}`);
      
      if (code === 0) {
        resolve({
          code,
          output,
          error: errorOutput
        });
      } else {
        reject({
          code,
          output,
          error: errorOutput
        });
      }
    });
    
    // Handle spawn errors
    pyProcess.on('error', (err) => {
      console.error('Failed to start Python process:', err);
      reject({
        code: -1,
        output: '',
        error: err.message
      });
    });
  });
}

module.exports = {
  runPythonScript,
  runPythonScriptWithProgress
};