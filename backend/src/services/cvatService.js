const axios = require('axios');
const FormData = require('form-data');
const { s3Client } = require('../config/s3Config');
const { GetObjectCommand } = require('@aws-sdk/client-s3');

class CVATService {
  constructor() {
    // CVAT is now running separately on Windows PC
    // Set CVAT_URL in environment to connect
    this.baseURL = process.env.CVAT_URL || null;
    this.apiURL = this.baseURL ? `${this.baseURL}/api` : null;
    this.username = process.env.CVAT_USERNAME || 'admin';
    this.password = process.env.CVAT_PASSWORD || 'admin_password';
    this.token = null;
    this.organizationId = null;
    this.isConfigured = !!this.baseURL;
  }

  /**
   * Authenticate with CVAT
   */
  async authenticate() {
    if (!this.isConfigured) {
      throw new Error('CVAT is not configured. Set CVAT_URL environment variable to connect to CVAT server.');
    }
    
    try {
      // First check if CVAT is reachable
      try {
        await axios.get(`${this.baseURL}/api/v2/server/about`, { timeout: 5000 });
      } catch (error) {
        throw new Error('CVAT server is not reachable');
      }
      
      // Try to authenticate
      const response = await axios.post(`${this.apiURL}/auth/login`, {
        username: this.username,
        password: this.password
      });
      
      this.token = response.data.key;
      
      // Get default organization
      const orgsResponse = await axios.get(`${this.apiURL}/organizations`, {
        headers: { 'Authorization': `Token ${this.token}` }
      });
      
      if (orgsResponse.data.results.length > 0) {
        this.organizationId = orgsResponse.data.results[0].id;
      }
      
      return this.token;
    } catch (error) {
      // Don't log the full error to avoid cluttering logs
      if (error.message === 'CVAT server is not reachable') {
        // This is expected when CVAT is starting up
        throw error;
      }
      console.error('CVAT authentication failed:', error.response?.status || error.message);
      throw error;
    }
  }

  /**
   * Get auth headers
   */
  async getAuthHeaders() {
    if (!this.token) {
      await this.authenticate();
    }
    
    return {
      'Authorization': `Token ${this.token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Create a project in CVAT
   */
  async createProject(name, labels) {
    try {
      const headers = await this.getAuthHeaders();
      
      const projectData = {
        name: name,
        labels: labels.map(label => ({
          name: label.name,
          color: label.color || '#FF0000',
          attributes: label.attributes || []
        })),
        organization: this.organizationId
      };
      
      const response = await axios.post(`${this.apiURL}/projects`, projectData, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to create CVAT project:', error.message);
      throw error;
    }
  }

  /**
   * Create a task in CVAT
   */
  async createTask(projectId, name, subset = '') {
    try {
      const headers = await this.getAuthHeaders();
      
      const taskData = {
        name: name,
        project_id: projectId,
        subset: subset,
        organization: this.organizationId
      };
      
      const response = await axios.post(`${this.apiURL}/tasks`, taskData, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to create CVAT task:', error.message);
      throw error;
    }
  }

  /**
   * Upload images to a task from S3
   */
  async uploadImagesToTask(taskId, s3Keys, bucket = 'astrameccanica') {
    try {
      const headers = await this.getAuthHeaders();
      delete headers['Content-Type']; // Let axios set the content type for multipart
      
      // Download images from S3 and prepare for upload
      const formData = new FormData();
      
      for (const s3Key of s3Keys) {
        const command = new GetObjectCommand({
          Bucket: bucket,
          Key: s3Key
        });
        
        const response = await s3Client.send(command);
        const chunks = [];
        
        for await (const chunk of response.Body) {
          chunks.push(chunk);
        }
        
        const buffer = Buffer.concat(chunks);
        const filename = s3Key.split('/').pop();
        
        formData.append('client_files[]', buffer, {
          filename: filename,
          contentType: 'image/jpeg'
        });
      }
      
      formData.append('image_quality', '95');
      
      const uploadResponse = await axios.post(
        `${this.apiURL}/tasks/${taskId}/data`,
        formData,
        {
          headers: {
            ...headers,
            ...formData.getHeaders()
          }
        }
      );
      
      return uploadResponse.data;
    } catch (error) {
      console.error('Failed to upload images to CVAT:', error.message);
      throw error;
    }
  }

  /**
   * Create cloud storage connection
   */
  async createCloudStorage(name, bucket, prefix = '') {
    try {
      const headers = await this.getAuthHeaders();
      
      const storageData = {
        provider_type: 'AWS_S3',
        resource: bucket,
        display_name: name,
        credentials_type: 'KEY_SECRET_PAIR',
        key: process.env.MINIO_ACCESS_KEY,
        secret_key: process.env.MINIO_SECRET_KEY,
        specific_attributes: {
          endpoint_url: 'http://195.32.105.166:9000',
          region: 'us-east-1',
          prefix: prefix
        },
        organization: this.organizationId
      };
      
      const response = await axios.post(`${this.apiURL}/cloudstorages`, storageData, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to create cloud storage:', error.message);
      throw error;
    }
  }

  /**
   * Get annotations for a task
   */
  async getAnnotations(taskId, format = 'CVAT for images 1.1') {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await axios.get(
        `${this.apiURL}/tasks/${taskId}/annotations`,
        {
          headers,
          params: { format }
        }
      );
      
      return response.data;
    } catch (error) {
      console.error('Failed to get annotations:', error.message);
      throw error;
    }
  }

  /**
   * Export annotations in specific format
   */
  async exportAnnotations(taskId, format = 'COCO 1.0') {
    try {
      const headers = await this.getAuthHeaders();
      
      // Start export job
      const exportResponse = await axios.get(
        `${this.apiURL}/tasks/${taskId}/dataset`,
        {
          headers,
          params: { format },
          responseType: 'arraybuffer'
        }
      );
      
      return exportResponse.data;
    } catch (error) {
      console.error('Failed to export annotations:', error.message);
      throw error;
    }
  }

  /**
   * Create AI automatic annotation job
   */
  async createAutoAnnotationJob(taskId, modelName = 'sam') {
    try {
      const headers = await this.getAuthHeaders();
      
      const jobData = {
        task: taskId,
        function: modelName,
        cleanup: false,
        mapping: {
          points_per_side: 32,
          pred_iou_thresh: 0.88,
          stability_score_thresh: 0.95,
          min_mask_region_area: 100
        }
      };
      
      const response = await axios.post(`${this.apiURL}/lambda/requests`, jobData, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to create auto annotation job:', error.message);
      throw error;
    }
  }

  /**
   * Get available AI models
   */
  async getAvailableModels() {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.apiURL}/lambda/functions`, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to get AI models:', error.message);
      throw error;
    }
  }

  /**
   * Create roof annotation project with predefined labels
   */
  async createRoofAnnotationProject() {
    const labels = [
      {
        name: 'roof',
        color: '#FF6B6B',
        attributes: [
          {
            name: 'material',
            mutable: true,
            input_type: 'select',
            values: ['tile', 'metal', 'asphalt', 'concrete', 'slate']
          },
          {
            name: 'condition',
            mutable: true,
            input_type: 'select',
            values: ['excellent', 'good', 'fair', 'poor']
          }
        ]
      },
      {
        name: 'solar_panel',
        color: '#4ECDC4',
        attributes: [
          {
            name: 'type',
            mutable: true,
            input_type: 'select',
            values: ['photovoltaic', 'thermal']
          }
        ]
      },
      {
        name: 'chimney',
        color: '#95E1D3'
      },
      {
        name: 'skylight',
        color: '#F38181'
      }
    ];
    
    return await this.createProject('Roof Detection - AstraMeccanica', labels);
  }

  /**
   * Import map for annotation
   */
  async importMapForAnnotation(mapId, mapData) {
    try {
      // Create task for this specific map
      const project = await this.createRoofAnnotationProject();
      const task = await this.createTask(
        project.id,
        `Map ${mapId} - ${new Date().toLocaleDateString('it-IT')}`
      );
      
      // Upload the optimized image
      const s3Keys = [mapData.storageKey];
      await this.uploadImagesToTask(task.id, s3Keys, mapData.storageBucket);
      
      // Create automatic annotation job with SAM
      await this.createAutoAnnotationJob(task.id, 'sam');
      
      return {
        projectId: project.id,
        taskId: task.id,
        cvatUrl: `${this.baseURL}/tasks/${task.id}`
      };
    } catch (error) {
      console.error('Failed to import map for annotation:', error.message);
      throw error;
    }
  }

  /**
   * Sync annotations back to our database
   */
  async syncAnnotations(taskId, mapId) {
    try {
      // Get annotations from CVAT
      const annotations = await this.getAnnotations(taskId);
      
      // Convert CVAT format to our database format
      const convertedAnnotations = this.convertCVATAnnotations(annotations, mapId);
      
      return convertedAnnotations;
    } catch (error) {
      console.error('Failed to sync annotations:', error.message);
      throw error;
    }
  }

  /**
   * Convert CVAT annotations to our format
   */
  convertCVATAnnotations(cvatData, mapId) {
    const annotations = [];
    
    if (cvatData.shapes) {
      for (const shape of cvatData.shapes) {
        if (shape.type === 'polygon') {
          const annotation = {
            mapId: mapId,
            class: shape.label,
            points: shape.points.map((point, index) => ({
              x: point[index * 2],
              y: point[index * 2 + 1]
            })),
            attributes: {}
          };
          
          // Extract attributes
          for (const attr of shape.attributes) {
            annotation.attributes[attr.spec_id] = attr.value;
          }
          
          annotations.push(annotation);
        }
      }
    }
    
    return annotations;
  }
}

module.exports = new CVATService();