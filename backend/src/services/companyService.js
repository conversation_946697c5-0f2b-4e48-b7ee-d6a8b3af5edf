const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();
const logger = require('../utils/logger');

class CompanyService {
  async createCompany(data) {
    try {
      const company = await prisma.company.create({
        data: {
          name: data.name,
          vatNumber: data.vatNumber,
          taxCode: data.taxCode,
          legalForm: data.legalForm,
          address: data.address,
          city: data.city,
          province: data.province,
          postalCode: data.postalCode,
          country: data.country || 'IT',
          email: data.email,
          phone: data.phone,
          website: data.website,
          description: data.description,
          industry: data.industry,
          foundedYear: data.foundedYear,
          employeesCount: data.employeesCount,
          annualRevenue: data.annualRevenue,
          isActive: data.isActive !== undefined ? data.isActive : true,
          logo: data.logo,
          metadata: data.metadata
        }
      });
      logger.info(`Company created: ${company.id}`);
      return company;
    } catch (error) {
      logger.error('Error creating company:', error);
      throw error;
    }
  }

  async getCompanies(params = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        search,
        city,
        province,
        industry,
        isActive,
        dataSourceId,
        categoryId
      } = params;

      const skip = (page - 1) * limit;
      const where = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { vatNumber: { contains: search, mode: 'insensitive' } },
          { taxCode: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (city) {
        where.city = { contains: city, mode: 'insensitive' };
      }

      if (province) {
        where.province = { equals: province, mode: 'insensitive' };
      }

      if (industry) {
        where.industry = { contains: industry, mode: 'insensitive' };
      }

      if (isActive !== undefined) {
        where.isActive = isActive === 'true' || isActive === true;
      }

      if (dataSourceId) {
        where.dataSourceId = dataSourceId;
      }

      if (categoryId) {
        where.categories = {
          some: {
            categoryId
          }
        };
      }

      const [companies, total] = await Promise.all([
        prisma.company.findMany({
          where,
          skip,
          take: parseInt(limit),
          include: {
            dataSource: true,
            categories: {
              include: {
                category: true
              }
            }
          },
          orderBy: {
            [sortBy]: sortOrder
          }
        }),
        prisma.company.count({ where })
      ]);

      return {
        companies,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error fetching companies:', error);
      throw error;
    }
  }

  async getCompanyById(id) {
    try {
      const company = await prisma.company.findUnique({
        where: { id }
      });
      
      if (!company) {
        throw new Error('Company not found');
      }
      
      return company;
    } catch (error) {
      logger.error(`Error fetching company ${id}:`, error);
      throw error;
    }
  }

  async updateCompany(id, data) {
    try {
      const company = await prisma.company.update({
        where: { id },
        data: {
          name: data.name,
          vatNumber: data.vatNumber,
          taxCode: data.taxCode,
          legalForm: data.legalForm,
          address: data.address,
          city: data.city,
          province: data.province,
          postalCode: data.postalCode,
          country: data.country,
          email: data.email,
          phone: data.phone,
          website: data.website,
          description: data.description,
          industry: data.industry,
          foundedYear: data.foundedYear,
          employeesCount: data.employeesCount,
          annualRevenue: data.annualRevenue,
          isActive: data.isActive,
          logo: data.logo,
          metadata: data.metadata
        }
      });
      logger.info(`Company updated: ${company.id}`);
      return company;
    } catch (error) {
      logger.error(`Error updating company ${id}:`, error);
      throw error;
    }
  }

  async deleteCompany(id) {
    try {
      await prisma.company.delete({
        where: { id }
      });
      logger.info(`Company deleted: ${id}`);
      return { success: true, message: 'Company deleted successfully' };
    } catch (error) {
      logger.error(`Error deleting company ${id}:`, error);
      throw error;
    }
  }

  async searchCompanies(searchTerm) {
    try {
      const companies = await prisma.company.findMany({
        where: {
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' } },
            { vatNumber: { contains: searchTerm, mode: 'insensitive' } },
            { taxCode: { contains: searchTerm, mode: 'insensitive' } },
            { city: { contains: searchTerm, mode: 'insensitive' } },
            { industry: { contains: searchTerm, mode: 'insensitive' } }
          ]
        },
        take: 10
      });
      return companies;
    } catch (error) {
      logger.error('Error searching companies:', error);
      throw error;
    }
  }

  async getCompanyStats() {
    try {
      const [total, active, byIndustry, byProvince] = await Promise.all([
        prisma.company.count(),
        prisma.company.count({ where: { isActive: true } }),
        prisma.company.groupBy({
          by: ['industry'],
          _count: true,
          where: { industry: { not: null } }
        }),
        prisma.company.groupBy({
          by: ['province'],
          _count: true,
          where: { province: { not: null } }
        })
      ]);

      return {
        total,
        active,
        inactive: total - active,
        byIndustry: byIndustry.map(item => ({
          industry: item.industry,
          count: item._count
        })),
        byProvince: byProvince.map(item => ({
          province: item.province,
          count: item._count
        }))
      };
    } catch (error) {
      logger.error('Error fetching company stats:', error);
      throw error;
    }
  }
}

module.exports = new CompanyService();