const axios = require('axios');

/**
 * Convert an address to coordinates using Google Geocoding API
 * @param {string} address - The address to geocode
 * @returns {Promise<{lat: number, lng: number}>} The coordinates
 */
async function geocodeAddress(address) {
  const apiKey = process.env.GOOGLE_MAPS_API_KEY;
  
  if (!apiKey) {
    throw new Error('Google Maps API key not configured');
  }
  
  try {
    const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
      params: {
        address: address,
        key: apiKey
      }
    });
    
    if (response.data.status === 'OK' && response.data.results.length > 0) {
      const location = response.data.results[0].geometry.location;
      return {
        lat: location.lat,
        lng: location.lng,
        formatted_address: response.data.results[0].formatted_address
      };
    } else if (response.data.status === 'ZERO_RESULTS') {
      throw new Error('Nessun risultato trovato per questo indirizzo');
    } else {
      throw new Error(`Errore geocoding: ${response.data.status}`);
    }
  } catch (error) {
    if (error.response) {
      console.error('Geocoding API error:', error.response.data);
      throw new Error('Errore nel servizio di geocoding');
    }
    throw error;
  }
}

/**
 * Calculate bounding box from center coordinates and radius
 * @param {number} lat - Center latitude
 * @param {number} lng - Center longitude
 * @param {number} radiusKm - Radius in kilometers
 * @returns {{min_lon: number, min_lat: number, max_lon: number, max_lat: number}}
 */
function calculateBoundingBox(lat, lng, radiusKm) {
  // Earth's radius in km
  const R = 6371;
  
  // Convert radius to radians
  const radDist = radiusKm / R;
  
  // Convert center point to radians
  const radLat = lat * Math.PI / 180;
  const radLng = lng * Math.PI / 180;
  
  // Calculate min/max latitudes
  const minLat = radLat - radDist;
  const maxLat = radLat + radDist;
  
  // Calculate min/max longitudes
  const deltaLng = Math.asin(Math.sin(radDist) / Math.cos(radLat));
  const minLng = radLng - deltaLng;
  const maxLng = radLng + deltaLng;
  
  // Convert back to degrees
  return {
    min_lon: minLng * 180 / Math.PI,
    min_lat: minLat * 180 / Math.PI,
    max_lon: maxLng * 180 / Math.PI,
    max_lat: maxLat * 180 / Math.PI
  };
}

module.exports = {
  geocodeAddress,
  calculateBoundingBox
};