const pool = require('../lib/db');

/**
 * Get location information (region, province, municipality) from coordinates
 * @param {number} longitude 
 * @param {number} latitude 
 * @returns {Object} Location info with region, province, municipality
 */
async function getLocationFromCoordinates(longitude, latitude) {
  try {
    // Query to find the municipality containing the point
    const municipalityQuery = `
      SELECT 
        m.pro_com as municipality_code,
        m.comune as municipality_name,
        p.cod_uts as province_code,
        p.den_prov as province_name,
        r.cod_reg as region_code,
        r.den_reg as region_name
      FROM municipalities m
      JOIN provinces p ON m.cod_uts = p.cod_uts
      JOIN regions r ON p.cod_reg = r.cod_reg
      WHERE ST_Contains(
        m.geometry, 
        ST_Transform(ST_SetSRID(ST_MakePoint($1, $2), 4326), 32632)
      )
      LIMIT 1;
    `;

    const result = await pool.query(municipalityQuery, [longitude, latitude]);
    
    if (result.rows.length > 0) {
      const location = result.rows[0];
      return {
        region: {
          code: location.region_code,
          name: location.region_name
        },
        province: {
          code: location.province_code,
          name: location.province_name
        },
        municipality: {
          code: location.municipality_code,
          name: location.municipality_name
        }
      };
    }

    // If no municipality found, try to find at least the province
    const provinceQuery = `
      SELECT 
        p.cod_uts as province_code,
        p.den_prov as province_name,
        r.cod_reg as region_code,
        r.den_reg as region_name
      FROM provinces p
      JOIN regions r ON p.cod_reg = r.cod_reg
      WHERE ST_Contains(
        p.geometry, 
        ST_Transform(ST_SetSRID(ST_MakePoint($1, $2), 4326), 32632)
      )
      LIMIT 1;
    `;

    const provinceResult = await pool.query(provinceQuery, [longitude, latitude]);
    
    if (provinceResult.rows.length > 0) {
      const location = provinceResult.rows[0];
      return {
        region: {
          code: location.region_code,
          name: location.region_name
        },
        province: {
          code: location.province_code,
          name: location.province_name
        },
        municipality: null
      };
    }

    // If no province found, try region
    const regionQuery = `
      SELECT 
        r.cod_reg as region_code,
        r.den_reg as region_name
      FROM regions r
      WHERE ST_Contains(
        r.geometry, 
        ST_Transform(ST_SetSRID(ST_MakePoint($1, $2), 4326), 32632)
      )
      LIMIT 1;
    `;

    const regionResult = await pool.query(regionQuery, [longitude, latitude]);
    
    if (regionResult.rows.length > 0) {
      const location = regionResult.rows[0];
      return {
        region: {
          code: location.region_code,
          name: location.region_name
        },
        province: null,
        municipality: null
      };
    }

    // No location found in Italy - check if coordinates are outside Italy
    // Italy roughly spans: Lat 35-47°N, Lon 6-19°E
    if (latitude < 35 || latitude > 47 || longitude < 6 || longitude > 19) {
      console.log(`Coordinates (${longitude}, ${latitude}) are outside Italy`);
      return {
        region: { code: 'INT', name: 'Internazionale' },
        province: { code: 'EXT', name: 'Estero' },
        municipality: { code: 'OUT', name: 'Fuori Italia' }
      };
    }
    
    // If in Italy but no match found
    return {
      region: { code: 'UNK', name: 'Sconosciuta' },
      province: { code: 'UNK', name: 'Sconosciuta' },
      municipality: { code: 'UNK', name: 'Sconosciuto' }
    };

  } catch (error) {
    console.error('Error getting location from coordinates:', error);
    throw error;
  }
}

/**
 * Get location from bounding box center
 * @param {Array} bbox [minLon, minLat, maxLon, maxLat]
 * @returns {Object} Location info
 */
async function getLocationFromBBox(bbox) {
  // Calculate center point of bounding box
  const centerLon = (bbox[0] + bbox[2]) / 2;
  const centerLat = (bbox[1] + bbox[3]) / 2;
  
  return getLocationFromCoordinates(centerLon, centerLat);
}

/**
 * Create S3 path from location info
 * @param {Object} location 
 * @param {string} baseFolder 
 * @returns {string} S3 path
 */
function createS3PathFromLocation(location, baseFolder = 'maps') {
  const parts = [baseFolder];
  
  if (location.region) {
    // Sanitize name for file path
    const regionName = location.region.name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
    parts.push(regionName);
  }
  
  if (location.province) {
    const provinceName = location.province.name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
    parts.push(provinceName);
  }
  
  if (location.municipality) {
    const municipalityName = location.municipality.name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
    parts.push(municipalityName);
  }
  
  // If no location info, use 'unknown' folder
  if (parts.length === 1) {
    parts.push('unknown');
  }
  
  return parts.join('/');
}

module.exports = {
  getLocationFromCoordinates,
  getLocationFromBBox,
  createS3PathFromLocation
};