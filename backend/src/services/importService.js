const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();
const logger = require('../utils/logger');

class ImportService {
  async createImportBatch(dataSourceId) {
    return await prisma.importBatch.create({
      data: {
        dataSourceId,
        status: 'pending',
        startedAt: new Date()
      }
    });
  }

  async updateImportBatch(id, data) {
    return await prisma.importBatch.update({
      where: { id },
      data
    });
  }

  async importCompanies(dataSourceId, companies, batchSize = 100) {
    const importBatch = await this.createImportBatch(dataSourceId);
    const results = {
      success: [],
      failed: [],
      totalRecords: companies.length,
      processedRecords: 0
    };

    try {
      // Process companies in batches
      for (let i = 0; i < companies.length; i += batchSize) {
        const batch = companies.slice(i, i + batchSize);
        
        for (const companyData of batch) {
          try {
            const company = await this.importSingleCompany(
              companyData, 
              dataSourceId, 
              importBatch.id
            );
            results.success.push(company.id);
          } catch (error) {
            logger.error(`Failed to import company: ${companyData.name}`, error);
            results.failed.push({
              name: companyData.name,
              error: error.message
            });
          }
          results.processedRecords++;
        }

        // Update batch progress
        await this.updateImportBatch(importBatch.id, {
          processedRecords: results.processedRecords,
          successRecords: results.success.length,
          failedRecords: results.failed.length
        });
      }

      // Finalize batch
      await this.updateImportBatch(importBatch.id, {
        status: 'completed',
        completedAt: new Date(),
        totalRecords: results.totalRecords,
        processedRecords: results.processedRecords,
        successRecords: results.success.length,
        failedRecords: results.failed.length,
        errors: results.failed.length > 0 ? results.failed : null
      });

    } catch (error) {
      await this.updateImportBatch(importBatch.id, {
        status: 'failed',
        completedAt: new Date(),
        errors: { message: error.message }
      });
      throw error;
    }

    return results;
  }

  async importSingleCompany(data, dataSourceId, importBatchId) {
    const {
      name,
      vatNumber,
      taxCode,
      address,
      city,
      province,
      postalCode,
      country,
      email,
      phone,
      website,
      description,
      industry,
      foundedYear,
      employeesCount,
      latitude,
      longitude,
      categories = [],
      externalId,
      ...metadata
    } = data;

    // Check if company already exists
    let company = null;
    
    if (vatNumber) {
      company = await prisma.company.findUnique({
        where: { vatNumber }
      });
    }
    
    if (!company && taxCode) {
      company = await prisma.company.findUnique({
        where: { taxCode }
      });
    }

    const companyData = {
      name,
      vatNumber,
      taxCode,
      address,
      city,
      province,
      postalCode,
      country: country || 'IT',
      email,
      phone,
      website,
      description,
      industry,
      foundedYear,
      employeesCount,
      latitude,
      longitude,
      dataSourceId,
      importBatchId,
      externalId,
      metadata: Object.keys(metadata).length > 0 ? metadata : null
    };

    if (company) {
      // Update existing company
      company = await prisma.company.update({
        where: { id: company.id },
        data: companyData
      });
    } else {
      // Create new company
      company = await prisma.company.create({
        data: companyData
      });
    }

    // Handle categories
    if (categories.length > 0) {
      await this.assignCategoriesToCompany(company.id, categories, dataSourceId);
    }

    return company;
  }

  async assignCategoriesToCompany(companyId, categoryNames, dataSourceId) {
    const categoryAssignments = [];

    for (const categoryName of categoryNames) {
      // Find or create category
      let category = await prisma.category.findFirst({
        where: { 
          name: categoryName,
          dataSourceId
        }
      });

      if (!category) {
        const slug = categoryName.toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');

        category = await prisma.category.create({
          data: {
            name: categoryName,
            slug,
            dataSourceId
          }
        });
      }

      categoryAssignments.push({
        companyId,
        categoryId: category.id
      });
    }

    if (categoryAssignments.length > 0) {
      await prisma.companyCategory.createMany({
        data: categoryAssignments,
        skipDuplicates: true
      });
    }
  }

  async getImportBatches(dataSourceId = null, limit = 20) {
    const where = {};
    if (dataSourceId) {
      where.dataSourceId = dataSourceId;
    }

    return await prisma.importBatch.findMany({
      where,
      include: {
        dataSource: true,
        _count: {
          select: { companies: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: limit
    });
  }

  async getImportBatchById(id) {
    const batch = await prisma.importBatch.findUnique({
      where: { id },
      include: {
        dataSource: true,
        companies: {
          take: 100
        }
      }
    });

    if (!batch) {
      throw new Error('Import batch not found');
    }

    return batch;
  }
}

module.exports = new ImportService();