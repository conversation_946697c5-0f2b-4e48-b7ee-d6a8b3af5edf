const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();
const logger = require('../utils/logger');

class DataSourceService {
  async getDataSources(filters = {}) {
    try {
      const where = {};
      
      if (filters.isActive !== undefined) {
        where.isActive = filters.isActive === 'true';
      }
      
      if (filters.type) {
        where.type = filters.type;
      }

      const dataSources = await prisma.dataSource.findMany({
        where,
        include: {
          _count: {
            select: { 
              companies: true,
              categories: true,
              importBatches: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return dataSources;
    } catch (error) {
      logger.error('Error fetching data sources:', error);
      throw error;
    }
  }

  async getDataSourceById(id) {
    const dataSource = await prisma.dataSource.findUnique({
      where: { id },
      include: {
        categories: true,
        importBatches: {
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        _count: {
          select: { companies: true }
        }
      }
    });

    if (!dataSource) {
      throw new Error('Data source not found');
    }

    return dataSource;
  }

  async createDataSource(data) {
    const { name, type, url, apiKey, schedule, config } = data;

    return await prisma.dataSource.create({
      data: {
        name,
        type,
        url,
        apiKey,
        schedule,
        config
      }
    });
  }

  async updateDataSource(id, data) {
    const { name, type, url, apiKey, schedule, config, isActive } = data;
    
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (type !== undefined) updateData.type = type;
    if (url !== undefined) updateData.url = url;
    if (apiKey !== undefined) updateData.apiKey = apiKey;
    if (schedule !== undefined) updateData.schedule = schedule;
    if (config !== undefined) updateData.config = config;
    if (isActive !== undefined) updateData.isActive = isActive;

    return await prisma.dataSource.update({
      where: { id },
      data: updateData
    });
  }

  async deleteDataSource(id) {
    // Check if data source has companies
    const dataSource = await prisma.dataSource.findUnique({
      where: { id },
      include: {
        _count: {
          select: { companies: true }
        }
      }
    });

    if (!dataSource) {
      throw new Error('Data source not found');
    }

    if (dataSource._count.companies > 0) {
      throw new Error('Cannot delete data source with associated companies');
    }

    await prisma.dataSource.delete({
      where: { id }
    });

    return { message: 'Data source deleted successfully' };
  }

  async syncDataSource(id) {
    const dataSource = await this.getDataSourceById(id);
    
    if (!dataSource.isActive) {
      throw new Error('Data source is not active');
    }

    // Update last sync time
    await prisma.dataSource.update({
      where: { id },
      data: { lastSyncAt: new Date() }
    });

    // Here you would implement the actual sync logic based on the source type
    // For now, we'll return a placeholder response
    return {
      message: 'Sync initiated',
      dataSourceId: id,
      type: dataSource.type,
      startedAt: new Date()
    };
  }
}

module.exports = new DataSourceService();