const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');
const fs = require('fs').promises;
const sharp = require('sharp');
const { s3Client, bucketConfig } = require('../config/s3Config');
const { PutObjectCommand, GetObjectCommand } = require('@aws-sdk/client-s3');
const prisma = require('../lib/prisma');

const execAsync = promisify(exec);

/**
 * Compression service for optimizing map files
 */
const compressionService = {
  /**
   * Compress a GeoTIFF file using GDAL with various compression methods
   * @param {string} inputPath - Path to input GeoTIFF
   * @param {string} outputPath - Path for compressed output
   * @param {string} compressionType - Type of compression (LZW, DEFLATE, JPEG, WEBP)
   * @param {number} quality - Quality for lossy compression (1-100)
   * @returns {Promise<Object>} Compression results
   */
  compressGeoTIFF: async (inputPath, outputPath, compressionType = 'LZW', quality = 90) => {
    try {
      // Build GDAL command based on compression type
      let gdalCommand = `gdal_translate -of GTiff`;
      
      switch (compressionType.toUpperCase()) {
        case 'LZW':
          // Lossless compression with predictor for better compression
          gdalCommand += ` -co COMPRESS=LZW -co PREDICTOR=2`;
          break;
        case 'DEFLATE':
          // Lossless compression, better than LZW but slower
          gdalCommand += ` -co COMPRESS=DEFLATE -co PREDICTOR=2 -co ZLEVEL=9`;
          break;
        case 'JPEG':
          // Lossy compression, good for aerial imagery
          gdalCommand += ` -co COMPRESS=JPEG -co JPEG_QUALITY=${quality}`;
          break;
        case 'WEBP':
          // Modern lossy/lossless compression
          gdalCommand += ` -co COMPRESS=WEBP -co WEBP_LEVEL=${quality}`;
          break;
        case 'ZSTD':
          // Modern fast compression
          gdalCommand += ` -co COMPRESS=ZSTD -co ZSTD_LEVEL=9`;
          break;
        default:
          throw new Error(`Unsupported compression type: ${compressionType}`);
      }
      
      // Add tiling for better performance with large files
      gdalCommand += ` -co TILED=YES -co BLOCKXSIZE=512 -co BLOCKYSIZE=512`;
      
      // Add overview levels for multi-resolution support
      gdalCommand += ` -co COPY_SRC_OVERVIEWS=YES`;
      
      // Complete command
      gdalCommand += ` "${inputPath}" "${outputPath}"`;
      
      console.log('Executing GDAL command:', gdalCommand);
      
      // Execute compression
      const { stdout, stderr } = await execAsync(gdalCommand);
      
      if (stderr && !stderr.includes('Warning')) {
        throw new Error(`GDAL error: ${stderr}`);
      }
      
      // Get file sizes for comparison
      const inputStats = await fs.stat(inputPath);
      const outputStats = await fs.stat(outputPath);
      
      const compressionRatio = ((1 - outputStats.size / inputStats.size) * 100).toFixed(2);
      
      return {
        success: true,
        originalSize: inputStats.size,
        compressedSize: outputStats.size,
        compressionRatio: `${compressionRatio}%`,
        compressionType,
        quality
      };
      
    } catch (error) {
      console.error('GeoTIFF compression error:', error);
      throw error;
    }
  },

  /**
   * Create cloud-optimized GeoTIFF (COG) for efficient web serving
   * @param {string} inputPath - Path to input GeoTIFF
   * @param {string} outputPath - Path for COG output
   * @returns {Promise<Object>} COG creation results
   */
  createCloudOptimizedGeoTIFF: async (inputPath, outputPath) => {
    try {
      // Use GDAL to create COG with appropriate settings
      const gdalCommand = `gdal_translate -of COG -co COMPRESS=DEFLATE -co PREDICTOR=2 -co BIGTIFF=IF_SAFER -co OVERVIEWS=IGNORE_EXISTING -co BLOCKSIZE=512 -co RESAMPLING=AVERAGE "${inputPath}" "${outputPath}"`;
      
      console.log('Creating COG:', gdalCommand);
      
      const { stdout, stderr } = await execAsync(gdalCommand);
      
      if (stderr && !stderr.includes('Warning')) {
        throw new Error(`GDAL COG error: ${stderr}`);
      }
      
      // Validate COG
      const validateCommand = `rio cogeo validate "${outputPath}"`;
      try {
        await execAsync(validateCommand);
        console.log('COG validation passed');
      } catch (validateError) {
        console.warn('COG validation warning:', validateError.message);
      }
      
      const inputStats = await fs.stat(inputPath);
      const outputStats = await fs.stat(outputPath);
      
      return {
        success: true,
        originalSize: inputStats.size,
        cogSize: outputStats.size,
        compressionRatio: `${((1 - outputStats.size / inputStats.size) * 100).toFixed(2)}%`
      };
      
    } catch (error) {
      console.error('COG creation error:', error);
      throw error;
    }
  },

  /**
   * Convert GeoTIFF to JPEG with world file preservation
   * @param {string} inputPath - Path to input GeoTIFF
   * @param {string} outputPath - Path for JPEG output
   * @param {number} quality - JPEG quality (1-100)
   * @returns {Promise<Object>} Conversion results
   */
  convertToJPEG: async (inputPath, outputPath, quality = 85) => {
    try {
      // Extract georeferencing information
      const gdalInfoCommand = `gdalinfo -json "${inputPath}"`;
      const { stdout: gdalInfo } = await execAsync(gdalInfoCommand);
      const info = JSON.parse(gdalInfo);
      
      // Convert to JPEG using sharp for better quality control
      await sharp(inputPath)
        .jpeg({ quality, mozjpeg: true }) // Use mozjpeg encoder for better compression
        .toFile(outputPath);
      
      // Create world file (.jgw) to preserve georeferencing
      if (info.geoTransform) {
        const worldFilePath = outputPath.replace(/\.[^.]+$/, '.jgw');
        const worldFileContent = [
          info.geoTransform[1],  // pixel size in x direction
          info.geoTransform[2],  // rotation about y axis
          info.geoTransform[4],  // rotation about x axis
          info.geoTransform[5],  // pixel size in y direction
          info.geoTransform[0],  // x coordinate of upper left corner
          info.geoTransform[3]   // y coordinate of upper left corner
        ].join('\n');
        
        await fs.writeFile(worldFilePath, worldFileContent);
      }
      
      const inputStats = await fs.stat(inputPath);
      const outputStats = await fs.stat(outputPath);
      
      return {
        success: true,
        originalSize: inputStats.size,
        jpegSize: outputStats.size,
        compressionRatio: `${((1 - outputStats.size / inputStats.size) * 100).toFixed(2)}%`,
        quality,
        worldFileCreated: true
      };
      
    } catch (error) {
      console.error('JPEG conversion error:', error);
      throw error;
    }
  },

  /**
   * Create progressive JPEG for web optimization
   * @param {string} inputPath - Path to input image
   * @param {string} outputPath - Path for progressive JPEG output
   * @param {number} quality - JPEG quality (1-100)
   * @returns {Promise<Object>} Conversion results
   */
  createProgressiveJPEG: async (inputPath, outputPath, quality = 85) => {
    try {
      await sharp(inputPath)
        .jpeg({ 
          quality, 
          progressive: true,
          mozjpeg: true,
          optimizeScans: true
        })
        .toFile(outputPath);
      
      const inputStats = await fs.stat(inputPath);
      const outputStats = await fs.stat(outputPath);
      
      return {
        success: true,
        originalSize: inputStats.size,
        progressiveSize: outputStats.size,
        compressionRatio: `${((1 - outputStats.size / inputStats.size) * 100).toFixed(2)}%`,
        quality
      };
      
    } catch (error) {
      console.error('Progressive JPEG error:', error);
      throw error;
    }
  },

  /**
   * Generate multiple resolution versions for web display
   * @param {string} inputPath - Path to input image
   * @param {string} outputDir - Directory for output files
   * @returns {Promise<Object>} Generation results
   */
  generateWebVersions: async (inputPath, outputDir) => {
    try {
      const versions = [];
      const resolutions = [
        { suffix: '_thumb', width: 256 },
        { suffix: '_small', width: 512 },
        { suffix: '_medium', width: 1024 },
        { suffix: '_large', width: 2048 },
        { suffix: '_xlarge', width: 4096 }
      ];
      
      await fs.mkdir(outputDir, { recursive: true });
      
      for (const res of resolutions) {
        const outputPath = path.join(outputDir, `image${res.suffix}.jpg`);
        
        await sharp(inputPath)
          .resize(res.width, null, { 
            withoutEnlargement: true,
            kernel: sharp.kernel.lanczos3
          })
          .jpeg({ 
            quality: 85, 
            progressive: true,
            mozjpeg: true 
          })
          .toFile(outputPath);
        
        const stats = await fs.stat(outputPath);
        versions.push({
          resolution: res.width,
          path: outputPath,
          size: stats.size
        });
      }
      
      return {
        success: true,
        versions
      };
      
    } catch (error) {
      console.error('Web version generation error:', error);
      throw error;
    }
  },

  /**
   * Compress and upload to S3
   * @param {string} mapId - Map ID from database
   * @param {string} compressionType - Type of compression
   * @param {number} quality - Compression quality
   * @returns {Promise<Object>} Upload results
   */
  compressAndUploadMap: async (mapId, compressionType = 'LZW', quality = 90) => {
    try {
      // Get map from database
      const prisma = require('../lib/prisma');
      const map = await prisma.map.findUnique({
        where: { id: mapId }
      });
      
      if (!map) {
        throw new Error('Map not found');
      }
      
      // Download original from S3
      const getCommand = new GetObjectCommand({
        Bucket: map.storageBucket,
        Key: map.storageKey
      });
      
      const response = await s3Client.send(getCommand);
      const tempDir = path.join(__dirname, '../../temp');
      await fs.mkdir(tempDir, { recursive: true });
      
      const originalPath = path.join(tempDir, `original_${mapId}.tif`);
      const compressedPath = path.join(tempDir, `compressed_${mapId}.tif`);
      
      // Save original to temp file
      const chunks = [];
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      await fs.writeFile(originalPath, Buffer.concat(chunks));
      
      // Handle different compression types
      let compressionResult;
      let fileBuffer;
      let contentType = 'image/tiff';
      let fileExtension = '.tif';
      
      if (compressionType === 'JPEG') {
        // Convert to JPEG instead of compressing as TIFF
        const jpegPath = path.join(tempDir, `compressed_${mapId}.jpg`);
        compressionResult = await compressionService.convertToJPEG(
          originalPath,
          jpegPath,
          quality
        );
        compressedPath = jpegPath;
        contentType = 'image/jpeg';
        fileExtension = '.jpg';
      } else if (compressionType === 'WEBP') {
        // Convert to WebP using sharp
        const webpPath = path.join(tempDir, `compressed_${mapId}.webp`);
        await sharp(originalPath)
          .webp({ quality })
          .toFile(webpPath);
        
        const inputStats = await fs.stat(originalPath);
        const outputStats = await fs.stat(webpPath);
        
        compressionResult = {
          success: true,
          originalSize: inputStats.size,
          compressedSize: outputStats.size,
          compressionRatio: `${((1 - outputStats.size / inputStats.size) * 100).toFixed(2)}%`,
          quality
        };
        compressedPath = webpPath;
        contentType = 'image/webp';
        fileExtension = '.webp';
      } else {
        // Use standard GeoTIFF compression
        compressionResult = await compressionService.compressGeoTIFF(
          originalPath,
          compressedPath,
          compressionType,
          quality
        );
      }
      
      // Get the original file name from the storage key
      const originalFileName = path.basename(map.storageKey);
      
      // Build compressed key by replacing 'original' with 'compressed' in the path
      let compressedKey;
      if (map.storageKey.includes('/original/')) {
        // Replace original with compressed in the path
        compressedKey = map.storageKey.replace('/original/', '/compressed/');
        // Update filename with new extension
        const baseFileName = originalFileName.replace(/\.(tif|tiff|jpg|jpeg)$/i, '');
        compressedKey = compressedKey.replace(/[^/]+$/, `${baseFileName}_${compressionType.toLowerCase()}${fileExtension}`);
      } else {
        // If not in expected structure, create new structure
        const { getLocationFromBBox, createS3PathFromLocation } = require('./geoLocationService');
        // Reconstruct bbox from boundingBoxNW and boundingBoxSE
        const bbox = [
          map.boundingBoxNW[0], // west/left longitude
          map.boundingBoxSE[1], // south/bottom latitude
          map.boundingBoxSE[0], // east/right longitude
          map.boundingBoxNW[1]  // north/top latitude
        ];
        const location = await getLocationFromBBox(bbox);
        const s3Path = createS3PathFromLocation(location, 'compressed');
        const baseFileName = originalFileName.replace(/\.(tif|tiff|jpg|jpeg)$/i, '');
        const compressedFileName = `${baseFileName}_${compressionType.toLowerCase()}${fileExtension}`;
        compressedKey = `${s3Path}/${compressedFileName}`;
      }
      
      console.log('Uploading compressed file to:', compressedKey);
      
      fileBuffer = await fs.readFile(compressedPath);
      
      const putCommand = new PutObjectCommand({
        Bucket: map.storageBucket,
        Key: compressedKey,
        Body: fileBuffer,
        ContentType: contentType,
        Metadata: {
          'compression-type': compressionType,
          'compression-quality': quality.toString(),
          'original-size': compressionResult.originalSize.toString(),
          'compressed-size': compressionResult.compressedSize.toString(),
          'compression-ratio': compressionResult.compressionRatio,
          'original-key': map.storageKey
        }
      });
      
      await s3Client.send(putCommand);
      
      // Clean up temp files
      await fs.unlink(originalPath);
      await fs.unlink(compressedPath);
      
      return {
        success: true,
        mapId,
        originalKey: map.storageKey,
        compressedKey,
        ...compressionResult
      };
      
    } catch (error) {
      console.error('Compress and upload error:', error);
      throw error;
    }
  },

  /**
   * Compress and upload optimized version for AI processing
   * @param {string} mapId - Map ID from database
   * @param {number} quality - JPEG quality (1-100)
   * @returns {Promise<Object>} Compression result
   */
  async compressAndUploadMapForAI(mapId, quality = 88) {
    try {
      const map = await prisma.map.findUnique({
        where: { id: mapId }
      });
      
      if (!map) {
        throw new Error('Map not found');
      }
      
      // Download original from S3
      const tempDir = path.join(__dirname, '../../temp');
      await fs.mkdir(tempDir, { recursive: true });
      
      const originalPath = path.join(tempDir, `${mapId}_original.tif`);
      const optimizedPath = path.join(tempDir, `${mapId}_ai_optimized.jpg`);
      
      // Download file from S3
      const getCommand = new GetObjectCommand({
        Bucket: map.storageBucket,
        Key: map.storageKey
      });
      
      const response = await s3Client.send(getCommand);
      const chunks = [];
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      await fs.writeFile(originalPath, Buffer.concat(chunks));
      
      // Convert to JPEG with specified quality using sharp
      const sharp = require('sharp');
      const metadata = await sharp(originalPath).metadata();
      
      await sharp(originalPath)
        .jpeg({ 
          quality: quality,
          progressive: true,
          mozjpeg: true // Use mozjpeg encoder for better compression
        })
        .toFile(optimizedPath);
      
      // Get file sizes for comparison
      const originalStats = await fs.stat(originalPath);
      const optimizedStats = await fs.stat(optimizedPath);
      
      // Upload AI-optimized version to S3 with specific naming
      const aiOptimizedKey = map.storageKey.replace(/\.(tif|tiff|jpg|jpeg)$/i, '_ai_optimized.jpg');
      const fileBuffer = await fs.readFile(optimizedPath);
      
      const putCommand = new PutObjectCommand({
        Bucket: map.storageBucket,
        Key: aiOptimizedKey,
        Body: fileBuffer,
        ContentType: 'image/jpeg',
        Metadata: {
          'compression-type': 'JPEG_AI_OPTIMIZED',
          'compression-quality': quality.toString(),
          'original-size': originalStats.size.toString(),
          'compressed-size': optimizedStats.size.toString(),
          'compression-ratio': ((1 - optimizedStats.size / originalStats.size) * 100).toFixed(2) + '%',
          'purpose': 'ai-processing'
        }
      });
      
      await s3Client.send(putCommand);
      
      // Skip world file generation for AI optimization
      // The AI doesn't need georeferencing for roof detection
      
      // Clean up temp files
      await fs.unlink(originalPath);
      await fs.unlink(optimizedPath);
      
      return {
        success: true,
        mapId,
        originalKey: map.storageKey,
        aiOptimizedKey,
        originalSize: originalStats.size,
        optimizedSize: optimizedStats.size,
        compressionRatio: ((1 - optimizedStats.size / originalStats.size) * 100).toFixed(2) + '%',
        quality,
        notes: [
          'File optimized specifically for AI processing',
          'Will be used automatically when running AI detection',
          'Georeferencing preserved via world file'
        ]
      };
      
    } catch (error) {
      console.error('AI optimization error:', error);
      throw error;
    }
  }
};

module.exports = compressionService;