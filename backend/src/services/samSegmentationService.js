const Replicate = require('replicate');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class SAMSegmentationService {
  constructor() {
    // Initialize Replicate client
    this.replicate = new Replicate({
      auth: process.env.REPLICATE_API_TOKEN,
    });
    
    // SAM model version on Replicate
    this.modelVersion = "meta/sam-2:0dff6d6c-06e6-4453-8ae5-6a8285967dd8";
    
    // Local SAM server URL
    this.localSamUrl = process.env.SAM_SERVER_URL || 'http://192.168.0.110:8080';
  }

  /**
   * Segment an image using SAM with automatic mask generation
   * @param {string} imageUrl - URL or base64 of the image
   * @param {Object} options - Segmentation options
   * @returns {Promise<Object>} Segmentation results
   */
  async segmentImageAuto(imageUrl, options = {}) {
    try {
      const input = {
        image: imageUrl,
        points_per_side: options.pointsPerSide || 32,
        points_per_batch: options.pointsPerBatch || 64,
        pred_iou_thresh: options.predIouThresh || 0.88,
        stability_score_thresh: options.stabilityScoreThresh || 0.95,
        stability_score_offset: options.stabilityScoreOffset || 1.0,
        crop_n_layers: options.cropNLayers || 0,
        crop_n_points_downscale_factor: options.cropNPointsDownscaleFactor || 1,
        min_mask_region_area: options.minMaskRegionArea || 0,
      };

      console.log('Starting SAM auto segmentation with input:', input);

      const output = await this.replicate.run(this.modelVersion, { input });

      return {
        success: true,
        masks: output,
        metadata: {
          model: 'SAM-2',
          mode: 'automatic',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('SAM auto segmentation error:', error);
      return {
        success: false,
        error: error.message,
        details: error.response?.data || error
      };
    }
  }

  /**
   * Segment an image using SAM with point prompts
   * @param {string} imageUrl - URL or base64 of the image
   * @param {Array} points - Array of points [{x, y, label}] where label: 1=foreground, 0=background
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Segmentation results
   */
  async segmentImageWithPoints(imageUrl, points, options = {}) {
    try {
      // Format points for SAM (it expects format: [[x1,y1], [x2,y2], ...] and labels: [1,0,...])
      const pointCoords = points.map(p => [p.x, p.y]);
      const pointLabels = points.map(p => p.label || 1);

      const input = {
        image: imageUrl,
        point_coords: pointCoords,
        point_labels: pointLabels,
        multimask_output: options.multimaskOutput !== false, // default true
        return_logits: options.returnLogits || false,
      };

      // Add box prompt if provided
      if (options.box) {
        input.box = options.box; // format: [x1, y1, x2, y2]
      }

      console.log('Starting SAM point-based segmentation with input:', input);

      const output = await this.replicate.run(this.modelVersion, { input });

      return {
        success: true,
        masks: output,
        metadata: {
          model: 'SAM-2',
          mode: 'point-prompted',
          points: points,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('SAM point segmentation error:', error);
      return {
        success: false,
        error: error.message,
        details: error.response?.data || error
      };
    }
  }

  /**
   * Segment an image using SAM with box prompt
   * @param {string} imageUrl - URL or base64 of the image
   * @param {Array} box - Bounding box [x1, y1, x2, y2]
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Segmentation results
   */
  async segmentImageWithBox(imageUrl, box, options = {}) {
    try {
      const input = {
        image: imageUrl,
        box: box, // format: [x1, y1, x2, y2]
        multimask_output: options.multimaskOutput !== false,
        return_logits: options.returnLogits || false,
      };

      console.log('Starting SAM box-based segmentation with input:', input);

      const output = await this.replicate.run(this.modelVersion, { input });

      return {
        success: true,
        masks: output,
        metadata: {
          model: 'SAM-2',
          mode: 'box-prompted',
          box: box,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('SAM box segmentation error:', error);
      return {
        success: false,
        error: error.message,
        details: error.response?.data || error
      };
    }
  }

  /**
   * Process segmentation for roof analysis
   * @param {string} imageUrl - URL of the roof image
   * @param {Object} options - Analysis options
   * @returns {Promise<Object>} Roof segmentation results
   */
  async segmentRoof(imageUrl, options = {}) {
    try {
      // Use automatic segmentation optimized for roof detection
      const segmentOptions = {
        pointsPerSide: 64, // Higher density for detailed roof structures
        predIouThresh: 0.90, // Higher threshold for better quality
        stabilityScoreThresh: 0.92,
        minMaskRegionArea: 100, // Filter out tiny segments
        ...options
      };

      const result = await this.segmentImageAuto(imageUrl, segmentOptions);

      if (result.success && result.masks) {
        // Post-process masks for roof-specific analysis
        const processedMasks = this.processRoofMasks(result.masks);
        
        return {
          ...result,
          roofAnalysis: {
            totalSegments: processedMasks.length,
            largestSegment: processedMasks[0], // Assuming sorted by area
            possiblePanels: this.identifyPossiblePanels(processedMasks),
            roofArea: this.estimateRoofArea(processedMasks)
          }
        };
      }

      return result;
    } catch (error) {
      console.error('Roof segmentation error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process masks for roof-specific features
   * @private
   */
  processRoofMasks(masks) {
    // Filter and sort masks by area
    if (!masks || !Array.isArray(masks)) return [];
    
    return masks
      .filter(mask => {
        // Filter based on mask properties
        // This is a placeholder - actual implementation depends on mask format
        return true;
      })
      .sort((a, b) => {
        // Sort by area (largest first)
        // This is a placeholder - actual implementation depends on mask format
        return 0;
      });
  }

  /**
   * Identify possible solar panels in segmented masks
   * @private
   */
  identifyPossiblePanels(masks) {
    // Analyze masks for rectangular shapes that could be solar panels
    const possiblePanels = [];
    
    // Placeholder logic - would need actual mask analysis
    masks.forEach((mask, index) => {
      // Check if mask has panel-like characteristics
      // - Rectangular shape
      // - Appropriate size
      // - Regular pattern
      possiblePanels.push({
        maskIndex: index,
        confidence: 0.0, // Placeholder
        characteristics: {}
      });
    });
    
    return possiblePanels;
  }

  /**
   * Estimate roof area from masks
   * @private
   */
  estimateRoofArea(masks) {
    // Calculate total area from all roof masks
    // This is a placeholder - actual implementation depends on mask format and image resolution
    return {
      pixelArea: 0,
      estimatedM2: 0,
      confidence: 0.0
    };
  }

  /**
   * Save segmentation results to file
   * @param {Object} results - Segmentation results
   * @param {string} outputDir - Output directory
   * @returns {Promise<string>} Path to saved results
   */
  async saveResults(results, outputDir) {
    try {
      const timestamp = Date.now();
      const filename = `sam_segmentation_${timestamp}.json`;
      const filepath = path.join(outputDir, filename);
      
      await fs.mkdir(outputDir, { recursive: true });
      await fs.writeFile(filepath, JSON.stringify(results, null, 2));
      
      console.log(`Segmentation results saved to: ${filepath}`);
      return filepath;
    } catch (error) {
      console.error('Error saving segmentation results:', error);
      throw error;
    }
  }

  /**
   * Check local SAM server health
   * @returns {Promise<Object>} Health check result
   */
  async checkLocalSamHealth() {
    try {
      const response = await axios.get(`${this.localSamUrl}/health`);
      return {
        success: true,
        ...response.data
      };
    } catch (error) {
      console.error('Local SAM health check failed:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Segment image using local SAM server
   * @param {string} base64Image - Base64 encoded image
   * @param {Array} points - Array of points [[x,y], [x,y]]
   * @returns {Promise<Object>} Segmentation result
   */
  async segmentWithLocalSam(base64Image, points) {
    try {
      const response = await axios.post(`${this.localSamUrl}/segment`, {
        image: base64Image,
        points: points
      });
      
      return {
        success: true,
        masks: response.data.masks,
        scores: response.data.scores,
        metadata: {
          model: 'SAM-Local',
          mode: 'point-prompted',
          timestamp: new Date().toISOString(),
          server: this.localSamUrl
        }
      };
    } catch (error) {
      console.error('Local SAM segmentation failed:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Convert base64 image to URL (if needed for Replicate)
   * @param {string} base64Image - Base64 encoded image
   * @returns {Promise<string>} URL of the image
   */
  async prepareImageUrl(base64Image) {
    // If it's already a URL, return as is
    if (base64Image.startsWith('http://') || base64Image.startsWith('https://')) {
      return base64Image;
    }

    // If it's a base64 string, we need to convert it to a URL
    // Replicate accepts base64 directly with the data URI scheme
    if (base64Image.startsWith('data:image')) {
      return base64Image;
    }

    // Add data URI prefix if it's raw base64
    return `data:image/jpeg;base64,${base64Image}`;
  }
}

module.exports = new SAMSegmentationService();