const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();
const logger = require('../utils/logger');

class CategoryService {
  async getCategories(filters = {}) {
    try {
      const where = {};
      
      if (filters.isActive !== undefined) {
        where.isActive = filters.isActive === 'true';
      }
      
      if (filters.dataSourceId) {
        where.dataSourceId = filters.dataSourceId;
      }
      
      if (filters.parentId) {
        where.parentId = filters.parentId;
      }

      const categories = await prisma.category.findMany({
        where,
        include: {
          dataSource: true,
          parent: true,
          children: true,
          _count: {
            select: { companies: true }
          }
        },
        orderBy: { name: 'asc' }
      });

      return categories;
    } catch (error) {
      logger.error('Error fetching categories:', error);
      throw error;
    }
  }

  async getCategoryById(id) {
    const category = await prisma.category.findUnique({
      where: { id },
      include: {
        dataSource: true,
        parent: true,
        children: true,
        companies: {
          include: {
            company: true
          }
        }
      }
    });

    if (!category) {
      throw new Error('Category not found');
    }

    return category;
  }

  async createCategory(data) {
    const { name, description, parentId, dataSourceId, metadata } = data;
    
    // Generate slug from name
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Check if slug exists
    const existingSlug = await prisma.category.findUnique({
      where: { slug }
    });

    const finalSlug = existingSlug ? `${slug}-${Date.now()}` : slug;

    return await prisma.category.create({
      data: {
        name,
        slug: finalSlug,
        description,
        parentId,
        dataSourceId,
        metadata
      },
      include: {
        dataSource: true,
        parent: true
      }
    });
  }

  async updateCategory(id, data) {
    const { name, description, parentId, dataSourceId, metadata, isActive } = data;
    
    const updateData = {};
    if (name !== undefined) {
      updateData.name = name;
      // Update slug if name changes
      updateData.slug = name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }
    if (description !== undefined) updateData.description = description;
    if (parentId !== undefined) updateData.parentId = parentId;
    if (dataSourceId !== undefined) updateData.dataSourceId = dataSourceId;
    if (metadata !== undefined) updateData.metadata = metadata;
    if (isActive !== undefined) updateData.isActive = isActive;

    return await prisma.category.update({
      where: { id },
      data: updateData,
      include: {
        dataSource: true,
        parent: true
      }
    });
  }

  async deleteCategory(id) {
    // Check if category has companies
    const category = await prisma.category.findUnique({
      where: { id },
      include: {
        _count: {
          select: { companies: true }
        }
      }
    });

    if (!category) {
      throw new Error('Category not found');
    }

    if (category._count.companies > 0) {
      throw new Error('Cannot delete category with associated companies');
    }

    await prisma.category.delete({
      where: { id }
    });

    return { message: 'Category deleted successfully' };
  }

  async assignCompaniesToCategory(categoryId, companyIds) {
    const assignments = companyIds.map(companyId => ({
      categoryId,
      companyId
    }));

    await prisma.companyCategory.createMany({
      data: assignments,
      skipDuplicates: true
    });

    return { message: `${companyIds.length} companies assigned to category` };
  }

  async removeCompaniesFromCategory(categoryId, companyIds) {
    await prisma.companyCategory.deleteMany({
      where: {
        categoryId,
        companyId: { in: companyIds }
      }
    });

    return { message: `${companyIds.length} companies removed from category` };
  }

  async getCategoryTree() {
    const categories = await prisma.category.findMany({
      where: { parentId: null },
      include: {
        children: {
          include: {
            children: true
          }
        }
      }
    });

    return categories;
  }
}

module.exports = new CategoryService();