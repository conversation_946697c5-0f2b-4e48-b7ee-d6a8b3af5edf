const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { s3Client } = require('../config/s3Config');
const { GetObjectCommand } = require('@aws-sdk/client-s3');

class AIService {
  constructor() {
    this.baseURL = process.env.AI_SERVER_URL || 'http://localhost:8001';
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 300000, // 5 minutes timeout for AI operations
      maxContentLength: Infinity,
      maxBodyLength: Infinity
    });
  }

  /**
   * Check if AI server is healthy
   */
  async checkHealth() {
    try {
      const response = await this.client.get('/health');
      return response.data;
    } catch (error) {
      console.error('AI server health check failed:', error.message);
      return { status: 'unhealthy', error: error.message };
    }
  }

  /**
   * Retry wrapper with exponential backoff
   */
  async retryWithBackoff(fn, maxRetries = 3, initialDelay = 1000) {
    let lastError;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        // Don't retry on client errors (4xx)
        if (error.response && error.response.status >= 400 && error.response.status < 500) {
          throw error;
        }
        
        // Calculate delay with exponential backoff
        const delay = initialDelay * Math.pow(2, i);
        console.log(`Retry ${i + 1}/${maxRetries} after ${delay}ms...`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }

  /**
   * Segment roofs in an image
   */
  async segmentRoofs(imageBase64, points = null, boxes = null, useAutoSegment = false) {
    return this.retryWithBackoff(async () => {
      try {
        if (useAutoSegment) {
          // Use automatic segmentation
          const response = await this.client.post('/api/segmentation/auto-segment', 
            new URLSearchParams({
              image: imageBase64,
              points_per_side: 32,
              pred_iou_thresh: 0.88,
              stability_score_thresh: 0.95,
              min_mask_region_area: 100,
              return_polygons: true,
              return_image_size: true  // Request image dimensions in response
            }),
            {
              headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }
          );
          
          // Add image dimensions to response if not present
          if (!response.data.image_width || !response.data.image_height) {
            // Extract dimensions from base64 if needed
            console.log('Warning: AI did not return image dimensions');
          }
          
          return response.data;
        } else {
          // Use prompt-based segmentation
          const params = new URLSearchParams({
            image: imageBase64,
            multimask: true,
            return_polygons: true
          });
          
          if (points) params.append('points', JSON.stringify(points));
          if (boxes) params.append('boxes', JSON.stringify(boxes));
          
          const response = await this.client.post('/api/segmentation/segment', params, {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
          });
          return response.data;
        }
      } catch (error) {
        console.error('Segmentation failed:', error.message);
        throw error;
      }
    });
  }

  /**
   * Classify roof attributes
   */
  async classifyRoof(imageBase64, polygon) {
    try {
      const params = new URLSearchParams({
        image: imageBase64,
        polygon: JSON.stringify(polygon),
        include_confidence: true
      });

      const response = await this.client.post('/api/classification/classify-roof', params, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      });

      return response.data;
    } catch (error) {
      console.error('Classification failed:', error.message);
      throw error;
    }
  }

  /**
   * Get OSM buildings for area
   */
  async getOSMBuildings(bbox) {
    try {
      const [minLon, minLat, maxLon, maxLat] = bbox;
      
      const response = await this.client.get('/api/osm/buildings', {
        params: {
          min_lon: minLon,
          min_lat: minLat,
          max_lon: maxLon,
          max_lat: maxLat
        }
      });

      return response.data;
    } catch (error) {
      console.error('OSM query failed:', error.message);
      throw error;
    }
  }

  /**
   * Start active learning iteration
   */
  async startActiveLearning(batchId, sampleSize = 100, strategy = 'uncertainty') {
    try {
      const response = await this.client.post('/api/active-learning/start-iteration', {
        batch_id: batchId,
        sample_size: sampleSize,
        strategy: strategy,
        confidence_threshold: 0.8
      });

      return response.data;
    } catch (error) {
      console.error('Active learning failed:', error.message);
      throw error;
    }
  }

  /**
   * Convert image file to base64
   */
  async imageToBase64(imagePath) {
    const imageBuffer = await fs.promises.readFile(imagePath);
    return `data:image/jpeg;base64,${imageBuffer.toString('base64')}`;
  }

  /**
   * Get map image from S3 and convert to base64
   */
  async getMapImageFromS3(bucket, key) {
    try {
      const command = new GetObjectCommand({
        Bucket: bucket,
        Key: key
      });
      
      const response = await s3Client.send(command);
      
      // Convert stream to buffer
      const chunks = [];
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      const imageBuffer = Buffer.concat(chunks);
      
      // Determine image type from key extension
      const ext = path.extname(key).toLowerCase();
      const mimeType = ext === '.tif' || ext === '.tiff' ? 'image/tiff' : 'image/jpeg';
      
      return `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
    } catch (error) {
      console.error('Error fetching image from S3:', error);
      throw error;
    }
  }

  /**
   * Process a complete tile for roof detection
   */
  async processTileForRoofs(tilePath, tileInfo) {
    try {
      // Check if file exists
      try {
        await fs.promises.access(tilePath, fs.constants.R_OK);
      } catch (error) {
        throw new Error(`File not found or not readable: ${tilePath}`);
      }
      
      // Convert image to base64
      const imageBase64 = await this.imageToBase64(tilePath);
      
      // 1. Get OSM buildings for the tile area
      let osmData = { buildings: [], num_buildings: 0 };
      try {
        osmData = await this.getOSMBuildings(tileInfo.bbox);
        console.log(`Found ${osmData.num_buildings} OSM buildings`);
      } catch (osmError) {
        console.warn('OSM data fetch failed, continuing without it:', osmError.message);
      }

      // 2. Segment roofs in the tile using auto-segmentation
      const segmentation = await this.segmentRoofs(imageBase64, null, null, true);
      console.log(`Detected ${segmentation.num_masks || 0} potential roofs`);

      // 3. Classify each detected roof
      const classifications = [];
      if (segmentation.masks && Array.isArray(segmentation.masks)) {
        for (const mask of segmentation.masks) {
          if (mask.polygon && mask.polygon.length > 0) {
            try {
              const classification = await this.classifyRoof(imageBase64, mask.polygon);
              classifications.push({
                mask_id: mask.id,
                polygon: mask.polygon,
                area: mask.area,
                ...classification.classification
              });
            } catch (classError) {
              console.warn(`Classification failed for mask ${mask.id}:`, classError.message);
            }
          }
        }
      }

      return {
        tile_id: tileInfo.id,
        osm_buildings: osmData.buildings || [],
        segmentation_masks: segmentation.masks || [],
        classifications: classifications,
        num_roofs_detected: segmentation.num_masks || 0
      };
    } catch (error) {
      console.error('Tile processing failed:', error.message);
      throw error;
    }
  }

  /**
   * Refine mask with user feedback
   */
  async refineMask(imageBase64, currentMask, positivePoints, negativePoints) {
    try {
      const params = new URLSearchParams({
        image: imageBase64,
        mask: currentMask,
        return_polygon: true
      });
      
      if (positivePoints && positivePoints.length > 0) {
        params.append('positive_points', JSON.stringify(positivePoints));
      }
      if (negativePoints && negativePoints.length > 0) {
        params.append('negative_points', JSON.stringify(negativePoints));
      }

      const response = await this.client.post('/api/segmentation/refine-mask', params, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      });

      return response.data;
    } catch (error) {
      console.error('Mask refinement failed:', error.message);
      throw error;
    }
  }

  /**
   * Batch process multiple tiles
   */
  async processBatch(tileIds, progressCallback) {
    const results = [];
    
    for (let i = 0; i < tileIds.length; i++) {
      const tileId = tileIds[i];
      
      try {
        // In production, fetch tile info and path from database
        const tileInfo = {
          id: tileId,
          bbox: [9.18, 45.46, 9.19, 45.47], // Mock bbox
          path: `/path/to/tile/${tileId}.jpg`
        };

        const result = await this.processTileForRoofs(tileInfo.path, tileInfo);
        results.push(result);

        // Report progress
        if (progressCallback) {
          progressCallback({
            current: i + 1,
            total: tileIds.length,
            percentage: ((i + 1) / tileIds.length) * 100,
            currentTile: tileId
          });
        }
      } catch (error) {
        console.error(`Failed to process tile ${tileId}:`, error.message);
        results.push({
          tile_id: tileId,
          error: error.message,
          status: 'failed'
        });
      }
    }

    return results;
  }

  /**
   * Interactive SAM2 segmentation with point prompts
   */
  async segmentWithSAM2(params) {
    return this.retryWithBackoff(async () => {
      try {
        const response = await this.client.post('/api/segmentation/sam2-interactive', 
          new URLSearchParams({
            image: params.image,
            input_points: JSON.stringify(params.input_points),
            input_labels: JSON.stringify(params.input_labels),
            multimask_output: params.multimask_output || false,
            return_polygons: true
          }),
          {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
          }
        );
        
        return response.data;
      } catch (error) {
        console.error('SAM2 segmentation failed:', error.message);
        throw error;
      }
    });
  }
}

module.exports = new AIService();