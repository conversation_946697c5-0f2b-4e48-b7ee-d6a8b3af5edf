const prisma = require('../lib/prisma');
const bcrypt = require('bcryptjs');

/**
 * User service for database operations
 */
const userService = {
  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user
   */
  createUser: async (userData) => {
    const { email, name, password, role = 'user' } = userData;
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    return prisma.user.create({
      data: {
        email,
        name,
        password: hashedPassword,
        role
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true
      }
    });
  },

  /**
   * Get user by ID
   * @param {string} id - User ID
   * @returns {Promise<Object|null>} User or null
   */
  getUserById: async (id) => {
    return prisma.user.findUnique({
      where: { id },
      include: {
        maps: true
      }
    });
  },

  /**
   * Get user by email
   * @param {string} email - User email
   * @returns {Promise<Object|null>} User or null
   */
  getUserByEmail: async (email) => {
    return prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true,
        lastLogin: true
      }
    });
  },

  /**
   * Get user by email with password (for authentication)
   * @param {string} email - User email
   * @returns {Promise<Object|null>} User with password or null
   */
  getUserByEmailWithPassword: async (email) => {
    return prisma.user.findUnique({
      where: { email }
    });
  },

  /**
   * Update last login timestamp
   * @param {string} id - User ID
   * @returns {Promise<Object>} Updated user
   */
  updateLastLogin: async (id) => {
    return prisma.user.update({
      where: { id },
      data: { lastLogin: new Date() }
    });
  },

  /**
   * Update user
   * @param {string} id - User ID
   * @param {Object} userData - User data to update
   * @returns {Promise<Object>} Updated user
   */
  updateUser: async (id, userData) => {
    const updateData = {};
    
    if (userData.email) updateData.email = userData.email;
    if (userData.name) updateData.name = userData.name;
    if (userData.role) updateData.role = userData.role;
    
    if (userData.password) {
      updateData.password = await bcrypt.hash(userData.password, 10);
    }
    
    return prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true,
        lastLogin: true
      }
    });
  },

  /**
   * Delete user
   * @param {string} id - User ID
   * @returns {Promise<Object>} Deleted user
   */
  deleteUser: async (id) => {
    return prisma.user.delete({
      where: { id }
    });
  },

  /**
   * Get all users
   * @returns {Promise<Array>} List of users
   */
  getAllUsers: async () => {
    return prisma.user.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true,
        lastLogin: true
      }
    });
  },

  /**
   * Verify user password
   * @param {string} email - User email
   * @param {string} password - Plain password
   * @returns {Promise<Object|null>} User or null
   */
  verifyPassword: async (email, password) => {
    const user = await prisma.user.findUnique({
      where: { email }
    });
    
    if (!user) return null;
    
    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) return null;
    
    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() }
    });
    
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    };
  }
};

module.exports = userService;