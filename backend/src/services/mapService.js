const prisma = require('../lib/prisma');
const { s3Client, bucketConfig } = require('../config/s3Config');
const { HeadObjectCommand } = require('@aws-sdk/client-s3');

/**
 * Map service for database operations
 */
const mapService = {
  /**
   * Create a new map
   * @param {Object} mapData - Map data
   * @returns {Promise<Object>} Created map
   */
  createMap: async (mapData) => {
    return prisma.map.create({
      data: mapData
    });
  },

  /**
   * Get map by ID
   * @param {string} id - Map ID
   * @returns {Promise<Object|null>} Map or null
   */
  getMapById: async (id) => {
    return prisma.map.findUnique({
      where: { id },
      include: {
        user: true
      }
    });
  },

  /**
   * Get maps by user ID
   * @param {string} userId - User ID
   * @returns {Promise<Array>} List of maps
   */
  getMapsByUserId: async (userId) => {
    return prisma.map.findMany({
      where: {
        userId: userId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  },

  /**
   * Update map
   * @param {string} id - Map ID
   * @param {Object} mapData - Map data to update
   * @returns {Promise<Object>} Updated map
   */
  updateMap: async (id, mapData) => {
    return prisma.map.update({
      where: { id },
      data: mapData
    });
  },

  /**
   * Delete map
   * @param {string} id - Map ID
   * @returns {Promise<Object>} Deleted map
   */
  deleteMap: async (id) => {
    return prisma.map.delete({
      where: { id }
    });
  },

  /**
   * Get all maps
   * @returns {Promise<Array>} List of maps
   */
  getAllMaps: async () => {
    return prisma.map.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
  },

  /**
   * Get maps with file sizes from S3
   * @returns {Promise<Array>} List of maps with size information
   */
  getMapsWithSizes: async () => {
    const maps = await prisma.map.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Add file size information from S3
    const mapsWithSizes = await Promise.all(
      maps.map(async (map) => {
        try {
          const command = new HeadObjectCommand({
            Bucket: bucketConfig.name,
            Key: map.storageKey
          });
          const response = await s3Client.send(command);
          return {
            ...map,
            fileSize: response.ContentLength || 0
          };
        } catch (error) {
          console.error(`Error getting size for ${map.storageKey}:`, error);
          return {
            ...map,
            fileSize: 0
          };
        }
      })
    );

    return mapsWithSizes;
  },

  /**
   * Get storage statistics by zoom level
   * @returns {Promise<Object>} Statistics grouped by zoom level
   */
  getStorageStatistics: async () => {
    const mapsWithSizes = await mapService.getMapsWithSizes();
    
    // Group by zoom level
    const statsByZoom = {};
    
    for (const map of mapsWithSizes) {
      const zoom = map.zoomLevel;
      if (!statsByZoom[zoom]) {
        statsByZoom[zoom] = {
          count: 0,
          totalSize: 0,
          totalArea: 0,
          avgSizePerSqKm: 0,
          maps: []
        };
      }
      
      // Calculate area in square kilometers
      const latDiff = Math.abs(map.boundingBoxSE[1] - map.boundingBoxNW[1]);
      const lonDiff = Math.abs(map.boundingBoxSE[0] - map.boundingBoxNW[0]);
      const avgLat = (map.boundingBoxSE[1] + map.boundingBoxNW[1]) / 2;
      
      // Approximate area calculation (more accurate for small areas)
      const earthRadius = 6371; // km
      const latDistance = latDiff * 111.32; // km per degree
      const lonDistance = lonDiff * 111.32 * Math.cos(avgLat * Math.PI / 180); // km per degree at latitude
      const areaInSqKm = latDistance * lonDistance;
      
      statsByZoom[zoom].count++;
      statsByZoom[zoom].totalSize += map.fileSize;
      statsByZoom[zoom].totalArea += areaInSqKm;
      statsByZoom[zoom].maps.push({
        id: map.id,
        area: areaInSqKm,
        size: map.fileSize,
        sizePerSqKm: areaInSqKm > 0 ? map.fileSize / areaInSqKm : 0
      });
    }
    
    // Calculate averages
    for (const zoom in statsByZoom) {
      const stats = statsByZoom[zoom];
      if (stats.totalArea > 0) {
        stats.avgSizePerSqKm = stats.totalSize / stats.totalArea;
      }
    }
    
    return statsByZoom;
  }
};

module.exports = mapService;