const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();
const { s3Client, bucketConfig, s3Config } = require('../config/s3Config');
const { PutObjectCommand, DeleteObjectCommand, GetObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const crypto = require('crypto');
const sharp = require('sharp');

class SAMImageService {
  constructor() {
    this.bucketName = bucketConfig.name || 'astrameccanica';
    this.imagePrefix = 'sam-images';
    this.thumbnailPrefix = 'sam-thumbnails';
    this.endpoint = s3Config.endpoint || process.env.AWS_ENDPOINT;
  }

  /**
   * Upload an image to S3 and save metadata to database
   */
  async uploadImage({ file, userId, name, description, metadata }) {
    try {
      // Generate unique key for the image
      const imageId = crypto.randomBytes(16).toString('hex');
      const timestamp = Date.now();
      const extension = file.originalname.split('.').pop();
      const s3Key = `${this.imagePrefix}/${userId}/${imageId}_${timestamp}.${extension}`;
      
      // Create thumbnail
      const thumbnailBuffer = await sharp(file.buffer)
        .resize(300, 300, { 
          fit: 'inside',
          withoutEnlargement: true 
        })
        .jpeg({ quality: 80 })
        .toBuffer();
      
      const thumbnailKey = `${this.thumbnailPrefix}/${userId}/${imageId}_${timestamp}_thumb.jpg`;
      
      // Upload original image to S3
      const uploadCommand = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
        Body: file.buffer,
        ContentType: file.mimetype
      });
      await s3Client.send(uploadCommand);
      
      // Upload thumbnail to S3
      const thumbnailCommand = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: thumbnailKey,
        Body: thumbnailBuffer,
        ContentType: 'image/jpeg'
      });
      await s3Client.send(thumbnailCommand);
      
      // Get image dimensions
      const imageMetadata = await sharp(file.buffer).metadata();
      
      // Save to database
      const image = await prisma.samImage.create({
        data: {
          id: imageId,
          userId,
          name,
          description,
          s3Key,
          thumbnailKey,
          url: `${this.endpoint}/${this.bucketName}/${s3Key}`,
          thumbnailUrl: `${this.endpoint}/${this.bucketName}/${thumbnailKey}`,
          size: file.size,
          mimeType: file.mimetype,
          width: imageMetadata.width,
          height: imageMetadata.height,
          metadata: metadata || {},
          createdAt: new Date()
        }
      });
      
      return image;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  }

  /**
   * Get user's image history
   */
  async getUserImageHistory({ userId, limit, offset, sortBy, order }) {
    try {
      const where = { userId };
      
      const [images, total] = await Promise.all([
        prisma.samImage.findMany({
          where,
          take: limit,
          skip: offset,
          orderBy: {
            [sortBy]: order
          },
          include: {
            _count: {
              select: { segmentations: true }
            }
          }
        }),
        prisma.samImage.count({ where })
      ]);
      
      return {
        images,
        total,
        limit,
        offset,
        hasMore: offset + images.length < total
      };
    } catch (error) {
      console.error('Error getting image history:', error);
      throw error;
    }
  }

  /**
   * Get a specific image
   */
  async getImage(imageId, userId) {
    try {
      const image = await prisma.samImage.findFirst({
        where: {
          id: imageId,
          userId
        },
        include: {
          segmentations: {
            orderBy: {
              createdAt: 'desc'
            },
            take: 5
          }
        }
      });
      
      if (image && image.s3Key) {
        // Generate a signed URL for secure access
        const command = new GetObjectCommand({
          Bucket: this.bucketName,
          Key: image.s3Key
        });
        const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 });
        image.signedUrl = signedUrl;
      }
      
      return image;
    } catch (error) {
      console.error('Error getting image:', error);
      throw error;
    }
  }

  /**
   * Delete an image
   */
  async deleteImage(imageId, userId) {
    try {
      // First check if image exists and belongs to user
      const image = await prisma.samImage.findFirst({
        where: {
          id: imageId,
          userId
        }
      });
      
      if (!image) {
        return null;
      }
      
      // Delete from S3
      if (image.s3Key) {
        const deleteCommand = new DeleteObjectCommand({
          Bucket: this.bucketName,
          Key: image.s3Key
        });
        await s3Client.send(deleteCommand);
      }
      if (image.thumbnailKey) {
        const deleteThumbnailCommand = new DeleteObjectCommand({
          Bucket: this.bucketName,
          Key: image.thumbnailKey
        });
        await s3Client.send(deleteThumbnailCommand);
      }
      
      // Delete segmentation masks from S3
      const segmentations = await prisma.samSegmentation.findMany({
        where: { imageId }
      });
      
      for (const seg of segmentations) {
        if (seg.masks && Array.isArray(seg.masks)) {
          for (const mask of seg.masks) {
            if (mask.s3Key) {
              const deleteMaskCommand = new DeleteObjectCommand({
                Bucket: this.bucketName,
                Key: mask.s3Key
              });
              await s3Client.send(deleteMaskCommand);
            }
          }
        }
      }
      
      // Delete from database (cascades to segmentations)
      await prisma.samImage.delete({
        where: { id: imageId }
      });
      
      return true;
    } catch (error) {
      console.error('Error deleting image:', error);
      throw error;
    }
  }

  /**
   * Update image metadata
   */
  async updateImage(imageId, userId, updates) {
    try {
      const image = await prisma.samImage.updateMany({
        where: {
          id: imageId,
          userId
        },
        data: {
          ...(updates.name && { name: updates.name }),
          ...(updates.description !== undefined && { description: updates.description }),
          ...(updates.metadata && { metadata: updates.metadata }),
          updatedAt: new Date()
        }
      });
      
      if (image.count === 0) {
        return null;
      }
      
      return await this.getImage(imageId, userId);
    } catch (error) {
      console.error('Error updating image:', error);
      throw error;
    }
  }

  /**
   * Save segmentation results
   */
  async saveSegmentationResults({ imageId, userId, masks, points, metadata }) {
    try {
      // Verify image ownership
      const image = await prisma.samImage.findFirst({
        where: {
          id: imageId,
          userId
        }
      });
      
      if (!image) {
        throw new Error('Image not found or unauthorized');
      }
      
      // Save masks to S3 if provided as base64
      const processedMasks = [];
      if (masks && Array.isArray(masks)) {
        for (let i = 0; i < masks.length; i++) {
          const mask = masks[i];
          if (mask.data && mask.data.startsWith('data:image')) {
            // Extract base64 data
            const base64Data = mask.data.split(',')[1];
            const buffer = Buffer.from(base64Data, 'base64');
            
            // Generate S3 key for mask
            const maskKey = `${this.imagePrefix}/${userId}/masks/${imageId}_mask_${i}_${Date.now()}.png`;
            
            // Upload to S3
            const uploadMaskCommand = new PutObjectCommand({
              Bucket: this.bucketName,
              Key: maskKey,
              Body: buffer,
              ContentType: 'image/png'
            });
            await s3Client.send(uploadMaskCommand);
            
            processedMasks.push({
              ...mask,
              s3Key: maskKey,
              url: `${this.endpoint}/${this.bucketName}/${maskKey}`
            });
          } else {
            processedMasks.push(mask);
          }
        }
      }
      
      // Save to database
      const segmentation = await prisma.samSegmentation.create({
        data: {
          imageId,
          userId,
          masks: processedMasks,
          points: points || [],
          metadata: metadata || {},
          createdAt: new Date()
        }
      });
      
      return segmentation;
    } catch (error) {
      console.error('Error saving segmentation results:', error);
      throw error;
    }
  }

  /**
   * Get segmentation results for an image
   */
  async getSegmentationResults(imageId, userId) {
    try {
      const results = await prisma.samSegmentation.findMany({
        where: {
          imageId,
          userId
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      
      return results;
    } catch (error) {
      console.error('Error getting segmentation results:', error);
      throw error;
    }
  }
}

module.exports = new SAMImageService();