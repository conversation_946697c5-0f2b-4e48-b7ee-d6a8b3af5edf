const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const userService = require('./userService');

// Get JWT secret from environment or use a default for development
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-this-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

const authService = {
  /**
   * Generate JWT token
   */
  generateToken(user) {
    const payload = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    };
    
    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
  },

  /**
   * Verify JWT token
   */
  verifyToken(token) {
    try {
      return jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return null;
    }
  },

  /**
   * Login user
   */
  async login(email, password) {
    // Get user with password
    const user = await userService.getUserByEmailWithPassword(email);
    if (!user) {
      return { success: false, error: 'Credenziali non valide' };
    }

    // Verify password
    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      return { success: false, error: 'Credenziali non valide' };
    }

    // Update last login
    await userService.updateLastLogin(user.id);

    // Generate token
    const token = this.generateToken(user);

    // Return user data without password
    const { password: _, ...userWithoutPassword } = user;

    return {
      success: true,
      user: userWithoutPassword,
      token
    };
  },

  /**
   * Get user from token
   */
  async getUserFromToken(token) {
    const decoded = this.verifyToken(token);
    if (!decoded) return null;

    return userService.getUserById(decoded.id);
  }
};

module.exports = authService;