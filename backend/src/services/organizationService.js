const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();
const logger = require('../utils/logger');

class OrganizationService {
  async createOrganization(data, userId) {
    try {
      const slug = data.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      const organization = await prisma.organization.create({
        data: {
          name: data.name,
          slug,
          logo: data.logo,
          plan: data.plan || 'base',
          users: {
            create: {
              userId,
              role: 'owner'
            }
          }
        },
        include: {
          users: {
            include: {
              user: true
            }
          }
        }
      });

      logger.info(`Organizzazione creata: ${organization.id}`);
      return organization;
    } catch (error) {
      logger.error('Errore creazione organizzazione:', error);
      throw error;
    }
  }

  async getOrganizations(userId) {
    try {
      const organizations = await prisma.organization.findMany({
        where: {
          users: {
            some: {
              userId
            }
          }
        },
        include: {
          users: {
            where: {
              userId
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      });

      return organizations;
    } catch (error) {
      logger.error('Errore recupero organizzazioni:', error);
      throw error;
    }
  }

  async getOrganizationById(id, userId) {
    try {
      const organization = await prisma.organization.findFirst({
        where: {
          id,
          users: {
            some: {
              userId
            }
          }
        },
        include: {
          users: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          },
          _count: {
            select: {
              companies: true,
              categories: true,
              dataSources: true
            }
          }
        }
      });

      if (!organization) {
        throw new Error('Organizzazione non trovata');
      }

      return organization;
    } catch (error) {
      logger.error(`Errore recupero organizzazione ${id}:`, error);
      throw error;
    }
  }

  async updateOrganization(id, data, userId) {
    try {
      // Verifica che l'utente sia admin o owner
      const userOrg = await prisma.userOrganization.findFirst({
        where: {
          organizationId: id,
          userId,
          role: { in: ['owner', 'admin'] }
        }
      });

      if (!userOrg) {
        throw new Error('Non autorizzato');
      }

      const organization = await prisma.organization.update({
        where: { id },
        data: {
          name: data.name,
          logo: data.logo,
          plan: data.plan,
          isActive: data.isActive
        }
      });

      logger.info(`Organizzazione aggiornata: ${organization.id}`);
      return organization;
    } catch (error) {
      logger.error(`Errore aggiornamento organizzazione ${id}:`, error);
      throw error;
    }
  }

  async deleteOrganization(id, userId) {
    try {
      // Verifica che l'utente sia owner
      const userOrg = await prisma.userOrganization.findFirst({
        where: {
          organizationId: id,
          userId,
          role: 'owner'
        }
      });

      if (!userOrg) {
        throw new Error('Non autorizzato');
      }

      await prisma.organization.delete({
        where: { id }
      });

      logger.info(`Organizzazione eliminata: ${id}`);
      return { success: true, message: 'Organizzazione eliminata con successo' };
    } catch (error) {
      logger.error(`Errore eliminazione organizzazione ${id}:`, error);
      throw error;
    }
  }

  async addUserToOrganization(organizationId, email, role = 'member', requestingUserId) {
    try {
      // Verifica che l'utente richiedente sia admin o owner
      const requesterOrg = await prisma.userOrganization.findFirst({
        where: {
          organizationId,
          userId: requestingUserId,
          role: { in: ['owner', 'admin'] }
        }
      });

      if (!requesterOrg) {
        throw new Error('Non autorizzato');
      }

      // Trova l'utente da aggiungere
      const user = await prisma.user.findUnique({
        where: { email }
      });

      if (!user) {
        throw new Error('Utente non trovato');
      }

      // Aggiungi l'utente all'organizzazione
      const userOrganization = await prisma.userOrganization.create({
        data: {
          userId: user.id,
          organizationId,
          role
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      logger.info(`Utente ${user.id} aggiunto all'organizzazione ${organizationId}`);
      return userOrganization;
    } catch (error) {
      logger.error('Errore aggiunta utente all\'organizzazione:', error);
      throw error;
    }
  }

  async removeUserFromOrganization(organizationId, userId, requestingUserId) {
    try {
      // Verifica che l'utente richiedente sia admin o owner
      const requesterOrg = await prisma.userOrganization.findFirst({
        where: {
          organizationId,
          userId: requestingUserId,
          role: { in: ['owner', 'admin'] }
        }
      });

      if (!requesterOrg) {
        throw new Error('Non autorizzato');
      }

      // Non permettere la rimozione dell'owner
      const targetOrg = await prisma.userOrganization.findFirst({
        where: {
          organizationId,
          userId,
          role: 'owner'
        }
      });

      if (targetOrg) {
        throw new Error('Impossibile rimuovere il proprietario dell\'organizzazione');
      }

      await prisma.userOrganization.delete({
        where: {
          userId_organizationId: {
            userId,
            organizationId
          }
        }
      });

      logger.info(`Utente ${userId} rimosso dall'organizzazione ${organizationId}`);
      return { success: true, message: 'Utente rimosso con successo' };
    } catch (error) {
      logger.error('Errore rimozione utente dall\'organizzazione:', error);
      throw error;
    }
  }

  async updateUserRole(organizationId, userId, newRole, requestingUserId) {
    try {
      // Solo l'owner può cambiare i ruoli
      const requesterOrg = await prisma.userOrganization.findFirst({
        where: {
          organizationId,
          userId: requestingUserId,
          role: 'owner'
        }
      });

      if (!requesterOrg) {
        throw new Error('Solo il proprietario può modificare i ruoli');
      }

      const userOrganization = await prisma.userOrganization.update({
        where: {
          userId_organizationId: {
            userId,
            organizationId
          }
        },
        data: {
          role: newRole
        }
      });

      logger.info(`Ruolo utente ${userId} aggiornato a ${newRole} nell'organizzazione ${organizationId}`);
      return userOrganization;
    } catch (error) {
      logger.error('Errore aggiornamento ruolo utente:', error);
      throw error;
    }
  }
}

module.exports = new OrganizationService();