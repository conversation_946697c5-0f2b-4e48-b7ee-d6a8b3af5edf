const nodemailer = require('nodemailer');
const CryptoJS = require('crypto-js');
const { PrismaClient } = require('../generated/prisma');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

class EmailService {
  constructor() {
    this.encryptionKey = process.env.SMTP_ENCRYPTION_KEY || 'default-encryption-key-change-in-production';
  }

  /**
   * <PERSON><PERSON><PERSON> le credenziali SMTP
   */
  encryptConfig(config) {
    const configString = JSON.stringify(config);
    return CryptoJS.AES.encrypt(configString, this.encryptionKey).toString();
  }

  /**
   * Decripta le credenziali SMTP
   */
  decryptConfig(encryptedConfig) {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedConfig, this.encryptionKey);
      return JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
    } catch (error) {
      logger.error('Errore decriptazione configurazione SMTP:', error);
      return null;
    }
  }

  /**
   * Salva o aggiorna la configurazione SMTP per un'organizzazione
   */
  async saveSmtpConfig(organizationId, config) {
    try {
      // Cripta solo i campi sensibili
      const configToSave = {
        host: config.host,
        port: config.port,
        secure: config.secure,
        from: config.from,
        fromName: config.fromName,
        auth: {
          user: config.auth.user,
          pass: this.encryptPassword(config.auth.pass)
        }
      };

      await prisma.organization.update({
        where: { id: organizationId },
        data: {
          smtpConfig: configToSave
        }
      });

      logger.info(`Configurazione SMTP salvata per organizzazione ${organizationId}`);
      return { success: true };
    } catch (error) {
      logger.error('Errore salvataggio configurazione SMTP:', error);
      throw error;
    }
  }

  /**
   * Cripta solo la password
   */
  encryptPassword(password) {
    return CryptoJS.AES.encrypt(password, this.encryptionKey).toString();
  }

  /**
   * Decripta solo la password
   */
  decryptPassword(encryptedPassword) {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedPassword, this.encryptionKey);
      return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      logger.error('Errore decriptazione password:', error);
      return null;
    }
  }

  /**
   * Ottiene la configurazione SMTP di un'organizzazione
   */
  async getSmtpConfig(organizationId) {
    try {
      const org = await prisma.organization.findUnique({
        where: { id: organizationId },
        select: { smtpConfig: true }
      });

      if (!org?.smtpConfig) {
        return null;
      }

      // Decripta la password
      const config = org.smtpConfig;
      if (config.auth?.pass) {
        config.auth.pass = this.decryptPassword(config.auth.pass);
      }

      return config;
    } catch (error) {
      logger.error('Errore recupero configurazione SMTP:', error);
      throw error;
    }
  }

  /**
   * Crea un transporter per l'invio email
   */
  async createTransporter(organizationId) {
    const config = await this.getSmtpConfig(organizationId);
    
    if (!config) {
      throw new Error('Configurazione SMTP non trovata per questa organizzazione');
    }

    try {
      const transporter = nodemailer.createTransport({
        host: config.host,
        port: config.port,
        secure: config.secure,
        auth: {
          user: config.auth.user,
          pass: config.auth.pass
        }
      });

      // Verifica la connessione
      await transporter.verify();
      
      return transporter;
    } catch (error) {
      logger.error('Errore creazione transporter:', error);
      throw new Error('Impossibile connettersi al server SMTP. Verifica le credenziali.');
    }
  }

  /**
   * Invia un'email
   */
  async sendEmail(organizationId, mailOptions) {
    try {
      const transporter = await this.createTransporter(organizationId);
      const config = await this.getSmtpConfig(organizationId);

      // Imposta il mittente dalle configurazioni
      if (!mailOptions.from && config) {
        mailOptions.from = config.fromName 
          ? `"${config.fromName}" <${config.from}>`
          : config.from;
      }

      const info = await transporter.sendMail(mailOptions);
      
      logger.info(`Email inviata: ${info.messageId}`);
      return {
        success: true,
        messageId: info.messageId,
        accepted: info.accepted,
        rejected: info.rejected
      };
    } catch (error) {
      logger.error('Errore invio email:', error);
      throw error;
    }
  }

  /**
   * Testa la configurazione SMTP
   */
  async testSmtpConfig(config) {
    try {
      const transporter = nodemailer.createTransport({
        host: config.host,
        port: config.port,
        secure: config.secure,
        auth: {
          user: config.auth.user,
          pass: config.auth.pass
        }
      });

      // Verifica la connessione
      await transporter.verify();
      
      // Invia email di test
      const testEmail = await transporter.sendMail({
        from: config.fromName ? `"${config.fromName}" <${config.from}>` : config.from,
        to: config.testEmail || config.from,
        subject: 'Test Configurazione SMTP - AstraMeccanica',
        text: 'Questa è un\'email di test per verificare la configurazione SMTP.',
        html: `
          <div style="font-family: Arial, sans-serif; padding: 20px;">
            <h2>Test Configurazione SMTP</h2>
            <p>Questa è un'email di test inviata da AstraMeccanica.</p>
            <p>Se stai ricevendo questa email, la configurazione SMTP è corretta!</p>
            <hr />
            <p style="color: #666; font-size: 12px;">
              Server: ${config.host}:${config.port}<br />
              Sicurezza: ${config.secure ? 'SSL/TLS' : 'STARTTLS'}<br />
              Data: ${new Date().toLocaleString('it-IT')}
            </p>
          </div>
        `
      });

      logger.info('Test email inviata con successo:', testEmail.messageId);
      return {
        success: true,
        messageId: testEmail.messageId,
        message: 'Email di test inviata con successo'
      };
    } catch (error) {
      logger.error('Errore test configurazione SMTP:', error);
      
      // Messaggi di errore più user-friendly
      let errorMessage = 'Errore nella configurazione SMTP';
      
      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'Impossibile connettersi al server SMTP. Verifica host e porta.';
      } else if (error.code === 'EAUTH') {
        errorMessage = 'Autenticazione fallita. Verifica username e password.';
      } else if (error.code === 'ESOCKET') {
        errorMessage = 'Errore di connessione. Verifica le impostazioni di sicurezza.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      throw new Error(errorMessage);
    }
  }

  /**
   * Invia email di benvenuto
   */
  async sendWelcomeEmail(organizationId, userEmail, userName) {
    const mailOptions = {
      to: userEmail,
      subject: 'Benvenuto in AstraMeccanica!',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Benvenuto in AstraMeccanica!</h1>
          <p>Ciao ${userName},</p>
          <p>Il tuo account è stato creato con successo. Ora puoi accedere a tutte le funzionalità della piattaforma.</p>
          <p>Se hai domande o necessiti di assistenza, non esitare a contattarci.</p>
          <br />
          <p>Il team di AstraMeccanica</p>
        </div>
      `
    };

    return this.sendEmail(organizationId, mailOptions);
  }

  /**
   * Invia email di reset password
   */
  async sendPasswordResetEmail(organizationId, userEmail, resetToken) {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    
    const mailOptions = {
      to: userEmail,
      subject: 'Reset Password - AstraMeccanica',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Reset Password</h1>
          <p>Hai richiesto di reimpostare la tua password.</p>
          <p>Clicca sul link seguente per procedere:</p>
          <a href="${resetUrl}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0;">
            Reimposta Password
          </a>
          <p>Se non hai richiesto tu questa operazione, ignora questa email.</p>
          <p>Il link scadrà tra 1 ora.</p>
          <br />
          <p>Il team di AstraMeccanica</p>
        </div>
      `
    };

    return this.sendEmail(organizationId, mailOptions);
  }
}

module.exports = new EmailService();