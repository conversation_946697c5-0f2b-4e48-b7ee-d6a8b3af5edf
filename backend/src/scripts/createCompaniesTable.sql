-- Create companies table
CREATE TABLE IF NOT EXISTS companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    vat_number VARCHAR(50) UNIQUE,
    tax_code VARCHAR(50) UNIQUE,
    legal_form VARCHAR(100),
    address VARCHAR(255),
    city VARCHAR(100),
    province VARCHAR(10),
    postal_code VARCHAR(10),
    country VARCHAR(2) DEFAULT 'IT',
    email VARCHAR(255),
    phone VARCHAR(50),
    website VARCHAR(255),
    description TEXT,
    industry VARCHAR(100),
    founded_year INTEGER,
    employees_count INTEGER,
    annual_revenue DOUBLE PRECISION,
    is_active BOOLEAN DEFAULT true,
    logo VARCHAR(500),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_vat_number ON companies(vat_number);
CREATE INDEX IF NOT EXISTS idx_companies_tax_code ON companies(tax_code);
CREATE INDEX IF NOT EXISTS idx_companies_city ON companies(city);
CREATE INDEX IF NOT EXISTS idx_companies_province ON companies(province);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_companies_updated_at BEFORE UPDATE
    ON companies FOR EACH ROW EXECUTE PROCEDURE 
    update_updated_at_column();