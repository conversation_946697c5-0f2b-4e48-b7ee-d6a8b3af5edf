#!/usr/bin/env node

/**
 * Import high-quality ISTAT 2025 municipality boundaries into PostGIS database
 * Replaces the old poor-quality boundaries with official ISTAT data
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

const HIGH_QUALITY_DATA_PATH = path.join(__dirname, '../data/high_quality/municipalities_istat_2025.geojson');

// Load environment variables
require('dotenv').config();

// Database configuration from environment
const dbConfig = {
  connectionString: process.env.DATABASE_URL || '**********************************************************************************************************/astrameccanica'
};

async function importISTATBoundaries() {
  console.log('🚀 Starting ISTAT boundaries import process...');
  
  // Check if the GeoJSON file exists
  if (!fs.existsSync(HIGH_QUALITY_DATA_PATH)) {
    throw new Error(`High-quality data file not found: ${HIGH_QUALITY_DATA_PATH}`);
  }
  
  console.log('📁 Found high-quality ISTAT data file');
  
  const client = new Client(dbConfig);
  
  try {
    // Connect to database
    console.log('🔌 Connecting to PostgreSQL database...');
    await client.connect();
    console.log('✅ Connected to database');
    
    // Check if PostGIS is available
    console.log('🗺️  Checking PostGIS extension...');
    const postgisCheck = await client.query("SELECT version() as postgis_version FROM postgis_lib_version()");
    console.log(`✅ PostGIS version: ${postgisCheck.rows[0].postgis_version}`);
    
    // Backup current table by renaming it
    console.log('💾 Backing up current municipalities table...');
    await client.query(`
      DROP TABLE IF EXISTS municipalities_backup;
      ALTER TABLE IF EXISTS municipalities RENAME TO municipalities_backup;
    `);
    console.log('✅ Current table backed up');
    
    // Create new municipalities table with ISTAT schema
    console.log('🏗️  Creating new municipalities table...');
    await client.query(`
      CREATE TABLE municipalities (
        id SERIAL PRIMARY KEY,
        cod_rip INTEGER,
        cod_reg INTEGER,
        cod_prov INTEGER,
        cod_cm INTEGER,
        cod_uts INTEGER,
        pro_com INTEGER,
        pro_com_t VARCHAR(10),
        comune VARCHAR(255),
        comune_a VARCHAR(255),
        cc_uts INTEGER,
        shape_leng NUMERIC,
        shape_area NUMERIC,
        geom GEOMETRY(MultiPolygon, 4326),
        geom_simple GEOMETRY(MultiPolygon, 4326),
        centroid GEOMETRY(Point, 4326),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ New table created');
    
    // Read and parse GeoJSON
    console.log('📖 Reading ISTAT GeoJSON data...');
    const geoJsonData = JSON.parse(fs.readFileSync(HIGH_QUALITY_DATA_PATH, 'utf8'));
    console.log(`📊 Found ${geoJsonData.features.length.toLocaleString()} municipality features`);
    
    // Import features in batches
    const BATCH_SIZE = 100;
    let imported = 0;
    let errors = 0;
    
    console.log('⬆️  Starting batch import...');
    
    for (let i = 0; i < geoJsonData.features.length; i += BATCH_SIZE) {
      const batch = geoJsonData.features.slice(i, i + BATCH_SIZE);
      
      try {
        await client.query('BEGIN');
        
        for (const feature of batch) {
          const props = feature.properties;
          const geom = JSON.stringify(feature.geometry);
          
          // Insert municipality with all ISTAT properties
          await client.query(`
            INSERT INTO municipalities (
              cod_rip, cod_reg, cod_prov, cod_cm, cod_uts,
              pro_com, pro_com_t, comune, comune_a, cc_uts,
              shape_leng, shape_area, geom
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12,
              ST_GeomFromGeoJSON($13)
            )
          `, [
            props.COD_RIP, props.COD_REG, props.COD_PROV, props.COD_CM, props.COD_UTS,
            props.PRO_COM, props.PRO_COM_T, props.COMUNE, props.COMUNE_A, props.CC_UTS,
            props.Shape_Leng, props.Shape_Area, geom
          ]);
          
          imported++;
        }
        
        await client.query('COMMIT');
        
        // Progress update
        const progress = ((i + batch.length) / geoJsonData.features.length * 100).toFixed(1);
        process.stdout.write(`\r⬆️  Imported: ${imported.toLocaleString()} / ${geoJsonData.features.length.toLocaleString()} (${progress}%)`);
        
      } catch (error) {
        await client.query('ROLLBACK');
        console.error(`\n❌ Batch error at position ${i}:`, error.message);
        errors++;
        
        // Continue with next batch despite errors
        if (errors > 10) {
          throw new Error('Too many import errors, aborting');
        }
      }
    }
    
    console.log(`\n✅ Import completed: ${imported.toLocaleString()} municipalities imported`);
    
    // Generate simplified geometries for performance
    console.log('🔧 Generating simplified geometries...');
    await client.query(`
      UPDATE municipalities 
      SET geom_simple = ST_SimplifyPreserveTopology(geom, 0.001)
      WHERE geom_simple IS NULL
    `);
    
    // Generate centroids
    console.log('📍 Computing centroids...');
    await client.query(`
      UPDATE municipalities 
      SET centroid = ST_Centroid(geom)
      WHERE centroid IS NULL
    `);
    
    // Create spatial indexes
    console.log('🗂️  Creating spatial indexes...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_municipalities_geom ON municipalities USING GIST(geom);
      CREATE INDEX IF NOT EXISTS idx_municipalities_geom_simple ON municipalities USING GIST(geom_simple);
      CREATE INDEX IF NOT EXISTS idx_municipalities_centroid ON municipalities USING GIST(centroid);
      CREATE INDEX IF NOT EXISTS idx_municipalities_comune ON municipalities(comune);
      CREATE INDEX IF NOT EXISTS idx_municipalities_pro_com ON municipalities(pro_com);
    `);
    
    // Analyze table for query optimization
    console.log('📊 Analyzing table for query optimization...');
    await client.query('ANALYZE municipalities');
    
    // Generate statistics
    const stats = await client.query(`
      SELECT 
        COUNT(*) as total_municipalities,
        COUNT(DISTINCT cod_reg) as regions,
        COUNT(DISTINCT cod_prov) as provinces,
        MIN(shape_area) as min_area,
        MAX(shape_area) as max_area,
        AVG(shape_area) as avg_area
      FROM municipalities
    `);
    
    console.log('\n📊 Import Statistics:');
    console.log(`  • Total municipalities: ${stats.rows[0].total_municipalities.toLocaleString()}`);
    console.log(`  • Regions: ${stats.rows[0].regions}`);
    console.log(`  • Provinces: ${stats.rows[0].provinces}`);
    console.log(`  • Avg area: ${(stats.rows[0].avg_area / 1000000).toFixed(2)} km²`);
    
    // Test query
    console.log('🧪 Testing search functionality...');
    const testResult = await client.query(`
      SELECT comune, cod_prov, ST_AsText(centroid) as center_point
      FROM municipalities 
      WHERE comune ILIKE '%Milano%'
      LIMIT 3
    `);
    
    console.log('📝 Sample search results:');
    testResult.rows.forEach((row, i) => {
      console.log(`  ${i + 1}. ${row.comune} (Province: ${row.cod_prov})`);
    });
    
    console.log('\n🎉 ISTAT boundaries import completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Test the improved boundaries in the frontend');
    console.log('2. Verify boundary quality and precision');
    console.log('3. Remove backup table if everything works well:');
    console.log('   DROP TABLE municipalities_backup;');
    
  } catch (error) {
    console.error('\n💥 Import failed:', error.message);
    
    // Restore backup if something went wrong
    try {
      console.log('🔄 Attempting to restore backup...');
      await client.query(`
        DROP TABLE IF EXISTS municipalities;
        ALTER TABLE IF EXISTS municipalities_backup RENAME TO municipalities;
      `);
      console.log('✅ Backup restored');
    } catch (restoreError) {
      console.error('❌ Failed to restore backup:', restoreError.message);
    }
    
    throw error;
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

async function main() {
  try {
    await importISTATBoundaries();
  } catch (error) {
    console.error('\n💥 Process failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { importISTATBoundaries };