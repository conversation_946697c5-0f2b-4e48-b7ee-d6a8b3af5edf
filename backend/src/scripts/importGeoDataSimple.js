#!/usr/bin/env node

/**
 * Script to import GeoJSON data (provinces and municipalities) into PostgreSQL database
 * This version uses JSON fields instead of PostGIS
 */

const fs = require('fs');
const path = require('path');
const prisma = require('../lib/prisma');
require('dotenv').config({ path: path.join(__dirname, '../../..', 'config', '.env') });

async function importProvinces() {
  console.log('📍 Importing provinces data...');
  
  const provincesPath = path.join(__dirname, '../data/provinces.geojson');
  
  if (!fs.existsSync(provincesPath)) {
    throw new Error(`Provinces GeoJSON file not found at ${provincesPath}`);
  }
  
  const provincesData = JSON.parse(fs.readFileSync(provincesPath, 'utf8'));
  
  // Clear existing data
  console.log('🗑️ Clearing existing data...');
  await prisma.municipality.deleteMany();
  await prisma.province.deleteMany();
  
  let importedCount = 0;
  
  for (const feature of provincesData.features) {
    const properties = feature.properties;
    const geometry = feature.geometry;
    
    const code = properties.prov_code || properties.code || properties.COD_PROV;
    const name = properties.prov_name || properties.name || properties.DEN_PROV;
    const regionCode = properties.reg_code || properties.region_code || properties.COD_REG;
    const regionName = properties.reg_name || properties.region_name || properties.DEN_REG;
    
    if (!code || !name) {
      console.warn('⚠️ Skipping province with missing code or name:', properties);
      continue;
    }
    
    try {
      await prisma.province.create({
        data: {
          code: code.toString(),
          name: name,
          regionCode: regionCode?.toString() || '',
          regionName: regionName || '',
          geometry: geometry
        }
      });
      
      importedCount++;
      
      if (importedCount % 10 === 0) {
        console.log(`📍 Imported ${importedCount} provinces...`);
      }
    } catch (error) {
      console.error(`❌ Error importing province ${name}:`, error.message);
    }
  }
  
  console.log(`✅ Imported ${importedCount} provinces successfully`);
}

async function importMunicipalities() {
  console.log('🏘️ Importing municipalities data...');
  
  const municipalitiesPath = path.join(__dirname, '../data/municipalities.geojson');
  
  if (!fs.existsSync(municipalitiesPath)) {
    throw new Error(`Municipalities GeoJSON file not found at ${municipalitiesPath}`);
  }
  
  const municipalitiesData = JSON.parse(fs.readFileSync(municipalitiesPath, 'utf8'));
  
  let importedCount = 0;
  let skippedCount = 0;
  
  // Get all province codes to validate foreign keys
  const provinces = await prisma.province.findMany({ select: { code: true } });
  const provinceCodes = new Set(provinces.map(p => p.code));
  
  for (const feature of municipalitiesData.features) {
    const properties = feature.properties;
    const geometry = feature.geometry;
    
    const code = properties.code || properties.com_istat_code || properties.PRO_COM;
    const name = properties.name || properties.comune || properties.com_name || properties.COMUNE;
    const provinceCode = properties.prov_code || properties.prov_istat_code || properties.COD_PROV;
    const regionCode = properties.reg_code || properties.region_code || properties.COD_REG;
    const regionName = properties.reg_name || properties.region_name || properties.DEN_REG;
    
    if (!code || !name || !provinceCode) {
      console.warn('⚠️ Skipping municipality with missing data:', {
        code, name, provinceCode
      });
      skippedCount++;
      continue;
    }
    
    // Check if the province exists
    if (!provinceCodes.has(provinceCode.toString())) {
      console.warn(`⚠️ Skipping municipality ${name} - province ${provinceCode} not found`);
      skippedCount++;
      continue;
    }
    
    try {
      await prisma.municipality.create({
        data: {
          code: code.toString(),
          name: name,
          provinceCode: provinceCode.toString(),
          regionCode: regionCode?.toString() || '',
          regionName: regionName || '',
          geometry: geometry
        }
      });
      
      importedCount++;
      
      if (importedCount % 500 === 0) {
        console.log(`🏘️ Imported ${importedCount} municipalities...`);
      }
    } catch (error) {
      console.error(`❌ Error importing municipality ${name}:`, error.message);
      skippedCount++;
    }
  }
  
  console.log(`✅ Imported ${importedCount} municipalities successfully`);
  if (skippedCount > 0) {
    console.log(`⚠️ Skipped ${skippedCount} municipalities due to errors or missing data`);
  }
}

async function showStatistics() {
  console.log('📊 Database statistics:');
  
  try {
    const provinceCount = await prisma.province.count();
    const municipalityCount = await prisma.municipality.count();
    
    console.log(`📍 Total provinces: ${provinceCount}`);
    console.log(`🏘️ Total municipalities: ${municipalityCount}`);
    
    // Show some sample data
    const sampleProvinces = await prisma.province.findMany({
      select: { code: true, name: true, regionName: true },
      orderBy: { name: 'asc' },
      take: 5
    });
    
    console.log('\n📍 Sample provinces:');
    sampleProvinces.forEach(province => {
      console.log(`  - ${province.name} (${province.code}) - ${province.regionName}`);
    });
    
  } catch (error) {
    console.error('❌ Error getting statistics:', error.message);
  }
}

async function main() {
  console.log('🚀 Starting GeoJSON data import...');
  
  try {
    await importProvinces();
    await importMunicipalities();
    await showStatistics();
    
    console.log('\n🎉 Data import completed successfully!');
  } catch (error) {
    console.error('\n💥 Import failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { main };