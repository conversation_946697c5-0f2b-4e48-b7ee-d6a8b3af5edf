const { PrismaClient } = require('../generated/prisma');
const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');

const execAsync = promisify(exec);
const prisma = new PrismaClient();

const HIGH_QUALITY_DATA_PATH = path.join(__dirname, '../data/high_quality');

async function importShapefile(shapefilePath, tableName, srid = 32632) {
  const dbUrl = process.env.DATABASE_URL;
  const matches = dbUrl.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
  
  if (!matches) {
    throw new Error('Invalid DATABASE_URL format');
  }

  const [, user, password, host, port, database] = matches;
  
  // Use ogr2ogr to import shapefile directly to PostGIS
  // Add -nlt PROMOTE_TO_MULTI to handle multi polygons
  // Add -lco PRECISION=NO to avoid numeric precision issues
  const command = `PGPASSWORD=${password} ogr2ogr -f "PostgreSQL" ` +
    `"PG:host=${host} port=${port} dbname=${database} user=${user}" ` +
    `"${shapefilePath}" -nln ${tableName}_temp -t_srs EPSG:${srid} ` +
    `-nlt PROMOTE_TO_MULTI -lco PRECISION=NO ` +
    `-overwrite -progress`;

  console.log(`Importing ${shapefilePath} to ${tableName}_temp...`);
  
  try {
    const { stdout, stderr } = await execAsync(command);
    if (stdout) console.log(stdout);
    if (stderr && !stderr.includes('Warning')) console.error(stderr);
    console.log(`Successfully imported ${tableName}_temp`);
  } catch (error) {
    console.error(`Error importing ${shapefilePath}:`, error);
    throw error;
  }
}

async function importMunicipalities() {
  console.log('Importing municipalities...');
  
  const shapefilePath = path.join(HIGH_QUALITY_DATA_PATH, 'Com01012025/Com01012025_WGS84.shp');
  await importShapefile(shapefilePath, 'municipalities');
  
  // Transfer data from temp table to Prisma model in batches
  const batchSize = 500;
  const totalCountResult = await prisma.$queryRawUnsafe('SELECT COUNT(*) FROM municipalities_temp');
  const totalCount = parseInt(totalCountResult[0].count);
  
  console.log(`Total municipalities to import: ${totalCount}`);
  
  for (let offset = 0; offset < totalCount; offset += batchSize) {
    console.log(`Importing batch ${offset / batchSize + 1} of ${Math.ceil(totalCount / batchSize)}...`);
    
    await prisma.$executeRawUnsafe(`
      INSERT INTO municipalities (id, cod_rip, cod_reg, cod_prov, cod_cm, cod_uts, pro_com, pro_com_t, comune, comune_a, cc_uts, shape_length, shape_area, geometry)
      SELECT 
        gen_random_uuid(),
        cod_rip::integer,
        cod_reg::integer,
        cod_prov::integer,
        cod_cm::integer,
        cod_uts::integer,
        pro_com::integer,
        pro_com_t,
        comune,
        comune_a,
        cc_uts::integer,
        shape_leng::double precision,
        shape_area::double precision,
        wkb_geometry
      FROM municipalities_temp
      ORDER BY ogc_fid
      LIMIT ${batchSize} OFFSET ${offset}
      ON CONFLICT DO NOTHING
    `);
    
    console.log(`Batch imported successfully`);
  }
  
  await prisma.$executeRawUnsafe('DROP TABLE IF EXISTS municipalities_temp');
  console.log('Municipalities imported successfully');
}

async function main() {
  try {
    console.log('Starting municipality import...');
    
    await importMunicipalities();
    
    // Get counts
    const municipalityCount = await prisma.municipality.count();
    
    console.log(`Import completed successfully!`);
    console.log(`- Municipalities: ${municipalityCount}`);
    
  } catch (error) {
    console.error('Error during import:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch(console.error)
  .finally(() => process.exit());