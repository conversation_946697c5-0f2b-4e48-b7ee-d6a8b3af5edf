#!/usr/bin/env node

/**
 * Import high-quality municipality data to replace existing boundaries
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config({ path: path.join(__dirname, '../../../config/.env') });

const HIGH_QUALITY_FILE = path.join(__dirname, '../data/high_quality/municipalities_high_quality.geojson');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function replaceWithHighQualityData() {
  console.log('🔄 Replacing municipality boundaries with high-quality data...');
  
  if (!fs.existsSync(HIGH_QUALITY_FILE)) {
    throw new Error(`High-quality data not found: ${HIGH_QUALITY_FILE}`);
  }
  
  console.log('📖 Reading high-quality GeoJSON...');
  const geoJsonData = JSON.parse(fs.readFileSync(HIGH_QUALITY_FILE, 'utf8'));
  console.log(`📊 Found ${geoJsonData.features.length} municipalities`);
  
  // Backup existing data
  console.log('💾 Creating backup...');
  try {
    await pool.query('DROP TABLE IF EXISTS municipalities_backup');
    await pool.query('CREATE TABLE municipalities_backup AS SELECT * FROM municipalities');
    console.log('✅ Backup created');
  } catch (error) {
    console.warn('⚠️ Backup failed, continuing:', error.message);
  }
  
  // Clear existing data
  console.log('🗑️ Clearing existing municipalities...');
  await pool.query('DELETE FROM municipalities');
  
  // Import new data
  console.log('📥 Importing high-quality boundaries...');
  
  let imported = 0;
  let skipped = 0;
  
  for (const feature of geoJsonData.features) {
    const props = feature.properties;
    const geometry = feature.geometry;
    
    // Extract fields (different sources use different field names)
    const name = props.name || props.COMUNE || props.comune || props.COM_NAME;
    const code = props.cod_com || props.PRO_COM || props.com_istat_code || props.code;
    let provinceCode = props.cod_prov || props.COD_PROV || props.prov_code;
    const regionName = props.reg_name || props.regione || props.REGIONE;
    
    if (!name || !code) {
      skipped++;
      continue;
    }
    
    // If province code is numeric, try to map to letter code
    if (provinceCode && /^\d+$/.test(provinceCode)) {
      try {
        const result = await pool.query(`
          SELECT code FROM provinces 
          WHERE region_name ILIKE $1 
          LIMIT 1
        `, [`%${regionName || ''}%`]);
        if (result.rows.length > 0) {
          provinceCode = result.rows[0].code;
        }
      } catch (err) {
        // Keep original or default
      }
    }
    
    try {
      await pool.query(`
        INSERT INTO municipalities (code, name, province_code, region_code, region_name, geometry)
        VALUES ($1, $2, $3, $4, $5, ST_SetSRID(ST_GeomFromGeoJSON($6), 4326))
        ON CONFLICT (code) DO UPDATE SET
          name = EXCLUDED.name,
          geometry = EXCLUDED.geometry
      `, [
        code.toString(),
        name,
        provinceCode || 'UNKNOWN',
        '', // region_code
        regionName || '',
        JSON.stringify(geometry)
      ]);
      
      imported++;
      
      if (imported % 500 === 0) {
        console.log(`📥 Imported ${imported} municipalities...`);
      }
      
    } catch (err) {
      console.error(`❌ Error importing ${name}:`, err.message);
      skipped++;
    }
  }
  
  console.log(`✅ Import completed: ${imported} imported, ${skipped} skipped`);
  
  // Verify
  const result = await pool.query('SELECT COUNT(*) FROM municipalities');
  console.log(`📊 Total municipalities in database: ${result.rows[0].count}`);
}

async function main() {
  try {
    await replaceWithHighQualityData();
    console.log('\n🎉 High-quality municipality boundaries imported successfully!');
    console.log('🔄 Refresh the frontend to see improved boundary quality');
  } catch (error) {
    console.error('\n💥 Import failed:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };