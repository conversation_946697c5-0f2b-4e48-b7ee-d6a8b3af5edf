const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();

const testCompanies = [
  {
    name: "AstraMeccanica S.r.l.",
    vatNumber: "*************",
    taxCode: "12345678901",
    legalForm: "S.r.l.",
    address: "Via Roma 123",
    city: "Milano",
    province: "MI",
    postalCode: "20121",
    email: "<EMAIL>",
    phone: "+39 02 1234567",
    website: "https://www.astrameccanica.it",
    description: "Azienda leader nel settore delle coperture e analisi satellitare",
    industry: "Tecnologia",
    foundedYear: 2020,
    employeesCount: 50,
    annualRevenue: 5000000,
    isActive: true
  },
  {
    name: "EdilProgress S.p.A.",
    vatNumber: "*************",
    taxCode: "98765432109",
    legalForm: "S.p.A.",
    address: "Corso Italia 456",
    city: "Roma",
    province: "RM",
    postalCode: "00186",
    email: "<EMAIL>",
    phone: "+39 06 9876543",
    website: "https://www.edilprogress.com",
    description: "Costruzioni e ristrutturazioni edili di alta qualità",
    industry: "Edilizia",
    foundedYear: 2015,
    employeesCount: 120,
    annualRevenue: 15000000,
    isActive: true
  },
  {
    name: "TettoSano Coop",
    vatNumber: "*************",
    taxCode: "11223344556",
    legalForm: "Cooperativa",
    address: "Via Verdi 789",
    city: "Torino",
    province: "TO",
    postalCode: "10121",
    email: "<EMAIL>",
    phone: "+39 011 5544332",
    website: "https://www.tettosano.it",
    description: "Specialisti in manutenzione e riparazione tetti",
    industry: "Manutenzione",
    foundedYear: 2018,
    employeesCount: 35,
    annualRevenue: 3000000,
    isActive: true
  },
  {
    name: "Solar Tech Italia S.r.l.",
    vatNumber: "*************",
    taxCode: "55667788990",
    legalForm: "S.r.l.",
    address: "Via Solare 10",
    city: "Bologna",
    province: "BO",
    postalCode: "40121",
    email: "<EMAIL>",
    phone: "+39 051 2233445",
    website: "https://www.solartechitalia.it",
    description: "Installazione e manutenzione pannelli solari",
    industry: "Energia Rinnovabile",
    foundedYear: 2019,
    employeesCount: 80,
    annualRevenue: 10000000,
    isActive: true
  },
  {
    name: "Coperture Innovative S.n.c.",
    vatNumber: "*************",
    taxCode: "33445566778",
    legalForm: "S.n.c.",
    address: "Via dell'Industria 25",
    city: "Napoli",
    province: "NA",
    postalCode: "80142",
    email: "<EMAIL>",
    phone: "+39 081 7788990",
    website: "https://www.copertureinnovative.it",
    description: "Soluzioni innovative per coperture industriali",
    industry: "Manifattura",
    foundedYear: 2017,
    employeesCount: 25,
    annualRevenue: 2000000,
    isActive: false
  }
];

async function seedCompanies() {
  try {
    console.log('Starting company seed...');
    
    // Clear existing companies
    await prisma.company.deleteMany();
    console.log('Cleared existing companies');
    
    // Insert test companies
    for (const company of testCompanies) {
      const created = await prisma.company.create({
        data: company
      });
      console.log(`Created company: ${created.name}`);
    }
    
    console.log(`Successfully seeded ${testCompanies.length} companies`);
  } catch (error) {
    console.error('Error seeding companies:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedCompanies();