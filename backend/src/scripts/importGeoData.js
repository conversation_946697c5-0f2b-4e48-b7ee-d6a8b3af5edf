#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to import GeoJSON data (provinces and municipalities) into PostgreSQL database
 * This script reads the large GeoJSON files and imports them into PostGIS-enabled tables
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config({ path: path.join(__dirname, '../../..', 'config', '.env') });

// PostgreSQL connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function enablePostGIS() {
  console.log('🔧 Enabling PostGIS extension...');
  
  try {
    await pool.query('CREATE EXTENSION IF NOT EXISTS postgis;');
    console.log('✅ PostGIS extension enabled');
  } catch (error) {
    console.error('❌ Error enabling PostGIS:', error.message);
    throw error;
  }
}

async function createTables() {
  console.log('🗃️ Creating database tables...');
  
  const createTablesSQL = `
    -- Create provinces table
    CREATE TABLE IF NOT EXISTS provinces (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      code VARCHAR(10) UNIQUE NOT NULL,
      name VARCHAR(255) NOT NULL,
      region_code VARCHAR(10) NOT NULL,
      region_name VARCHAR(255) NOT NULL,
      geometry GEOMETRY(MULTIPOLYGON, 4326),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Create municipalities table
    CREATE TABLE IF NOT EXISTS municipalities (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      code VARCHAR(20) UNIQUE NOT NULL,
      name VARCHAR(255) NOT NULL,
      province_code VARCHAR(10) NOT NULL,
      region_code VARCHAR(10) NOT NULL,
      region_name VARCHAR(255) NOT NULL,
      geometry GEOMETRY(MULTIPOLYGON, 4326),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (province_code) REFERENCES provinces(code)
    );

    -- Create indexes for better performance
    CREATE INDEX IF NOT EXISTS idx_provinces_geometry ON provinces USING GIST (geometry);
    CREATE INDEX IF NOT EXISTS idx_municipalities_geometry ON municipalities USING GIST (geometry);
    CREATE INDEX IF NOT EXISTS idx_municipalities_province_code ON municipalities (province_code);
  `;

  try {
    await pool.query(createTablesSQL);
    console.log('✅ Database tables created successfully');
  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
    throw error;
  }
}

async function importProvinces() {
  console.log('📍 Importing provinces data...');
  
  const provincesPath = path.join(__dirname, '../data/provinces.geojson');
  
  if (!fs.existsSync(provincesPath)) {
    throw new Error(`Provinces GeoJSON file not found at ${provincesPath}`);
  }
  
  const provincesData = JSON.parse(fs.readFileSync(provincesPath, 'utf8'));
  
  // Clear existing data
  await pool.query('DELETE FROM municipalities; DELETE FROM provinces;');
  
  let importedCount = 0;
  
  for (const feature of provincesData.features) {
    const properties = feature.properties;
    const geometry = feature.geometry;
    
    const code = properties.prov_acr || properties.prov_code || properties.code || properties.COD_PROV;
    const name = properties.prov_name || properties.name || properties.DEN_PROV;
    const regionCode = properties.reg_istat_code || properties.reg_code || properties.region_code || properties.COD_REG;
    const regionName = properties.reg_name || properties.region_name || properties.DEN_REG;
    
    if (!code || !name) {
      console.warn('⚠️ Skipping province with missing code or name:', properties);
      continue;
    }
    
    try {
      await pool.query(`
        INSERT INTO provinces (code, name, region_code, region_name, geometry)
        VALUES ($1, $2, $3, $4, ST_SetSRID(ST_GeomFromGeoJSON($5), 4326))
        ON CONFLICT (code) DO NOTHING
      `, [code, name, regionCode || '', regionName || '', JSON.stringify(geometry)]);
      
      importedCount++;
      
      if (importedCount % 10 === 0) {
        console.log(`📍 Imported ${importedCount} provinces...`);
      }
    } catch (error) {
      console.error(`❌ Error importing province ${name}:`, error.message);
    }
  }
  
  console.log(`✅ Imported ${importedCount} provinces successfully`);
}

async function importMunicipalities() {
  console.log('🏘️ Importing municipalities data...');
  
  const municipalitiesPath = path.join(__dirname, '../data/municipalities.geojson');
  
  if (!fs.existsSync(municipalitiesPath)) {
    throw new Error(`Municipalities GeoJSON file not found at ${municipalitiesPath}`);
  }
  
  const municipalitiesData = JSON.parse(fs.readFileSync(municipalitiesPath, 'utf8'));
  
  let importedCount = 0;
  let skippedCount = 0;
  
  for (const feature of municipalitiesData.features) {
    const properties = feature.properties;
    const geometry = feature.geometry;
    
    const code = properties.code || properties.com_istat_code || properties.PRO_COM;
    const name = properties.name || properties.comune || properties.com_name || properties.COMUNE;
    const provinceCode = properties.prov_acr || properties.prov_code || properties.prov_istat_code || properties.COD_PROV;
    const regionCode = properties.reg_istat_code || properties.reg_code || properties.region_code || properties.COD_REG;
    const regionName = properties.reg_name || properties.region_name || properties.DEN_REG;
    
    if (!code || !name || !provinceCode) {
      console.warn('⚠️ Skipping municipality with missing data:', properties);
      skippedCount++;
      continue;
    }
    
    try {
      await pool.query(`
        INSERT INTO municipalities (code, name, province_code, region_code, region_name, geometry)
        VALUES ($1, $2, $3, $4, $5, ST_SetSRID(ST_GeomFromGeoJSON($6), 4326))
        ON CONFLICT (code) DO NOTHING
      `, [code, name, provinceCode, regionCode || '', regionName || '', JSON.stringify(geometry)]);
      
      importedCount++;
      
      if (importedCount % 500 === 0) {
        console.log(`🏘️ Imported ${importedCount} municipalities...`);
      }
    } catch (error) {
      console.error(`❌ Error importing municipality ${name}:`, error.message);
      skippedCount++;
    }
  }
  
  console.log(`✅ Imported ${importedCount} municipalities successfully`);
  if (skippedCount > 0) {
    console.log(`⚠️ Skipped ${skippedCount} municipalities due to errors or missing data`);
  }
}

async function showStatistics() {
  console.log('📊 Database statistics:');
  
  try {
    const provinceCount = await pool.query('SELECT COUNT(*) FROM provinces');
    const municipalityCount = await pool.query('SELECT COUNT(*) FROM municipalities');
    
    console.log(`📍 Total provinces: ${provinceCount.rows[0].count}`);
    console.log(`🏘️ Total municipalities: ${municipalityCount.rows[0].count}`);
    
    // Show some sample data
    const sampleProvinces = await pool.query(`
      SELECT code, name, region_name 
      FROM provinces 
      ORDER BY name 
      LIMIT 5
    `);
    
    console.log('\n📍 Sample provinces:');
    sampleProvinces.rows.forEach(row => {
      console.log(`  - ${row.name} (${row.code}) - ${row.region_name}`);
    });
    
  } catch (error) {
    console.error('❌ Error getting statistics:', error.message);
  }
}

async function main() {
  console.log('🚀 Starting GeoJSON data import...');
  
  try {
    await enablePostGIS();
    await createTables();
    await importProvinces();
    await importMunicipalities();
    await showStatistics();
    
    console.log('\n🎉 Data import completed successfully!');
  } catch (error) {
    console.error('\n💥 Import failed:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the import if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { main };