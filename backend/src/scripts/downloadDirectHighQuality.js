#!/usr/bin/env node

/**
 * Direct download of high-quality Italian administrative boundaries
 * Using pre-processed GeoJSON from reliable sources
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

const DATA_DIR = path.join(__dirname, '../data');
const HIGH_QUALITY_DIR = path.join(DATA_DIR, 'high_quality');

// Ensure directories exist
if (!fs.existsSync(HIGH_QUALITY_DIR)) {
  fs.mkdirSync(HIGH_QUALITY_DIR, { recursive: true });
}

// Pre-processed high-quality sources
const QUALITY_SOURCES = {
  // Option 1: ISTAT official data (pre-converted to GeoJSON)
  istat_municipalities: {
    url: 'https://raw.githubusercontent.com/openpolis/geojson-italy/master/geojson/limits_IT_municipalities.geojson',
    filename: 'municipalities_istat_quality.geojson',
    description: 'ISTAT municipalities - High precision boundaries',
    size: '~50MB'
  },
  
  // Option 2: Smaller, optimized version
  istat_municipalities_simplified: {
    url: 'https://raw.githubusercontent.com/openpolis/geojson-italy/master/geojson/limits_IT_municipalities_simplified.geojson',
    filename: 'municipalities_simplified_quality.geojson', 
    description: 'ISTAT municipalities - Optimized for web display',
    size: '~15MB'
  },
  
  // Option 3: Alternative source with good quality
  alternative_municipalities: {
    url: 'https://raw.githubusercontent.com/stefanocudini/italy-geojson/master/italy_municipalities_1000.geojson',
    filename: 'municipalities_alternative.geojson',
    description: 'Alternative source - Good quality boundaries',
    size: '~25MB'
  }
};

function downloadFile(url, filepath, description = '') {
  return new Promise((resolve, reject) => {
    console.log(`📥 Downloading: ${description}`);
    console.log(`🔗 URL: ${url}`);
    
    const file = fs.createWriteStream(filepath);
    const request = https.get(url, (response) => {
      // Handle redirects
      if (response.statusCode === 301 || response.statusCode === 302) {
        console.log(`🔄 Following redirect...`);
        return downloadFile(response.headers.location, filepath, description).then(resolve).catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download: ${response.statusCode} ${response.statusMessage}`));
        return;
      }
      
      const totalSize = parseInt(response.headers['content-length'], 10);
      let downloadedSize = 0;
      let lastProgress = 0;
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        if (totalSize) {
          const progress = Math.floor((downloadedSize / totalSize) * 100);
          if (progress > lastProgress + 5 || progress === 100) { // Update every 5%
            process.stdout.write(`\r📥 Progress: ${progress}% (${(downloadedSize / 1024 / 1024).toFixed(1)}MB)`);
            lastProgress = progress;
          }
        }
      });
      
      response.pipe(file);
    });
    
    file.on('finish', () => {
      file.close();
      console.log(`\n✅ Download completed: ${filepath}`);
      resolve();
    });
    
    request.on('error', (err) => {
      fs.unlink(filepath, () => {}); // Delete partial file
      reject(err);
    });
  });
}

async function validateGeoJSON(filepath) {
  console.log(`🔍 Validating GeoJSON: ${path.basename(filepath)}`);
  
  try {
    const stats = fs.statSync(filepath);
    console.log(`📊 File size: ${(stats.size / 1024 / 1024).toFixed(1)} MB`);
    
    // Read first 1KB to check structure
    const buffer = Buffer.alloc(1024);
    const fd = fs.openSync(filepath, 'r');
    const bytesRead = fs.readSync(fd, buffer, 0, 1024, 0);
    fs.closeSync(fd);
    
    const preview = buffer.slice(0, bytesRead).toString();
    
    if (preview.includes('FeatureCollection') && preview.includes('geometry')) {
      console.log('✅ Valid GeoJSON structure detected');
      
      // Count features by reading the file in chunks
      const content = fs.readFileSync(filepath, 'utf8');
      const matches = content.match(/{"type":"Feature"/g);
      const featureCount = matches ? matches.length : 0;
      
      console.log(`📍 Estimated features: ${featureCount.toLocaleString()}`);
      
      if (featureCount > 7000 && featureCount < 9000) {
        console.log('✅ Feature count looks correct for Italian municipalities');
        return true;
      } else {
        console.log('⚠️ Unexpected feature count - please verify data');
        return false;
      }
    } else {
      console.log('❌ Invalid GeoJSON structure');
      return false;
    }
  } catch (error) {
    console.error('❌ Error validating file:', error.message);
    return false;
  }
}

async function downloadQualityData() {
  console.log('🎯 Downloading high-quality municipality boundaries...');
  
  // Try sources in order of preference
  const sources = [
    QUALITY_SOURCES.istat_municipalities_simplified, // Best balance of quality/size
    QUALITY_SOURCES.istat_municipalities,            // Highest quality
    QUALITY_SOURCES.alternative_municipalities       // Fallback
  ];
  
  for (const source of sources) {
    const filepath = path.join(HIGH_QUALITY_DIR, source.filename);
    
    try {
      console.log(`\n🔄 Trying: ${source.description} (${source.size})`);
      
      // Skip if already downloaded and valid
      if (fs.existsSync(filepath)) {
        console.log('📁 File already exists, checking validity...');
        if (await validateGeoJSON(filepath)) {
          console.log('✅ Existing file is valid');
          return filepath;
        } else {
          console.log('🗑️ Removing invalid file...');
          fs.unlinkSync(filepath);
        }
      }
      
      // Download the file
      await downloadFile(source.url, filepath, source.description);
      
      // Validate the downloaded file
      if (await validateGeoJSON(filepath)) {
        console.log(`\n🎉 Successfully downloaded high-quality data!`);
        console.log(`📁 Location: ${filepath}`);
        return filepath;
      } else {
        console.log('❌ Downloaded file failed validation, trying next source...');
        fs.unlinkSync(filepath);
      }
      
    } catch (error) {
      console.error(`❌ Failed to download ${source.description}:`, error.message);
      console.log('🔄 Trying next source...');
    }
  }
  
  throw new Error('All download sources failed');
}

async function createImportScript(geoJsonPath) {
  const scriptPath = path.join(__dirname, 'importHighQualityData.js');
  
  const scriptContent = `#!/usr/bin/env node

/**
 * Import high-quality municipality data into PostgreSQL
 * Generated automatically by downloadDirectHighQuality.js
 */

const fs = require('fs');
const { Pool } = require('pg');
require('dotenv').config({ path: require('path').join(__dirname, '../../../config/.env') });

const HIGH_QUALITY_FILE = '${geoJsonPath}';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function replaceHighQualityData() {
  console.log('🔄 Replacing municipality data with high-quality boundaries...');
  
  if (!fs.existsSync(HIGH_QUALITY_FILE)) {
    throw new Error(\`High-quality data file not found: \${HIGH_QUALITY_FILE}\`);
  }
  
  const geoJsonData = JSON.parse(fs.readFileSync(HIGH_QUALITY_FILE, 'utf8'));
  console.log(\`📊 Found \${geoJsonData.features.length} municipalities in high-quality data\`);
  
  // Backup existing data (optional)
  console.log('💾 Creating backup of existing municipality data...');
  await pool.query(\`
    CREATE TABLE IF NOT EXISTS municipalities_backup AS 
    SELECT * FROM municipalities
  \`);
  
  // Clear existing municipalities (keep provinces)
  console.log('🗑️ Clearing existing municipality data...');
  await pool.query('DELETE FROM municipalities');
  
  // Import new high-quality data
  console.log('📥 Importing high-quality municipality boundaries...');
  
  let imported = 0;
  let skipped = 0;
  
  for (const feature of geoJsonData.features) {
    const props = feature.properties;
    const geometry = feature.geometry;
    
    // Extract municipality info from different possible field names
    const name = props.name || props.COMUNE || props.comune || props.COM_NAME;
    const code = props.cod_com || props.PRO_COM || props.com_istat_code || props.code;
    const provinceCode = props.cod_prov || props.COD_PROV || props.prov_code || props.provincia;
    const regionName = props.reg_name || props.regione || props.REGIONE;
    
    if (!name || !code) {
      console.warn(\`⚠️ Skipping feature with missing name or code:, \${JSON.stringify(props, null, 2).substring(0, 200)}...\`);
      skipped++;
      continue;
    }
    
    // Find province code from database if not available
    let finalProvinceCode = provinceCode;
    if (!provinceCode && regionName) {
      try {
        const result = await pool.query(\`
          SELECT code FROM provinces 
          WHERE region_name ILIKE $1 
          LIMIT 1
        \`, [\`%\${regionName}%\`]);
        if (result.rows.length > 0) {
          finalProvinceCode = result.rows[0].code;
        }
      } catch (err) {
        // Continue without province code
      }
    }
    
    try {
      await pool.query(\`
        INSERT INTO municipalities (code, name, province_code, region_code, region_name, geometry)
        VALUES ($1, $2, $3, $4, $5, ST_SetSRID(ST_GeomFromGeoJSON($6), 4326))
        ON CONFLICT (code) DO UPDATE SET
          name = EXCLUDED.name,
          geometry = EXCLUDED.geometry
      \`, [
        code.toString(),
        name,
        finalProvinceCode || 'UNKNOWN',
        '', // region_code - will be updated if needed
        regionName || '',
        JSON.stringify(geometry)
      ]);
      
      imported++;
      
      if (imported % 1000 === 0) {
        console.log(\`📥 Imported \${imported} municipalities...\`);
      }
      
    } catch (err) {
      console.error(\`❌ Error importing municipality \${name}:\`, err.message);
      skipped++;
    }
  }
  
  console.log(\`✅ Import completed: \${imported} imported, \${skipped} skipped\`);
  
  // Update statistics
  const count = await pool.query('SELECT COUNT(*) FROM municipalities');
  console.log(\`📊 Total municipalities in database: \${count.rows[0].count}\`);
}

async function main() {
  try {
    await replaceHighQualityData();
    console.log('🎉 High-quality municipality data import completed!');
  } catch (error) {
    console.error('💥 Import failed:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main();
}
`;

  fs.writeFileSync(scriptPath, scriptContent);
  console.log(`📝 Created import script: ${scriptPath}`);
  return scriptPath;
}

async function main() {
  console.log('🚀 Starting high-quality boundary data download...');
  
  try {
    const geoJsonPath = await downloadQualityData();
    const importScript = await createImportScript(geoJsonPath);
    
    console.log('\n🎯 Next steps:');
    console.log('1. Review the downloaded data quality');
    console.log(`2. Run: node ${path.relative(process.cwd(), importScript)}`);
    console.log('3. Check the improved boundaries in the frontend');
    console.log('4. Compare with the previous low-quality data');
    
  } catch (error) {
    console.error('\n💥 Download process failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { downloadQualityData, createImportScript };