#!/usr/bin/env node

/**
 * Direct SQL import of ISTAT 2025 data
 * Uses raw SQL to handle PostGIS geometry types
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL || '**********************************************************************************************************/astrameccanica'
};

async function importProvincesDirectly() {
  console.log('📍 Importing provinces from ISTAT 2025 data using direct SQL...');
  
  const provincesPath = path.join(__dirname, '../data/high_quality/provinces_istat_2025.geojson');
  
  if (!fs.existsSync(provincesPath)) {
    throw new Error(`Provinces ISTAT file not found at ${provincesPath}`);
  }
  
  const provincesData = JSON.parse(fs.readFileSync(provincesPath, 'utf8'));
  const client = new Client(dbConfig);
  await client.connect();
  
  try {
    // Clear existing data
    console.log('🗑️ Clearing existing data...');
    await client.query('DELETE FROM municipalities');
    await client.query('DELETE FROM provinces');
    
    let importedCount = 0;
    let skippedCount = 0;
    
    for (const feature of provincesData.features) {
      const props = feature.properties;
      const geometry = feature.geometry;
      
      // Extract data from ISTAT format
      const code = props.SIGLA || props.sigla_prov;
      const name = props.DEN_PROV || props.DEN_UTS || props.denom_prov;
      const regionCode = props.COD_REG ? props.COD_REG.toString() : '';
      const regionName = props.DEN_REG || props.denom_reg || '';
      
      if (!code || !name) {
        console.warn('⚠️ Skipping province with missing code or name:', props);
        skippedCount++;
        continue;
      }
      
      try {
        await client.query(`
          INSERT INTO provinces (id, code, name, region_code, region_name, geometry)
          VALUES (gen_random_uuid(), $1, $2, $3, $4, ST_GeomFromGeoJSON($5))
          ON CONFLICT (code) DO UPDATE SET
            name = EXCLUDED.name,
            region_code = EXCLUDED.region_code,
            region_name = EXCLUDED.region_name,
            geometry = EXCLUDED.geometry
        `, [
          code,
          name,
          regionCode,
          regionName,
          JSON.stringify(geometry)
        ]);
        
        importedCount++;
        
        if (importedCount % 10 === 0) {
          console.log(`📍 Imported ${importedCount} provinces...`);
        }
      } catch (error) {
        console.error(`❌ Error importing province ${name}:`, error.message);
        skippedCount++;
      }
    }
    
    console.log(`✅ Imported ${importedCount} provinces successfully`);
    if (skippedCount > 0) {
      console.log(`⚠️ Skipped ${skippedCount} provinces`);
    }
    
    return importedCount;
  } finally {
    await client.end();
  }
}

async function importMunicipalitiesDirectly() {
  console.log('🏘️ Importing municipalities from ISTAT 2025 data using direct SQL...');
  
  const municipalitiesPath = path.join(__dirname, '../data/high_quality/municipalities_istat_2025.geojson');
  
  if (!fs.existsSync(municipalitiesPath)) {
    throw new Error(`Municipalities ISTAT file not found at ${municipalitiesPath}`);
  }
  
  const municipalitiesData = JSON.parse(fs.readFileSync(municipalitiesPath, 'utf8'));
  const client = new Client(dbConfig);
  await client.connect();
  
  try {
    // Create province mapping
    const provinceMapping = {};
    const provincesPath = path.join(__dirname, '../data/high_quality/provinces_istat_2025.geojson');
    const provincesData = JSON.parse(fs.readFileSync(provincesPath, 'utf8'));
    
    for (const feature of provincesData.features) {
      const props = feature.properties;
      const numericCode = props.COD_PROV;
      const letterCode = props.SIGLA;
      if (numericCode && letterCode) {
        provinceMapping[numericCode.toString()] = letterCode;
      }
    }
    
    // Get existing provinces
    const existingProvinces = await client.query('SELECT code FROM provinces');
    const provinceCodes = new Set(existingProvinces.rows.map(p => p.code));
    
    let importedCount = 0;
    let skippedCount = 0;
    
    // Process in batches to improve performance
    const batchSize = 100;
    const batches = [];
    
    for (let i = 0; i < municipalitiesData.features.length; i += batchSize) {
      batches.push(municipalitiesData.features.slice(i, i + batchSize));
    }
    
    for (const [batchIndex, batch] of batches.entries()) {
      const validFeatures = [];
      const params = [];
      
      for (const feature of batch) {
        const props = feature.properties;
        const geometry = feature.geometry;
        
        // Extract data from ISTAT format
        const code = props.PRO_COM_T || props.PRO_COM || props.pro_com_t;
        const name = props.COMUNE || props.COMUNE_A || props.comune;
        const numericProvinceCode = props.COD_PROV ? props.COD_PROV.toString() : '';
        const regionCode = props.COD_REG ? props.COD_REG.toString() : '';
        const regionName = props.DEN_REG || '';
        
        // Map numeric province code to letter code
        let provinceCode = provinceMapping[numericProvinceCode] || numericProvinceCode;
        
        if (!code || !name || !provinceCode) {
          skippedCount++;
          continue;
        }
        
        // Check if the province exists
        if (!provinceCodes.has(provinceCode)) {
          skippedCount++;
          continue;
        }
        
        validFeatures.push({code, name, provinceCode, regionCode, regionName, geometry});
      }
      
      if (validFeatures.length > 0) {
        const values = validFeatures.map((_, i) => {
          const idx = i * 6 + 1;
          return `(gen_random_uuid(), $${idx}, $${idx+1}, $${idx+2}, $${idx+3}, $${idx+4}, ST_GeomFromGeoJSON($${idx+5}))`
        }).join(', ');
        
        validFeatures.forEach(f => {
          params.push(f.code.toString(), f.name, f.provinceCode, f.regionCode, f.regionName, JSON.stringify(f.geometry));
        });
        
        try {
          await client.query(`
            INSERT INTO municipalities (id, code, name, province_code, region_code, region_name, geometry)
            VALUES ${values}
            ON CONFLICT (code) DO UPDATE SET
              name = EXCLUDED.name,
              province_code = EXCLUDED.province_code,
              region_code = EXCLUDED.region_code,
              region_name = EXCLUDED.region_name,
              geometry = EXCLUDED.geometry
          `, params);
          
          importedCount += validFeatures.length;
          console.log(`🏘️ Batch ${batchIndex + 1}/${batches.length}: Imported ${importedCount} municipalities...`);
        } catch (error) {
          console.error(`❌ Error in batch ${batchIndex + 1}:`, error.message);
          skippedCount += validFeatures.length;
        }
      }
    }
    
    console.log(`✅ Imported ${importedCount} municipalities successfully`);
    if (skippedCount > 0) {
      console.log(`⚠️ Skipped ${skippedCount} municipalities`);
    }
    
    return importedCount;
  } finally {
    await client.end();
  }
}

async function createSpatialIndexes() {
  console.log('🗂️ Creating spatial indexes...');
  
  const client = new Client(dbConfig);
  await client.connect();
  
  try {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_provinces_geometry ON provinces USING GIST(geometry)',
      'CREATE INDEX IF NOT EXISTS idx_municipalities_geometry ON municipalities USING GIST(geometry)',
      'CREATE INDEX IF NOT EXISTS idx_municipalities_province_code ON municipalities(province_code)',
      'CREATE INDEX IF NOT EXISTS idx_municipalities_name ON municipalities(name)',
      'CREATE INDEX IF NOT EXISTS idx_provinces_name ON provinces(name)',
      'CREATE INDEX IF NOT EXISTS idx_provinces_code ON provinces(code)'
    ];
    
    for (const indexQuery of indexes) {
      await client.query(indexQuery);
      console.log(`✅ ${indexQuery.match(/idx_\w+/)[0]} created`);
    }
    
  } catch (error) {
    console.error('⚠️ Error creating indexes:', error.message);
  } finally {
    await client.end();
  }
}

async function showStatistics() {
  console.log('\n📊 Database statistics:');
  
  const client = new Client(dbConfig);
  await client.connect();
  
  try {
    const stats = await client.query(`
      SELECT 
        (SELECT COUNT(*) FROM provinces) as provinces,
        (SELECT COUNT(*) FROM municipalities) as municipalities
    `);
    
    console.log(`📍 Total provinces: ${stats.rows[0].provinces}`);
    console.log(`🏘️ Total municipalities: ${stats.rows[0].municipalities}`);
    
    // Test Piacenza
    const piacenza = await client.query(`
      SELECT p.code, p.name, COUNT(m.id) as municipality_count
      FROM provinces p
      LEFT JOIN municipalities m ON p.code = m.province_code
      WHERE p.code = 'PC'
      GROUP BY p.code, p.name
    `);
    
    if (piacenza.rows.length > 0) {
      console.log(`\n🧪 Test - ${piacenza.rows[0].name} (${piacenza.rows[0].code}): ${piacenza.rows[0].municipality_count} municipalities`);
    }
    
    // Show regions summary
    const regionSummary = await client.query(`
      SELECT region_name, COUNT(DISTINCT code) as province_count
      FROM provinces
      WHERE region_name != ''
      GROUP BY region_name
      ORDER BY region_name
    `);
    
    console.log('\n📍 Provinces by region:');
    regionSummary.rows.forEach(region => {
      console.log(`  - ${region.region_name}: ${region.province_count} provinces`);
    });
    
  } finally {
    await client.end();
  }
}

async function main() {
  console.log('🚀 Starting ISTAT 2025 direct SQL import...');
  
  try {
    const provinceCount = await importProvincesDirectly();
    
    if (provinceCount > 0) {
      const municipalityCount = await importMunicipalitiesDirectly();
      
      if (municipalityCount > 0) {
        await createSpatialIndexes();
      }
    }
    
    await showStatistics();
    
    console.log('\n🎉 ISTAT 2025 data import completed successfully!');
    console.log('🔄 The frontend should now show all provinces and municipalities');
    
  } catch (error) {
    console.error('\n💥 Import failed:', error);
    console.error(error.stack);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };