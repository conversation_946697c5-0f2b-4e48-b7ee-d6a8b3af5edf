#!/usr/bin/env node

/**
 * Download municipality boundaries directly from ISTAT (official Italian statistics office)
 * This is the most authoritative source for Italian administrative boundaries
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

const DATA_DIR = path.join(__dirname, '../data/high_quality');

// Ensure directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Official ISTAT sources for 2025 - using the specific URL provided by user
const ISTAT_SOURCES = [
  {
    url: 'https://www.istat.it/storage/cartografia/confini_amministrativi/non_generalizzati/2025/Limiti01012025.zip',
    filename: 'ISTAT_2025_detailed.zip',
    description: 'ISTAT 2025 Administrative Boundaries (Non-generalized, Most Detailed)'
  }
];

function downloadFile(url, filepath) {
  return new Promise((resolve, reject) => {
    console.log(`📥 Downloading: ${url}`);
    
    const file = fs.createWriteStream(filepath);
    
    https.get(url, (response) => {
      if (response.statusCode === 301 || response.statusCode === 302) {
        console.log('🔄 Following redirect...');
        return downloadFile(response.headers.location, filepath).then(resolve).catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      const totalSize = parseInt(response.headers['content-length'], 10);
      let downloadedSize = 0;
      
      console.log(`📊 Total size: ${(totalSize / 1024 / 1024).toFixed(1)} MB`);
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
        process.stdout.write(`\r📥 Progress: ${progress}% (${(downloadedSize / 1024 / 1024).toFixed(1)} MB)`);
      });
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`\n✅ Downloaded: ${filepath}`);
        resolve();
      });
      
    }).on('error', reject);
  });
}

function extractAndConvert(zipPath) {
  console.log('📦 Extracting ZIP file...');
  
  try {
    // Extract ZIP
    execSync(`cd "${DATA_DIR}" && unzip -o "${path.basename(zipPath)}"`, { stdio: 'pipe' });
    
    // Find shapefiles in subdirectories
    const files = fs.readdirSync(DATA_DIR);
    let municipalityShp = null;
    let shpPath = null;
    
    for (const file of files) {
      const filePath = path.join(DATA_DIR, file);
      if (fs.statSync(filePath).isDirectory()) {
        const subFiles = fs.readdirSync(filePath);
        const shapefiles = subFiles.filter(f => f.endsWith('.shp'));
        
        console.log(`📁 Found shapefiles in ${file}:`);
        shapefiles.forEach(shp => console.log(`  • ${shp}`));
        
        // Look for municipality shapefile (Com = Comuni)
        const municipalityFile = shapefiles.find(f => 
          f.toLowerCase().includes('com') && 
          f.toLowerCase().includes('wgs84')
        );
        
        if (municipalityFile) {
          municipalityShp = municipalityFile;
          shpPath = path.join(filePath, municipalityFile);
          console.log(`🎯 Using municipalities shapefile: ${file}/${municipalityFile}`);
          break;
        }
      }
    }
    
    if (!municipalityShp) {
      throw new Error('No municipality shapefile found');
    }
    
    // Convert to GeoJSON
    const geoJsonPath = path.join(DATA_DIR, 'municipalities_istat_2025.geojson');
    
    console.log('🔄 Converting to GeoJSON...');
    
    // Use ogr2ogr to convert with reasonable simplification
    const cmd = `ogr2ogr -f GeoJSON "${geoJsonPath}" "${shpPath}" ` +
               `-simplify 0.0008 ` +          // Reasonable simplification 
               `-t_srs EPSG:4326 ` +          // WGS84 projection
               `-lco COORDINATE_PRECISION=6`; // Limit decimal places
    
    execSync(cmd, { stdio: 'inherit' });
    
    console.log(`✅ Created: ${geoJsonPath}`);
    
    // Check file size and feature count
    const stats = fs.statSync(geoJsonPath);
    console.log(`📊 File size: ${(stats.size / 1024 / 1024).toFixed(1)} MB`);
    
    // Quick validation
    const sample = fs.readFileSync(geoJsonPath, { encoding: 'utf8', start: 0, end: 2000 });
    if (sample.includes('FeatureCollection')) {
      console.log('✅ Valid GeoJSON created');
      
      // Count features
      const content = fs.readFileSync(geoJsonPath, 'utf8');
      const matches = content.match(/"type":"Feature"/g);
      if (matches) {
        console.log(`📍 Features found: ${matches.length.toLocaleString()}`);
      }
      
      return geoJsonPath;
    } else {
      throw new Error('Invalid GeoJSON generated');
    }
    
  } catch (error) {
    console.error('❌ Extraction/conversion failed:', error.message);
    throw error;
  }
}

async function downloadISTATData() {
  console.log('🇮🇹 Downloading from ISTAT (official Italian source)...');
  
  // Try generalized version first (smaller, faster)
  for (const source of ISTAT_SOURCES) {
    const zipPath = path.join(DATA_DIR, source.filename);
    
    try {
      console.log(`\n🔄 Trying: ${source.description}`);
      
      // Download
      await downloadFile(source.url, zipPath);
      
      // Extract and convert
      const geoJsonPath = extractAndConvert(zipPath);
      
      console.log('\n🎉 ISTAT data successfully downloaded and converted!');
      console.log(`📁 Location: ${geoJsonPath}`);
      
      return geoJsonPath;
      
    } catch (error) {
      console.error(`❌ Failed with ${source.description}:`, error.message);
      console.log('🔄 Trying next source...\n');
      
      // Clean up failed download
      if (fs.existsSync(zipPath)) {
        fs.unlinkSync(zipPath);
      }
    }
  }
  
  throw new Error('All ISTAT sources failed');
}

async function main() {
  console.log('🚀 Starting ISTAT official data download...');
  
  // Check dependencies
  try {
    execSync('which unzip', { stdio: 'ignore' });
    execSync('which ogr2ogr', { stdio: 'ignore' });
  } catch (error) {
    console.error('❌ Missing dependencies. Install with:');
    console.error('macOS: brew install gdal');
    console.error('Linux: sudo apt-get install gdal-bin unzip');
    process.exit(1);
  }
  
  try {
    const geoJsonPath = await downloadISTATData();
    
    console.log('\n📋 Next steps:');
    console.log('1. Import the data: npm run import-istat-data');
    console.log('2. Check the improved boundaries in the frontend');
    console.log('\n✅ High-quality ISTAT boundaries ready for import!');
    
  } catch (error) {
    console.error('\n💥 Download process failed:', error.message);
    console.log('\n💡 Alternative: You can manually download from:');
    console.log('https://www.istat.it/it/archivio/222527');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { downloadISTATData };