const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../../config/.env') });
dotenv.config();

const pool = require('../lib/db');

async function seedAnnotations() {
  const client = await pool.connect();
  
  try {
    console.log('Starting to seed annotation data...');
    
    // Start transaction
    await client.query('BEGIN');
    
    // Create a test batch
    const batchResult = await client.query(`
      INSERT INTO annotation_batches (name, status, total_roofs, completed_roofs, iteration, avg_confidence, avg_iou)
      VALUES 
        ('Batch Milano Centro - 2025-01-15', 'in_progress', 500, 234, 3, 0.87, 0.82),
        ('Batch Roma Trastevere - 2025-01-10', 'completed', 300, 300, 5, 0.91, 0.85),
        ('Batch Napoli Vomero - 2025-01-18', 'pending', 200, 0, NULL, NULL, NULL)
      RETURNING id, name
    `);
    
    console.log('Created batches:', batchResult.rows.map(r => r.name));
    
    // Get a sample map to reference (if exists)
    const mapResult = await client.query('SELECT id FROM maps LIMIT 1');
    const mapId = mapResult.rows.length > 0 ? mapResult.rows[0].id : null;
    
    // Insert some sample roof annotations for Milano batch
    const milanoBatchId = batchResult.rows[0].id;
    
    // Generate sample roof annotations with realistic Milan coordinates
    const roofAnnotations = [];
    const baseLat = 45.4654;  // Milano center latitude
    const baseLon = 9.1859;   // Milano center longitude
    
    for (let i = 0; i < 50; i++) {
      const lat = baseLat + (Math.random() - 0.5) * 0.01;
      const lon = baseLon + (Math.random() - 0.5) * 0.01;
      
      // Create a simple rectangle polygon for the roof
      const size = 0.0001 + Math.random() * 0.0002; // Random size
      const polygon = `POLYGON((
        ${lon} ${lat},
        ${lon + size} ${lat},
        ${lon + size} ${lat + size},
        ${lon} ${lat + size},
        ${lon} ${lat}
      ))`;
      
      const confidence = 0.7 + Math.random() * 0.3;
      const iou = 0.75 + Math.random() * 0.2;
      const boundaryF1 = 0.7 + Math.random() * 0.25;
      
      const materials = ['terracotta', 'cement', 'metal', 'asphalt'];
      const roofTypes = ['gable', 'hip', 'flat', 'shed'];
      const buildingTypes = ['residential', 'commercial', 'industrial'];
      
      const annotation = await client.query(`
        INSERT INTO roof_annotations (
          map_id,
          geometry,
          confidence,
          annotation_source,
          roof_type,
          material,
          material_confidence,
          has_solar_panels,
          solar_panel_area,
          building_type,
          iou_score,
          boundary_f1,
          human_verified
        ) VALUES (
          $1,
          ST_Transform(ST_GeomFromText($2, 4326), 32632),
          $3,
          $4,
          $5,
          $6,
          $7,
          $8,
          $9,
          $10,
          $11,
          $12,
          $13
        ) RETURNING id
      `, [
        mapId,
        polygon,
        confidence,
        i < 20 ? 'sam2' : (i < 35 ? 'osm' : 'manual'),
        roofTypes[Math.floor(Math.random() * roofTypes.length)],
        materials[Math.floor(Math.random() * materials.length)],
        confidence - 0.05,
        Math.random() > 0.8,
        Math.random() > 0.8 ? (10 + Math.random() * 30) : null,
        buildingTypes[Math.floor(Math.random() * buildingTypes.length)],
        iou,
        boundaryF1,
        i < 30
      ]);
      
      roofAnnotations.push(annotation.rows[0].id);
    }
    
    console.log(`Created ${roofAnnotations.length} roof annotations`);
    
    // Link annotations to batch
    for (let i = 0; i < Math.min(roofAnnotations.length, 234); i++) {
      await client.query(`
        INSERT INTO batch_annotations (batch_id, annotation_id)
        VALUES ($1, $2)
      `, [milanoBatchId, roofAnnotations[i]]);
    }
    
    // Create some annotations for Roma batch (completed)
    const romaBatchId = batchResult.rows[1].id;
    
    for (let i = 0; i < 20; i++) {
      const lat = 41.8902 + (Math.random() - 0.5) * 0.01; // Roma Trastevere
      const lon = 12.4922 + (Math.random() - 0.5) * 0.01;
      
      const size = 0.0001 + Math.random() * 0.0002;
      const polygon = `POLYGON((
        ${lon} ${lat},
        ${lon + size} ${lat},
        ${lon + size} ${lat + size},
        ${lon} ${lat + size},
        ${lon} ${lat}
      ))`;
      
      const annotation = await client.query(`
        INSERT INTO roof_annotations (
          geometry,
          confidence,
          annotation_source,
          roof_type,
          material,
          has_solar_panels,
          building_type,
          iou_score,
          boundary_f1,
          human_verified
        ) VALUES (
          ST_Transform(ST_GeomFromText($1, 4326), 32632),
          $2, 'ensemble', 'hip', 'terracotta', false, 'residential',
          $3, $4, true
        ) RETURNING id
      `, [
        polygon,
        0.88 + Math.random() * 0.1,
        0.82 + Math.random() * 0.15,
        0.78 + Math.random() * 0.2
      ]);
      
      await client.query(`
        INSERT INTO batch_annotations (batch_id, annotation_id)
        VALUES ($1, $2)
      `, [romaBatchId, annotation.rows[0].id]);
    }
    
    console.log('Created annotations for Roma batch');
    
    // Update batch statistics
    await client.query(`
      UPDATE annotation_batches ab
      SET 
        avg_confidence = stats.avg_confidence,
        avg_iou = stats.avg_iou,
        completed_roofs = stats.count
      FROM (
        SELECT 
          ba.batch_id,
          COUNT(ra.id) as count,
          AVG(ra.confidence) as avg_confidence,
          AVG(ra.iou_score) as avg_iou
        FROM batch_annotations ba
        JOIN roof_annotations ra ON ba.annotation_id = ra.id
        GROUP BY ba.batch_id
      ) stats
      WHERE ab.id = stats.batch_id
    `);
    
    // Commit transaction
    await client.query('COMMIT');
    
    console.log('Seed data created successfully!');
    
    // Display some statistics
    const statsResult = await client.query(`
      SELECT 
        COUNT(*) as total_annotations,
        COUNT(*) FILTER (WHERE human_verified = true) as verified,
        COUNT(*) FILTER (WHERE has_solar_panels = true) as with_solar,
        AVG(confidence) as avg_confidence,
        AVG(iou_score) as avg_iou
      FROM roof_annotations
    `);
    
    console.log('\nAnnotation Statistics:');
    console.log('Total annotations:', statsResult.rows[0].total_annotations);
    console.log('Verified:', statsResult.rows[0].verified);
    console.log('With solar panels:', statsResult.rows[0].with_solar);
    console.log('Average confidence:', parseFloat(statsResult.rows[0].avg_confidence).toFixed(3));
    console.log('Average IoU:', parseFloat(statsResult.rows[0].avg_iou).toFixed(3));
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error seeding data:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run if executed directly
if (require.main === module) {
  seedAnnotations().catch(console.error);
}

module.exports = seedAnnotations;