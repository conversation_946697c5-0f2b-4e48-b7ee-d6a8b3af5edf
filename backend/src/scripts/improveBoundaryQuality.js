#!/usr/bin/env node

/**
 * Improve existing boundary quality using PostGIS functions
 * This script optimizes the existing municipality boundaries for better visualization
 */

const { Pool } = require('pg');
require('dotenv').config({ path: require('path').join(__dirname, '../../../config/.env') });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function improveBoundaryQuality() {
  console.log('🔧 Improving municipality boundary quality...');
  
  try {
    // 1. Create backup
    console.log('💾 Creating backup...');
    await pool.query(`
      DROP TABLE IF EXISTS municipalities_original_backup;
      CREATE TABLE municipalities_original_backup AS 
      SELECT * FROM municipalities;
    `);
    
    // 2. Clean and improve geometries
    console.log('🧹 Cleaning and improving geometries...');
    
    // Remove invalid geometries and apply smart simplification
    const result = await pool.query(`
      UPDATE municipalities SET
        geometry = ST_Multi(ST_Buffer(ST_SimplifyPreserveTopology(
          ST_MakeValid(geometry), 
          0.0005  -- Balanced simplification: keeps detail but reduces complexity
        ), 0))
      WHERE ST_IsValid(geometry) = true;
    `);
    
    console.log(`✅ Updated ${result.rowCount} municipality geometries`);
    
    // 3. Fix any remaining invalid geometries
    console.log('🔧 Fixing invalid geometries...');
    const fixResult = await pool.query(`
      UPDATE municipalities SET
        geometry = ST_Multi(ST_Buffer(ST_MakeValid(geometry), 0))
      WHERE ST_IsValid(geometry) = false;
    `);
    
    console.log(`🔧 Fixed ${fixResult.rowCount} invalid geometries`);
    
    // 4. Create/rebuild spatial index
    console.log('📊 Creating spatial index...');
    await pool.query(`
      DROP INDEX IF EXISTS idx_municipalities_geometry;
      CREATE INDEX idx_municipalities_geometry ON municipalities USING GIST (geometry);
    `);
    
    // 5. Get statistics
    const stats = await pool.query(`
      SELECT 
        COUNT(*) as total_municipalities,
        COUNT(CASE WHEN ST_IsValid(geometry) THEN 1 END) as valid_geometries,
        AVG(ST_NPoints(geometry)) as avg_points_per_municipality,
        MAX(ST_NPoints(geometry)) as max_points,
        MIN(ST_NPoints(geometry)) as min_points
      FROM municipalities;
    `);
    
    const stat = stats.rows[0];
    console.log('\n📊 Boundary Quality Statistics:');
    console.log(`• Total municipalities: ${stat.total_municipalities}`);
    console.log(`• Valid geometries: ${stat.valid_geometries}`);
    console.log(`• Average points per boundary: ${Math.round(stat.avg_points_per_municipality)}`);
    console.log(`• Most complex boundary: ${stat.max_points} points`);
    console.log(`• Simplest boundary: ${stat.min_points} points`);
    
    // 6. Test a sample conversion
    console.log('\n🧪 Testing sample GeoJSON conversion...');
    const sample = await pool.query(`
      SELECT name, ST_AsGeoJSON(ST_Simplify(geometry, 0.001)) as geometry
      FROM municipalities
      WHERE name ILIKE '%Milano%'
      LIMIT 1;
    `);
    
    if (sample.rows.length > 0) {
      const geoJson = JSON.parse(sample.rows[0].geometry);
      const pointCount = JSON.stringify(geoJson).match(/\[[\d\.-]+,[\d\.-]+\]/g)?.length || 0;
      console.log(`• Sample (${sample.rows[0].name}): ${pointCount} points in simplified GeoJSON`);
    }
    
    console.log('\n✅ Boundary quality improvement completed!');
    console.log('🔄 Refresh the frontend to see improved boundaries');
    
  } catch (error) {
    console.error('❌ Error improving boundaries:', error.message);
    throw error;
  }
}

async function downloadActualHighQualityData() {
  console.log('📥 Alternative: Downloading from working source...');
  
  // Let's try a different approach - download working data
  const https = require('https');
  const fs = require('fs');
  const path = require('path');
  
  const HIGH_QUALITY_DIR = path.join(__dirname, '../data/high_quality');
  if (!fs.existsSync(HIGH_QUALITY_DIR)) {
    fs.mkdirSync(HIGH_QUALITY_DIR, { recursive: true });
  }
  
  // Try this reliable source for Italian administrative data
  const url = 'https://github.com/datasets/geo-boundaries-italy-provinces/raw/master/data/geo-boundaries-italy-provinces.geojson';
  const filepath = path.join(HIGH_QUALITY_DIR, 'italy_boundaries_working.geojson');
  
  return new Promise((resolve, reject) => {
    console.log('📥 Downloading from reliable source...');
    const file = fs.createWriteStream(filepath);
    
    https.get(url, (response) => {
      if (response.statusCode === 200) {
        response.pipe(file);
        file.on('finish', () => {
          file.close();
          console.log(`✅ Downloaded: ${filepath}`);
          resolve(filepath);
        });
      } else {
        reject(new Error(`HTTP ${response.statusCode}`));
      }
    }).on('error', reject);
  });
}

async function createOptimizedMunicipalityEndpoint() {
  console.log('⚡ Creating optimized municipality data endpoint...');
  
  // Create a materialized view for faster municipality queries
  await pool.query(`
    DROP MATERIALIZED VIEW IF EXISTS municipalities_optimized;
    CREATE MATERIALIZED VIEW municipalities_optimized AS
    SELECT 
      code,
      name,
      province_code,
      region_name,
      ST_AsGeoJSON(ST_Simplify(geometry, 0.002)) as simplified_geometry,
      ST_AsGeoJSON(ST_Centroid(geometry)) as centroid,
      ST_Area(geometry) as area_sq_meters
    FROM municipalities
    WHERE ST_IsValid(geometry) = true;
    
    CREATE INDEX IF NOT EXISTS idx_municipalities_optimized_province 
    ON municipalities_optimized (province_code);
    
    CREATE INDEX IF NOT EXISTS idx_municipalities_optimized_name 
    ON municipalities_optimized (name);
  `);
  
  console.log('✅ Created optimized municipality view');
}

async function main() {
  console.log('🚀 Starting boundary quality improvement process...');
  
  try {
    // Option 1: Improve existing data
    await improveBoundaryQuality();
    
    // Option 2: Create optimized views
    await createOptimizedMunicipalityEndpoint();
    
    console.log('\n🎯 Improvements completed!');
    console.log('• Boundary geometries optimized for web display');
    console.log('• Spatial indexes rebuilt for better performance'); 
    console.log('• Optimized materialized view created');
    console.log('\n📋 Benefits:');
    console.log('• 📈 Faster map loading');
    console.log('• 🎨 Cleaner boundary rendering');
    console.log('• ⚡ Reduced data transfer');
    console.log('• 🔍 Better zoom performance');
    
  } catch (error) {
    console.error('\n💥 Process failed:', error.message);
    
    // Try alternative download
    try {
      console.log('\n🔄 Trying alternative approach...');
      await downloadActualHighQualityData();
    } catch (downloadError) {
      console.error('Alternative download also failed:', downloadError.message);
    }
    
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main();
}

module.exports = { improveBoundaryQuality, createOptimizedMunicipalityEndpoint };