const { PrismaClient } = require('../generated/prisma');
const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');

const execAsync = promisify(exec);
const prisma = new PrismaClient();

const HIGH_QUALITY_DATA_PATH = path.join(__dirname, '../data/high_quality');

async function importShapefile(shapefilePath, tableName, srid = 32632) {
  const dbUrl = process.env.DATABASE_URL;
  const matches = dbUrl.match(/postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
  
  if (!matches) {
    throw new Error('Invalid DATABASE_URL format');
  }

  const [, user, password, host, port, database] = matches;
  
  // Use ogr2ogr to import shapefile directly to PostGIS
  // Add -nlt PROMOTE_TO_MULTI to handle multi polygons
  // Add -lco PRECISION=NO to avoid numeric precision issues
  const command = `PGPASSWORD=${password} ogr2ogr -f "PostgreSQL" ` +
    `"PG:host=${host} port=${port} dbname=${database} user=${user}" ` +
    `"${shapefilePath}" -nln ${tableName}_temp -t_srs EPSG:${srid} ` +
    `-nlt PROMOTE_TO_MULTI -lco PRECISION=NO ` +
    `-overwrite -progress`;

  console.log(`Importing ${shapefilePath} to ${tableName}_temp...`);
  
  try {
    const { stdout, stderr } = await execAsync(command);
    if (stdout) console.log(stdout);
    if (stderr && !stderr.includes('Warning')) console.error(stderr);
    console.log(`Successfully imported ${tableName}_temp`);
  } catch (error) {
    console.error(`Error importing ${shapefilePath}:`, error);
    throw error;
  }
}

async function importRegions() {
  console.log('Importing regions...');
  
  const shapefilePath = path.join(HIGH_QUALITY_DATA_PATH, 'Reg01012025/Reg01012025_WGS84.shp');
  await importShapefile(shapefilePath, 'regions');
  
  // Transfer data from temp table to Prisma model
  await prisma.$executeRawUnsafe(`
    INSERT INTO regions (id, cod_rip, cod_reg, den_reg, shape_length, shape_area, geometry)
    SELECT 
      gen_random_uuid(),
      cod_rip::integer,
      cod_reg::integer,
      den_reg,
      shape_leng::double precision,
      shape_area::double precision,
      wkb_geometry
    FROM regions_temp
    ON CONFLICT DO NOTHING
  `);
  
  await prisma.$executeRawUnsafe('DROP TABLE IF EXISTS regions_temp');
  console.log('Regions imported successfully');
}

async function importProvinces() {
  console.log('Importing provinces...');
  
  const shapefilePath = path.join(HIGH_QUALITY_DATA_PATH, 'ProvCM01012025/ProvCM01012025_WGS84.shp');
  await importShapefile(shapefilePath, 'provinces');
  
  // Transfer data from temp table to Prisma model
  await prisma.$executeRawUnsafe(`
    INSERT INTO provinces (id, cod_rip, cod_reg, cod_prov, cod_cm, cod_uts, den_prov, den_cm, den_uts, sigla, tipo_uts, shape_length, shape_area, geometry)
    SELECT 
      gen_random_uuid(),
      cod_rip::integer,
      cod_reg::integer,
      cod_prov::integer,
      cod_cm::integer,
      cod_uts::integer,
      den_prov,
      den_cm,
      den_uts,
      sigla,
      tipo_uts,
      shape_leng::double precision,
      shape_area::double precision,
      wkb_geometry
    FROM provinces_temp
    ON CONFLICT DO NOTHING
  `);
  
  await prisma.$executeRawUnsafe('DROP TABLE IF EXISTS provinces_temp');
  console.log('Provinces imported successfully');
}

async function importMunicipalities() {
  console.log('Importing municipalities...');
  
  const shapefilePath = path.join(HIGH_QUALITY_DATA_PATH, 'Com01012025/Com01012025_WGS84.shp');
  await importShapefile(shapefilePath, 'municipalities');
  
  // Transfer data from temp table to Prisma model
  await prisma.$executeRawUnsafe(`
    INSERT INTO municipalities (id, cod_rip, cod_reg, cod_prov, cod_cm, cod_uts, pro_com, pro_com_t, comune, comune_a, cc_uts, shape_length, shape_area, geometry)
    SELECT 
      gen_random_uuid(),
      cod_rip::integer,
      cod_reg::integer,
      cod_prov::integer,
      cod_cm::integer,
      cod_uts::integer,
      pro_com::integer,
      pro_com_t,
      comune,
      comune_a,
      cc_uts::integer,
      shape_leng::double precision,
      shape_area::double precision,
      wkb_geometry
    FROM municipalities_temp
    ON CONFLICT DO NOTHING
  `);
  
  await prisma.$executeRawUnsafe('DROP TABLE IF EXISTS municipalities_temp');
  console.log('Municipalities imported successfully');
}

async function createSpatialIndexes() {
  console.log('Creating spatial indexes...');
  
  await prisma.$executeRawUnsafe('CREATE INDEX IF NOT EXISTS idx_regions_geometry ON regions USING GIST (geometry)');
  await prisma.$executeRawUnsafe('CREATE INDEX IF NOT EXISTS idx_provinces_geometry ON provinces USING GIST (geometry)');
  await prisma.$executeRawUnsafe('CREATE INDEX IF NOT EXISTS idx_municipalities_geometry ON municipalities USING GIST (geometry)');
  
  console.log('Spatial indexes created');
}

async function main() {
  try {
    console.log('Starting high-quality geo data import...');
    
    // Import in order: regions first, then provinces, then municipalities
    await importRegions();
    await importProvinces();
    await importMunicipalities();
    
    // Create spatial indexes for better performance
    await createSpatialIndexes();
    
    // Get counts
    const regionCount = await prisma.region.count();
    const provinceCount = await prisma.province.count();
    const municipalityCount = await prisma.municipality.count();
    
    console.log(`Import completed successfully!`);
    console.log(`- Regions: ${regionCount}`);
    console.log(`- Provinces: ${provinceCount}`);
    console.log(`- Municipalities: ${municipalityCount}`);
    
  } catch (error) {
    console.error('Error during import:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch(console.error)
  .finally(() => process.exit());