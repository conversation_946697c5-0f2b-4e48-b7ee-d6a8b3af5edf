#!/usr/bin/env node

/**
 * Download high-quality municipality boundaries from OpenStreetMap
 * OSM has excellent, constantly updated boundary data for Italy
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

const DATA_DIR = path.join(__dirname, '../data/high_quality');

if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// OpenStreetMap-based sources with good quality Italian boundaries
const OSM_SOURCES = [
  {
    url: 'https://osm-boundaries.com/Download/Submit?apikey=&db=osm20240101&osmIds=-365331&recursive&minAdminLevel=8&maxAdminLevel=8&format=GeoJSON&srid=4326',
    filename: 'italy_municipalities_osm.geojson',
    description: 'OSM Italy Municipalities (Admin Level 8)'
  },
  {
    // Backup: Use Geofabrik extract
    url: 'https://download.geofabrik.de/europe/italy-latest.osm.pbf',
    filename: 'italy-latest.osm.pbf',
    description: 'Geofabrik Italy (requires processing)'
  }
];

// Alternative: Use already processed GeoJSON from reliable sources
const PROCESSED_SOURCES = [
  {
    url: 'https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson',
    filename: 'test_world.geojson',
    description: 'Test download (world boundaries)'
  }
];

function downloadFile(url, filepath, description) {
  return new Promise((resolve, reject) => {
    console.log(`📥 Downloading: ${description}`);
    console.log(`🔗 URL: ${url.substring(0, 80)}...`);
    
    const file = fs.createWriteStream(filepath);
    
    https.get(url, (response) => {
      if (response.statusCode === 301 || response.statusCode === 302) {
        console.log('🔄 Following redirect...');
        return downloadFile(response.headers.location, filepath, description).then(resolve).catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      const totalSize = parseInt(response.headers['content-length'], 10);
      let downloadedSize = 0;
      
      if (totalSize) {
        console.log(`📊 Size: ${(totalSize / 1024 / 1024).toFixed(1)} MB`);
      }
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        if (totalSize && downloadedSize % (1024 * 1024) === 0) { // Update every 1MB
          const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
          process.stdout.write(`\r📥 Progress: ${progress}%`);
        }
      });
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`\n✅ Downloaded: ${filepath}`);
        resolve();
      });
      
    }).on('error', reject);
  });
}

function validateGeoJSON(filepath) {
  try {
    const stats = fs.statSync(filepath);
    const sizeMB = stats.size / 1024 / 1024;
    
    console.log(`📊 File size: ${sizeMB.toFixed(1)} MB`);
    
    if (sizeMB < 0.1) {
      console.log('❌ File too small, likely empty or error page');
      return false;
    }
    
    // Read first 2KB to check structure
    const sample = fs.readFileSync(filepath, { encoding: 'utf8', start: 0, end: 2000 });
    
    if (sample.includes('FeatureCollection') && sample.includes('geometry')) {
      console.log('✅ Valid GeoJSON structure detected');
      return true;
    } else if (sample.includes('<!DOCTYPE') || sample.includes('<html')) {
      console.log('❌ Downloaded HTML page instead of GeoJSON');
      return false;
    } else {
      console.log('❌ Unknown file format');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Validation error:', error.message);
    return false;
  }
}

async function tryManualUrls() {
  console.log('🔄 Trying alternative sources for Italian boundaries...');
  
  // These are known working sources for Italian administrative data
  const manualSources = [
    {
      url: 'https://raw.githubusercontent.com/deldersveld/topojson/master/countries/italy/italy-regions.json',
      filename: 'italy_regions_topojson.json',
      description: 'Italy Regions (TopoJSON - needs conversion)'
    },
    {
      url: 'https://raw.githubusercontent.com/deldersveld/topojson/master/countries/italy/italy-provinces.json', 
      filename: 'italy_provinces_topojson.json',
      description: 'Italy Provinces (TopoJSON - needs conversion)'
    }
  ];
  
  for (const source of manualSources) {
    const filepath = path.join(DATA_DIR, source.filename);
    
    try {
      await downloadFile(source.url, filepath, source.description);
      
      const stats = fs.statSync(filepath);
      if (stats.size > 1000) { // At least 1KB
        console.log(`✅ Successfully downloaded: ${source.description}`);
        
        if (source.filename.includes('topojson')) {
          console.log('💡 Note: This is TopoJSON format, you may need to convert to GeoJSON');
          console.log('   Use: topojson-client (npm install -g topojson-client)');
          console.log('   Convert with: topo2geo < input.json > output.geojson');
        }
        
        return filepath;
      }
    } catch (error) {
      console.log(`❌ Failed: ${source.description} - ${error.message}`);
    }
  }
  
  return null;
}

async function createTestData() {
  console.log('🧪 Creating test data with improved boundaries...');
  
  // Create a sample GeoJSON with a few Italian regions for testing
  const testGeoJSON = {
    type: 'FeatureCollection',
    features: [
      {
        type: 'Feature',
        properties: {
          name: 'Milano',
          code: '015146',
          province_code: 'MI',
          region_name: 'Lombardia'
        },
        geometry: {
          type: 'Polygon',
          coordinates: [[
            [9.0404, 45.4167], [9.0404, 45.5167], [9.1404, 45.5167],
            [9.1404, 45.4167], [9.0404, 45.4167]
          ]]
        }
      },
      {
        type: 'Feature', 
        properties: {
          name: 'Roma',
          code: '058091',
          province_code: 'RM',
          region_name: 'Lazio'
        },
        geometry: {
          type: 'Polygon',
          coordinates: [[
            [12.3404, 41.7167], [12.3404, 41.9167], [12.5404, 41.9167],
            [12.5404, 41.7167], [12.3404, 41.7167]
          ]]
        }
      }
    ]
  };
  
  const testPath = path.join(DATA_DIR, 'test_municipalities.geojson');
  fs.writeFileSync(testPath, JSON.stringify(testGeoJSON, null, 2));
  
  console.log(`✅ Created test data: ${testPath}`);
  return testPath;
}

async function main() {
  console.log('🌍 Downloading high-quality Italian municipality boundaries...');
  
  let successfulDownload = null;
  
  // Try alternative sources
  successfulDownload = await tryManualUrls();
  
  if (!successfulDownload) {
    console.log('\n⚠️ All downloads failed. Creating test data for development...');
    successfulDownload = await createTestData();
  }
  
  if (successfulDownload) {
    console.log('\n🎯 Next steps:');
    console.log('1. Check the downloaded data quality');
    console.log('2. If needed, convert format (TopoJSON → GeoJSON)');
    console.log('3. Import with: npm run import-quality-geo');
    console.log('\n📁 Downloaded to:', successfulDownload);
    
    console.log('\n💡 For better data, try manually downloading from:');
    console.log('• https://www.istat.it/it/archivio/222527 (ISTAT official)');
    console.log('• https://www.naturalearthdata.com/ (Natural Earth)');
    console.log('• https://gadm.org/download_country.html (GADM)');
    
  } else {
    console.error('\n💥 All download attempts failed');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { tryManualUrls, createTestData };