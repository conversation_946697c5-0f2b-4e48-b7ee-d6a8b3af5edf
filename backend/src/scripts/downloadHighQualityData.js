#!/usr/bin/env node

/**
 * Script to download and import high-quality municipality boundary data from ISTAT
 * This will replace the current low-quality boundary data
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

const DATA_DIR = path.join(__dirname, '../data');
const HIGH_QUALITY_DIR = path.join(DATA_DIR, 'high_quality');

// Ensure directories exist
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}
if (!fs.existsSync(HIGH_QUALITY_DIR)) {
  fs.mkdirSync(HIGH_QUALITY_DIR, { recursive: true });
}

// ISTAT data sources (updated for 2024)
const DATA_SOURCES = {
  // Administrative boundaries at municipal level
  municipalities: {
    url: 'https://www.istat.it/storage/cartografia/confini_amministrativi/non_generalizzati/Limiti01012024.zip',
    filename: 'ISTAT_municipalities_2024.zip',
    description: 'ISTAT Administrative boundaries 2024 - High precision'
  },
  // Alternative: OpenStreetMap data via Geofabrik
  osm_italy: {
    url: 'https://download.geofabrik.de/europe/italy-latest.osm.pbf',
    filename: 'italy-latest.osm.pbf',
    description: 'OpenStreetMap Italy data - Updated daily'
  }
};

function downloadFile(url, filepath) {
  return new Promise((resolve, reject) => {
    console.log(`📥 Downloading ${url}...`);
    
    const file = fs.createWriteStream(filepath);
    const request = https.get(url, (response) => {
      // Handle redirects
      if (response.statusCode === 301 || response.statusCode === 302) {
        console.log(`🔄 Following redirect to ${response.headers.location}`);
        return downloadFile(response.headers.location, filepath).then(resolve).catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download: ${response.statusCode} ${response.statusMessage}`));
        return;
      }
      
      const totalSize = parseInt(response.headers['content-length'], 10);
      let downloadedSize = 0;
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        if (totalSize) {
          const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
          process.stdout.write(`\r📥 Progress: ${progress}% (${(downloadedSize / 1024 / 1024).toFixed(1)}MB)`);
        }
      });
      
      response.pipe(file);
    });
    
    file.on('finish', () => {
      file.close();
      console.log(`\n✅ Download completed: ${filepath}`);
      resolve();
    });
    
    request.on('error', (err) => {
      fs.unlink(filepath, () => {}); // Delete partial file
      reject(err);
    });
  });
}

function checkDependencies() {
  console.log('🔍 Checking required dependencies...');
  
  const commands = ['unzip', 'ogr2ogr', 'ogrinfo'];
  const missing = [];
  
  for (const cmd of commands) {
    try {
      execSync(`which ${cmd}`, { stdio: 'ignore' });
      console.log(`✅ ${cmd} found`);
    } catch (error) {
      missing.push(cmd);
      console.log(`❌ ${cmd} not found`);
    }
  }
  
  if (missing.length > 0) {
    console.log('\n🚨 Missing dependencies. Install with:');
    console.log('macOS: brew install gdal');
    console.log('Ubuntu: sudo apt-get install gdal-bin unzip');
    console.log('CentOS: sudo yum install gdal unzip');
    return false;
  }
  
  return true;
}

async function downloadISTATData() {
  const source = DATA_SOURCES.municipalities;
  const zipPath = path.join(HIGH_QUALITY_DIR, source.filename);
  
  try {
    // Download ISTAT data
    await downloadFile(source.url, zipPath);
    
    // Extract ZIP file
    console.log('📦 Extracting ZIP file...');
    execSync(`cd "${HIGH_QUALITY_DIR}" && unzip -o "${source.filename}"`, { stdio: 'inherit' });
    
    // Find the shapefile
    console.log('🔍 Looking for shapefiles...');
    const files = fs.readdirSync(HIGH_QUALITY_DIR);
    const shapefiles = files.filter(f => f.endsWith('.shp'));
    
    console.log('📋 Found shapefiles:');
    shapefiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file}`);
    });
    
    return shapefiles;
    
  } catch (error) {
    console.error('❌ Error downloading ISTAT data:', error.message);
    throw error;
  }
}

function convertShapefileToGeoJSON(shapefilePath, outputPath) {
  console.log(`🔄 Converting ${shapefilePath} to GeoJSON...`);
  
  try {
    // Get info about the shapefile
    const info = execSync(`ogrinfo "${shapefilePath}"`, { encoding: 'utf8' });
    console.log('📊 Shapefile info:');
    console.log(info.substring(0, 500) + '...');
    
    // Convert to GeoJSON with simplification for better performance
    const cmd = `ogr2ogr -f GeoJSON "${outputPath}" "${shapefilePath}" -simplify 0.0001 -t_srs EPSG:4326`;
    execSync(cmd, { stdio: 'inherit' });
    
    console.log(`✅ Converted to: ${outputPath}`);
    return outputPath;
    
  } catch (error) {
    console.error('❌ Error converting shapefile:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 Starting high-quality boundary data download...');
  console.log('📂 Data directory:', HIGH_QUALITY_DIR);
  
  try {
    // Check dependencies
    if (!checkDependencies()) {
      process.exit(1);
    }
    
    // Download and extract ISTAT data
    const shapefiles = await downloadISTATData();
    
    // Process municipalities shapefile
    const municipalitiesShp = shapefiles.find(f => 
      f.toLowerCase().includes('com') || 
      f.toLowerCase().includes('admin') ||
      f.toLowerCase().includes('municipi')
    );
    
    if (municipalitiesShp) {
      const shpPath = path.join(HIGH_QUALITY_DIR, municipalitiesShp);
      const geoJsonPath = path.join(HIGH_QUALITY_DIR, 'municipalities_high_quality.geojson');
      
      convertShapefileToGeoJSON(shpPath, geoJsonPath);
      
      console.log('\n🎉 High-quality municipality data ready!');
      console.log(`📁 Location: ${geoJsonPath}`);
      console.log('\n📋 Next steps:');
      console.log('1. Review the data quality');
      console.log('2. Run the import script to update the database');
      console.log('3. Test the improved boundary visualization');
      
    } else {
      console.log('❌ Could not find municipalities shapefile in the downloaded data');
      console.log('📋 Available files:');
      shapefiles.forEach(file => console.log(`  - ${file}`));
    }
    
  } catch (error) {
    console.error('\n💥 Download failed:', error.message);
    process.exit(1);
  }
}

// Alternative: Download from OpenStreetMap
async function downloadOSMData() {
  console.log('🗺️ Alternative: Downloading OpenStreetMap data...');
  
  const source = DATA_SOURCES.osm_italy;
  const osmPath = path.join(HIGH_QUALITY_DIR, source.filename);
  
  try {
    await downloadFile(source.url, osmPath);
    
    console.log('📊 OSM data downloaded. To extract administrative boundaries:');
    console.log('1. Install osmium-tool: brew install osmium-tool');
    console.log('2. Extract admin boundaries: osmium tags-filter italy-latest.osm.pbf boundary=administrative -o admin.osm.pbf');
    console.log('3. Convert to GeoJSON: ogr2ogr -f GeoJSON admin.geojson admin.osm.pbf lines');
    
  } catch (error) {
    console.error('❌ Error downloading OSM data:', error.message);
  }
}

// Check if user wants OSM data instead
const args = process.argv.slice(2);
if (args.includes('--osm')) {
  downloadOSMData();
} else {
  main();
}

module.exports = { downloadISTATData, downloadOSMData };