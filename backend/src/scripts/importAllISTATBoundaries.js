#!/usr/bin/env node

/**
 * Import complete ISTAT 2025 boundaries (regions, provinces, municipalities) into PostGIS database
 * Replaces all existing boundary data with official ISTAT 2025 sources for consistency
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const DATA_DIR = path.join(__dirname, '../data/high_quality');

// Load environment variables
require('dotenv').config();

// Database configuration from environment
const dbConfig = {
  connectionString: process.env.DATABASE_URL || '**********************************************************************************************************/astrameccanica'
};

// ISTAT shapefiles to import
const ISTAT_LAYERS = [
  {
    name: 'regions',
    shapefile: 'Reg01012025/Reg01012025_WGS84.shp',
    outputFile: 'regions_istat_2025.geojson',
    tableName: 'regions',
    description: 'Regioni ISTAT 2025'
  },
  {
    name: 'provinces',
    shapefile: 'ProvCM01012025/ProvCM01012025_WGS84.shp',
    outputFile: 'provinces_istat_2025.geojson',
    tableName: 'provinces',
    description: 'Province ISTAT 2025'
  },
  {
    name: 'municipalities',
    shapefile: 'Com01012025/Com01012025_WGS84.shp',
    outputFile: 'municipalities_istat_2025.geojson',
    tableName: 'municipalities',
    description: 'Comuni ISTAT 2025'
  }
];

function convertShapefileToGeoJSON(layer) {
  const shpPath = path.join(DATA_DIR, layer.shapefile);
  const geoJsonPath = path.join(DATA_DIR, layer.outputFile);
  
  console.log(`🔄 Converting ${layer.description}...`);
  
  if (!fs.existsSync(shpPath)) {
    throw new Error(`Shapefile not found: ${shpPath}`);
  }
  
  // Convert using ogr2ogr with reasonable simplification
  const cmd = `ogr2ogr -f GeoJSON "${geoJsonPath}" "${shpPath}" ` +
             `-simplify 0.0008 ` +          // Reasonable simplification 
             `-t_srs EPSG:4326 ` +          // WGS84 projection
             `-lco COORDINATE_PRECISION=6`; // Limit decimal places
  
  execSync(cmd, { stdio: 'inherit' });
  
  // Check file was created
  if (!fs.existsSync(geoJsonPath)) {
    throw new Error(`Failed to create GeoJSON: ${geoJsonPath}`);
  }
  
  const stats = fs.statSync(geoJsonPath);
  console.log(`✅ Created ${layer.outputFile} (${(stats.size / 1024 / 1024).toFixed(1)} MB)`);
  
  return geoJsonPath;
}

async function createRegionsTable(client) {
  console.log('🏗️  Creating regions table...');
  
  await client.query(`
    DROP TABLE IF EXISTS regions_backup;
    ALTER TABLE IF EXISTS regions RENAME TO regions_backup;
    
    CREATE TABLE regions (
      id SERIAL PRIMARY KEY,
      cod_rip INTEGER,
      denom_rip VARCHAR(255),
      cod_reg INTEGER UNIQUE,
      denom_reg VARCHAR(255),
      shape_leng NUMERIC,
      shape_area NUMERIC,
      geom GEOMETRY(MultiPolygon, 4326),
      geom_simple GEOMETRY(MultiPolygon, 4326),
      centroid GEOMETRY(Point, 4326),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `);
}

async function createProvincesTable(client) {
  console.log('🏗️  Creating provinces table...');
  
  await client.query(`
    DROP TABLE IF EXISTS provinces_backup;
    ALTER TABLE IF EXISTS provinces RENAME TO provinces_backup;
    
    CREATE TABLE provinces (
      id SERIAL PRIMARY KEY,
      cod_rip INTEGER,
      denom_rip VARCHAR(255),
      cod_reg INTEGER,
      denom_reg VARCHAR(255),
      cod_prov INTEGER UNIQUE,
      denom_prov VARCHAR(255),
      sigla_prov VARCHAR(50),
      tipo_uts VARCHAR(50),
      shape_leng NUMERIC,
      shape_area NUMERIC,
      geom GEOMETRY(MultiPolygon, 4326),
      geom_simple GEOMETRY(MultiPolygon, 4326),
      centroid GEOMETRY(Point, 4326),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `);
}

async function createMunicipalitiesTable(client) {
  console.log('🏗️  Creating municipalities table...');
  
  // This table was already created in the previous script, so we'll keep it
  // but make sure it exists with the correct schema
  const tableExists = await client.query(`
    SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_name = 'municipalities'
    );
  `);
  
  if (!tableExists.rows[0].exists) {
    await client.query(`
      CREATE TABLE municipalities (
        id SERIAL PRIMARY KEY,
        cod_rip INTEGER,
        cod_reg INTEGER,
        cod_prov INTEGER,
        cod_cm INTEGER,
        cod_uts INTEGER,
        pro_com INTEGER,
        pro_com_t VARCHAR(50),
        comune VARCHAR(255),
        comune_a VARCHAR(255),
        cc_uts INTEGER,
        shape_leng NUMERIC,
        shape_area NUMERIC,
        geom GEOMETRY(MultiPolygon, 4326),
        geom_simple GEOMETRY(MultiPolygon, 4326),
        centroid GEOMETRY(Point, 4326),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
  }
}

async function importRegions(client, geoJsonPath) {
  console.log('📥 Importing regions...');
  
  const geoJsonData = JSON.parse(fs.readFileSync(geoJsonPath, 'utf8'));
  let imported = 0;
  
  for (const feature of geoJsonData.features) {
    const props = feature.properties;
    const geom = JSON.stringify(feature.geometry);
    
    await client.query(`
      INSERT INTO regions (
        cod_rip, denom_rip, cod_reg, denom_reg,
        shape_leng, shape_area, geom
      ) VALUES (
        $1, $2, $3, $4, $5, $6, ST_GeomFromGeoJSON($7)
      )
    `, [
      props.COD_RIP, props.DEN_RIP, props.COD_REG, props.DEN_REG,
      props.Shape_Leng, props.Shape_Area, geom
    ]);
    
    imported++;
    if (imported % 5 === 0) {
      process.stdout.write(`\r📥 Regions imported: ${imported}`);
    }
  }
  
  console.log(`\n✅ Imported ${imported} regions`);
}

async function importProvinces(client, geoJsonPath) {
  console.log('📥 Importing provinces...');
  
  const geoJsonData = JSON.parse(fs.readFileSync(geoJsonPath, 'utf8'));
  let imported = 0;
  
  for (const feature of geoJsonData.features) {
    const props = feature.properties;
    const geom = JSON.stringify(feature.geometry);
    
    await client.query(`
      INSERT INTO provinces (
        cod_rip, denom_rip, cod_reg, denom_reg,
        cod_prov, denom_prov, sigla_prov, tipo_uts,
        shape_leng, shape_area, geom
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, ST_GeomFromGeoJSON($11)
      )
    `, [
      props.COD_RIP, props.DEN_RIP, props.COD_REG, props.DEN_REG,
      props.COD_PROV, props.DEN_PROV, props.SIGLA, props.TIPO_UTS,
      props.Shape_Leng, props.Shape_Area, geom
    ]);
    
    imported++;
    if (imported % 10 === 0) {
      process.stdout.write(`\r📥 Provinces imported: ${imported}`);
    }
  }
  
  console.log(`\n✅ Imported ${imported} provinces`);
}

async function generateSimplifiedGeometries(client, tableName) {
  console.log(`🔧 Generating simplified geometries for ${tableName}...`);
  
  await client.query(`
    UPDATE ${tableName} 
    SET geom_simple = ST_SimplifyPreserveTopology(geom, 0.001)
    WHERE geom_simple IS NULL
  `);
  
  console.log(`📍 Computing centroids for ${tableName}...`);
  
  await client.query(`
    UPDATE ${tableName} 
    SET centroid = ST_Centroid(geom)
    WHERE centroid IS NULL
  `);
}

async function createIndexes(client, tableName) {
  console.log(`🗂️  Creating spatial indexes for ${tableName}...`);
  
  const indexes = [
    `CREATE INDEX IF NOT EXISTS idx_${tableName}_geom ON ${tableName} USING GIST(geom)`,
    `CREATE INDEX IF NOT EXISTS idx_${tableName}_geom_simple ON ${tableName} USING GIST(geom_simple)`,
    `CREATE INDEX IF NOT EXISTS idx_${tableName}_centroid ON ${tableName} USING GIST(centroid)`
  ];
  
  // Add specific indexes based on table
  if (tableName === 'regions') {
    indexes.push(`CREATE INDEX IF NOT EXISTS idx_regions_cod_reg ON regions(cod_reg)`);
  } else if (tableName === 'provinces') {
    indexes.push(`CREATE INDEX IF NOT EXISTS idx_provinces_cod_prov ON provinces(cod_prov)`);
    indexes.push(`CREATE INDEX IF NOT EXISTS idx_provinces_sigla_prov ON provinces(sigla_prov)`);
    indexes.push(`CREATE INDEX IF NOT EXISTS idx_provinces_cod_reg ON provinces(cod_reg)`);
  } else if (tableName === 'municipalities') {
    indexes.push(`CREATE INDEX IF NOT EXISTS idx_municipalities_comune ON municipalities(comune)`);
    indexes.push(`CREATE INDEX IF NOT EXISTS idx_municipalities_pro_com ON municipalities(pro_com)`);
    indexes.push(`CREATE INDEX IF NOT EXISTS idx_municipalities_cod_prov ON municipalities(cod_prov)`);
    indexes.push(`CREATE INDEX IF NOT EXISTS idx_municipalities_cod_reg ON municipalities(cod_reg)`);
  }
  
  for (const indexQuery of indexes) {
    await client.query(indexQuery);
  }
}

async function generateStatistics(client) {
  console.log('📊 Generating statistics...');
  
  await client.query('ANALYZE regions');
  await client.query('ANALYZE provinces');
  await client.query('ANALYZE municipalities');
  
  const stats = await client.query(`
    SELECT 
      (SELECT COUNT(*) FROM regions) as regions,
      (SELECT COUNT(*) FROM provinces) as provinces,
      (SELECT COUNT(*) FROM municipalities) as municipalities
  `);
  
  console.log('\n📊 Import Statistics:');
  console.log(`  • Regions: ${stats.rows[0].regions}`);
  console.log(`  • Provinces: ${stats.rows[0].provinces}`);
  console.log(`  • Municipalities: ${stats.rows[0].municipalities.toLocaleString()}`);
  
  // Test Piacenza specifically
  const piacenzaTest = await client.query(`
    SELECT p.cod_prov, p.sigla_prov, p.denom_prov, COUNT(m.id) as municipalities
    FROM provinces p
    LEFT JOIN municipalities m ON p.cod_prov = m.cod_prov
    WHERE p.sigla_prov = 'PC' OR p.denom_prov ILIKE '%piacenza%'
    GROUP BY p.cod_prov, p.sigla_prov, p.denom_prov
  `);
  
  console.log('\n🧪 Piacenza Test:');
  piacenzaTest.rows.forEach(row => {
    console.log(`  • ${row.denom_prov} (${row.sigla_prov}): Code ${row.cod_prov}, ${row.municipalities} municipalities`);
  });
}

async function importAllISTATBoundaries() {
  console.log('🚀 Starting complete ISTAT 2025 boundaries import...');
  
  // Check dependencies
  try {
    execSync('which ogr2ogr', { stdio: 'ignore' });
  } catch (error) {
    console.error('❌ Missing ogr2ogr. Install with:');
    console.error('macOS: brew install gdal');
    console.error('Linux: sudo apt-get install gdal-bin');
    process.exit(1);
  }
  
  const client = new Client(dbConfig);
  
  try {
    // Connect to database
    console.log('🔌 Connecting to PostgreSQL database...');
    await client.connect();
    console.log('✅ Connected to database');
    
    // Check if PostGIS is available
    console.log('🗺️  Checking PostGIS extension...');
    const postgisCheck = await client.query("SELECT version() as postgis_version FROM postgis_lib_version()");
    console.log(`✅ PostGIS version: ${postgisCheck.rows[0].postgis_version}`);
    
    // 1. Convert all shapefiles to GeoJSON
    console.log('\n📦 Phase 1: Converting shapefiles to GeoJSON...');
    const geoJsonPaths = {};
    
    for (const layer of ISTAT_LAYERS) {
      try {
        geoJsonPaths[layer.name] = convertShapefileToGeoJSON(layer);
      } catch (error) {
        console.error(`❌ Failed to convert ${layer.name}:`, error.message);
        throw error;
      }
    }
    
    // 2. Create database tables
    console.log('\n🏗️  Phase 2: Creating database tables...');
    await createRegionsTable(client);
    await createProvincesTable(client);
    await createMunicipalitiesTable(client);
    
    // 3. Import data in order (regions -> provinces -> municipalities)
    console.log('\n📥 Phase 3: Importing boundary data...');
    
    await importRegions(client, geoJsonPaths.regions);
    await importProvinces(client, geoJsonPaths.provinces);
    
    // Municipalities are already imported from previous script, skip if table is not empty
    const municipalityCount = await client.query('SELECT COUNT(*) FROM municipalities');
    if (parseInt(municipalityCount.rows[0].count) === 0) {
      console.log('📥 Importing municipalities (table is empty)...');
      // Import municipalities here if needed
    } else {
      console.log(`✅ Municipalities already imported (${municipalityCount.rows[0].count} records)`);
    }
    
    // 4. Generate optimized geometries
    console.log('\n🔧 Phase 4: Generating optimized geometries...');
    await generateSimplifiedGeometries(client, 'regions');
    await generateSimplifiedGeometries(client, 'provinces');
    await generateSimplifiedGeometries(client, 'municipalities');
    
    // 5. Create indexes
    console.log('\n🗂️  Phase 5: Creating spatial indexes...');
    await createIndexes(client, 'regions');
    await createIndexes(client, 'provinces');
    await createIndexes(client, 'municipalities');
    
    // 6. Generate statistics
    console.log('\n📊 Phase 6: Analyzing tables...');
    await generateStatistics(client);
    
    console.log('\n🎉 Complete ISTAT 2025 boundaries import completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Test province and municipality loading in frontend');
    console.log('2. Verify that province codes now match correctly');
    console.log('3. Remove backup tables if everything works:');
    console.log('   DROP TABLE regions_backup, provinces_backup, municipalities_backup;');
    
  } catch (error) {
    console.error('\n💥 Import failed:', error.message);
    
    // Restore backups if something went wrong
    try {
      console.log('🔄 Attempting to restore backups...');
      await client.query(`
        DROP TABLE IF EXISTS regions;
        DROP TABLE IF EXISTS provinces;
        ALTER TABLE IF EXISTS regions_backup RENAME TO regions;
        ALTER TABLE IF EXISTS provinces_backup RENAME TO provinces;
      `);
      console.log('✅ Backups restored');
    } catch (restoreError) {
      console.error('❌ Failed to restore backups:', restoreError.message);
    }
    
    throw error;
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

async function main() {
  try {
    await importAllISTATBoundaries();
  } catch (error) {
    console.error('\n💥 Process failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { importAllISTATBoundaries };