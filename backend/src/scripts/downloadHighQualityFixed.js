#!/usr/bin/env node

/**
 * Direct download of high-quality Italian administrative boundaries
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

const DATA_DIR = path.join(__dirname, '../data');
const HIGH_QUALITY_DIR = path.join(DATA_DIR, 'high_quality');

// Ensure directories exist
if (!fs.existsSync(HIGH_QUALITY_DIR)) {
  fs.mkdirSync(HIGH_QUALITY_DIR, { recursive: true });
}

// High-quality data sources
const SOURCES = {
  simplified: {
    url: 'https://raw.githubusercontent.com/stefanocudini/italy-geojson/master/italy_municipalities_1000.geojson',
    filename: 'municipalities_high_quality.geojson',
    description: 'Italy municipalities - High quality boundaries',
    size: '~20MB'
  },
  alternative: {
    url: 'https://raw.githubusercontent.com/MatteoHenryChinaski/Comuni-Italiani-2018-Umbria/master/italy_geo.json',
    filename: 'municipalities_alternative.geojson',
    description: 'Alternative Italy municipalities source',
    size: '~10MB'
  },
  istat_direct: {
    url: 'https://www.istat.it/storage/cartografia/confini_amministrativi/generalizzati/Limiti01012023_g.zip',
    filename: 'istat_2023.zip',
    description: 'ISTAT 2023 - Direct download (requires extraction)',
    size: '~30MB'
  }
};

function downloadFile(url, filepath, description) {
  return new Promise((resolve, reject) => {
    console.log(`📥 Downloading: ${description}`);
    
    const file = fs.createWriteStream(filepath);
    const request = https.get(url, (response) => {
      if (response.statusCode === 301 || response.statusCode === 302) {
        return downloadFile(response.headers.location, filepath, description).then(resolve).catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`Failed: ${response.statusCode}`));
        return;
      }
      
      const totalSize = parseInt(response.headers['content-length'], 10);
      let downloadedSize = 0;
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        if (totalSize) {
          const progress = Math.floor((downloadedSize / totalSize) * 100);
          if (progress % 10 === 0) {
            process.stdout.write(`\r📥 Progress: ${progress}%`);
          }
        }
      });
      
      response.pipe(file);
    });
    
    file.on('finish', () => {
      file.close();
      console.log(`\n✅ Downloaded: ${filepath}`);
      resolve();
    });
    
    request.on('error', reject);
  });
}

async function validateFile(filepath) {
  try {
    const stats = fs.statSync(filepath);
    console.log(`📊 Size: ${(stats.size / 1024 / 1024).toFixed(1)} MB`);
    
    const sample = fs.readFileSync(filepath, { encoding: 'utf8', start: 0, end: 1000 });
    if (sample.includes('FeatureCollection')) {
      console.log('✅ Valid GeoJSON detected');
      return true;
    }
    return false;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🚀 Downloading high-quality municipality boundaries...');
  
  const source = SOURCES.simplified; // Use simplified for better performance
  const filepath = path.join(HIGH_QUALITY_DIR, source.filename);
  
  try {
    // Check if already exists
    if (fs.existsSync(filepath) && await validateFile(filepath)) {
      console.log('✅ High-quality data already available');
      console.log(`📁 Location: ${filepath}`);
    } else {
      // Download
      await downloadFile(source.url, filepath, source.description);
      
      if (!(await validateFile(filepath))) {
        throw new Error('Downloaded file validation failed');
      }
    }
    
    console.log('\n🎯 Next steps:');
    console.log('1. Import the data: npm run import-quality-geo');
    console.log('2. Check the improved boundaries in the map');
    
    return filepath;
    
  } catch (error) {
    console.error('\n💥 Download failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };