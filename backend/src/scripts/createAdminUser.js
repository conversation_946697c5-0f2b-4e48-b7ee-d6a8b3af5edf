const prisma = require('../lib/prisma');
const bcrypt = require('bcryptjs');

async function createAdminUser() {
  try {
    // Hash della password
    const hashedPassword = await bcrypt.hash('Al0xan999', 10);
    
    // Crea o aggiorna l'utente amministratore
    const user = await prisma.user.upsert({
      where: {
        email: '<EMAIL>'
      },
      update: {
        name: '<PERSON><PERSON>',
        role: 'admin',
        password: hashedPassword
      },
      create: {
        email: '<EMAIL>',
        name: '<PERSON><PERSON>',
        role: 'admin',
        password: hashedPassword
      }
    });
    
    console.log('Utente amministratore creato/aggiornato con successo:', {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Errore nella creazione dell\'utente:', error);
    process.exit(1);
  }
}

createAdminUser();