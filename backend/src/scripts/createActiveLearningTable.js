const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../../config/.env') });
dotenv.config();

const pool = require('../lib/db');

async function createActiveLearningTable() {
  try {
    console.log('Creating active learning table...');
    
    const query = `
      CREATE TABLE IF NOT EXISTS active_learning_iterations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        iteration_id VARCHAR(255) UNIQUE NOT NULL,
        batch_id UUID REFERENCES annotation_batches(id),
        num_samples INTEGER NOT NULL,
        strategy VARCHAR(50) NOT NULL,
        cvat_task_id VARCHAR(255),
        status VARCHAR(50) DEFAULT 'pending',
        started_at TIMESTAMP DEFAULT NOW(),
        completed_at TIMESTAMP,
        metrics JSONB DEFAULT '{}'::jsonb,
        CONSTRAINT status_check CHECK (status IN ('pending', 'in_progress', 'completed', 'failed'))
      );
      
      CREATE INDEX IF NOT EXISTS idx_al_iterations_batch ON active_learning_iterations(batch_id);
      CREATE INDEX IF NOT EXISTS idx_al_iterations_status ON active_learning_iterations(status);
    `;
    
    await pool.query(query);
    
    console.log('Active learning table created successfully!');
  } catch (error) {
    console.error('Error creating active learning table:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run if executed directly
if (require.main === module) {
  createActiveLearningTable().catch(console.error);
}

module.exports = createActiveLearningTable;