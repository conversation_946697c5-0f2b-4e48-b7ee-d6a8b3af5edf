#!/usr/bin/env node

/**
 * Import ISTAT 2025 data into Prisma schema
 * Converts from ISTAT format to Prisma schema format
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
const prisma = require('../lib/prisma');

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL || '**********************************************************************************************************/astrameccanica'
};

async function importProvincesFromISTAT() {
  console.log('📍 Importing provinces from ISTAT 2025 data...');
  
  const provincesPath = path.join(__dirname, '../data/high_quality/provinces_istat_2025.geojson');
  
  if (!fs.existsSync(provincesPath)) {
    throw new Error(`Provinces ISTAT file not found at ${provincesPath}`);
  }
  
  const provincesData = JSON.parse(fs.readFileSync(provincesPath, 'utf8'));
  
  // Clear existing data
  console.log('🗑️ Clearing existing provinces...');
  await prisma.municipality.deleteMany();
  await prisma.province.deleteMany();
  
  let importedCount = 0;
  let skippedCount = 0;
  
  for (const feature of provincesData.features) {
    const props = feature.properties;
    const geometry = feature.geometry;
    
    // Extract data from ISTAT format
    const code = props.SIGLA || props.sigla_prov;
    const name = props.DEN_PROV || props.DEN_UTS || props.denom_prov;
    const regionCode = props.COD_REG ? props.COD_REG.toString() : '';
    const regionName = props.DEN_REG || props.denom_reg || '';
    
    if (!code || !name) {
      console.warn('⚠️ Skipping province with missing code or name:', props);
      skippedCount++;
      continue;
    }
    
    try {
      await prisma.province.create({
        data: {
          code: code,
          name: name,
          regionCode: regionCode,
          regionName: regionName,
          geometry: geometry
        }
      });
      
      importedCount++;
      
      if (importedCount % 10 === 0) {
        console.log(`📍 Imported ${importedCount} provinces...`);
      }
    } catch (error) {
      console.error(`❌ Error importing province ${name}:`, error.message);
      skippedCount++;
    }
  }
  
  console.log(`✅ Imported ${importedCount} provinces successfully`);
  if (skippedCount > 0) {
    console.log(`⚠️ Skipped ${skippedCount} provinces`);
  }
  
  return importedCount;
}

async function importMunicipalitiesFromISTAT() {
  console.log('🏘️ Importing municipalities from ISTAT 2025 data...');
  
  const municipalitiesPath = path.join(__dirname, '../data/high_quality/municipalities_istat_2025.geojson');
  
  if (!fs.existsSync(municipalitiesPath)) {
    throw new Error(`Municipalities ISTAT file not found at ${municipalitiesPath}`);
  }
  
  const municipalitiesData = JSON.parse(fs.readFileSync(municipalitiesPath, 'utf8'));
  
  // Get all province codes with their numeric codes for mapping
  const client = new Client(dbConfig);
  await client.connect();
  
  const provinceMapping = {};
  try {
    // Read the ISTAT provinces to create a mapping from numeric to letter codes
    const provincesPath = path.join(__dirname, '../data/high_quality/provinces_istat_2025.geojson');
    const provincesData = JSON.parse(fs.readFileSync(provincesPath, 'utf8'));
    
    for (const feature of provincesData.features) {
      const props = feature.properties;
      const numericCode = props.COD_PROV;
      const letterCode = props.SIGLA;
      if (numericCode && letterCode) {
        provinceMapping[numericCode.toString()] = letterCode;
      }
    }
  } catch (err) {
    console.error('Warning: Could not create province mapping:', err.message);
  } finally {
    await client.end();
  }
  
  // Get existing provinces from database
  const provinces = await prisma.province.findMany({ select: { code: true } });
  const provinceCodes = new Set(provinces.map(p => p.code));
  
  let importedCount = 0;
  let skippedCount = 0;
  
  for (const feature of municipalitiesData.features) {
    const props = feature.properties;
    const geometry = feature.geometry;
    
    // Extract data from ISTAT format
    const code = props.PRO_COM_T || props.PRO_COM || props.pro_com_t;
    const name = props.COMUNE || props.COMUNE_A || props.comune;
    const numericProvinceCode = props.COD_PROV ? props.COD_PROV.toString() : '';
    const regionCode = props.COD_REG ? props.COD_REG.toString() : '';
    const regionName = props.DEN_REG || '';
    
    // Map numeric province code to letter code
    let provinceCode = provinceMapping[numericProvinceCode] || numericProvinceCode;
    
    if (!code || !name || !provinceCode) {
      console.warn('⚠️ Skipping municipality with missing data:', {
        code, name, provinceCode, props
      });
      skippedCount++;
      continue;
    }
    
    // Check if the province exists
    if (!provinceCodes.has(provinceCode)) {
      console.warn(`⚠️ Skipping municipality ${name} - province ${provinceCode} not found`);
      skippedCount++;
      continue;
    }
    
    try {
      await prisma.municipality.create({
        data: {
          code: code.toString(),
          name: name,
          provinceCode: provinceCode,
          regionCode: regionCode,
          regionName: regionName,
          geometry: geometry
        }
      });
      
      importedCount++;
      
      if (importedCount % 500 === 0) {
        console.log(`🏘️ Imported ${importedCount} municipalities...`);
      }
    } catch (error) {
      console.error(`❌ Error importing municipality ${name}:`, error.message);
      skippedCount++;
    }
  }
  
  console.log(`✅ Imported ${importedCount} municipalities successfully`);
  if (skippedCount > 0) {
    console.log(`⚠️ Skipped ${skippedCount} municipalities`);
  }
  
  return importedCount;
}

async function createSpatialIndexes() {
  console.log('🗂️ Creating spatial indexes...');
  
  const client = new Client(dbConfig);
  await client.connect();
  
  try {
    // Add geometry columns and indexes
    await client.query(`
      ALTER TABLE provinces ADD COLUMN IF NOT EXISTS geom geometry(Geometry, 4326);
      ALTER TABLE municipalities ADD COLUMN IF NOT EXISTS geom geometry(Geometry, 4326);
    `);
    
    // Update geometry columns from JSON
    await client.query(`
      UPDATE provinces 
      SET geom = ST_GeomFromGeoJSON(geometry::text) 
      WHERE geom IS NULL AND geometry IS NOT NULL;
      
      UPDATE municipalities 
      SET geom = ST_GeomFromGeoJSON(geometry::text) 
      WHERE geom IS NULL AND geometry IS NOT NULL;
    `);
    
    // Create indexes
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_provinces_geom ON provinces USING GIST(geom);
      CREATE INDEX IF NOT EXISTS idx_municipalities_geom ON municipalities USING GIST(geom);
      CREATE INDEX IF NOT EXISTS idx_municipalities_province_code ON municipalities(province_code);
      CREATE INDEX IF NOT EXISTS idx_municipalities_name ON municipalities(name);
    `);
    
    console.log('✅ Spatial indexes created');
  } catch (error) {
    console.error('⚠️ Error creating indexes:', error.message);
  } finally {
    await client.end();
  }
}

async function showStatistics() {
  console.log('\n📊 Database statistics:');
  
  const provinceCount = await prisma.province.count();
  const municipalityCount = await prisma.municipality.count();
  
  console.log(`📍 Total provinces: ${provinceCount}`);
  console.log(`🏘️ Total municipalities: ${municipalityCount}`);
  
  // Test Piacenza
  const piacenza = await prisma.province.findFirst({
    where: { code: 'PC' },
    include: {
      municipalities: {
        select: { name: true }
      }
    }
  });
  
  if (piacenza) {
    console.log(`\n🧪 Test - Piacenza (PC): ${piacenza.municipalities.length} municipalities`);
  }
  
  // Show regions summary
  const regionSummary = await prisma.province.groupBy({
    by: ['regionName'],
    _count: {
      regionName: true
    },
    orderBy: {
      regionName: 'asc'
    }
  });
  
  console.log('\n📍 Provinces by region:');
  regionSummary.forEach(region => {
    if (region.regionName) {
      console.log(`  - ${region.regionName}: ${region._count.regionName} provinces`);
    }
  });
}

async function main() {
  console.log('🚀 Starting ISTAT 2025 data import to Prisma schema...');
  
  try {
    const provinceCount = await importProvincesFromISTAT();
    
    if (provinceCount > 0) {
      const municipalityCount = await importMunicipalitiesFromISTAT();
      
      if (municipalityCount > 0) {
        await createSpatialIndexes();
      }
    }
    
    await showStatistics();
    
    console.log('\n🎉 ISTAT 2025 data import completed successfully!');
    console.log('🔄 The frontend should now show all provinces and municipalities');
    
  } catch (error) {
    console.error('\n💥 Import failed:', error);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };