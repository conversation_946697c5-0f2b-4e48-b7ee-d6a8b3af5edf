// Test script per verificare la geolocalizzazione
const { getLocationFromBBox } = require('../services/geoLocationService');

async function testLocations() {
  console.log('Testing location service...\n');
  
  // Test cases per diverse zone d'Italia
  const testCases = [
    {
      name: 'Milano Centro',
      bbox: [9.185, 45.464, 9.195, 45.469]
    },
    {
      name: 'Roma Centro', 
      bbox: [12.480, 41.890, 12.490, 41.895]
    },
    {
      name: 'Napoli',
      bbox: [14.240, 40.835, 14.250, 40.840]
    },
    {
      name: '<PERSON><PERSON><PERSON> (Palermo)',
      bbox: [13.360, 38.110, 13.370, 38.115]
    }
  ];
  
  for (const test of testCases) {
    try {
      console.log(`\nTesting ${test.name}:`);
      console.log(`BBox: ${test.bbox.join(', ')}`);
      
      const location = await getLocationFromBBox(test.bbox);
      
      console.log('Result:');
      console.log(`- Region: ${location.region?.name || 'Not found'}`);
      console.log(`- Province: ${location.province?.name || 'Not found'}`);
      console.log(`- Municipality: ${location.municipality?.name || 'Not found'}`);
      
      const path = `original/${location.region?.name || 'unknown'}/${location.province?.name || 'unknown'}/${location.municipality?.name || 'unknown'}/`;
      console.log(`- S3 Path: ${path}`);
      
    } catch (error) {
      console.error(`Error for ${test.name}:`, error.message);
    }
  }
  
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  testLocations();
}

module.exports = { testLocations };