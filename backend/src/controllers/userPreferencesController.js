const prisma = require('../lib/prisma');

// Default preferences
const DEFAULT_PREFERENCES = {
  mapDownloader: {
    defaultStyle: 's',
    defaultFormat: 'jpeg',
    defaultZoom: '18',
    autoDownload: false,
    saveHistory: true,
    cacheEnabled: true,
    cacheSize: '500',
    compressionLevel: '80',
    maxConcurrentDownloads: '3',
    notifyOnComplete: true,
    notifyOnError: true,
    emailNotifications: false,
    notificationEmail: '',
    apiKey: '',
    apiEndpoint: 'http://localhost:3000/api'
  }
};

/**
 * Get user preferences
 */
async function getPreferences(req, res) {
  try {
    const userId = req.user.id;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { preferences: true }
    });

    if (!user) {
      return res.status(404).json({ error: 'Utente non trovato' });
    }

    // Merge with defaults to ensure all fields are present
    const preferences = user.preferences || {};
    const mergedPreferences = {
      ...DEFAULT_PREFERENCES,
      ...preferences,
      mapDownloader: {
        ...DEFAULT_PREFERENCES.mapDownloader,
        ...(preferences.mapDownloader || {})
      }
    };

    res.json({
      success: true,
      preferences: mergedPreferences
    });
  } catch (error) {
    console.error('Error getting user preferences:', error);
    res.status(500).json({
      error: 'Errore nel recupero delle preferenze',
      details: error.message
    });
  }
}

/**
 * Update user preferences
 */
async function updatePreferences(req, res) {
  try {
    const userId = req.user.id;
    const { preferences } = req.body;

    if (!preferences) {
      return res.status(400).json({ error: 'Preferenze mancanti' });
    }

    // Update user preferences
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        preferences: preferences
      },
      select: {
        id: true,
        email: true,
        preferences: true
      }
    });

    res.json({
      success: true,
      message: 'Preferenze aggiornate con successo',
      preferences: updatedUser.preferences
    });
  } catch (error) {
    console.error('Error updating user preferences:', error);
    res.status(500).json({
      error: 'Errore nell\'aggiornamento delle preferenze',
      details: error.message
    });
  }
}

/**
 * Reset user preferences to defaults
 */
async function resetPreferences(req, res) {
  try {
    const userId = req.user.id;

    // Reset to default preferences
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        preferences: DEFAULT_PREFERENCES
      },
      select: {
        id: true,
        email: true,
        preferences: true
      }
    });

    res.json({
      success: true,
      message: 'Preferenze ripristinate ai valori predefiniti',
      preferences: updatedUser.preferences
    });
  } catch (error) {
    console.error('Error resetting user preferences:', error);
    res.status(500).json({
      error: 'Errore nel ripristino delle preferenze',
      details: error.message
    });
  }
}

module.exports = {
  getPreferences,
  updatePreferences,
  resetPreferences
};