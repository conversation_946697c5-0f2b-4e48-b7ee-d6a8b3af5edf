const userService = require('../services/userService');
const authService = require('../services/authService');

/**
 * Get all users
 */
async function getAllUsers(req, res) {
  try {
    const users = await userService.getAllUsers();
    res.json({
      success: true,
      users: users,
      total: users.length
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({
      success: false,
      error: 'Errore nel recupero degli utenti'
    });
  }
}

/**
 * Get user by ID
 */
async function getUserById(req, res) {
  try {
    const user = await userService.getUserById(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Utente non trovato'
      });
    }
    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({
      success: false,
      error: 'Errore nel recupero dell\'utente'
    });
  }
}

/**
 * Create new user
 */
async function createUser(req, res) {
  try {
    const { email, name, password, role } = req.body;
    
    // Validate required fields
    if (!email || !name || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email, nome e password sono obbligatori'
      });
    }
    
    // Check if user already exists
    const existingUser = await userService.getUserByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: 'Un utente con questa email esiste già'
      });
    }
    
    const user = await userService.createUser({
      email,
      name,
      password,
      role: role || 'user'
    });
    
    res.status(201).json({
      success: true,
      user
    });
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({
      success: false,
      error: 'Errore nella creazione dell\'utente'
    });
  }
}

/**
 * Update user
 */
async function updateUser(req, res) {
  try {
    const { id } = req.params;
    const userData = req.body;
    
    // Check if user exists
    const existingUser = await userService.getUserById(id);
    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: 'Utente non trovato'
      });
    }
    
    // If email is being updated, check if it's already taken
    if (userData.email && userData.email !== existingUser.email) {
      const emailUser = await userService.getUserByEmail(userData.email);
      if (emailUser) {
        return res.status(409).json({
          success: false,
          error: 'Email già in uso'
        });
      }
    }
    
    const user = await userService.updateUser(id, userData);
    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({
      success: false,
      error: 'Errore nell\'aggiornamento dell\'utente'
    });
  }
}

/**
 * Delete user
 */
async function deleteUser(req, res) {
  try {
    const { id } = req.params;
    
    // Check if user exists
    const existingUser = await userService.getUserById(id);
    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: 'Utente non trovato'
      });
    }
    
    await userService.deleteUser(id);
    res.json({
      success: true,
      message: 'Utente eliminato con successo'
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({
      success: false,
      error: 'Errore nell\'eliminazione dell\'utente'
    });
  }
}

/**
 * Login user
 */
async function login(req, res) {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email e password sono obbligatori'
      });
    }
    
    const result = await authService.login(email, password);
    
    if (!result.success) {
      return res.status(401).json(result);
    }
    
    res.json({
      success: true,
      user: result.user,
      token: result.token
    });
  } catch (error) {
    console.error('Error during login:', error);
    res.status(500).json({
      success: false,
      error: 'Errore durante il login'
    });
  }
}

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  login
};