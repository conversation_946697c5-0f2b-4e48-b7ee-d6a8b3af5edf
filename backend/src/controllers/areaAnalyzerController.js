const prisma = require('../lib/prisma');
const { Pool } = require('pg');
const path = require('path');
const fs = require('fs');

// PostgreSQL connection for PostGIS queries
const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

/**
 * Get buildings data for a specific area
 * Returns GeoJSON with buildings from OpenStreetMap or Google data
 */
async function getBuildingsInArea(req, res) {
  try {
    const { bounds, source = 'osm' } = req.query;
    
    if (!bounds) {
      return res.status(400).json({ error: 'Bounds parameter is required' });
    }
    
    // Parse bounds: "sw_lng,sw_lat,ne_lng,ne_lat"
    const [swLng, swLat, neLng, neLat] = bounds.split(',').map(Number);
    
    if (!swLng || !swLat || !neLng || !neLat) {
      return res.status(400).json({ error: 'Invalid bounds format. Use: sw_lng,sw_lat,ne_lng,ne_lat' });
    }
    
    // First check if PostGIS tables exist
    const checkTableQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'planet_osm_polygon'
      ) as table_exists
    `;
    
    let tableExists = false;
    try {
      const checkResult = await pool.query(checkTableQuery);
      tableExists = checkResult.rows[0]?.table_exists || false;
    } catch (err) {
      console.log('PostGIS tables not found, will use fallback data');
    }
    
    if (!tableExists) {
      // Return empty result if OSM tables don't exist
      return res.json({
        type: 'FeatureCollection',
        features: [],
        metadata: {
          source,
          total: 0,
          bounds: { swLng, swLat, neLng, neLat },
          message: 'OpenStreetMap data not available. Please import OSM data or use downloaded maps.'
        }
      });
    }
    
    // Query buildings from OpenStreetMap data in PostGIS
    const query = `
      SELECT 
        osm_id as id,
        name,
        building,
        amenity,
        shop,
        office,
        "addr:housenumber" as house_number,
        "addr:street" as street,
        "addr:city" as city,
        "addr:postcode" as postcode,
        height,
        "building:levels" as levels,
        ST_AsGeoJSON(ST_Transform(way, 4326)) as geometry,
        ST_Area(ST_Transform(way, 3857)) as area
      FROM planet_osm_polygon
      WHERE building IS NOT NULL
        AND ST_Intersects(
          way,
          ST_Transform(
            ST_MakeEnvelope($1, $2, $3, $4, 4326),
            3857
          )
        )
      LIMIT 1000
    `;
    
    try {
      const result = await pool.query(query, [swLng, swLat, neLng, neLat]);
      
      const features = result.rows.map(row => ({
        type: 'Feature',
        id: row.id,
        properties: {
          id: row.id,
          name: row.name || `Building ${row.id}`,
          type: row.building || 'yes',
          address: [
            row.house_number,
            row.street,
            row.city,
            row.postcode
          ].filter(Boolean).join(', ') || 'Unknown address',
          floors: row.levels ? parseInt(row.levels) : null,
          height: row.height ? parseFloat(row.height) : null,
          area: Math.round(row.area),
          amenity: row.amenity,
          shop: row.shop,
          office: row.office
        },
        geometry: JSON.parse(row.geometry)
      }));
      
      res.json({
        type: 'FeatureCollection',
        features,
        metadata: {
          source,
          total: features.length,
          bounds: { swLng, swLat, neLng, neLat }
        }
      });
    } catch (dbError) {
      console.error('Database query error:', dbError);
      
      // Fallback: try to get data from downloaded maps
      const allMaps = await prisma.map.findMany({
        orderBy: {
          zoomLevel: 'desc'
        }
      });
      
      const maps = allMaps.filter(map => {
        if (!map.boundingBoxNW || !map.boundingBoxSE) return false;
        const [mapNwLng, mapNwLat] = map.boundingBoxNW;
        const [mapSeLng, mapSeLat] = map.boundingBoxSE;
        // Check if map overlaps with requested bounds
        return mapNwLng <= neLng && mapSeLng >= swLng && 
               mapNwLat >= swLat && mapSeLat <= neLat;
      }).slice(0, 1);
      
      if (maps.length > 0) {
        // Return map metadata as building placeholder
        res.json({
          type: 'FeatureCollection',
          features: [],
          metadata: {
            source: 'maps',
            mapAvailable: true,
            mapId: maps[0].id,
            zoomLevel: maps[0].zoomLevel,
            message: 'Building extraction from maps coming soon'
          }
        });
      } else {
        res.json({
          type: 'FeatureCollection',
          features: [],
          metadata: {
            source,
            message: 'No data available for this area'
          }
        });
      }
    }
  } catch (error) {
    console.error('Error fetching buildings:', error);
    res.status(500).json({ error: 'Error fetching buildings data' });
  }
}

/**
 * Get companies/businesses in a specific area
 * Returns data from Google Places or other sources
 */
async function getCompaniesInArea(req, res) {
  try {
    const { bounds, types = 'all' } = req.query;
    
    if (!bounds) {
      return res.status(400).json({ error: 'Bounds parameter is required' });
    }
    
    // Parse bounds
    const [swLng, swLat, neLng, neLat] = bounds.split(',').map(Number);
    
    if (!swLng || !swLat || !neLng || !neLat) {
      return res.status(400).json({ error: 'Invalid bounds format' });
    }
    
    // First check if PostGIS tables exist
    const checkTableQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'planet_osm_polygon'
      ) as table_exists
    `;
    
    let tableExists = false;
    try {
      const checkResult = await pool.query(checkTableQuery);
      tableExists = checkResult.rows[0]?.table_exists || false;
    } catch (err) {
      console.log('PostGIS tables not found');
    }
    
    if (!tableExists) {
      // Return empty result if OSM tables don't exist
      return res.json({
        success: true,
        companies: [],
        total: 0,
        bounds: { swLng, swLat, neLng, neLat },
        message: 'OpenStreetMap data not available'
      });
    }
    
    // Try to get POI data from OpenStreetMap
    const query = `
      SELECT 
        osm_id as id,
        name,
        amenity,
        shop,
        office,
        craft,
        tourism,
        "addr:housenumber" as house_number,
        "addr:street" as street,
        "addr:city" as city,
        phone,
        website,
        opening_hours,
        ST_X(ST_Transform(ST_Centroid(way), 4326)) as lng,
        ST_Y(ST_Transform(ST_Centroid(way), 4326)) as lat
      FROM planet_osm_polygon
      WHERE (
        amenity IS NOT NULL 
        OR shop IS NOT NULL 
        OR office IS NOT NULL 
        OR craft IS NOT NULL
        OR tourism IS NOT NULL
      )
        AND name IS NOT NULL
        AND ST_Intersects(
          way,
          ST_Transform(
            ST_MakeEnvelope($1, $2, $3, $4, 4326),
            3857
          )
        )
      UNION ALL
      SELECT 
        osm_id as id,
        name,
        amenity,
        shop,
        office,
        craft,
        tourism,
        "addr:housenumber" as house_number,
        "addr:street" as street,
        "addr:city" as city,
        phone,
        website,
        opening_hours,
        ST_X(ST_Transform(way, 4326)) as lng,
        ST_Y(ST_Transform(way, 4326)) as lat
      FROM planet_osm_point
      WHERE (
        amenity IS NOT NULL 
        OR shop IS NOT NULL 
        OR office IS NOT NULL 
        OR craft IS NOT NULL
        OR tourism IS NOT NULL
      )
        AND name IS NOT NULL
        AND ST_Intersects(
          way,
          ST_Transform(
            ST_MakeEnvelope($1, $2, $3, $4, 4326),
            3857
          )
        )
      LIMIT 500
    `;
    
    try {
      const result = await pool.query(query, [swLng, swLat, neLng, neLat]);
      
      const companies = result.rows.map(row => {
        // Determine company type
        let type = 'other';
        let category = row.amenity || row.shop || row.office || row.craft || row.tourism;
        
        if (row.amenity) type = 'amenity';
        if (row.shop) type = 'shop';
        if (row.office) type = 'office';
        if (row.craft) type = 'craft';
        if (row.tourism) type = 'tourism';
        
        return {
          id: `osm_${row.id}`,
          name: row.name,
          type,
          category,
          address: [
            row.house_number,
            row.street,
            row.city
          ].filter(Boolean).join(', ') || 'Unknown address',
          phone: row.phone,
          website: row.website,
          openingHours: row.opening_hours,
          location: {
            lat: row.lat,
            lng: row.lng
          },
          source: 'openstreetmap'
        };
      });
      
      res.json({
        success: true,
        companies,
        total: companies.length,
        bounds: { swLng, swLat, neLng, neLat }
      });
    } catch (dbError) {
      console.error('Database query error:', dbError);
      
      // Return empty result if database query fails
      res.json({
        success: true,
        companies: [],
        total: 0,
        message: 'No company data available for this area'
      });
    }
  } catch (error) {
    console.error('Error fetching companies:', error);
    res.status(500).json({ error: 'Error fetching companies data' });
  }
}

/**
 * Get area statistics (buildings count, types, etc.)
 */
async function getAreaStatistics(req, res) {
  try {
    const { bounds } = req.query;
    
    if (!bounds) {
      return res.status(400).json({ error: 'Bounds parameter is required' });
    }
    
    const [swLng, swLat, neLng, neLat] = bounds.split(',').map(Number);
    
    if (!swLng || !swLat || !neLng || !neLat) {
      return res.status(400).json({ error: 'Invalid bounds format' });
    }
    
    // First check if PostGIS tables exist
    const checkTableQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'planet_osm_polygon'
      ) as table_exists
    `;
    
    let tableExists = false;
    try {
      const checkResult = await pool.query(checkTableQuery);
      tableExists = checkResult.rows[0]?.table_exists || false;
    } catch (err) {
      console.log('PostGIS tables not found');
    }
    
    if (!tableExists) {
      // Return basic statistics from maps if OSM not available
      // Fetch all maps and filter in JavaScript since Prisma doesn't support JSON path queries well
      const allMaps = await prisma.map.findMany();
      const maps = allMaps.filter(map => {
        if (!map.boundingBoxNW || !map.boundingBoxSE) return false;
        const [mapNwLng, mapNwLat] = map.boundingBoxNW;
        const [mapSeLng, mapSeLat] = map.boundingBoxSE;
        // Check if map overlaps with requested bounds
        return mapNwLng <= neLng && mapSeLng >= swLng && 
               mapNwLat >= swLat && mapSeLat <= neLat;
      });
      
      return res.json({
        totalBuildings: 0,
        totalArea: 0,
        buildingTypes: {},
        mapsAvailable: maps.length,
        bounds: { swLng, swLat, neLng, neLat },
        message: 'Building statistics not available. OpenStreetMap data needed.'
      });
    }
    
    // Get statistics from database
    const statsQuery = `
      WITH area_buildings AS (
        SELECT 
          building,
          COUNT(*) as count,
          SUM(ST_Area(ST_Transform(way, 3857))) as total_area,
          AVG(CASE WHEN "building:levels" ~ '^[0-9]+$' THEN "building:levels"::int ELSE NULL END) as avg_floors
        FROM planet_osm_polygon
        WHERE building IS NOT NULL
          AND ST_Intersects(
            way,
            ST_Transform(
              ST_MakeEnvelope($1, $2, $3, $4, 4326),
              3857
            )
          )
        GROUP BY building
      )
      SELECT 
        building,
        count,
        total_area,
        avg_floors
      FROM area_buildings
      ORDER BY count DESC
    `;
    
    try {
      const result = await pool.query(statsQuery, [swLng, swLat, neLng, neLat]);
      
      // Calculate totals
      const totalBuildings = result.rows.reduce((sum, row) => sum + parseInt(row.count), 0);
      const totalArea = result.rows.reduce((sum, row) => sum + parseFloat(row.total_area), 0);
      
      // Group by type
      const buildingTypes = {};
      result.rows.forEach(row => {
        buildingTypes[row.building || 'unknown'] = {
          count: parseInt(row.count),
          area: Math.round(row.total_area),
          avgFloors: row.avg_floors ? Math.round(row.avg_floors) : null
        };
      });
      
      res.json({
        totalBuildings,
        totalArea: Math.round(totalArea),
        buildingTypes,
        bounds: { swLng, swLat, neLng, neLat }
      });
    } catch (dbError) {
      console.error('Statistics query error:', dbError);
      
      // Return basic statistics from maps if available
      const maps = await prisma.map.findMany({
        where: {
          AND: [
            { boundingBoxNW: { path: '[0]', gte: swLng } },
            { boundingBoxSE: { path: '[0]', lte: neLng } }
          ]
        }
      });
      
      res.json({
        totalBuildings: 0,
        totalArea: 0,
        mapsAvailable: maps.length,
        message: 'Building statistics not available, but maps are available for analysis'
      });
    }
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({ error: 'Error fetching area statistics' });
  }
}

/**
 * Get available map tiles for an area
 */
async function getAvailableMaps(req, res) {
  try {
    const { bounds } = req.query;
    
    if (!bounds) {
      // Return all available maps
      const maps = await prisma.map.findMany({
        orderBy: {
          createdAt: 'desc'
        },
        take: 100
      });
      
      return res.json({
        maps: maps.map(map => ({
          id: map.id,
          bounds: {
            northwest: map.boundingBoxNW,
            southeast: map.boundingBoxSE
          },
          zoomLevel: map.zoomLevel,
          style: map.mapStyle,
          format: map.outputFormat,
          createdAt: map.createdAt,
          storageKey: map.storageKey
        })),
        total: maps.length
      });
    }
    
    const [swLng, swLat, neLng, neLat] = bounds.split(',').map(Number);
    
    // Find maps that intersect with the requested bounds
    const allMaps = await prisma.map.findMany({
      orderBy: {
        zoomLevel: 'desc'
      }
    });
    
    const maps = allMaps.filter(map => {
      if (!map.boundingBoxNW || !map.boundingBoxSE) return false;
      const [mapNwLng, mapNwLat] = map.boundingBoxNW;
      const [mapSeLng, mapSeLat] = map.boundingBoxSE;
      // Check if map overlaps with requested bounds
      return mapNwLng <= neLng && mapSeLng >= swLng && 
             mapNwLat >= swLat && mapSeLat <= neLat;
    });
    
    res.json({
      maps: maps.map(map => ({
        id: map.id,
        bounds: {
          northwest: map.boundingBoxNW,
          southeast: map.boundingBoxSE
        },
        zoomLevel: map.zoomLevel,
        style: map.mapStyle,
        format: map.outputFormat,
        createdAt: map.createdAt,
        storageKey: map.storageKey,
        downloadUrl: `/api/maps/download/${map.storageKey}`
      })),
      total: maps.length,
      requestedBounds: { swLng, swLat, neLng, neLat }
    });
  } catch (error) {
    console.error('Error fetching available maps:', error);
    res.status(500).json({ error: 'Error fetching available maps' });
  }
}

module.exports = {
  getBuildingsInArea,
  getCompaniesInArea,
  getAreaStatistics,
  getAvailableMaps
};