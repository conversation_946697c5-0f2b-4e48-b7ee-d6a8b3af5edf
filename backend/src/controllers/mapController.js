const path = require('path');
const fs = require('fs');
const { runPythonScript, runPythonScriptWithProgress } = require('../pythonRunner');
const { uploadToS3, bucketConfig, getFromS3, createPresignedGetUrl } = require('../config/s3Config');
const prisma = require('../lib/prisma');
const { getLocationFromBBox, createS3PathFromLocation } = require('../services/geoLocationService');
const compressionService = require('../services/compressionService');
const { geocodeAddress, calculateBoundingBox } = require('../services/geocodingService');

/**
 * Generate a map image based on the provided parameters
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function generateMap(req, res) {
  try {
    const { bbox, zoom, style, format } = req.body;
    
    // Validate input parameters
    if (!bbox || !Array.isArray(bbox) || bbox.length !== 4) {
      return res.status(400).json({ error: 'Parametro bbox mancante o non valido' });
    }
    
    if (!zoom || isNaN(parseInt(zoom))) {
      return res.status(400).json({ error: 'Parametro zoom mancante o non valido' });
    }
    
    // Ensure zoom is in valid range (1-21 for Google Maps)
    const zoomLevel = parseInt(zoom);
    if (zoomLevel < 1 || zoomLevel > 21) {
      return res.status(400).json({ error: 'Livello di zoom deve essere tra 1 e 21' });
    }
    
    if (!style || typeof style !== 'string') {
      return res.status(400).json({ error: 'Parametro style mancante o non valido' });
    }
    
    if (!format || !['jpeg', 'geotiff'].includes(format.toLowerCase())) {
      return res.status(400).json({ error: 'Parametro format mancante o non valido (deve essere "jpeg" o "geotiff")' });
    }
    
    // Create a unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `map_${bbox.join('_')}_z${zoom}_${style}_${timestamp}.${format === 'jpeg' ? 'jpg' : 'tif'}`;
    const outputPath = path.join(__dirname, '../../../outputs', filename);
    
    // Prepare arguments for Python script with new interface
    const args = [
      '--left', bbox[0].toString(),     // min longitude
      '--bottom', bbox[1].toString(),   // min latitude
      '--right', bbox[2].toString(),    // max longitude
      '--top', bbox[3].toString(),      // max latitude
      '--zoom', zoom.toString(),
      '--style', style,
      '--output', outputPath,
      '--threads', '10'                 // Use 10 threads for downloading
    ];
    
    // Run the Python script
    const scriptPath = path.join(__dirname, '../../../python/downloader.py');
    console.log(`Running Python script with args: ${args.join(' ')}`);
    const result = await runPythonScript(scriptPath, args);
    
    // Check if the file was created
    if (fs.existsSync(outputPath)) {
      // Upload the file to S3
      try {
        const fileContent = fs.readFileSync(outputPath);
        const metadata = {
          bbox: JSON.stringify(bbox),
          zoom: zoom.toString(),
          style,
          format
        };
        
        // Upload to S3
        const s3Result = await uploadToS3(
          fileContent, 
          filename, 
          bucketConfig.folders.processedMaps,
          metadata
        );
        
        // Save map metadata to database
        try {
          await prisma.map.create({
            data: {
              boundingBoxNW: [bbox[0], bbox[3]], // [west/left longitude, north/top latitude]
              boundingBoxSE: [bbox[2], bbox[1]], // [east/right longitude, south/bottom latitude]
              zoomLevel: zoomLevel,
              mapStyle: style,
              outputFormat: format,
              storageBucket: bucketConfig.name,
              storageKey: s3Result.Key,
              metadata: {
                timestamp: new Date().toISOString(),
                filename: filename,
                s3Location: s3Result.Location
              }
            }
          });
          console.log('Map metadata saved to database');
        } catch (dbError) {
          console.error('Error saving map to database:', dbError);
          // Continue even if database save fails - don't fail the entire request
        }
        
        // Return the S3 URL for download
        res.json({ 
          success: true, 
          message: 'Mappa generata con successo',
          fileUrl: s3Result.url,
          s3: {
            bucket: bucketConfig.name,
            key: s3Result.Key,
            location: s3Result.Location
          }
        });
        
        // Optionally remove local file after upload to save space
        // fs.unlinkSync(outputPath);
      } catch (uploadError) {
        console.error('Error uploading to S3:', uploadError);
        // Fallback to local file URL if S3 upload fails
        const fileUrl = `/outputs/${filename}`;
        
        // Save map metadata to database even for local storage
        try {
          await prisma.map.create({
            data: {
              boundingBoxNW: [bbox[0], bbox[3]], // [west/left longitude, north/top latitude]
              boundingBoxSE: [bbox[2], bbox[1]], // [east/right longitude, south/bottom latitude]
              zoomLevel: zoomLevel,
              mapStyle: style,
              outputFormat: format,
              storageBucket: 'local',
              storageKey: filename,
              metadata: {
                timestamp: new Date().toISOString(),
                filename: filename,
                localPath: outputPath
              }
            }
          });
          console.log('Map metadata saved to database (local storage)');
        } catch (dbError) {
          console.error('Error saving map to database:', dbError);
        }
        
        res.json({ 
          success: true, 
          message: 'Mappa generata con successo (salvataggio locale)',
          fileUrl,
          warning: 'Upload S3 fallito, utilizzando storage locale'
        });
      }
    } else {
      res.status(500).json({ 
        error: 'Errore nella generazione della mappa', 
        details: result.output
      });
    }
  } catch (error) {
    console.error('Error in generate map:', error);
    res.status(500).json({ 
      error: 'Errore del server',
      details: error.message
    });
  }
}

/**
 * Get available map styles
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
function getMapStyles(req, res) {
  // Return available map styles
  // Map style codes for Google Maps API:
  // m = standard roadmap
  // s = satellite only
  // y = satellite with labels (hybrid)
  // t = terrain only
  // p = terrain with labels
  // h = labels only (transparent)
  const styles = [
    { id: 's', name: 'Satellite' },
    { id: 'm', name: 'Stradale' },
    { id: 't', name: 'Terreno' },
    { id: 'y', name: 'Ibrido' },
    { id: 'p', name: 'Terreno con etichette' },
    { id: 'h', name: 'Solo etichette' }
  ];
  
  res.json(styles);
}

/**
 * Generate a map image asynchronously with progress updates
 * Uses the async Python downloader with WebSocket progress
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function generateMapAsync(req, res) {
  try {
    const { bbox, zoom, style, format } = req.body;
    
    // Validate input parameters
    if (!bbox || !Array.isArray(bbox) || bbox.length !== 4) {
      return res.status(400).json({ error: 'Parametro bbox mancante o non valido' });
    }
    
    if (!zoom || isNaN(parseInt(zoom))) {
      return res.status(400).json({ error: 'Parametro zoom mancante o non valido' });
    }
    
    // Ensure zoom is in valid range (1-21 for Google Maps)
    const zoomLevel = parseInt(zoom);
    if (zoomLevel < 1 || zoomLevel > 21) {
      return res.status(400).json({ error: 'Livello di zoom deve essere tra 1 e 21' });
    }
    
    if (!style || typeof style !== 'string') {
      return res.status(400).json({ error: 'Parametro style mancante o non valido' });
    }
    
    if (!format || !['jpeg', 'geotiff'].includes(format.toLowerCase())) {
      return res.status(400).json({ error: 'Parametro format mancante o non valido (deve essere "jpeg" o "geotiff")' });
    }
    
    // Create a unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `map_${bbox.join('_')}_z${zoom}_${style}_${timestamp}.${format === 'jpeg' ? 'jpg' : 'tif'}`;
    const outputPath = path.join(__dirname, '../../../outputs', filename);
    
    // Prepare arguments for Python script with new interface
    const args = [
      '--left', bbox[0].toString(),     // min longitude
      '--bottom', bbox[1].toString(),   // min latitude
      '--right', bbox[2].toString(),    // max longitude
      '--top', bbox[3].toString(),      // max latitude
      '--zoom', zoom.toString(),
      '--style', style,
      '--output', outputPath
    ];
    
    // Get the broadcast function from app.locals
    const broadcastProgress = req.app.locals.broadcastProgress;
    
    // Run the async Python script with progress tracking
    const scriptPath = path.join(__dirname, '../../../python/downloader_async.py');
    console.log(`Running async Python script with args: ${args.join(' ')}`);
    
    // Return initial response quickly
    res.json({ 
      success: true, 
      message: 'Generazione mappa avviata. Connettiti al WebSocket per aggiornamenti in tempo reale.',
      websocketUrl: `ws://localhost:3000`
    });
    
    // Continue processing in background
    runPythonScriptWithProgress(scriptPath, args, (progress) => {
      // Broadcast progress to all connected WebSocket clients
      if (broadcastProgress) {
        broadcastProgress(progress);
      }
    }).then(async (result) => {
      // Check if the file was created
      if (fs.existsSync(outputPath)) {
        // Get file size
        const stats = fs.statSync(outputPath);
        const fileSizeInMB = stats.size / (1024 * 1024);
        console.log(`Generated file size: ${fileSizeInMB.toFixed(2)} MB`);
        
        // Get location information from coordinates
        const location = await getLocationFromBBox(bbox);
        console.log('Location info:', location);
        
        // Create S3 path based on location hierarchy
        const s3Path = createS3PathFromLocation(location, 'original');
        console.log('S3 path for upload:', s3Path);
        
        // Upload the file to S3
        try {
          const metadata = {
            bbox: JSON.stringify(bbox),
            zoom: zoom.toString(),
            style,
            format,
            fileSize: stats.size.toString(),
            region: location.region?.name || 'unknown',
            province: location.province?.name || 'unknown',
            municipality: location.municipality?.name || 'unknown'
          };
          
          // For large files, use streaming
          let s3Result;
          if (stats.size > 1024 * 1024 * 1024) { // If larger than 1GB
            console.log('Using streaming upload for large file');
            const fileStream = fs.createReadStream(outputPath);
            s3Result = await uploadToS3(
              fileStream, 
              filename, 
              s3Path,
              metadata
            );
          } else {
            const fileContent = fs.readFileSync(outputPath);
            s3Result = await uploadToS3(
              fileContent, 
              filename, 
              s3Path,
              metadata
            );
          }
          
          console.log('S3 upload result:', {
            key: s3Result.Key,
            location: s3Result.Location
          });
          
          
          // Save to database first to get mapId
          let mapId = null;
          try {
            const mapRecord = await prisma.map.create({
              data: {
                boundingBoxNW: [bbox[0], bbox[3]], // [west/left longitude, north/top latitude]
                boundingBoxSE: [bbox[2], bbox[1]], // [east/right longitude, south/bottom latitude]
                zoomLevel: zoomLevel,
                mapStyle: style,
                outputFormat: format,
                storageBucket: bucketConfig.name,
                storageKey: s3Result.Key,
                fileSize: BigInt(stats.size),
                metadata: {
                  timestamp: new Date().toISOString(),
                  filename: filename,
                  location: location,
                  uploadedAt: new Date().toISOString(),
                  bbox: bbox // Keep original bbox in metadata for reference
                }
              }
            });
            mapId = mapRecord.id;
          } catch (dbError) {
            console.error('Error saving map to database:', dbError);
          }
          
          // Broadcast completion
          if (broadcastProgress) {
            broadcastProgress({
              type: 'completed',
              mapId: mapId, // Include the map ID
              fileUrl: `/api/download-map/${s3Result.Key}`, // Use download endpoint
              directUrl: s3Result.url, // Keep direct URL for reference
              s3: {
                bucket: bucketConfig.name,
                key: s3Result.Key,
                location: s3Result.Location
              },
              largeFile: stats.size > 100 * 1024 * 1024, // Mark as large if > 100MB
              fileSize: fileSizeInMB,
              fileSizeBytes: stats.size,
              locationInfo: {
                region: location.region?.name || 'unknown',
                province: location.province?.name || 'unknown',
                municipality: location.municipality?.name || 'unknown'
              },
              mapData: {
                bbox,
                zoom: zoomLevel,
                format,
                style,
                filename
              }
            });
          }
        } catch (uploadError) {
          console.error('Error uploading to S3:', uploadError);
          
          // For large files, provide local download link
          if (stats.size > 1024 * 1024 * 1024) { // If larger than 1GB
            // Save to database with local storage
            try {
              const mapRecord = await prisma.map.create({
                data: {
                  boundingBoxNW: [bbox[0], bbox[3]],
                  boundingBoxSE: [bbox[2], bbox[1]],
                  zoomLevel: zoomLevel,
                  mapStyle: style,
                  outputFormat: format,
                  storageBucket: 'local',
                  storageKey: filename,
                  metadata: {
                    timestamp: new Date().toISOString(),
                    filename: filename,
                    localPath: outputPath,
                    fileSize: stats.size,
                    fileSizeMB: fileSizeInMB
                  }
                }
              });
              
              // Broadcast completion with local download
              if (broadcastProgress) {
                broadcastProgress({
                  type: 'completed',
                  fileUrl: `/outputs/${filename}`,
                  largeFile: true,
                  fileSize: fileSizeInMB,
                  message: `File grande (${fileSizeInMB.toFixed(2)} MB) - Download locale`
                });
              }
            } catch (dbError) {
              console.error('Error saving to database:', dbError);
            }
          } else {
            // Broadcast error for smaller files
            if (broadcastProgress) {
              broadcastProgress({
                type: 'error',
                message: 'Upload S3 fallito',
                error: uploadError.message
              });
            }
          }
        }
      } else {
        // Broadcast error
        if (broadcastProgress) {
          broadcastProgress({
            type: 'error',
            message: 'File di output non creato',
            details: result.output
          });
        }
      }
    }).catch((error) => {
      console.error('Error in async map generation:', error);
      
      // Broadcast error
      if (broadcastProgress) {
        broadcastProgress({
          type: 'error',
          message: 'Errore nella generazione della mappa',
          error: error.message
        });
      }
    });
    
  } catch (error) {
    console.error('Error in generate map async:', error);
    res.status(500).json({ 
      error: 'Errore del server',
      details: error.message
    });
  }
}

/**
 * Download a map file from S3
 * Streams the file through the backend to avoid CORS and auth issues
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function downloadMap(req, res) {
  try {
    const key = req.params.key;
    
    if (!key) {
      return res.status(400).json({ error: 'Chiave file mancante' });
    }
    
    // Get file from S3 as stream for large files
    const s3Response = await getFromS3(key, true);
    
    // Extract filename from key
    const filename = path.basename(key);
    
    // Set headers for file download
    res.setHeader('Content-Type', s3Response.ContentType || 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    if (s3Response.ContentLength) {
      res.setHeader('Content-Length', s3Response.ContentLength);
    }
    
    // Stream the file from S3 to the response
    if (s3Response.Body) {
      // For AWS SDK v3, Body is a ReadableStream
      const stream = s3Response.Body;
      stream.pipe(res);
    } else {
      res.status(500).json({ error: 'Errore nel recupero del file' });
    }
    
  } catch (error) {
    console.error('Error downloading from S3:', error);
    
    // If S3 fails, try local file
    const filename = path.basename(req.params.key);
    const localPath = path.join(__dirname, '../../../outputs', filename);
    
    if (fs.existsSync(localPath)) {
      res.download(localPath);
    } else {
      res.status(404).json({ 
        error: 'File non trovato',
        details: error.message 
      });
    }
  }
}

/**
 * Get a presigned URL for direct S3 download
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getPresignedUrl(req, res) {
  try {
    const key = req.params.key;
    
    if (!key) {
      return res.status(400).json({ error: 'Chiave file mancante' });
    }
    
    // Generate presigned URL valid for 1 hour
    const presignedUrl = await createPresignedGetUrl(key, 3600);
    
    res.json({
      success: true,
      url: presignedUrl,
      expiresIn: 3600
    });
    
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    res.status(500).json({ 
      error: 'Errore nella generazione del link',
      details: error.message 
    });
  }
}

/**
 * Get downloaded areas from database with optional filters
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getDownloadedAreas(req, res) {
  try {
    const { zoom, style, limit = 500 } = req.query;
    
    // Build where clause based on filters
    const where = {};
    
    if (zoom) {
      if (zoom === '21') {
        where.zoomLevel = 21;
      } else if (zoom === '20') {
        where.zoomLevel = 20;
      } else if (zoom === '18-19') {
        where.zoomLevel = { in: [18, 19] };
      } else if (zoom === '15-17') {
        where.zoomLevel = { in: [15, 16, 17] };
      }
    }
    
    if (style && style !== 'all') {
      where.mapStyle = style;
    }
    
    // Fetch maps from database
    const maps = await prisma.map.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      take: parseInt(limit),
      select: {
        id: true,
        boundingBoxNW: true,
        boundingBoxSE: true,
        zoomLevel: true,
        mapStyle: true,
        outputFormat: true,
        createdAt: true,
        storageKey: true,
        metadata: true
      }
    });
    
    res.json({
      success: true,
      areas: maps,
      total: maps.length
    });
    
  } catch (error) {
    console.error('Error fetching downloaded areas:', error);
    res.status(500).json({ 
      error: 'Errore nel recupero delle aree scaricate',
      details: error.message 
    });
  }
}

/**
 * Geocode an address and return coordinates with bounding box
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function geocodeAddressEndpoint(req, res) {
  try {
    const { address, radius } = req.body;
    
    if (!address) {
      return res.status(400).json({ error: 'Indirizzo mancante' });
    }
    
    const radiusKm = parseFloat(radius) || 1; // Default 1km radius
    
    // Geocode the address
    const location = await geocodeAddress(address);
    
    // Calculate bounding box
    const bbox = calculateBoundingBox(location.lat, location.lng, radiusKm);
    
    res.json({
      success: true,
      location: location,
      bbox: [bbox.min_lon, bbox.min_lat, bbox.max_lon, bbox.max_lat],
      radius: radiusKm
    });
    
  } catch (error) {
    console.error('Geocoding error:', error);
    res.status(400).json({ 
      error: error.message || 'Errore nella geocodifica dell\'indirizzo'
    });
  }
}

module.exports = {
  generateMap,
  generateMapAsync,
  getMapStyles,
  downloadMap,
  getPresignedUrl,
  getDownloadedAreas,
  geocodeAddressEndpoint
};