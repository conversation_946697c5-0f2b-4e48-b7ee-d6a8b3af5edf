const express = require('express');
const router = express.Router();
const {
  getBuildingsInArea,
  getCompaniesInArea,
  getAreaStatistics,
  getAvailableMaps
} = require('../controllers/areaAnalyzerController');
const { optionalAuth } = require('../middleware/auth');

// Apply optional authentication to all routes
// This allows both authenticated and non-authenticated access
router.use(optionalAuth);

// Get buildings in a specific area
router.get('/buildings', getBuildingsInArea);

// Get companies/businesses in a specific area
router.get('/companies', getCompaniesInArea);

// Get area statistics
router.get('/statistics', getAreaStatistics);

// Get available map tiles for an area
router.get('/maps', getAvailableMaps);

module.exports = router;