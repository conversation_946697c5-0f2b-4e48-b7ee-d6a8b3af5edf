const express = require('express');
const router = express.Router();
const dataSourceService = require('../services/dataSourceService');
const importService = require('../services/importService');
const { authenticate, requireAdmin } = require('../middleware/auth');
const logger = require('../utils/logger');

// Get all data sources (public for read)
router.get('/', async (req, res) => {
  try {
    const dataSources = await dataSourceService.getDataSources(req.query);
    res.json(dataSources);
  } catch (error) {
    logger.error('Error fetching data sources:', error);
    res.status(500).json({ error: 'Failed to fetch data sources' });
  }
});

// Get data source by ID (public for read)
router.get('/:id', async (req, res) => {
  try {
    const dataSource = await dataSourceService.getDataSourceById(req.params.id);
    res.json(dataSource);
  } catch (error) {
    if (error.message === 'Data source not found') {
      return res.status(404).json({ error: 'Data source not found' });
    }
    logger.error('Error fetching data source:', error);
    res.status(500).json({ error: 'Failed to fetch data source' });
  }
});

// Create new data source (admin only)
router.post('/', authenticate, requireAdmin, async (req, res) => {
  try {
    const dataSource = await dataSourceService.createDataSource(req.body);
    res.status(201).json(dataSource);
  } catch (error) {
    if (error.code === 'P2002') {
      return res.status(400).json({ error: 'Data source with this name already exists' });
    }
    logger.error('Error creating data source:', error);
    res.status(500).json({ error: 'Failed to create data source' });
  }
});

// Update data source (admin only)
router.put('/:id', authenticate, requireAdmin, async (req, res) => {
  try {
    const dataSource = await dataSourceService.updateDataSource(req.params.id, req.body);
    res.json(dataSource);
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Data source not found' });
    }
    logger.error('Error updating data source:', error);
    res.status(500).json({ error: 'Failed to update data source' });
  }
});

// Delete data source (admin only)
router.delete('/:id', authenticate, requireAdmin, async (req, res) => {
  try {
    const result = await dataSourceService.deleteDataSource(req.params.id);
    res.json(result);
  } catch (error) {
    if (error.message === 'Data source not found') {
      return res.status(404).json({ error: 'Data source not found' });
    }
    if (error.message === 'Cannot delete data source with associated companies') {
      return res.status(400).json({ error: error.message });
    }
    logger.error('Error deleting data source:', error);
    res.status(500).json({ error: 'Failed to delete data source' });
  }
});

// Sync data source
router.post('/:id/sync', authenticate, requireAdmin, async (req, res) => {
  try {
    const result = await dataSourceService.syncDataSource(req.params.id);
    res.json(result);
  } catch (error) {
    if (error.message === 'Data source not found') {
      return res.status(404).json({ error: 'Data source not found' });
    }
    if (error.message === 'Data source is not active') {
      return res.status(400).json({ error: error.message });
    }
    logger.error('Error syncing data source:', error);
    res.status(500).json({ error: 'Failed to sync data source' });
  }
});

// Import companies from data source
router.post('/:id/import', authenticate, requireAdmin, async (req, res) => {
  try {
    const { companies, batchSize = 100 } = req.body;
    
    if (!Array.isArray(companies)) {
      return res.status(400).json({ error: 'Companies must be an array' });
    }

    const result = await importService.importCompanies(
      req.params.id,
      companies,
      batchSize
    );
    
    res.json(result);
  } catch (error) {
    logger.error('Error importing companies:', error);
    res.status(500).json({ error: 'Failed to import companies' });
  }
});

// Get import batches for data source
router.get('/:id/import-batches', authenticate, async (req, res) => {
  try {
    const batches = await importService.getImportBatches(req.params.id);
    res.json(batches);
  } catch (error) {
    logger.error('Error fetching import batches:', error);
    res.status(500).json({ error: 'Failed to fetch import batches' });
  }
});

module.exports = router;