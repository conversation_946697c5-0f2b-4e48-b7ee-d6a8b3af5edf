const express = require('express');
const router = express.Router();
const emailService = require('../services/emailService');
const organizationService = require('../services/organizationService');
const { authenticate } = require('../middleware/auth');
const logger = require('../utils/logger');

// Ottieni configurazione SMTP (senza password)
router.get('/smtp-config', authenticate, async (req, res) => {
  try {
    const orgId = req.headers['x-organization-id'];
    
    if (!orgId) {
      return res.status(400).json({ error: 'ID organizzazione mancante' });
    }

    // Verifica che l'utente sia admin o owner dell'organizzazione
    const org = await organizationService.getOrganizationById(orgId, req.user.id);
    const userRole = org.users.find(u => u.userId === req.user.id)?.role;
    
    if (!userRole || (userRole !== 'owner' && userRole !== 'admin')) {
      return res.status(403).json({ error: 'Non autorizzato' });
    }

    const config = await emailService.getSmtpConfig(orgId);
    
    if (config) {
      // Non inviare la password al frontend
      if (config.auth) {
        config.auth.pass = '********';
      }
    }

    res.json({ config });
  } catch (error) {
    logger.error('Errore recupero configurazione SMTP:', error);
    res.status(500).json({ error: 'Errore nel recupero della configurazione' });
  }
});

// Salva configurazione SMTP
router.post('/smtp-config', authenticate, async (req, res) => {
  try {
    const orgId = req.headers['x-organization-id'];
    
    if (!orgId) {
      return res.status(400).json({ error: 'ID organizzazione mancante' });
    }

    // Verifica che l'utente sia admin o owner
    const org = await organizationService.getOrganizationById(orgId, req.user.id);
    const userRole = org.users.find(u => u.userId === req.user.id)?.role;
    
    if (!userRole || (userRole !== 'owner' && userRole !== 'admin')) {
      return res.status(403).json({ error: 'Non autorizzato' });
    }

    const { host, port, secure, from, fromName, auth, updatePassword } = req.body;

    // Validazione campi richiesti
    if (!host || !port || !from || !auth?.user) {
      return res.status(400).json({ 
        error: 'Campi richiesti mancanti' 
      });
    }

    // Se updatePassword è false e non c'è una password, ottieni quella esistente
    let finalConfig = {
      host,
      port: parseInt(port),
      secure: secure === true || secure === 'true',
      from,
      fromName,
      auth: {
        user: auth.user,
        pass: auth.pass
      }
    };

    if (!updatePassword && (!auth.pass || auth.pass === '********')) {
      // Ottieni la password esistente
      const existingConfig = await emailService.getSmtpConfig(orgId);
      if (existingConfig?.auth?.pass) {
        finalConfig.auth.pass = existingConfig.auth.pass;
      } else {
        return res.status(400).json({ 
          error: 'Password richiesta per la prima configurazione' 
        });
      }
    }

    await emailService.saveSmtpConfig(orgId, finalConfig);
    
    res.json({ 
      success: true, 
      message: 'Configurazione SMTP salvata con successo' 
    });
  } catch (error) {
    logger.error('Errore salvataggio configurazione SMTP:', error);
    res.status(500).json({ 
      error: 'Errore nel salvataggio della configurazione' 
    });
  }
});

// Test configurazione SMTP
router.post('/test-smtp', authenticate, async (req, res) => {
  try {
    const { host, port, secure, from, fromName, auth, testEmail } = req.body;

    // Validazione
    if (!host || !port || !from || !auth?.user || !auth?.pass) {
      return res.status(400).json({ 
        error: 'Tutti i campi sono richiesti per il test' 
      });
    }

    const config = {
      host,
      port: parseInt(port),
      secure: secure === true || secure === 'true',
      from,
      fromName,
      auth: {
        user: auth.user,
        pass: auth.pass
      },
      testEmail: testEmail || from
    };

    const result = await emailService.testSmtpConfig(config);
    
    res.json(result);
  } catch (error) {
    logger.error('Errore test SMTP:', error);
    res.status(400).json({ 
      error: error.message || 'Errore nel test della configurazione SMTP' 
    });
  }
});

// Test configurazione SMTP con password esistente
router.post('/test-smtp-existing', authenticate, async (req, res) => {
  try {
    const orgId = req.headers['x-organization-id'];
    
    if (!orgId) {
      return res.status(400).json({ error: 'ID organizzazione mancante' });
    }

    // Verifica che l'utente sia admin o owner
    const org = await organizationService.getOrganizationById(orgId, req.user.id);
    const userRole = org.users.find(u => u.userId === req.user.id)?.role;
    
    if (!userRole || (userRole !== 'owner' && userRole !== 'admin')) {
      return res.status(403).json({ error: 'Non autorizzato' });
    }

    const { host, port, secure, from, fromName, auth, testEmail } = req.body;

    // Ottieni la password esistente dal database
    const existingConfig = await emailService.getSmtpConfig(orgId);
    
    if (!existingConfig || !existingConfig.auth?.pass) {
      return res.status(400).json({ 
        error: 'Nessuna password salvata. Inserisci la password per il test.' 
      });
    }

    // Usa la password esistente
    const config = {
      host,
      port: parseInt(port),
      secure: secure === true || secure === 'true',
      from,
      fromName,
      auth: {
        user: auth.user,
        pass: existingConfig.auth.pass // Usa la password esistente decriptata
      },
      testEmail: testEmail || from
    };

    const result = await emailService.testSmtpConfig(config);
    
    res.json(result);
  } catch (error) {
    logger.error('Errore test SMTP con password esistente:', error);
    res.status(400).json({ 
      error: error.message || 'Errore nel test della configurazione SMTP' 
    });
  }
});

// Invia email generica
router.post('/send', authenticate, async (req, res) => {
  try {
    const orgId = req.headers['x-organization-id'];
    
    if (!orgId) {
      return res.status(400).json({ error: 'ID organizzazione mancante' });
    }

    const { to, subject, text, html } = req.body;

    if (!to || !subject || (!text && !html)) {
      return res.status(400).json({ 
        error: 'Destinatario, oggetto e contenuto sono richiesti' 
      });
    }

    const result = await emailService.sendEmail(orgId, {
      to,
      subject,
      text,
      html
    });

    res.json(result);
  } catch (error) {
    logger.error('Errore invio email:', error);
    res.status(500).json({ 
      error: error.message || 'Errore nell\'invio dell\'email' 
    });
  }
});

// Rimuovi configurazione SMTP
router.delete('/smtp-config', authenticate, async (req, res) => {
  try {
    const orgId = req.headers['x-organization-id'];
    
    if (!orgId) {
      return res.status(400).json({ error: 'ID organizzazione mancante' });
    }

    // Verifica che l'utente sia owner
    const org = await organizationService.getOrganizationById(orgId, req.user.id);
    const userRole = org.users.find(u => u.userId === req.user.id)?.role;
    
    if (userRole !== 'owner') {
      return res.status(403).json({ error: 'Solo il proprietario può rimuovere la configurazione SMTP' });
    }

    await emailService.saveSmtpConfig(orgId, null);
    
    res.json({ 
      success: true, 
      message: 'Configurazione SMTP rimossa' 
    });
  } catch (error) {
    logger.error('Errore rimozione configurazione SMTP:', error);
    res.status(500).json({ 
      error: 'Errore nella rimozione della configurazione' 
    });
  }
});

module.exports = router;