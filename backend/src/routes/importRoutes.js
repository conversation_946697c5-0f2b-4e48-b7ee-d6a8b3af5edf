const express = require('express');
const router = express.Router();
const importService = require('../services/importService');
const { authenticate, requireAdmin } = require('../middleware/auth');
const logger = require('../utils/logger');

// Get all import batches
router.get('/batches', authenticate, async (req, res) => {
  try {
    const { dataSourceId, limit = 20 } = req.query;
    const batches = await importService.getImportBatches(dataSourceId, parseInt(limit));
    res.json(batches);
  } catch (error) {
    logger.error('Error fetching import batches:', error);
    res.status(500).json({ error: 'Failed to fetch import batches' });
  }
});

// Get import batch by ID
router.get('/batches/:id', authenticate, async (req, res) => {
  try {
    const batch = await importService.getImportBatchById(req.params.id);
    res.json(batch);
  } catch (error) {
    if (error.message === 'Import batch not found') {
      return res.status(404).json({ error: 'Import batch not found' });
    }
    logger.error('Error fetching import batch:', error);
    res.status(500).json({ error: 'Failed to fetch import batch' });
  }
});

// Import companies (bulk import)
router.post('/bulk', authenticate, requireAdmin, async (req, res) => {
  try {
    const { dataSourceId, companies, batchSize = 100 } = req.body;
    
    if (!dataSourceId) {
      return res.status(400).json({ error: 'dataSourceId is required' });
    }
    
    if (!Array.isArray(companies)) {
      return res.status(400).json({ error: 'companies must be an array' });
    }

    const result = await importService.importCompanies(
      dataSourceId,
      companies,
      batchSize
    );
    
    res.json(result);
  } catch (error) {
    logger.error('Error importing companies:', error);
    res.status(500).json({ error: 'Failed to import companies' });
  }
});

// Import single company
router.post('/single', authenticate, async (req, res) => {
  try {
    const { dataSourceId, ...companyData } = req.body;
    
    if (!dataSourceId) {
      return res.status(400).json({ error: 'dataSourceId is required' });
    }

    // Create a batch for single import
    const importBatch = await importService.createImportBatch(dataSourceId);
    
    const company = await importService.importSingleCompany(
      companyData,
      dataSourceId,
      importBatch.id
    );

    // Update batch as completed
    await importService.updateImportBatch(importBatch.id, {
      status: 'completed',
      completedAt: new Date(),
      totalRecords: 1,
      processedRecords: 1,
      successRecords: 1,
      failedRecords: 0
    });
    
    res.status(201).json(company);
  } catch (error) {
    logger.error('Error importing company:', error);
    res.status(500).json({ error: 'Failed to import company' });
  }
});

module.exports = router;