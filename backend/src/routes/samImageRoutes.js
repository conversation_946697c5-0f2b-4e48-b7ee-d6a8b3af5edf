const express = require('express');
const router = express.Router();
const samImageService = require('../services/samImageService');
const { authenticate } = require('../middleware/auth');
const multer = require('multer');

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

/**
 * Upload and save an image for SAM segmentation
 */
router.post('/upload', authenticate, upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        success: false, 
        error: 'No image file provided' 
      });
    }

    const { name, description, metadata } = req.body;
    const userId = req.user.id;

    const result = await samImageService.uploadImage({
      file: req.file,
      userId,
      name: name || req.file.originalname,
      description: description || '',
      metadata: metadata ? JSON.parse(metadata) : {}
    });

    res.json({
      success: true,
      image: result
    });
  } catch (error) {
    console.error('Image upload error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

/**
 * Get user's image history
 */
router.get('/history', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 20, offset = 0, sortBy = 'createdAt', order = 'desc' } = req.query;

    const result = await samImageService.getUserImageHistory({
      userId,
      limit: parseInt(limit),
      offset: parseInt(offset),
      sortBy,
      order
    });

    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Get image history error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

/**
 * Get a specific image by ID
 */
router.get('/:imageId', authenticate, async (req, res) => {
  try {
    const { imageId } = req.params;
    const userId = req.user.id;

    const image = await samImageService.getImage(imageId, userId);

    if (!image) {
      return res.status(404).json({
        success: false,
        error: 'Image not found'
      });
    }

    res.json({
      success: true,
      image
    });
  } catch (error) {
    console.error('Get image error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

/**
 * Delete an image
 */
router.delete('/:imageId', authenticate, async (req, res) => {
  try {
    const { imageId } = req.params;
    const userId = req.user.id;

    const result = await samImageService.deleteImage(imageId, userId);

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Image not found or unauthorized'
      });
    }

    res.json({
      success: true,
      message: 'Image deleted successfully'
    });
  } catch (error) {
    console.error('Delete image error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

/**
 * Update image metadata
 */
router.patch('/:imageId', authenticate, async (req, res) => {
  try {
    const { imageId } = req.params;
    const userId = req.user.id;
    const { name, description, metadata } = req.body;

    const result = await samImageService.updateImage(imageId, userId, {
      name,
      description,
      metadata
    });

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Image not found or unauthorized'
      });
    }

    res.json({
      success: true,
      image: result
    });
  } catch (error) {
    console.error('Update image error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

/**
 * Save segmentation results for an image
 */
router.post('/:imageId/segmentation', authenticate, async (req, res) => {
  try {
    const { imageId } = req.params;
    const userId = req.user.id;
    const { masks, points, metadata } = req.body;

    const result = await samImageService.saveSegmentationResults({
      imageId,
      userId,
      masks,
      points,
      metadata
    });

    res.json({
      success: true,
      segmentation: result
    });
  } catch (error) {
    console.error('Save segmentation error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

/**
 * Get segmentation results for an image
 */
router.get('/:imageId/segmentation', authenticate, async (req, res) => {
  try {
    const { imageId } = req.params;
    const userId = req.user.id;

    const results = await samImageService.getSegmentationResults(imageId, userId);

    res.json({
      success: true,
      segmentations: results
    });
  } catch (error) {
    console.error('Get segmentation results error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

module.exports = router;