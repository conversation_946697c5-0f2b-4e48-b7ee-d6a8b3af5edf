const express = require('express');
const router = express.Router();
const categoryService = require('../services/categoryService');
const { authenticate, requireAdmin } = require('../middleware/auth');
const logger = require('../utils/logger');

// Get all categories (public for read)
router.get('/', async (req, res) => {
  try {
    const categories = await categoryService.getCategories(req.query);
    res.json(categories);
  } catch (error) {
    logger.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Get category tree
router.get('/tree', async (req, res) => {
  try {
    const tree = await categoryService.getCategoryTree();
    res.json(tree);
  } catch (error) {
    logger.error('Error fetching category tree:', error);
    res.status(500).json({ error: 'Failed to fetch category tree' });
  }
});

// Get category by ID
router.get('/:id', async (req, res) => {
  try {
    const category = await categoryService.getCategoryById(req.params.id);
    res.json(category);
  } catch (error) {
    if (error.message === 'Category not found') {
      return res.status(404).json({ error: 'Category not found' });
    }
    logger.error('Error fetching category:', error);
    res.status(500).json({ error: 'Failed to fetch category' });
  }
});

// Create new category (authenticated users only)
router.post('/', authenticate, async (req, res) => {
  try {
    const category = await categoryService.createCategory(req.body);
    res.status(201).json(category);
  } catch (error) {
    logger.error('Error creating category:', error);
    res.status(500).json({ error: 'Failed to create category' });
  }
});

// Update category (authenticated users only)
router.put('/:id', authenticate, async (req, res) => {
  try {
    const category = await categoryService.updateCategory(req.params.id, req.body);
    res.json(category);
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Category not found' });
    }
    logger.error('Error updating category:', error);
    res.status(500).json({ error: 'Failed to update category' });
  }
});

// Delete category (admin only)
router.delete('/:id', authenticate, requireAdmin, async (req, res) => {
  try {
    const result = await categoryService.deleteCategory(req.params.id);
    res.json(result);
  } catch (error) {
    if (error.message === 'Category not found') {
      return res.status(404).json({ error: 'Category not found' });
    }
    if (error.message === 'Cannot delete category with associated companies') {
      return res.status(400).json({ error: error.message });
    }
    logger.error('Error deleting category:', error);
    res.status(500).json({ error: 'Failed to delete category' });
  }
});

// Assign companies to category
router.post('/:id/companies', authenticate, async (req, res) => {
  try {
    const { companyIds } = req.body;
    if (!Array.isArray(companyIds)) {
      return res.status(400).json({ error: 'companyIds must be an array' });
    }
    const result = await categoryService.assignCompaniesToCategory(req.params.id, companyIds);
    res.json(result);
  } catch (error) {
    logger.error('Error assigning companies to category:', error);
    res.status(500).json({ error: 'Failed to assign companies to category' });
  }
});

// Remove companies from category
router.delete('/:id/companies', authenticate, async (req, res) => {
  try {
    const { companyIds } = req.body;
    if (!Array.isArray(companyIds)) {
      return res.status(400).json({ error: 'companyIds must be an array' });
    }
    const result = await categoryService.removeCompaniesFromCategory(req.params.id, companyIds);
    res.json(result);
  } catch (error) {
    logger.error('Error removing companies from category:', error);
    res.status(500).json({ error: 'Failed to remove companies from category' });
  }
});

module.exports = router;