const express = require('express');
const router = express.Router();
const cvatService = require('../services/cvatService');
const prisma = require('../lib/prisma');
const pool = require('../lib/db');

/**
 * @route GET /api/cvat/status
 * @desc Check CVAT server status
 */
router.get('/status', async (req, res) => {
  try {
    await cvatService.authenticate();
    
    res.json({
      status: 'connected',
      url: cvatService.baseURL,
      authenticated: true
    });
  } catch (error) {
    res.status(503).json({
      status: 'disconnected',
      error: error.message
    });
  }
});

/**
 * @route GET /api/cvat/models
 * @desc Get available AI models in CVAT
 */
router.get('/models', async (req, res) => {
  try {
    const models = await cvatService.getAvailableModels();
    
    res.json({
      status: 'success',
      models: models.results || []
    });
  } catch (error) {
    console.error('Failed to get models:', error);
    res.status(500).json({
      error: 'Failed to get AI models',
      message: error.message
    });
  }
});

/**
 * @route POST /api/cvat/import-map
 * @desc Import a map into CVAT for annotation
 */
router.post('/import-map', async (req, res) => {
  try {
    const { mapId } = req.body;
    
    if (!mapId) {
      return res.status(400).json({ error: 'Map ID required' });
    }
    
    // Get map data
    const map = await prisma.map.findUnique({
      where: { id: mapId }
    });
    
    if (!map) {
      return res.status(404).json({ error: 'Map not found' });
    }
    
    // Import map to CVAT
    const result = await cvatService.importMapForAnnotation(mapId, map);
    
    // Store CVAT task info in our database
    await pool.query(`
      INSERT INTO cvat_tasks (map_id, cvat_project_id, cvat_task_id, cvat_url, created_at)
      VALUES ($1, $2, $3, $4, NOW())
      ON CONFLICT (map_id) 
      DO UPDATE SET 
        cvat_task_id = $3,
        cvat_url = $4,
        updated_at = NOW()
    `, [mapId, result.projectId, result.taskId, result.cvatUrl]);
    
    res.json({
      status: 'success',
      ...result
    });
  } catch (error) {
    console.error('Import failed:', error);
    res.status(500).json({
      error: 'Failed to import map',
      message: error.message
    });
  }
});

/**
 * @route GET /api/cvat/task/:mapId
 * @desc Get CVAT task info for a map
 */
router.get('/task/:mapId', async (req, res) => {
  try {
    const { mapId } = req.params;
    
    const result = await pool.query(`
      SELECT * FROM cvat_tasks WHERE map_id = $1
    `, [mapId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'No CVAT task found for this map' });
    }
    
    res.json({
      status: 'success',
      task: result.rows[0]
    });
  } catch (error) {
    console.error('Failed to get task:', error);
    res.status(500).json({
      error: 'Failed to get CVAT task',
      message: error.message
    });
  }
});

/**
 * @route POST /api/cvat/sync-annotations
 * @desc Sync annotations from CVAT to our database
 */
router.post('/sync-annotations', async (req, res) => {
  try {
    const { mapId } = req.body;
    
    // Get CVAT task ID
    const taskResult = await pool.query(`
      SELECT cvat_task_id FROM cvat_tasks WHERE map_id = $1
    `, [mapId]);
    
    if (taskResult.rows.length === 0) {
      return res.status(404).json({ error: 'No CVAT task found for this map' });
    }
    
    const taskId = taskResult.rows[0].cvat_task_id;
    
    // Sync annotations
    const annotations = await cvatService.syncAnnotations(taskId, mapId);
    
    // Store annotations in our database
    for (const annotation of annotations) {
      const wktPoints = annotation.points.map(p => `${p.x} ${p.y}`).join(', ');
      const wkt = `POLYGON((${wktPoints}, ${annotation.points[0].x} ${annotation.points[0].y}))`;
      
      await pool.query(`
        INSERT INTO roof_annotations (
          map_id, 
          geometry, 
          annotation_source,
          roof_type,
          material,
          has_solar_panels,
          building_type,
          confidence,
          human_verified,
          quality_metrics
        ) VALUES (
          $1,
          ST_Transform(ST_GeomFromText($2, 4326), 32632),
          'cvat',
          $3,
          $4,
          $5,
          $6,
          1.0,
          true,
          $7
        )
      `, [
        mapId,
        wkt,
        annotation.class,
        annotation.attributes.material || null,
        annotation.class === 'solar_panel',
        annotation.attributes.building_type || null,
        annotation.attributes
      ]);
    }
    
    res.json({
      status: 'success',
      annotationsCount: annotations.length
    });
  } catch (error) {
    console.error('Sync failed:', error);
    res.status(500).json({
      error: 'Failed to sync annotations',
      message: error.message
    });
  }
});

/**
 * @route POST /api/cvat/create-project
 * @desc Create a new annotation project
 */
router.post('/create-project', async (req, res) => {
  try {
    const project = await cvatService.createRoofAnnotationProject();
    
    res.json({
      status: 'success',
      project: project
    });
  } catch (error) {
    console.error('Failed to create project:', error);
    res.status(500).json({
      error: 'Failed to create project',
      message: error.message
    });
  }
});

/**
 * @route POST /api/cvat/setup-cloud-storage
 * @desc Setup MinIO as cloud storage in CVAT
 */
router.post('/setup-cloud-storage', async (req, res) => {
  try {
    const storage = await cvatService.createCloudStorage(
      'AstraMeccanica MinIO',
      'astrameccanica',
      'processed_maps/'
    );
    
    res.json({
      status: 'success',
      storage: storage
    });
  } catch (error) {
    console.error('Failed to setup cloud storage:', error);
    res.status(500).json({
      error: 'Failed to setup cloud storage',
      message: error.message
    });
  }
});

module.exports = router;