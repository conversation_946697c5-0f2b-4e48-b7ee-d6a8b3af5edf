const express = require('express');
const router = express.Router();
const compressionService = require('../services/compressionService');
const mapService = require('../services/mapService');
const { s3Client, bucketConfig } = require('../config/s3Config');
const { HeadObjectCommand } = require('@aws-sdk/client-s3');

/**
 * @route POST /api/compression/compress-map
 * @desc Compress a map file with specified compression type
 */
router.post('/compress-map', async (req, res) => {
  try {
    const { mapId, compressionType = 'LZW', quality = 90 } = req.body;
    
    if (!mapId) {
      return res.status(400).json({ error: 'Map ID required' });
    }
    
    const result = await compressionService.compressAndUploadMap(
      mapId,
      compressionType,
      quality
    );
    
    res.json({
      status: 'success',
      ...result
    });
    
  } catch (error) {
    console.error('Compression error:', error);
    res.status(500).json({ 
      error: 'Compression failed', 
      message: error.message 
    });
  }
});

/**
 * @route POST /api/compression/batch-compress
 * @desc Compress multiple maps in batch
 */
router.post('/batch-compress', async (req, res) => {
  try {
    const { mapIds, compressionType = 'LZW', quality = 90 } = req.body;
    
    if (!mapIds || !Array.isArray(mapIds)) {
      return res.status(400).json({ error: 'Map IDs array required' });
    }
    
    const results = [];
    const errors = [];
    
    for (const mapId of mapIds) {
      try {
        const result = await compressionService.compressAndUploadMap(
          mapId,
          compressionType,
          quality
        );
        results.push(result);
      } catch (error) {
        errors.push({
          mapId,
          error: error.message
        });
      }
    }
    
    res.json({
      status: 'success',
      totalMaps: mapIds.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors
    });
    
  } catch (error) {
    console.error('Batch compression error:', error);
    res.status(500).json({ 
      error: 'Batch compression failed', 
      message: error.message 
    });
  }
});

/**
 * @route GET /api/compression/analyze/:mapId
 * @desc Analyze compression options for a map
 */
router.get('/analyze/:mapId', async (req, res) => {
  try {
    const { mapId } = req.params;
    
    // Get map info
    const map = await mapService.getMapById(mapId);
    if (!map) {
      return res.status(404).json({ error: 'Map not found' });
    }
    
    // Get current file size from S3
    const headCommand = new HeadObjectCommand({
      Bucket: map.storageBucket,
      Key: map.storageKey
    });
    
    const headResponse = await s3Client.send(headCommand);
    const currentSize = headResponse.ContentLength;
    
    // Calculate estimated sizes for different compression types
    const compressionEstimates = {
      current: {
        size: currentSize,
        format: map.outputFormat
      },
      estimates: [
        {
          type: 'LZW',
          description: 'Lossless compression, good for preserving exact data',
          estimatedSize: Math.round(currentSize * 0.7), // ~30% reduction
          estimatedRatio: '30%',
          quality: 'Lossless'
        },
        {
          type: 'DEFLATE',
          description: 'Better lossless compression, slower processing',
          estimatedSize: Math.round(currentSize * 0.65), // ~35% reduction
          estimatedRatio: '35%',
          quality: 'Lossless'
        },
        {
          type: 'JPEG',
          description: 'Lossy compression, great for visual data',
          estimatedSize: Math.round(currentSize * 0.15), // ~85% reduction
          estimatedRatio: '85%',
          quality: 'Lossy (90% quality)',
          qualityOptions: [70, 80, 85, 90, 95]
        },
        {
          type: 'WEBP',
          description: 'Modern compression, better than JPEG',
          estimatedSize: Math.round(currentSize * 0.12), // ~88% reduction
          estimatedRatio: '88%',
          quality: 'Lossy (90% quality)',
          qualityOptions: [70, 80, 85, 90, 95]
        },
        {
          type: 'COG',
          description: 'Cloud Optimized GeoTIFF for web serving',
          estimatedSize: Math.round(currentSize * 0.68), // ~32% reduction
          estimatedRatio: '32%',
          quality: 'Lossless',
          benefits: ['Web optimized', 'Streaming support', 'Multi-resolution']
        }
      ]
    };
    
    res.json({
      status: 'success',
      mapId,
      analysis: compressionEstimates
    });
    
  } catch (error) {
    console.error('Analysis error:', error);
    res.status(500).json({ 
      error: 'Analysis failed', 
      message: error.message 
    });
  }
});

/**
 * @route GET /api/compression/comparison/:mapId
 * @desc Get all compressed versions of a map for comparison
 */
router.get('/comparison/:mapId', async (req, res) => {
  try {
    const { mapId } = req.params;
    
    const map = await mapService.getMapById(mapId);
    if (!map) {
      return res.status(404).json({ error: 'Map not found' });
    }
    
    // List all objects with the map ID prefix
    const { ListObjectsV2Command } = require('@aws-sdk/client-s3');
    const prefix = map.storageKey.substring(0, map.storageKey.lastIndexOf('.'));
    
    const listCommand = new ListObjectsV2Command({
      Bucket: map.storageBucket,
      Prefix: prefix
    });
    
    const listResponse = await s3Client.send(listCommand);
    const versions = [];
    
    if (listResponse.Contents) {
      for (const object of listResponse.Contents) {
        const headCommand = new HeadObjectCommand({
          Bucket: map.storageBucket,
          Key: object.Key
        });
        
        const headResponse = await s3Client.send(headCommand);
        
        versions.push({
          key: object.Key,
          size: object.Size,
          lastModified: object.LastModified,
          compressionType: headResponse.Metadata?.['compression-type'] || 'original',
          compressionQuality: headResponse.Metadata?.['compression-quality'],
          compressionRatio: headResponse.Metadata?.['compression-ratio']
        });
      }
    }
    
    res.json({
      status: 'success',
      mapId,
      originalKey: map.storageKey,
      versions: versions.sort((a, b) => a.size - b.size)
    });
    
  } catch (error) {
    console.error('Comparison error:', error);
    res.status(500).json({ 
      error: 'Comparison failed', 
      message: error.message 
    });
  }
});

/**
 * @route POST /api/compression/optimize-for-ai
 * @desc Optimize map for AI processing (balanced quality/size)
 */
router.post('/optimize-for-ai', async (req, res) => {
  try {
    const { mapId } = req.body;
    
    if (!mapId) {
      return res.status(400).json({ error: 'Map ID required' });
    }
    
    // For AI processing, we want to balance quality and file size
    // JPEG at 85-90% quality is usually optimal
    const result = await compressionService.compressAndUploadMapForAI(
      mapId,
      88 // 88% quality provides good balance
    );
    
    res.json({
      status: 'success',
      optimization: 'ai-ready',
      ...result,
      notes: [
        'Optimized for AI processing with JPEG 88% quality',
        'Reduces file size while maintaining feature detection quality',
        'Georeferencing preserved via world file'
      ]
    });
    
  } catch (error) {
    console.error('AI optimization error:', error);
    res.status(500).json({ 
      error: 'AI optimization failed', 
      message: error.message 
    });
  }
});

module.exports = router;