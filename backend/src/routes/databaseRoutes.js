const express = require('express');
const router = express.Router();
const userService = require('../services/userService');
const mapService = require('../services/mapService');

// GET all users
router.get('/users', async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.status(200).json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Errore nel recupero degli utenti' });
  }
});

// GET user by ID
router.get('/users/:id', async (req, res) => {
  try {
    const user = await userService.getUserById(req.params.id);
    if (!user) {
      return res.status(404).json({ error: 'Utente non trovato' });
    }
    res.status(200).json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Errore nel recupero dell\'utente' });
  }
});

// POST create user
router.post('/users', async (req, res) => {
  try {
    const newUser = await userService.createUser(req.body);
    res.status(201).json(newUser);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Errore nella creazione dell\'utente' });
  }
});

// PUT update user
router.put('/users/:id', async (req, res) => {
  try {
    const updatedUser = await userService.updateUser(req.params.id, req.body);
    res.status(200).json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ error: 'Errore nell\'aggiornamento dell\'utente' });
  }
});

// DELETE user
router.delete('/users/:id', async (req, res) => {
  try {
    await userService.deleteUser(req.params.id);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: 'Errore nell\'eliminazione dell\'utente' });
  }
});

// GET all maps
router.get('/maps', async (req, res) => {
  try {
    // Check if the client wants file sizes included
    const includeSizes = req.query.includeSizes === 'true';
    
    if (includeSizes) {
      const maps = await mapService.getMapsWithSizes();
      res.status(200).json(maps);
    } else {
      const maps = await mapService.getAllMaps();
      res.status(200).json(maps);
    }
  } catch (error) {
    console.error('Error fetching maps:', error);
    res.status(500).json({ error: 'Errore nel recupero delle mappe' });
  }
});

// GET map by ID
router.get('/maps/:id', async (req, res) => {
  try {
    const map = await mapService.getMapById(req.params.id);
    if (!map) {
      return res.status(404).json({ error: 'Mappa non trovata' });
    }
    res.status(200).json(map);
  } catch (error) {
    console.error('Error fetching map:', error);
    res.status(500).json({ error: 'Errore nel recupero della mappa' });
  }
});

// GET maps by user ID
router.get('/users/:id/maps', async (req, res) => {
  try {
    const maps = await mapService.getMapsByUserId(req.params.id);
    res.status(200).json(maps);
  } catch (error) {
    console.error('Error fetching user maps:', error);
    res.status(500).json({ error: 'Errore nel recupero delle mappe dell\'utente' });
  }
});

// POST create map
router.post('/maps', async (req, res) => {
  try {
    const newMap = await mapService.createMap(req.body);
    res.status(201).json(newMap);
  } catch (error) {
    console.error('Error creating map:', error);
    res.status(500).json({ error: 'Errore nella creazione della mappa' });
  }
});

// PUT update map
router.put('/maps/:id', async (req, res) => {
  try {
    const updatedMap = await mapService.updateMap(req.params.id, req.body);
    res.status(200).json(updatedMap);
  } catch (error) {
    console.error('Error updating map:', error);
    res.status(500).json({ error: 'Errore nell\'aggiornamento della mappa' });
  }
});

// DELETE map
router.delete('/maps/:id', async (req, res) => {
  try {
    await mapService.deleteMap(req.params.id);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting map:', error);
    res.status(500).json({ error: 'Errore nell\'eliminazione della mappa' });
  }
});

// GET storage statistics
router.get('/storage-statistics', async (req, res) => {
  try {
    const statistics = await mapService.getStorageStatistics();
    
    // Italy's total area is approximately 301,338 km²
    const italyTotalArea = 301338;
    
    // Calculate estimates for all of Italy
    const estimates = {};
    for (const zoom in statistics) {
      const stats = statistics[zoom];
      if (stats.avgSizePerSqKm > 0) {
        estimates[zoom] = {
          ...stats,
          italyEstimate: {
            totalSizeGB: (stats.avgSizePerSqKm * italyTotalArea) / (1024 * 1024 * 1024),
            totalSizeTB: (stats.avgSizePerSqKm * italyTotalArea) / (1024 * 1024 * 1024 * 1024)
          }
        };
      }
    }
    
    res.status(200).json({
      statistics,
      estimates,
      italyTotalArea
    });
  } catch (error) {
    console.error('Error fetching storage statistics:', error);
    res.status(500).json({ error: 'Errore nel recupero delle statistiche di storage' });
  }
});

module.exports = router;