const express = require('express');
const router = express.Router();
const { Pool } = require('pg');
const prisma = require('../lib/prisma');

// PostgreSQL connection for PostGIS queries
// Use the same DATABASE_URL that's already loaded in the backend
const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

/**
 * Get province boundaries GeoJSON from database using PostGIS
 */
router.get('/provinces', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        cod_uts as code,
        den_uts as name,
        sigla,
        cod_reg as region_code,
        (SELECT den_reg FROM regions WHERE cod_reg = provinces.cod_reg) as region_name,
        ST_AsGeoJSON(ST_Transform(geometry, 4326)) as geometry,
        ROUND((ST_Area(geometry) / 1000000)::numeric, 2) as area_km2
      FROM provinces 
      ORDER BY den_uts
    `);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ 
        error: 'Nessuna provincia trovata nel database. Esegui lo script di importazione.' 
      });
    }
    
    const features = result.rows.map(row => ({
      type: 'Feature',
      properties: {
        prov_code: row.code,
        prov_name: row.name,
        prov_sigla: row.sigla,
        reg_code: row.region_code,
        reg_name: row.region_name,
        area_km2: row.area_km2
      },
      geometry: JSON.parse(row.geometry)
    }));
    
    const geoJson = {
      type: 'FeatureCollection',
      features
    };
    
    res.json(geoJson);
  } catch (error) {
    console.error('Error fetching province boundaries:', error);
    res.status(500).json({ error: 'Errore nel recupero dei confini provinciali' });
  }
});

/**
 * Get simplified provinces for faster loading with optional coverage stats
 */
router.get('/provinces/simplified', async (req, res) => {
  try {
    // Get simplification level from query params (default: 0.01 for overview)
    const simplification = parseFloat(req.query.simplification) || 0.01;
    const includeStats = req.query.includeStats === 'true';

    let query = `
      SELECT 
        p.cod_uts as code,
        p.den_uts as name,
        p.sigla,
        p.cod_reg as region_code,
        (SELECT den_reg FROM regions WHERE cod_reg = p.cod_reg) as region_name,
        ST_AsGeoJSON(ST_Transform(ST_Simplify(p.geometry, ${simplification}), 4326)) as geometry,
        ROUND((ST_Area(p.geometry) / 1000000)::numeric, 2) as area_km2
    `;

    if (includeStats) {
      // Get all maps to calculate coverage
      const maps = await prisma.map.findMany();
      
      if (maps.length > 0) {
        query += `,
          (
            SELECT COUNT(DISTINCT m.pro_com)
            FROM municipalities m
            WHERE m.cod_uts = p.cod_uts
            AND EXISTS (
              SELECT 1 FROM (VALUES
        `;
        
        // Add map coverage areas
        const mapValues = maps.map((map, idx) => {
          const [nwLon, nwLat] = map.boundingBoxNW;
          const [seLon, seLat] = map.boundingBoxSE;
          return `(ST_Transform(ST_MakeEnvelope(${nwLon}, ${seLat}, ${seLon}, ${nwLat}, 4326), 32632))`;
        }).join(',');
        
        query += mapValues + `) AS t(geom)
              WHERE ST_Intersects(m.geometry, t.geom)
            )
          ) as downloaded_municipalities,
          (
            SELECT COUNT(*)
            FROM municipalities m
            WHERE m.cod_uts = p.cod_uts
          ) as total_municipalities
        `;
      } else {
        query += `,
          0 as downloaded_municipalities,
          (
            SELECT COUNT(*)
            FROM municipalities m
            WHERE m.cod_uts = p.cod_uts
          ) as total_municipalities
        `;
      }
    }

    query += `
      FROM provinces p
      ORDER BY p.den_uts
    `;

    const result = await pool.query(query);

    const features = result.rows.map(row => ({
      type: 'Feature',
      properties: {
        prov_code: row.code,
        prov_name: row.name,
        prov_sigla: row.sigla,
        reg_code: row.region_code,
        reg_name: row.region_name,
        area_km2: row.area_km2,
        ...(includeStats && {
          downloaded_municipalities: parseInt(row.downloaded_municipalities) || 0,
          total_municipalities: parseInt(row.total_municipalities) || 0,
          coverage_percentage: row.total_municipalities > 0 
            ? Math.round((parseInt(row.downloaded_municipalities) || 0) / parseInt(row.total_municipalities) * 100)
            : 0
        })
      },
      geometry: JSON.parse(row.geometry)
    }));

    const geoJson = {
      type: 'FeatureCollection',
      features
    };

    // Set cache headers for browser caching
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    res.setHeader('ETag', `provinces-simplified-${simplification}-${includeStats}`);
    
    res.json(geoJson);
  } catch (error) {
    console.error('Error fetching simplified provinces:', error);
    res.status(500).json({ error: 'Errore nel recupero delle province semplificate' });
  }
});

/**
 * Get downloaded map areas (coverage)
 */
router.get('/coverage', async (req, res) => {
  try {
    // Get all maps from database
    const maps = await prisma.map.findMany();
    
    // Transform into GeoJSON format
    const features = [];
    
    // Process only if we have maps
    if (maps && maps.length > 0) {
      for (const map of maps) {
        try {
          // Skip maps without valid bounding box data
          if (!map.boundingBoxNW || !map.boundingBoxSE || 
              !Array.isArray(map.boundingBoxNW) || map.boundingBoxNW.length !== 2 ||
              !Array.isArray(map.boundingBoxSE) || map.boundingBoxSE.length !== 2) {
            console.warn(`Skipping map ${map.id} due to invalid bounding box data`);
            continue;
          }
          
          // Create a polygon from the bounding box corners
          const [nwLon, nwLat] = map.boundingBoxNW;
          const [seLon, seLat] = map.boundingBoxSE;
          
          features.push({
            type: 'Feature',
            properties: {
              id: map.id,
              zoomLevel: map.zoomLevel,
              mapStyle: map.mapStyle,
              createdAt: map.createdAt,
              outputFormat: map.outputFormat
            },
            geometry: {
              type: 'Polygon',
              coordinates: [[
                [nwLon, nwLat], // Northwest
                [seLon, nwLat], // Northeast
                [seLon, seLat], // Southeast
                [nwLon, seLat], // Southwest
                [nwLon, nwLat]  // Close the polygon
              ]]
            }
          });
        } catch (err) {
          console.warn(`Error processing map ${map.id}:`, err);
          // Continue with next map
        }
      }
    }
    
    const geoJson = {
      type: 'FeatureCollection',
      features
    };
    
    res.json(geoJson);
  } catch (error) {
    console.error('Error fetching map coverage:', error);
    res.status(500).json({ error: 'Errore nel recupero delle aree scaricate' });
  }
});

/**
 * Get province coverage statistics calculated with PostGIS
 */
router.get('/coverage-stats', async (req, res) => {
  try {
    // First, get all maps and create temporary coverage polygons in PostGIS
    const maps = await prisma.map.findMany();
    
    if (maps.length === 0) {
      // Return empty stats if no maps
      return res.json({ stats: {} });
    }
    
    // Create a union of all coverage areas
    let coveragePolygons = [];
    for (const map of maps) {
      if (!map.boundingBoxNW || !map.boundingBoxSE || 
          !Array.isArray(map.boundingBoxNW) || map.boundingBoxNW.length !== 2 ||
          !Array.isArray(map.boundingBoxSE) || map.boundingBoxSE.length !== 2) {
        continue;
      }
      
      const [nwLon, nwLat] = map.boundingBoxNW;
      const [seLon, seLat] = map.boundingBoxSE;
      
      // Create WKT polygon string
      coveragePolygons.push(
        `ST_GeomFromText('POLYGON((${nwLon} ${nwLat}, ${seLon} ${nwLat}, ${seLon} ${seLat}, ${nwLon} ${seLat}, ${nwLon} ${nwLat}))', 4326)`
      );
    }
    
    if (coveragePolygons.length === 0) {
      return res.json({ stats: {} });
    }
    
    // Query to calculate intersection areas
    const query = `
      WITH coverage AS (
        SELECT ST_Union(ARRAY[${coveragePolygons.join(', ')}]) as geom
      )
      SELECT 
        p.cod_uts as code,
        p.den_uts as name,
        ST_Area(p.geometry) as total_area,
        COALESCE(
          ST_Area(
            ST_Intersection(
              ST_Transform(p.geometry, 4326),
              c.geom
            )::geography
          ), 0
        ) as covered_area
      FROM provinces p
      CROSS JOIN coverage c
      WHERE ST_Intersects(ST_Transform(p.geometry, 4326), c.geom)
      ORDER BY covered_area DESC
    `;
    
    const result = await pool.query(query);
    
    const stats = {};
    result.rows.forEach(row => {
      const percentage = row.total_area > 0 ? (row.covered_area / row.total_area) * 100 : 0;
      stats[row.code] = {
        name: row.name,
        totalArea: row.total_area,
        coveredArea: row.covered_area,
        percentage: percentage
      };
    });
    
    res.json({ stats });
  } catch (error) {
    console.error('Error calculating coverage statistics:', error);
    res.status(500).json({ error: 'Errore nel calcolo delle statistiche di copertura' });
  }
});

/**
 * Search municipalities by name for autocomplete
 */
router.get('/search/municipalities', async (req, res) => {
  try {
    const { q: query, limit = 10 } = req.query;
    
    if (!query || query.length < 2) {
      return res.json({ municipalities: [] });
    }
    
    const result = await pool.query(`
      SELECT 
        m.pro_com as code,
        m.comune as name,
        m.cod_uts as province_code,
        p.den_uts as province_name,
        p.sigla as province_sigla,
        m.cod_reg as region_code,
        ST_AsGeoJSON(ST_Transform(ST_Centroid(m.geometry), 4326)) as centroid
      FROM municipalities m
      LEFT JOIN provinces p ON m.cod_uts = p.cod_uts
      WHERE LOWER(m.comune) LIKE LOWER($1)
      ORDER BY 
        CASE WHEN LOWER(m.comune) LIKE LOWER($2) THEN 1 ELSE 2 END,
        m.comune
      LIMIT $3
    `, [`%${query}%`, `${query}%`, parseInt(limit)]);
    
    const municipalities = result.rows.map(row => ({
      code: row.code,
      name: row.name,
      province_code: row.province_code,
      province_name: row.province_name,
      province_sigla: row.province_sigla,
      region_code: row.region_code,
      centroid: row.centroid ? JSON.parse(row.centroid) : null
    }));
    
    res.json({ municipalities });
  } catch (error) {
    console.error('Error searching municipalities:', error);
    res.status(500).json({ error: 'Errore nella ricerca dei comuni' });
  }
});

/**
 * Utility route to convert province sigla to numeric code
 */
router.get('/provinces/sigla/:sigla', async (req, res) => {
  try {
    const { sigla } = req.params;
    
    const result = await pool.query(`
      SELECT cod_uts as code, den_uts as name, sigla
      FROM provinces 
      WHERE sigla = $1
    `, [sigla.toUpperCase()]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ 
        error: `Provincia con sigla '${sigla}' non trovata` 
      });
    }
    
    res.json({
      cod_prov: result.rows[0].code,
      denom_prov: result.rows[0].name,
      sigla_prov: result.rows[0].sigla
    });
  } catch (error) {
    console.error('Error finding province by sigla:', error);
    res.status(500).json({ error: 'Errore nella ricerca della provincia' });
  }
});

/**
 * Get municipalities for a specific province from database
 * Accepts both numeric codes and province sigla (PC, MI, etc.)
 */
router.get('/municipalities/:provinceCode', async (req, res) => {
  try {
    const { provinceCode } = req.params;
    const { limit = 100, offset = 0 } = req.query;
    
    if (!provinceCode) {
      return res.status(400).json({ error: 'Codice provincia richiesto' });
    }
    
    // Determine if it's a numeric code or sigla
    let queryCondition;
    let queryParam;
    
    if (/^\d+$/.test(provinceCode)) {
      // It's a numeric code
      queryCondition = 'm.cod_uts = $1';
      queryParam = parseInt(provinceCode);
    } else {
      // It's a sigla, we need to join with provinces table
      queryCondition = 'p.sigla = $1';
      queryParam = provinceCode.toUpperCase();
    }
    
    const result = await pool.query(`
      SELECT 
        m.pro_com as code,
        m.comune as name,
        m.cod_uts as province_code,
        m.cod_reg as region_code,
        p.den_uts as province_name,
        p.sigla as province_sigla,
        ST_AsGeoJSON(ST_Transform(ST_Simplify(m.geometry, 0.001), 4326)) as geometry
      FROM municipalities m
      LEFT JOIN provinces p ON m.cod_uts = p.cod_uts
      WHERE ${queryCondition}
      ORDER BY m.comune
      LIMIT $2 OFFSET $3
    `, [queryParam, parseInt(limit), parseInt(offset)]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ 
        error: `Nessun comune trovato per la provincia ${provinceCode}. Verifica il codice provincia o esegui lo script di importazione.` 
      });
    }
    
    const features = result.rows.map(row => ({
      type: 'Feature',
      properties: {
        code: row.code,
        name: row.name,
        com_istat_code: row.code,
        prov_code: row.province_code,
        prov_name: row.province_name,
        prov_sigla: row.province_sigla,
        reg_code: row.region_code
      },
      geometry: JSON.parse(row.geometry)
    }));
    
    // Get total count for pagination
    const countQuery = /^\d+$/.test(provinceCode) 
      ? 'SELECT COUNT(*) FROM municipalities WHERE cod_uts = $1'
      : 'SELECT COUNT(*) FROM municipalities m JOIN provinces p ON m.cod_uts = p.cod_uts WHERE p.sigla = $1';
    
    const countResult = await pool.query(countQuery, [queryParam]);
    
    const geoJson = {
      type: 'FeatureCollection',
      features,
      metadata: {
        total: parseInt(countResult.rows[0].count),
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: parseInt(offset) + parseInt(limit) < parseInt(countResult.rows[0].count)
      }
    };
    
    res.json(geoJson);
  } catch (error) {
    console.error('Error fetching municipalities:', error);
    res.status(500).json({ error: 'Errore nel recupero dei comuni' });
  }
});

/**
 * Get municipality coverage statistics for a province calculated with PostGIS
 */
router.get('/municipalities/:provinceCode/coverage-stats', async (req, res) => {
  try {
    const { provinceCode } = req.params;
    
    if (!provinceCode) {
      return res.status(400).json({ error: 'Codice provincia richiesto' });
    }
    
    // Get all maps to calculate coverage
    const maps = await prisma.map.findMany();
    
    if (maps.length === 0) {
      return res.json({ stats: {} });
    }
    
    // Create coverage polygons
    let coveragePolygons = [];
    for (const map of maps) {
      if (!map.boundingBoxNW || !map.boundingBoxSE || 
          !Array.isArray(map.boundingBoxNW) || map.boundingBoxNW.length !== 2 ||
          !Array.isArray(map.boundingBoxSE) || map.boundingBoxSE.length !== 2) {
        continue;
      }
      
      const [nwLon, nwLat] = map.boundingBoxNW;
      const [seLon, seLat] = map.boundingBoxSE;
      
      coveragePolygons.push(
        `ST_GeomFromText('POLYGON((${nwLon} ${nwLat}, ${seLon} ${nwLat}, ${seLon} ${seLat}, ${nwLon} ${seLat}, ${nwLon} ${nwLat}))', 4326)`
      );
    }
    
    if (coveragePolygons.length === 0) {
      return res.json({ stats: {} });
    }
    
    // Determine query condition
    let queryCondition;
    let queryParam;
    
    if (/^\d+$/.test(provinceCode)) {
      queryCondition = 'm.cod_uts = $1';
      queryParam = parseInt(provinceCode);
    } else {
      queryCondition = 'p.sigla = $1';
      queryParam = provinceCode.toUpperCase();
    }
    
    // Query to calculate intersection areas for municipalities
    const query = `
      WITH coverage AS (
        SELECT ST_Union(ARRAY[${coveragePolygons.join(', ')}]) as geom
      )
      SELECT 
        m.pro_com as code,
        m.comune as name,
        ST_Area(m.geometry) as total_area,
        COALESCE(
          ST_Area(
            ST_Intersection(
              ST_Transform(m.geometry, 4326),
              c.geom
            )::geography
          ), 0
        ) as covered_area
      FROM municipalities m
      LEFT JOIN provinces p ON m.cod_uts = p.cod_uts
      CROSS JOIN coverage c
      WHERE ${queryCondition} AND ST_Intersects(ST_Transform(m.geometry, 4326), c.geom)
      ORDER BY covered_area DESC
    `;
    
    const result = await pool.query(query, [queryParam]);
    
    const stats = {};
    result.rows.forEach(row => {
      const percentage = row.total_area > 0 ? (row.covered_area / row.total_area) * 100 : 0;
      stats[row.code] = {
        name: row.name,
        totalArea: row.total_area,
        coveredArea: row.covered_area,
        percentage: percentage
      };
    });
    
    res.json({ stats });
  } catch (error) {
    console.error('Error calculating municipality coverage statistics:', error);
    res.status(500).json({ error: 'Errore nel calcolo delle statistiche di copertura dei comuni' });
  }
});

/**
 * Get simplified municipalities for a province (for better performance)
 * Accepts both numeric codes and province sigla (PC, MI, etc.)
 */
router.get('/municipalities/:provinceCode/simplified', async (req, res) => {
  try {
    const { provinceCode } = req.params;
    
    if (!provinceCode) {
      return res.status(400).json({ error: 'Codice provincia richiesto' });
    }
    
    // Determine if it's a numeric code or sigla
    let queryCondition;
    let queryParam;
    
    if (/^\d+$/.test(provinceCode)) {
      // It's a numeric code
      queryCondition = 'm.cod_uts = $1';
      queryParam = parseInt(provinceCode);
    } else {
      // It's a sigla, we need to join with provinces table
      queryCondition = 'p.sigla = $1';
      queryParam = provinceCode.toUpperCase();
    }
    
    const result = await pool.query(`
      SELECT 
        m.pro_com as code,
        m.comune as name,
        m.cod_uts as province_code,
        p.den_uts as province_name,
        p.sigla as province_sigla,
        ST_AsGeoJSON(ST_Transform(ST_Simplify(m.geometry, 0.005), 4326)) as geometry
      FROM municipalities m
      LEFT JOIN provinces p ON m.cod_uts = p.cod_uts
      WHERE ${queryCondition}
      ORDER BY m.comune
    `, [queryParam]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ 
        error: `Nessun comune trovato per la provincia ${provinceCode}` 
      });
    }
    
    const features = result.rows.map(row => ({
      type: 'Feature',
      properties: {
        code: row.code,
        name: row.name,
        com_istat_code: row.code,
        prov_code: row.province_code,
        prov_name: row.province_name,
        prov_sigla: row.province_sigla
      },
      geometry: JSON.parse(row.geometry)
    }));
    
    const geoJson = {
      type: 'FeatureCollection',
      features
    };
    
    res.json(geoJson);
  } catch (error) {
    console.error('Error fetching simplified municipalities:', error);
    res.status(500).json({ error: 'Errore nel recupero dei comuni semplificati' });
  }
});

/**
 * Get province and municipality statistics
 */
router.get('/statistics', async (req, res) => {
  try {
    const provinceCount = await pool.query('SELECT COUNT(*) FROM provinces');
    const municipalityCount = await pool.query('SELECT COUNT(*) FROM municipalities');
    
    // Get provinces with municipality counts
    const provincesWithCounts = await pool.query(`
      SELECT 
        p.cod_uts as code,
        p.den_uts as name,
        (SELECT den_reg FROM regions WHERE cod_reg = p.cod_reg) as region_name,
        COUNT(m.id) as municipality_count
      FROM provinces p
      LEFT JOIN municipalities m ON p.cod_uts = m.cod_uts
      GROUP BY p.cod_uts, p.den_uts, p.cod_reg
      ORDER BY p.den_uts
    `);
    
    res.json({
      summary: {
        totalProvinces: parseInt(provinceCount.rows[0].count),
        totalMunicipalities: parseInt(municipalityCount.rows[0].count)
      },
      provinces: provincesWithCounts.rows
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({ error: 'Errore nel recupero delle statistiche' });
  }
});


/**
 * Health check for database connectivity
 */
router.get('/health', async (req, res) => {
  try {
    await pool.query('SELECT 1');
    res.json({ status: 'OK', message: 'Database connection successful' });
  } catch (error) {
    console.error('Database health check failed:', error);
    res.status(500).json({ status: 'ERROR', message: 'Database connection failed' });
  }
});

module.exports = router;