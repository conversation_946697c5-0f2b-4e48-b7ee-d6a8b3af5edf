const express = require('express');
const router = express.Router();
const aiService = require('../services/aiService');
const prisma = require('../lib/prisma');

// Simple authentication middleware - replace with proper auth in production
const authMiddleware = (req, res, next) => {
  // TODO: Implement proper authentication
  // For now, check for API key in header
  const apiKey = req.headers['x-api-key'];
  if (!apiKey || apiKey !== process.env.AI_API_KEY) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  next();
};

// Rate limiting for AI operations
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const MAX_REQUESTS = 10; // 10 requests per minute

const rateLimitMiddleware = (req, res, next) => {
  const clientId = req.headers['x-api-key'] || req.ip;
  const now = Date.now();
  
  if (!rateLimitMap.has(clientId)) {
    rateLimitMap.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return next();
  }
  
  const limit = rateLimitMap.get(clientId);
  
  if (now > limit.resetTime) {
    limit.count = 1;
    limit.resetTime = now + RATE_LIMIT_WINDOW;
    return next();
  }
  
  if (limit.count >= MAX_REQUESTS) {
    return res.status(429).json({ 
      error: 'Too many requests',
      retryAfter: Math.ceil((limit.resetTime - now) / 1000)
    });
  }
  
  limit.count++;
  next();
};

// Middleware to track processing time
const trackingMiddleware = (req, res, next) => {
  req.startTime = Date.now();
  next();
};

/**
 * @route GET /api/ai/health
 * @desc Check AI server health
 */
router.get('/health', async (req, res) => {
  try {
    const health = await aiService.checkHealth();
    res.json(health);
  } catch (error) {
    res.status(503).json({ 
      status: 'error', 
      message: 'AI server not available',
      error: error.message 
    });
  }
});

/**
 * @route POST /api/ai/segment-map
 * @desc Segment roofs in a map/tile
 */
router.post('/segment-map', authMiddleware, rateLimitMiddleware, trackingMiddleware, async (req, res) => {
  try {
    const { mapId, useAutoSegment = true } = req.body;
    
    // Input validation
    if (!mapId || typeof mapId !== 'string') {
      return res.status(400).json({ error: 'Invalid map ID' });
    }
    
    // Get map info from database
    const map = await prisma.map.findUnique({
      where: { id: mapId }
    });
    
    if (!map) {
      return res.status(404).json({ error: 'Map not found' });
    }
    
    // Check file size limit (50MB max for AI processing)
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    if (map.fileSize && map.fileSize > MAX_FILE_SIZE) {
      return res.status(400).json({ 
        error: 'File too large for AI processing',
        message: 'Please compress the file first using the optimization tools',
        currentSize: map.fileSize,
        maxSize: MAX_FILE_SIZE
      });
    }
    
    // Check if there's an AI-optimized version available
    let storageKey = map.storageKey;
    const aiOptimizedKey = map.storageKey.replace(/\.(tif|tiff|jpg|jpeg)$/i, '_ai_optimized.jpg');
    
    try {
      // Try to use the AI-optimized version if it exists
      const { HeadObjectCommand } = require('@aws-sdk/client-s3');
      const { s3Client } = require('../config/s3Config');
      
      const command = new HeadObjectCommand({
        Bucket: map.storageBucket,
        Key: aiOptimizedKey
      });
      
      await s3Client.send(command);
      storageKey = aiOptimizedKey;
      console.log('Using AI-optimized version:', aiOptimizedKey);
    } catch (error) {
      // AI-optimized version doesn't exist, use original
      console.log('AI-optimized version not found, using original');
    }
    
    // Fetch image from S3/MinIO
    const imageBase64 = await aiService.getMapImageFromS3(map.storageBucket, storageKey);
    
    // Perform segmentation
    const segmentation = await aiService.segmentRoofs(
      imageBase64, 
      null, 
      null, 
      useAutoSegment
    );
    
    // Get actual image dimensions from segmentation response
    // The AI is returning larger dimensions than expected
    const imageWidth = segmentation.image_width || 1024;
    const imageHeight = segmentation.image_height || 1024;
    
    console.log('Actual image dimensions:', { imageWidth, imageHeight });
    
    console.log('Segmentation response:', {
      num_masks: segmentation.num_masks,
      image_width: imageWidth,
      image_height: imageHeight,
      first_mask_polygon_sample: segmentation.masks[0]?.polygon?.slice(0, 3)
    });
    
    console.log('Map object:', map);
    
    // Convert bbox to coordinates - check multiple possible field names
    let bbox;
    if (map.bbox) {
      bbox = typeof map.bbox === 'string' ? JSON.parse(map.bbox) : map.bbox;
    } else if (map.boundingBox) {
      bbox = typeof map.boundingBox === 'string' ? JSON.parse(map.boundingBox) : map.boundingBox;
    } else if (map.boundingBoxNW && map.boundingBoxSE) {
      // Reconstruct bbox from NW and SE corners
      bbox = [
        map.boundingBoxNW[1], // min lon (NW longitude)
        map.boundingBoxSE[0], // min lat (SE latitude)
        map.boundingBoxSE[1], // max lon (SE longitude)
        map.boundingBoxNW[0]  // max lat (NW latitude)
      ];
    } else {
      throw new Error('No bounding box information found in map');
    }
    
    const minLon = bbox[0];
    const minLat = bbox[1];
    const maxLon = bbox[2];
    const maxLat = bbox[3];
    
    console.log('Map bbox:', { minLon, minLat, maxLon, maxLat });
    
    // Store results in database
    for (const mask of segmentation.masks) {
      if (mask.polygon && mask.polygon.length > 0) {
        // Convert pixel coordinates to geographic coordinates
        const geoPolygon = mask.polygon.map(point => {
          const pixelX = point[0];
          const pixelY = point[1];
          
          // Validate pixel coordinates are within image bounds
          if (pixelX < 0 || pixelX > imageWidth || pixelY < 0 || pixelY > imageHeight) {
            console.warn(`Invalid pixel coordinates: ${pixelX}, ${pixelY}`);
            return null;
          }
          
          // Convert pixel to geographic coordinates
          const lon = minLon + (pixelX / imageWidth) * (maxLon - minLon);
          const lat = maxLat - (pixelY / imageHeight) * (maxLat - minLat); // Y is inverted
          
          // Validate geographic coordinates
          if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
            console.warn(`Invalid geographic coordinates: ${lon}, ${lat}`);
            return null;
          }
          
          return [lon, lat];
        }).filter(point => point !== null);
        
        // Skip invalid polygons
        if (geoPolygon.length < 3) {
          console.warn('Polygon has less than 3 valid points, skipping');
          continue;
        }
        
        // Ensure polygon is closed
        if (geoPolygon.length > 0 && 
            (geoPolygon[0][0] !== geoPolygon[geoPolygon.length - 1][0] || 
             geoPolygon[0][1] !== geoPolygon[geoPolygon.length - 1][1])) {
          geoPolygon.push([geoPolygon[0][0], geoPolygon[0][1]]);
        }
        
        try {
          await prisma.$executeRaw`
            INSERT INTO roof_annotations (
              map_id, 
              geometry, 
              confidence, 
              annotation_source
            ) VALUES (
              ${mapId},
              ST_Transform(
                ST_SetSRID(
                  ST_GeomFromGeoJSON(${JSON.stringify({
                    type: 'Polygon',
                    coordinates: [geoPolygon]
                  })}),
                  4326
                ),
                32632
              ),
              ${mask.score},
              'sam2'
            )
          `;
        } catch (geoError) {
          console.error('Error storing polygon:', geoError);
          console.error('Polygon coordinates:', geoPolygon);
        }
      }
    }
    
    // Log AI usage for cost tracking
    console.log('AI_USAGE', {
      operation: 'segment-map',
      mapId,
      fileSize: map.fileSize,
      numRoofsDetected: segmentation.num_masks,
      processingTime: Date.now() - req.startTime,
      apiKey: req.headers['x-api-key']?.substring(0, 8) + '...',
      timestamp: new Date().toISOString()
    });
    
    res.json({
      status: 'success',
      mapId,
      numRoofsDetected: segmentation.num_masks,
      masks: segmentation.masks
    });
    
  } catch (error) {
    console.error('Segmentation error:', error);
    res.status(500).json({ 
      error: 'Segmentation failed', 
      message: error.message 
    });
  }
});

/**
 * @route POST /api/ai/classify-roofs
 * @desc Classify roofs in a map
 */
router.post('/classify-roofs', async (req, res) => {
  try {
    const { mapId } = req.body;
    
    // Get map and its annotations
    const map = await prisma.map.findUnique({
      where: { id: mapId }
    });
    
    const annotations = await prisma.$queryRaw`
      SELECT 
        id,
        ST_AsGeoJSON(ST_Transform(geometry, 4326)) as geometry_json,
        confidence
      FROM roof_annotations
      WHERE map_id = ${mapId}
        AND human_verified = FALSE
    `;
    
    if (!map || annotations.length === 0) {
      return res.status(404).json({ 
        error: 'Map or annotations not found' 
      });
    }
    
    // Check if there's an AI-optimized version available
    let storageKey = map.storageKey;
    const aiOptimizedKey = map.storageKey.replace(/\.(tif|tiff|jpg|jpeg)$/i, '_ai_optimized.jpg');
    
    try {
      // Try to use the AI-optimized version if it exists
      const { HeadObjectCommand } = require('@aws-sdk/client-s3');
      const { s3Client } = require('../config/s3Config');
      
      const command = new HeadObjectCommand({
        Bucket: map.storageBucket,
        Key: aiOptimizedKey
      });
      
      await s3Client.send(command);
      storageKey = aiOptimizedKey;
      console.log('Using AI-optimized version for classification:', aiOptimizedKey);
    } catch (error) {
      // AI-optimized version doesn't exist, use original
      console.log('AI-optimized version not found for classification, using original');
    }
    
    // Get image from S3
    const imageBase64 = await aiService.getMapImageFromS3(map.storageBucket, storageKey);
    
    // Get bbox for coordinate conversion
    let bbox;
    if (map.bbox) {
      bbox = typeof map.bbox === 'string' ? JSON.parse(map.bbox) : map.bbox;
    } else if (map.boundingBox) {
      bbox = typeof map.boundingBox === 'string' ? JSON.parse(map.boundingBox) : map.boundingBox;
    } else if (map.boundingBoxNW && map.boundingBoxSE) {
      // Reconstruct bbox from NW and SE corners
      bbox = [
        map.boundingBoxNW[1], // min lon (NW longitude)
        map.boundingBoxSE[0], // min lat (SE latitude)
        map.boundingBoxSE[1], // max lon (SE longitude)
        map.boundingBoxNW[0]  // max lat (NW latitude)
      ];
    } else {
      return res.status(400).json({ error: 'No bounding box information found in map' });
    }
    
    const minLon = bbox[0];
    const minLat = bbox[1];
    const maxLon = bbox[2];
    const maxLat = bbox[3];
    
    // Get image dimensions for coordinate conversion
    const imageWidth = 1024; // Default, should match AI processing
    const imageHeight = 1024;
    
    // Classify each roof
    const classifications = [];
    for (const annotation of annotations) {
      const geometry = JSON.parse(annotation.geometry_json);
      const geoPolygon = geometry.coordinates[0];
      
      // Convert geographic coordinates back to pixel coordinates for AI
      const pixelPolygon = geoPolygon.map(point => {
        const lon = point[0];
        const lat = point[1];
        
        // Convert from geographic to pixel coordinates
        const pixelX = Math.round((lon - minLon) / (maxLon - minLon) * imageWidth);
        const pixelY = Math.round((maxLat - lat) / (maxLat - minLat) * imageHeight);
        
        return [pixelX, pixelY];
      });
      
      console.log('Converting polygon for classification:', {
        first_geo_point: geoPolygon[0],
        first_pixel_point: pixelPolygon[0]
      });
      
      const classification = await aiService.classifyRoof(
        imageBase64, 
        pixelPolygon
      );
      
      // Update annotation with classification
      await prisma.$executeRaw`
        UPDATE roof_annotations
        SET 
          roof_type = ${classification.classification.material.class},
          material = ${classification.classification.material.class},
          material_confidence = ${classification.classification.material.confidence},
          has_solar_panels = ${classification.classification.has_solar_panels},
          building_type = ${classification.classification.building_type.class},
          quality_metrics = ${JSON.stringify(classification)}::jsonb
        WHERE id = ${annotation.id}::uuid
      `;
      
      classifications.push({
        annotationId: annotation.id,
        ...classification.classification
      });
    }
    
    res.json({
      status: 'success',
      mapId,
      numRoofsClassified: classifications.length,
      classifications
    });
    
  } catch (error) {
    console.error('Classification error:', error);
    res.status(500).json({ 
      error: 'Classification failed', 
      message: error.message 
    });
  }
});

/**
 * @route POST /api/ai/process-batch
 * @desc Process multiple maps in batch
 */
router.post('/process-batch', async (req, res) => {
  try {
    const { mapIds, includeClassification = true } = req.body;
    
    const results = [];
    
    for (const mapId of mapIds) {
      try {
        // Segment roofs
        const segmentResult = await aiService.segmentRoofs(mapId);
        
        let classificationResult = null;
        if (includeClassification && segmentResult.numRoofsDetected > 0) {
          // Classify roofs
          classificationResult = await aiService.classifyRoof(mapId);
        }
        
        results.push({
          mapId,
          status: 'success',
          segmentation: segmentResult,
          classification: classificationResult
        });
        
      } catch (error) {
        results.push({
          mapId,
          status: 'failed',
          error: error.message
        });
      }
    }
    
    res.json({
      status: 'success',
      processed: results.length,
      results
    });
    
  } catch (error) {
    console.error('Batch processing error:', error);
    res.status(500).json({ 
      error: 'Batch processing failed', 
      message: error.message 
    });
  }
});

/**
 * @route GET /api/ai/annotations/:mapId
 * @desc Get AI annotations for a map
 */
router.get('/annotations/:mapId', async (req, res) => {
  try {
    const { mapId } = req.params;
    
    const annotations = await prisma.$queryRaw`
      SELECT 
        id,
        ST_AsGeoJSON(geometry) as geometry,
        confidence,
        annotation_source,
        roof_type,
        material,
        material_confidence,
        has_solar_panels,
        building_type,
        human_verified,
        created_at
      FROM roof_annotations
      WHERE map_id = ${mapId}
      ORDER BY confidence DESC
    `;
    
    // Parse geometry JSON
    const parsedAnnotations = annotations.map(ann => ({
      ...ann,
      geometry: JSON.parse(ann.geometry)
    }));
    
    res.json({
      status: 'success',
      mapId,
      numAnnotations: parsedAnnotations.length,
      annotations: parsedAnnotations
    });
    
  } catch (error) {
    console.error('Get annotations error:', error);
    res.status(500).json({ 
      error: 'Failed to get annotations', 
      message: error.message 
    });
  }
});

/**
 * @route POST /api/ai/refine-annotation
 * @desc Refine an annotation with user feedback
 */
router.post('/refine-annotation', async (req, res) => {
  try {
    const { 
      annotationId, 
      mapId, 
      positivePoints, 
      negativePoints 
    } = req.body;
    
    // Get current annotation
    const annotation = await prisma.$queryRaw`
      SELECT 
        id,
        ST_AsGeoJSON(geometry) as geometry_json
      FROM roof_annotations
      WHERE id = ${annotationId}
    `;
    
    if (!annotation || annotation.length === 0) {
      return res.status(404).json({ error: 'Annotation not found' });
    }
    
    // Get map for image
    const map = await prisma.map.findUnique({
      where: { id: mapId }
    });
    
    const imagePath = `/outputs/${map.storageKey}`;
    const imageBase64 = await aiService.imageToBase64(imagePath);
    
    // Get current mask (for now, we'll use the polygon)
    const currentGeometry = JSON.parse(annotation[0].geometry_json);
    
    // Refine mask
    const refinement = await aiService.refineMask(
      imageBase64,
      '', // Current mask would be generated from polygon
      positivePoints,
      negativePoints
    );
    
    // Update annotation
    if (refinement.result.polygon) {
      await prisma.$executeRaw`
        UPDATE roof_annotations
        SET 
          geometry = ST_GeomFromGeoJSON(${JSON.stringify({
            type: 'Polygon',
            coordinates: [refinement.result.polygon]
          })}),
          confidence = ${refinement.result.score},
          human_verified = TRUE
        WHERE id = ${annotationId}
      `;
    }
    
    res.json({
      status: 'success',
      annotationId,
      refinement: refinement.result
    });
    
  } catch (error) {
    console.error('Refinement error:', error);
    res.status(500).json({ 
      error: 'Refinement failed', 
      message: error.message 
    });
  }
});

/**
 * @route POST /api/ai/start-active-learning
 * @desc Start active learning iteration
 */
router.post('/start-active-learning', async (req, res) => {
  try {
    const { 
      batchId = 'default', 
      sampleSize = 100, 
      strategy = 'uncertainty' 
    } = req.body;
    
    const result = await aiService.startActiveLearning(
      batchId, 
      sampleSize, 
      strategy
    );
    
    res.json(result);
    
  } catch (error) {
    console.error('Active learning error:', error);
    res.status(500).json({ 
      error: 'Active learning failed', 
      message: error.message 
    });
  }
});

/**
 * @route POST /api/ai/sam2-segment
 * @desc Interactive SAM2 segmentation with point prompts
 */
router.post('/sam2-segment', async (req, res) => {
  try {
    const { imageUrl, points } = req.body;
    
    if (!imageUrl || !points || points.length === 0) {
      return res.status(400).json({ 
        error: 'Missing required fields', 
        required: ['imageUrl', 'points'] 
      });
    }
    
    // Download image from URL (presigned S3 URL)
    const response = await fetch(imageUrl);
    const buffer = await response.buffer();
    const imageBase64 = buffer.toString('base64');
    
    // Convert points to SAM2 format
    const inputPoints = points.map(p => [p.x, p.y]);
    const inputLabels = points.map(p => p.label);
    
    // Call SAM2 for interactive segmentation
    const segmentation = await aiService.segmentWithSAM2({
      image: imageBase64,
      input_points: inputPoints,
      input_labels: inputLabels,
      multimask_output: false
    });
    
    // Convert mask to polygon
    let polygon = null;
    if (segmentation.masks && segmentation.masks.length > 0) {
      // Use the best mask (highest score)
      const bestMask = segmentation.masks.reduce((best, current) => 
        current.score > best.score ? current : best
      );
      
      polygon = bestMask.polygon;
    }
    
    res.json({
      status: 'success',
      mask: segmentation.masks?.[0]?.mask,
      polygon: polygon,
      score: segmentation.masks?.[0]?.score || 0
    });
    
  } catch (error) {
    console.error('SAM2 segmentation error:', error);
    res.status(500).json({ 
      error: 'SAM2 segmentation failed', 
      message: error.message 
    });
  }
});

module.exports = router;