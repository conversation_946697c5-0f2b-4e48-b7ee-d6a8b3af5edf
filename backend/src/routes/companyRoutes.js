const express = require('express');
const router = express.Router();
const companyService = require('../services/companyService');
const { authenticate, requireAdmin } = require('../middleware/auth');
const logger = require('../utils/logger');

// Get all companies with pagination and filters
router.get('/', async (req, res) => {
  try {
    const result = await companyService.getCompanies(req.query);
    res.json(result);
  } catch (error) {
    logger.error('Error fetching companies:', error);
    res.status(500).json({ error: 'Failed to fetch companies' });
  }
});

// Search companies
router.get('/search', async (req, res) => {
  try {
    const { q } = req.query;
    if (!q) {
      return res.status(400).json({ error: 'Search query is required' });
    }
    const companies = await companyService.searchCompanies(q);
    res.json(companies);
  } catch (error) {
    logger.error('Error searching companies:', error);
    res.status(500).json({ error: 'Failed to search companies' });
  }
});

// Get company statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await companyService.getCompanyStats();
    res.json(stats);
  } catch (error) {
    logger.error('Error fetching company stats:', error);
    res.status(500).json({ error: 'Failed to fetch company statistics' });
  }
});

// Get company by ID
router.get('/:id', async (req, res) => {
  try {
    const company = await companyService.getCompanyById(req.params.id);
    res.json(company);
  } catch (error) {
    if (error.message === 'Company not found') {
      return res.status(404).json({ error: 'Company not found' });
    }
    logger.error('Error fetching company:', error);
    res.status(500).json({ error: 'Failed to fetch company' });
  }
});

// Create new company (authenticated users only)
router.post('/', authenticate, async (req, res) => {
  try {
    const company = await companyService.createCompany(req.body);
    res.status(201).json(company);
  } catch (error) {
    if (error.code === 'P2002') {
      const field = error.meta?.target?.[0];
      return res.status(400).json({ 
        error: `Company with this ${field} already exists` 
      });
    }
    logger.error('Error creating company:', error);
    res.status(500).json({ error: 'Failed to create company' });
  }
});

// Update company (authenticated users only)
router.put('/:id', authenticate, async (req, res) => {
  try {
    const company = await companyService.updateCompany(req.params.id, req.body);
    res.json(company);
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Company not found' });
    }
    if (error.code === 'P2002') {
      const field = error.meta?.target?.[0];
      return res.status(400).json({ 
        error: `Company with this ${field} already exists` 
      });
    }
    logger.error('Error updating company:', error);
    res.status(500).json({ error: 'Failed to update company' });
  }
});

// Delete company (admin only)
router.delete('/:id', authenticate, requireAdmin, async (req, res) => {
  try {
    const result = await companyService.deleteCompany(req.params.id);
    res.json(result);
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Company not found' });
    }
    logger.error('Error deleting company:', error);
    res.status(500).json({ error: 'Failed to delete company' });
  }
});

// Bulk create companies (admin only)
router.post('/bulk', authenticate, requireAdmin, async (req, res) => {
  try {
    const { companies } = req.body;
    if (!Array.isArray(companies)) {
      return res.status(400).json({ error: 'Companies must be an array' });
    }

    const results = {
      success: [],
      errors: []
    };

    for (const companyData of companies) {
      try {
        const company = await companyService.createCompany(companyData);
        results.success.push(company);
      } catch (error) {
        results.errors.push({
          data: companyData,
          error: error.message
        });
      }
    }

    res.json(results);
  } catch (error) {
    logger.error('Error bulk creating companies:', error);
    res.status(500).json({ error: 'Failed to bulk create companies' });
  }
});

module.exports = router;