const express = require('express');
const router = express.Router();
const multer = require('multer');
const samService = require('../services/samSegmentationService');
const { authenticate } = require('../middleware/auth');

// Configure multer for image uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(file.originalname.toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

/**
 * @route POST /api/sam/segment-auto
 * @desc Automatic segmentation using SAM
 * @access Private
 */
router.post('/segment-auto', authenticate, upload.single('image'), async (req, res) => {
  try {
    let imageUrl;
    
    // Handle image input (file upload or URL)
    if (req.file) {
      // Convert uploaded file to base64
      const base64Image = req.file.buffer.toString('base64');
      imageUrl = `data:${req.file.mimetype};base64,${base64Image}`;
    } else if (req.body.imageUrl) {
      imageUrl = req.body.imageUrl;
    } else {
      return res.status(400).json({
        success: false,
        error: 'No image provided. Please upload an image or provide an image URL.'
      });
    }

    // Parse options from request body
    const options = {
      pointsPerSide: parseInt(req.body.pointsPerSide) || 32,
      predIouThresh: parseFloat(req.body.predIouThresh) || 0.88,
      stabilityScoreThresh: parseFloat(req.body.stabilityScoreThresh) || 0.95,
      minMaskRegionArea: parseInt(req.body.minMaskRegionArea) || 0,
    };

    // Process image with SAM
    const result = await samService.segmentImageAuto(imageUrl, options);

    if (result.success) {
      res.json({
        success: true,
        data: result
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        details: result.details
      });
    }
  } catch (error) {
    console.error('Segmentation endpoint error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/sam/segment-points
 * @desc Segmentation using point prompts
 * @access Private
 */
router.post('/segment-points', authenticate, upload.single('image'), async (req, res) => {
  try {
    let imageUrl;
    
    // Handle image input
    if (req.file) {
      const base64Image = req.file.buffer.toString('base64');
      imageUrl = `data:${req.file.mimetype};base64,${base64Image}`;
    } else if (req.body.imageUrl) {
      imageUrl = req.body.imageUrl;
    } else {
      return res.status(400).json({
        success: false,
        error: 'No image provided'
      });
    }

    // Parse points from request
    const points = req.body.points;
    if (!points || !Array.isArray(points) || points.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Points array is required'
      });
    }

    // Parse options
    const options = {
      multimaskOutput: req.body.multimaskOutput !== 'false',
      returnLogits: req.body.returnLogits === 'true',
      box: req.body.box ? JSON.parse(req.body.box) : undefined
    };

    // Process with SAM
    const result = await samService.segmentImageWithPoints(imageUrl, points, options);

    if (result.success) {
      res.json({
        success: true,
        data: result
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        details: result.details
      });
    }
  } catch (error) {
    console.error('Point segmentation error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/sam/segment-box
 * @desc Segmentation using bounding box
 * @access Private
 */
router.post('/segment-box', authenticate, upload.single('image'), async (req, res) => {
  try {
    let imageUrl;
    
    // Handle image input
    if (req.file) {
      const base64Image = req.file.buffer.toString('base64');
      imageUrl = `data:${req.file.mimetype};base64,${base64Image}`;
    } else if (req.body.imageUrl) {
      imageUrl = req.body.imageUrl;
    } else {
      return res.status(400).json({
        success: false,
        error: 'No image provided'
      });
    }

    // Parse box from request
    const box = req.body.box;
    if (!box || !Array.isArray(box) || box.length !== 4) {
      return res.status(400).json({
        success: false,
        error: 'Box array with 4 coordinates [x1, y1, x2, y2] is required'
      });
    }

    // Parse options
    const options = {
      multimaskOutput: req.body.multimaskOutput !== 'false',
      returnLogits: req.body.returnLogits === 'true'
    };

    // Process with SAM
    const result = await samService.segmentImageWithBox(imageUrl, box, options);

    if (result.success) {
      res.json({
        success: true,
        data: result
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        details: result.details
      });
    }
  } catch (error) {
    console.error('Box segmentation error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/sam/segment-roof
 * @desc Specialized roof segmentation
 * @access Private
 */
router.post('/segment-roof', authenticate, upload.single('image'), async (req, res) => {
  try {
    let imageUrl;
    
    // Handle image input
    if (req.file) {
      const base64Image = req.file.buffer.toString('base64');
      imageUrl = `data:${req.file.mimetype};base64,${base64Image}`;
    } else if (req.body.imageUrl) {
      imageUrl = req.body.imageUrl;
    } else {
      return res.status(400).json({
        success: false,
        error: 'No image provided'
      });
    }

    // Parse options for roof analysis
    const options = {
      pointsPerSide: parseInt(req.body.pointsPerSide) || 64,
      predIouThresh: parseFloat(req.body.predIouThresh) || 0.90,
      stabilityScoreThresh: parseFloat(req.body.stabilityScoreThresh) || 0.92,
      minMaskRegionArea: parseInt(req.body.minMaskRegionArea) || 100,
    };

    // Process roof segmentation
    const result = await samService.segmentRoof(imageUrl, options);

    if (result.success) {
      res.json({
        success: true,
        data: result
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        details: result.details
      });
    }
  } catch (error) {
    console.error('Roof segmentation error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/sam/batch-segment
 * @desc Batch process multiple images
 * @access Private
 */
router.post('/batch-segment', authenticate, async (req, res) => {
  try {
    const { images, mode = 'auto', options = {} } = req.body;

    if (!images || !Array.isArray(images) || images.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Images array is required'
      });
    }

    // Limit batch size to prevent overload
    if (images.length > 10) {
      return res.status(400).json({
        success: false,
        error: 'Maximum 10 images per batch'
      });
    }

    const results = [];
    const errors = [];

    // Process each image
    for (let i = 0; i < images.length; i++) {
      try {
        let result;
        const imageUrl = await samService.prepareImageUrl(images[i].url || images[i]);

        switch (mode) {
          case 'auto':
            result = await samService.segmentImageAuto(imageUrl, options);
            break;
          case 'roof':
            result = await samService.segmentRoof(imageUrl, options);
            break;
          default:
            throw new Error(`Invalid mode: ${mode}`);
        }

        results.push({
          index: i,
          success: result.success,
          data: result
        });
      } catch (error) {
        errors.push({
          index: i,
          error: error.message
        });
      }
    }

    res.json({
      success: errors.length === 0,
      results,
      errors,
      summary: {
        total: images.length,
        successful: results.filter(r => r.success).length,
        failed: errors.length
      }
    });
  } catch (error) {
    console.error('Batch segmentation error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/sam/status
 * @desc Check SAM service status
 * @access Private
 */
router.get('/status', async (req, res) => {
  try {
    const hasApiKey = !!process.env.REPLICATE_API_TOKEN;
    const localSamHealth = await samService.checkLocalSamHealth();
    
    res.json({
      success: true,
      status: {
        service: 'SAM Segmentation Service',
        provider: 'Replicate + Local SAM',
        model: 'meta/sam-2',
        apiKeyConfigured: hasApiKey,
        available: hasApiKey,
        localSam: localSamHealth,
        endpoints: [
          '/segment-auto',
          '/segment-points',
          '/segment-box',
          '/segment-roof',
          '/batch-segment',
          '/local/health',
          '/local/segment'
        ]
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/sam/local/health
 * @desc Check local SAM server health
 * @access Private
 */
router.get('/local/health', async (req, res) => {
  try {
    const result = await samService.checkLocalSamHealth();
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(503).json(result);
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/sam/local/segment
 * @desc Segment using local SAM server
 * @access Private
 */
router.post('/local/segment', authenticate, upload.single('image'), async (req, res) => {
  try {
    let base64Image;
    
    // Handle image input
    if (req.file) {
      base64Image = req.file.buffer.toString('base64');
    } else if (req.body.image) {
      base64Image = req.body.image;
    } else {
      return res.status(400).json({
        success: false,
        error: 'No image provided'
      });
    }
    
    // Parse points from request
    const points = req.body.points;
    if (!points || !Array.isArray(points)) {
      return res.status(400).json({
        success: false,
        error: 'Points array is required'
      });
    }
    
    const result = await samService.segmentWithLocalSam(base64Image, points);
    
    if (result.success) {
      res.json({
        success: true,
        data: result
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Local SAM segmentation error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;