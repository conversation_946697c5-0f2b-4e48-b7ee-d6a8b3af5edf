const express = require('express');
const router = express.Router();
const userPreferencesController = require('../controllers/userPreferencesController');
const { authenticate } = require('../middleware/auth');

/**
 * @route GET /api/user/preferences
 * @desc Get user preferences
 * @access Private
 */
router.get('/preferences', authenticate, userPreferencesController.getPreferences);

/**
 * @route PUT /api/user/preferences
 * @desc Update user preferences
 * @access Private
 */
router.put('/preferences', authenticate, userPreferencesController.updatePreferences);

/**
 * @route DELETE /api/user/preferences
 * @desc Reset user preferences to defaults
 * @access Private
 */
router.delete('/preferences', authenticate, userPreferencesController.resetPreferences);

module.exports = router;