const express = require('express');
const router = express.Router();
const prisma = require('../lib/prisma');
const pool = require('../lib/db');
const aiService = require('../services/aiService');

/**
 * Get annotation statistics
 */
router.get('/stats', async (req, res) => {
  try {
    // Get basic counts from the database
    const query = `
      SELECT 
        COUNT(*) FILTER (WHERE human_verified = true) as completed,
        COUNT(*) FILTER (WHERE confidence < 0.8 AND human_verified = false) as pending_review,
        COUNT(*) as total_annotations,
        AVG(confidence) as average_confidence,
        AVG(iou_score) as average_iou,
        COUNT(*) FILTER (WHERE has_solar_panels = true) as with_solar_panels,
        COUNT(DISTINCT material) as unique_materials
      FROM roof_annotations
    `;
    
    const batchQuery = `
      SELECT 
        COUNT(*) FILTER (WHERE status IN ('pending', 'in_progress')) as active_batches,
        COUNT(*) FILTER (WHERE status = 'completed') as completed_batches,
        SUM(total_roofs) as total_planned_roofs,
        SUM(completed_roofs) as total_completed_roofs
      FROM annotation_batches
    `;

    const [statsResult, batchResult] = await Promise.all([
      pool.query(query),
      pool.query(batchQuery)
    ]);

    const stats = statsResult.rows[0];
    const batches = batchResult.rows[0];

    res.json({
      totalAnnotations: parseInt(stats.total_annotations) || 0,
      pendingReview: parseInt(stats.pending_review) || 0,
      completed: parseInt(stats.completed) || 0,
      activeBatches: parseInt(batches.active_batches) || 0,
      averageConfidence: parseFloat(stats.average_confidence) || 0,
      averageIoU: parseFloat(stats.average_iou) || 0,
      withSolarPanels: parseInt(stats.with_solar_panels) || 0,
      uniqueMaterials: parseInt(stats.unique_materials) || 0,
      completedBatches: parseInt(batches.completed_batches) || 0,
      totalPlannedRoofs: parseInt(batches.total_planned_roofs) || 0,
      totalCompletedRoofs: parseInt(batches.total_completed_roofs) || 0
    });
  } catch (error) {
    console.error('Error fetching annotation stats:', error);
    res.status(500).json({ error: 'Errore nel recupero delle statistiche' });
  }
});

/**
 * Get annotation batches
 */
router.get('/batches', async (req, res) => {
  try {
    const query = `
      SELECT 
        id,
        name,
        status,
        total_roofs,
        completed_roofs,
        iteration,
        avg_confidence,
        avg_iou,
        avg_time_per_roof,
        created_at,
        completed_at
      FROM annotation_batches
      ORDER BY created_at DESC
      LIMIT 20
    `;

    const result = await pool.query(query);
    
    res.json(result.rows.map(row => ({
      id: row.id,
      name: row.name,
      status: row.status,
      totalRoofs: row.total_roofs,
      completedRoofs: row.completed_roofs,
      iteration: row.iteration,
      avgConfidence: row.avg_confidence,
      avgIou: row.avg_iou,
      avgTimePerRoof: row.avg_time_per_roof,
      createdAt: row.created_at,
      completedAt: row.completed_at
    })));
  } catch (error) {
    console.error('Error fetching batches:', error);
    res.status(500).json({ error: 'Errore nel recupero dei batch' });
  }
});

/**
 * Create new annotation batch
 */
router.post('/batches', async (req, res) => {
  try {
    const { name, totalRoofs } = req.body;

    const query = `
      INSERT INTO annotation_batches (name, total_roofs, status, created_at)
      VALUES ($1, $2, 'pending', NOW())
      RETURNING *
    `;

    const result = await pool.query(query, [name, totalRoofs]);
    
    res.json({
      id: result.rows[0].id,
      name: result.rows[0].name,
      status: result.rows[0].status,
      totalRoofs: result.rows[0].total_roofs,
      completedRoofs: 0,
      createdAt: result.rows[0].created_at
    });
  } catch (error) {
    console.error('Error creating batch:', error);
    res.status(500).json({ error: 'Errore nella creazione del batch' });
  }
});

/**
 * Get annotations for a specific area
 */
router.get('/area', async (req, res) => {
  try {
    const { bbox } = req.query;
    
    if (!bbox) {
      return res.status(400).json({ error: 'Bounding box richiesto' });
    }

    const [minLon, minLat, maxLon, maxLat] = bbox.split(',').map(Number);

    const query = `
      SELECT 
        id,
        ST_AsGeoJSON(ST_Transform(geometry, 4326)) as geometry,
        confidence,
        annotation_source,
        roof_type,
        material,
        has_solar_panels,
        building_type,
        iou_score,
        boundary_f1,
        human_verified,
        created_at
      FROM roof_annotations
      WHERE ST_Intersects(
        geometry,
        ST_Transform(
          ST_MakeEnvelope($1, $2, $3, $4, 4326),
          32632
        )
      )
      ORDER BY created_at DESC
      LIMIT 1000
    `;

    const result = await pool.query(query, [minLon, minLat, maxLon, maxLat]);

    const features = result.rows.map(row => ({
      type: 'Feature',
      properties: {
        id: row.id,
        confidence: row.confidence,
        source: row.annotation_source,
        roofType: row.roof_type,
        material: row.material,
        hasSolarPanels: row.has_solar_panels,
        buildingType: row.building_type,
        iouScore: row.iou_score,
        boundaryF1: row.boundary_f1,
        verified: row.human_verified,
        createdAt: row.created_at
      },
      geometry: JSON.parse(row.geometry)
    }));

    res.json({
      type: 'FeatureCollection',
      features
    });
  } catch (error) {
    console.error('Error fetching area annotations:', error);
    res.status(500).json({ error: 'Errore nel recupero delle annotazioni' });
  }
});

/**
 * Start active learning iteration
 */
router.post('/active-learning/start', async (req, res) => {
  try {
    const { batchId, sampleSize = 100, strategy = 'uncertainty' } = req.body;

    // Call AI service to start active learning
    const result = await aiService.startActiveLearning(batchId, sampleSize, strategy);

    // Store iteration info in database
    await pool.query(`
      INSERT INTO active_learning_iterations (
        iteration_id, batch_id, num_samples, strategy, cvat_task_id, status
      ) VALUES ($1, $2, $3, $4, $5, 'in_progress')
    `, [result.iteration_id, batchId, result.num_samples, strategy, result.cvat_task_id]);

    res.json({
      success: true,
      message: 'Active learning iteration avviata',
      ...result
    });
  } catch (error) {
    console.error('Error starting active learning:', error);
    res.status(500).json({ error: 'Errore nell\'avvio dell\'active learning' });
  }
});

/**
 * Update annotation
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Build update query dynamically based on provided fields
    const updateFields = [];
    const values = [id];
    let paramCount = 2;

    if (updates.confidence !== undefined) {
      updateFields.push(`confidence = $${paramCount}`);
      values.push(updates.confidence);
      paramCount++;
    }

    if (updates.humanVerified !== undefined) {
      updateFields.push(`human_verified = $${paramCount}`);
      values.push(updates.humanVerified);
      paramCount++;
    }

    if (updates.roofType !== undefined) {
      updateFields.push(`roof_type = $${paramCount}`);
      values.push(updates.roofType);
      paramCount++;
    }

    if (updates.material !== undefined) {
      updateFields.push(`material = $${paramCount}`);
      values.push(updates.material);
      paramCount++;
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'Nessun campo da aggiornare' });
    }

    const query = `
      UPDATE roof_annotations
      SET ${updateFields.join(', ')}, updated_at = NOW()
      WHERE id = $1
      RETURNING *
    `;

    const result = await pool.query(query, values);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Annotazione non trovata' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating annotation:', error);
    res.status(500).json({ error: 'Errore nell\'aggiornamento dell\'annotazione' });
  }
});

/**
 * Get quality metrics for a batch
 */
router.get('/batches/:batchId/quality', async (req, res) => {
  try {
    const { batchId } = req.params;

    const query = `
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE iou_score >= 0.85) as high_iou,
        COUNT(*) FILTER (WHERE boundary_f1 >= 0.80) as high_boundary,
        COUNT(*) FILTER (WHERE confidence >= 0.80) as high_confidence,
        AVG(iou_score) as avg_iou,
        AVG(boundary_f1) as avg_boundary_f1,
        AVG(confidence) as avg_confidence,
        MIN(iou_score) as min_iou,
        MAX(iou_score) as max_iou
      FROM roof_annotations
      WHERE id IN (
        SELECT annotation_id 
        FROM batch_annotations 
        WHERE batch_id = $1
      )
    `;

    const result = await pool.query(query, [batchId]);
    const metrics = result.rows[0];

    res.json({
      total: parseInt(metrics.total) || 0,
      passedQuality: {
        iou: parseInt(metrics.high_iou) || 0,
        boundary: parseInt(metrics.high_boundary) || 0,
        confidence: parseInt(metrics.high_confidence) || 0
      },
      averages: {
        iou: parseFloat(metrics.avg_iou) || 0,
        boundaryF1: parseFloat(metrics.avg_boundary_f1) || 0,
        confidence: parseFloat(metrics.avg_confidence) || 0
      },
      range: {
        iou: {
          min: parseFloat(metrics.min_iou) || 0,
          max: parseFloat(metrics.max_iou) || 0
        }
      }
    });
  } catch (error) {
    console.error('Error fetching quality metrics:', error);
    res.status(500).json({ error: 'Errore nel recupero delle metriche di qualità' });
  }
});

/**
 * Process tile with AI for roof detection
 */
router.post('/process-tile', async (req, res) => {
  try {
    const { tileId, tilePath, bbox } = req.body;

    // Validate input
    if (!tileId || !tilePath || !bbox || !Array.isArray(bbox) || bbox.length !== 4) {
      return res.status(400).json({ 
        error: 'Parametri mancanti o non validi. Richiesti: tileId, tilePath, bbox (array di 4 numeri)' 
      });
    }

    // Process tile with AI
    const startTime = Date.now();
    const result = await aiService.processTileForRoofs(tilePath, {
      id: tileId,
      bbox: bbox
    });
    const processingTime = Date.now() - startTime;

    // Store results in database
    let annotationsCreated = 0;
    
    if (result.segmentation_masks && result.segmentation_masks.length > 0) {
      for (const mask of result.segmentation_masks) {
        const classification = result.classifications?.find(c => c.mask_id === mask.id);
        
        // Convert mask polygon to PostGIS geometry if available
        if (mask.polygon && mask.polygon.length > 0) {
          try {
            // Create WKT polygon from mask coordinates
            const coordinates = mask.polygon.map(p => `${p[0]} ${p[1]}`).join(', ');
            const wkt = `POLYGON((${coordinates}, ${mask.polygon[0][0]} ${mask.polygon[0][1]}))`;
            
            await pool.query(`
              INSERT INTO roof_annotations (
                map_id, geometry, confidence, annotation_source,
                roof_type, material, material_confidence,
                has_solar_panels, building_type,
                iou_score, boundary_f1
              ) VALUES (
                $1, ST_Transform(ST_GeomFromText($2, 4326), 32632), $3, 'ai',
                $4, $5, $6, $7, $8, $9, $10
              )
            `, [
              tileId,
              wkt,
              mask.predicted_iou || 0.85,
              classification?.roof_type || null,
              classification?.material || null,
              classification?.material_confidence || null,
              classification?.has_solar_panels || false,
              classification?.building_type || null,
              mask.predicted_iou || 0.85,
              mask.stability_score || 0.80
            ]);
            
            annotationsCreated++;
          } catch (dbError) {
            console.error('Error inserting annotation:', dbError);
            // Continue with other annotations
          }
        }
      }
    }

    res.json({
      success: true,
      tile_id: tileId,
      roofs_detected: result.segmentation_masks?.length || 0,
      osm_buildings: result.osm_buildings?.length || 0,
      annotations_created: annotationsCreated,
      processing_time: processingTime
    });
  } catch (error) {
    console.error('Error processing tile:', error);
    res.status(500).json({ 
      error: 'Errore nel processamento del tile',
      message: error.message 
    });
  }
});

/**
 * Refine annotation with user feedback
 */
router.post('/refine/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { imagePath, positivePoints, negativePoints } = req.body;

    // Get current annotation
    const annotation = await pool.query(
      'SELECT * FROM roof_annotations WHERE id = $1',
      [id]
    );

    if (annotation.rows.length === 0) {
      return res.status(404).json({ error: 'Annotazione non trovata' });
    }

    // Get current mask (simplified - in production would retrieve from storage)
    const currentMask = 'base64_encoded_mask_data';

    // Refine with AI
    const refined = await aiService.refineMask(
      imagePath,
      currentMask,
      positivePoints,
      negativePoints
    );

    // Update annotation
    await pool.query(`
      UPDATE roof_annotations
      SET confidence = $1, human_verified = true, updated_at = NOW()
      WHERE id = $2
    `, [refined.confidence, id]);

    res.json({
      success: true,
      annotation_id: id,
      refined_mask: refined.refined_mask,
      confidence: refined.confidence
    });
  } catch (error) {
    console.error('Error refining annotation:', error);
    res.status(500).json({ error: 'Errore nel raffinamento dell\'annotazione' });
  }
});

/**
 * Get AI server status
 */
router.get('/ai-status', async (req, res) => {
  try {
    const health = await aiService.checkHealth();
    res.json(health);
  } catch (error) {
    console.error('Error checking AI status:', error);
    res.status(503).json({ 
      status: 'unavailable',
      error: 'AI server non disponibile' 
    });
  }
});

/**
 * Get annotations for a specific map
 */
router.get('/map/:mapId', async (req, res) => {
  try {
    const { mapId } = req.params;
    
    const query = `
      SELECT 
        id,
        ST_AsGeoJSON(ST_Transform(geometry, 4326)) as geometry,
        confidence,
        annotation_source as class,
        roof_type,
        material,
        has_solar_panels,
        building_type,
        iou_score,
        boundary_f1,
        human_verified,
        created_at,
        quality_metrics as attributes
      FROM roof_annotations
      WHERE map_id = $1
      ORDER BY created_at DESC
    `;
    
    const result = await pool.query(query, [mapId]);
    
    const annotations = result.rows.map(row => {
      const geometry = JSON.parse(row.geometry);
      const coordinates = geometry.coordinates[0];
      
      return {
        id: row.id,
        class: row.class || 'roof',
        points: coordinates.map(coord => ({ x: coord[0], y: coord[1] })),
        attributes: {
          material: row.material,
          roofType: row.roof_type,
          hasSolarPanels: row.has_solar_panels,
          buildingType: row.building_type,
          confidence: row.confidence,
          verified: row.human_verified,
          ...row.attributes
        },
        mapId: mapId
      };
    });
    
    res.json(annotations);
  } catch (error) {
    console.error('Error fetching map annotations:', error);
    res.status(500).json({ error: 'Errore nel recupero delle annotazioni' });
  }
});

/**
 * Create new annotation
 */
router.post('/', async (req, res) => {
  try {
    const { mapId, class: annotationClass, points, attributes } = req.body;
    
    // Convert points to WKT polygon
    const wktPoints = points.map(p => `${p.x} ${p.y}`).join(', ');
    const wkt = `POLYGON((${wktPoints}, ${points[0].x} ${points[0].y}))`;
    
    const query = `
      INSERT INTO roof_annotations (
        map_id, 
        geometry, 
        annotation_source,
        roof_type,
        material,
        has_solar_panels,
        building_type,
        confidence,
        human_verified,
        quality_metrics
      ) VALUES (
        $1,
        ST_Transform(ST_GeomFromText($2, 4326), 32632),
        $3,
        $4,
        $5,
        $6,
        $7,
        $8,
        true,
        $9
      ) RETURNING id
    `;
    
    const values = [
      mapId,
      wkt,
      annotationClass || 'manual',
      attributes?.roofType || null,
      attributes?.material || null,
      attributes?.hasSolarPanels || false,
      attributes?.buildingType || null,
      attributes?.confidence || 1.0,
      attributes || {}
    ];
    
    const result = await pool.query(query, values);
    
    res.json({
      success: true,
      id: result.rows[0].id
    });
  } catch (error) {
    console.error('Error creating annotation:', error);
    res.status(500).json({ error: 'Errore nella creazione dell\'annotazione' });
  }
});

module.exports = router;