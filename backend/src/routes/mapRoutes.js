const express = require('express');
const router = express.Router();
const mapController = require('../controllers/mapController');
const { authenticate } = require('../middleware/auth');

/**
 * @route POST /api/generate-map
 * @desc Generate a map image based on parameters
 * @access Public
 */
router.post('/generate-map', authenticate, mapController.generateMap);

/**
 * @route GET /api/map-styles
 * @desc Get available map styles
 * @access Public
 */
router.get('/map-styles', mapController.getMapStyles);

/**
 * @route POST /api/generate-map-async
 * @desc Generate a map image asynchronously with WebSocket progress updates
 * @access Public
 */
router.post('/generate-map-async', authenticate, mapController.generateMapAsync);

/**
 * @route GET /api/download-map/:key
 * @desc Download a map file from S3 with proper headers
 * @access Public
 */
router.get('/download-map/:key(*)', authenticate, mapController.downloadMap);

/**
 * @route GET /api/map-presigned-url/:key
 * @desc Get a presigned URL for direct S3 download
 * @access Public
 */
router.get('/map-presigned-url/:key(*)', authenticate, mapController.getPresignedUrl);

/**
 * @route GET /api/downloaded-areas
 * @desc Get list of downloaded map areas with optional filters
 * @access Public
 */
router.get('/downloaded-areas', authenticate, mapController.getDownloadedAreas);

/**
 * @route POST /api/geocode-address
 * @desc Geocode an address and return coordinates with bounding box
 * @access Public
 */
router.post('/geocode-address', mapController.geocodeAddressEndpoint);

module.exports = router;