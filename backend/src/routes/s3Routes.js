const express = require('express');
const router = express.Router();
const { 
  s3Client, 
  bucketConfig, 
  getFromS3, 
  createPresignedGetUrl 
} = require('../config/s3Config');
const { 
  ListBucketsCommand, 
  ListObjectsV2Command, 
  GetObjectCommand 
} = require('@aws-sdk/client-s3');

/**
 * Get all buckets
 */
router.get('/buckets', async (req, res) => {
  try {
    const command = new ListBucketsCommand({});
    const data = await s3Client.send(command);
    const buckets = data.Buckets.map(bucket => ({
      name: bucket.Name,
      creationDate: bucket.CreationDate
    }));
    
    res.json(buckets);
  } catch (error) {
    console.error('Error listing buckets:', error);
    res.status(500).json({ error: 'Errore nel recupero dei bucket' });
  }
});

/**
 * Get objects in a bucket with optional prefix
 */
router.get('/objects', async (req, res) => {
  try {
    const { bucket, prefix = '', delimiter = '/' } = req.query;
    
    if (!bucket) {
      return res.status(400).json({ error: 'Parametro bucket richiesto' });
    }
    
    const command = new ListObjectsV2Command({
      Bucket: bucket,
      Prefix: prefix,
      Delimiter: delimiter,
      MaxKeys: 1000
    });
    
    const data = await s3Client.send(command);
    
    // Process common prefixes (folders)
    const folders = data.CommonPrefixes ? data.CommonPrefixes.map(prefix => ({
      key: prefix.Prefix,
      name: prefix.Prefix.split('/').filter(p => p).pop(),
      isFolder: true
    })) : [];
    
    // Process objects (files)
    const files = data.Contents ? data.Contents
      // Filter out the prefix itself and objects that represent folders
      .filter(item => item.Key !== prefix && !item.Key.endsWith('/'))
      .map(item => ({
        key: item.Key,
        name: item.Key.split('/').pop(),
        size: item.Size,
        lastModified: item.LastModified,
        isFolder: false
      })) : [];
    
    // Combine folders and files
    const objects = [...folders, ...files];
    
    res.json(objects);
  } catch (error) {
    console.error('Error listing objects:', error);
    res.status(500).json({ error: 'Errore nel recupero degli oggetti' });
  }
});

/**
 * Search for objects in a bucket
 */
router.get('/search', async (req, res) => {
  try {
    const { bucket, query } = req.query;
    
    if (!bucket || !query) {
      return res.status(400).json({ error: 'Parametri bucket e query richiesti' });
    }
    
    const command = new ListObjectsV2Command({
      Bucket: bucket,
      MaxKeys: 1000
    });
    
    const data = await s3Client.send(command);
    
    // Filter objects that match the query
    const objects = data.Contents
      .filter(item => item.Key.toLowerCase().includes(query.toLowerCase()))
      .map(item => ({
        key: item.Key,
        name: item.Key.split('/').pop(),
        size: item.Size,
        lastModified: item.LastModified,
        isFolder: false
      }));
    
    res.json(objects);
  } catch (error) {
    console.error('Error searching objects:', error);
    res.status(500).json({ error: 'Errore nella ricerca degli oggetti' });
  }
});

/**
 * Get a specific object
 */
router.get('/objects/:key(*)', async (req, res) => {
  try {
    const { key } = req.params;
    const { bucket } = req.query;
    
    if (!bucket) {
      return res.status(400).json({ error: 'Parametro bucket richiesto' });
    }
    
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: key
    });
    
    const data = await s3Client.send(command);
    
    // Set appropriate headers
    if (data.ContentType) {
      res.setHeader('Content-Type', data.ContentType);
    }
    
    if (data.ContentLength) {
      res.setHeader('Content-Length', data.ContentLength);
    }
    
    // Convert the ReadableStream to Buffer and send it
    const chunks = [];
    for await (const chunk of data.Body) {
      chunks.push(chunk);
    }
    const bodyBuffer = Buffer.concat(chunks);
    
    // Send the response
    res.send(bodyBuffer);
  } catch (error) {
    console.error('Error retrieving object:', error);
    if (error.name === 'NoSuchKey') {
      return res.status(404).json({ error: 'Oggetto non trovato' });
    }
    res.status(500).json({ error: 'Errore nel recupero dell\'oggetto' });
  }
});

/**
 * Download an object
 */
router.get('/download', async (req, res) => {
  try {
    const { bucket, key } = req.query;
    
    if (!bucket || !key) {
      return res.status(400).json({ error: 'Parametri bucket e key richiesti' });
    }
    
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: key
    });
    
    const data = await s3Client.send(command);
    
    // Set appropriate headers for download
    if (data.ContentType) {
      res.setHeader('Content-Type', data.ContentType);
    }
    
    const filename = key.split('/').pop();
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    if (data.ContentLength) {
      res.setHeader('Content-Length', data.ContentLength);
    }
    
    // Convert the ReadableStream to Buffer and send it
    const chunks = [];
    for await (const chunk of data.Body) {
      chunks.push(chunk);
    }
    const bodyBuffer = Buffer.concat(chunks);
    
    // Send the response
    res.send(bodyBuffer);
  } catch (error) {
    console.error('Error downloading object:', error);
    if (error.name === 'NoSuchKey') {
      return res.status(404).json({ error: 'Oggetto non trovato' });
    }
    res.status(500).json({ error: 'Errore nel download dell\'oggetto' });
  }
});

/**
 * Get a presigned URL for direct S3 access
 * This is more efficient than proxying the file through the Node.js server
 */
router.get('/presigned/:key(*)', async (req, res) => {
  try {
    const key = req.params.key;
    const bucket = req.query.bucket || bucketConfig.name;
    
    // Create presigned URL with GetObjectCommand
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: key
    });
    
    // Use the getSignedUrl utility imported from s3-request-presigner
    const url = await createPresignedGetUrl(key, 3600); // URL valid for 1 hour
    
    res.json({ url });
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    res.status(500).json({ error: 'Errore nel generare l\'URL presigned' });
  }
});

/**
 * List files in a folder (legacy endpoint, kept for compatibility)
 */
router.get('/list/:folder', async (req, res) => {
  try {
    const folder = req.params.folder;
    
    // Security check - only allow access to the configured folders
    if (!Object.values(bucketConfig.folders).includes(folder)) {
      return res.status(403).json({ 
        error: 'Accesso negato', 
        message: 'Accesso consentito solo alle cartelle configurate' 
      });
    }
    
    const command = new ListObjectsV2Command({
      Bucket: bucketConfig.name,
      Prefix: `${folder}/`,
      MaxKeys: 1000
    });
    
    const data = await s3Client.send(command);
    
    // Format the response
    const files = data.Contents.map(item => ({
      key: item.Key,
      size: item.Size,
      lastModified: item.LastModified,
      url: `/api/s3/presigned/${item.Key}` // URL to get presigned URL
    }));
    
    res.json({ files });
  } catch (error) {
    console.error('Error listing S3 files:', error);
    res.status(500).json({ error: 'Errore nel recupero dei file' });
  }
});

/**
 * Delete an object from S3
 */
router.delete('/buckets/:bucket/objects/*', async (req, res) => {
  try {
    const bucket = req.params.bucket;
    const key = req.params[0]; // Get the wildcard part of the path
    
    if (!bucket || !key) {
      return res.status(400).json({ error: 'Parametri bucket e key richiesti' });
    }
    
    const { DeleteObjectCommand } = require('@aws-sdk/client-s3');
    const command = new DeleteObjectCommand({
      Bucket: bucket,
      Key: key
    });
    
    await s3Client.send(command);
    
    res.json({ 
      success: true, 
      message: `Oggetto ${key} eliminato con successo` 
    });
  } catch (error) {
    console.error('Error deleting object:', error);
    res.status(500).json({ error: 'Errore nell\'eliminazione dell\'oggetto' });
  }
});

module.exports = router;