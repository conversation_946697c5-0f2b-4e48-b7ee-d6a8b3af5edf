const express = require('express');
const router = express.Router();
const organizationService = require('../services/organizationService');
const { authenticate } = require('../middleware/auth');
const logger = require('../utils/logger');

// Ottieni tutte le organizzazioni dell'utente
router.get('/', authenticate, async (req, res) => {
  try {
    const organizations = await organizationService.getOrganizations(req.user.id);
    res.json(organizations);
  } catch (error) {
    logger.error('Errore recupero organizzazioni:', error);
    res.status(500).json({ error: 'Impossibile recuperare le organizzazioni' });
  }
});

// Ottieni organizzazione specifica
router.get('/:id', authenticate, async (req, res) => {
  try {
    const organization = await organizationService.getOrganizationById(req.params.id, req.user.id);
    res.json(organization);
  } catch (error) {
    if (error.message === 'Organizzazione non trovata') {
      return res.status(404).json({ error: 'Organizzazione non trovata' });
    }
    logger.error('Errore recupero organizzazione:', error);
    res.status(500).json({ error: 'Impossibile recuperare l\'organizzazione' });
  }
});

// Crea nuova organizzazione
router.post('/', authenticate, async (req, res) => {
  try {
    const organization = await organizationService.createOrganization(req.body, req.user.id);
    res.status(201).json(organization);
  } catch (error) {
    if (error.code === 'P2002') {
      return res.status(400).json({ 
        error: 'Un\'organizzazione con questo nome esiste già' 
      });
    }
    logger.error('Errore creazione organizzazione:', error);
    res.status(500).json({ error: 'Impossibile creare l\'organizzazione' });
  }
});

// Aggiorna organizzazione
router.put('/:id', authenticate, async (req, res) => {
  try {
    const organization = await organizationService.updateOrganization(
      req.params.id, 
      req.body, 
      req.user.id
    );
    res.json(organization);
  } catch (error) {
    if (error.message === 'Non autorizzato') {
      return res.status(403).json({ error: 'Non autorizzato' });
    }
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Organizzazione non trovata' });
    }
    logger.error('Errore aggiornamento organizzazione:', error);
    res.status(500).json({ error: 'Impossibile aggiornare l\'organizzazione' });
  }
});

// Elimina organizzazione
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const result = await organizationService.deleteOrganization(req.params.id, req.user.id);
    res.json(result);
  } catch (error) {
    if (error.message === 'Non autorizzato') {
      return res.status(403).json({ error: 'Solo il proprietario può eliminare l\'organizzazione' });
    }
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Organizzazione non trovata' });
    }
    logger.error('Errore eliminazione organizzazione:', error);
    res.status(500).json({ error: 'Impossibile eliminare l\'organizzazione' });
  }
});

// Aggiungi utente all'organizzazione
router.post('/:id/users', authenticate, async (req, res) => {
  try {
    const { email, role } = req.body;
    
    if (!email) {
      return res.status(400).json({ error: 'Email richiesta' });
    }

    const userOrganization = await organizationService.addUserToOrganization(
      req.params.id,
      email,
      role,
      req.user.id
    );
    
    res.status(201).json(userOrganization);
  } catch (error) {
    if (error.message === 'Non autorizzato') {
      return res.status(403).json({ error: 'Non autorizzato' });
    }
    if (error.message === 'Utente non trovato') {
      return res.status(404).json({ error: 'Utente non trovato' });
    }
    if (error.code === 'P2002') {
      return res.status(400).json({ error: 'L\'utente è già membro dell\'organizzazione' });
    }
    logger.error('Errore aggiunta utente:', error);
    res.status(500).json({ error: 'Impossibile aggiungere l\'utente' });
  }
});

// Rimuovi utente dall'organizzazione
router.delete('/:id/users/:userId', authenticate, async (req, res) => {
  try {
    const result = await organizationService.removeUserFromOrganization(
      req.params.id,
      req.params.userId,
      req.user.id
    );
    res.json(result);
  } catch (error) {
    if (error.message === 'Non autorizzato') {
      return res.status(403).json({ error: 'Non autorizzato' });
    }
    if (error.message === 'Impossibile rimuovere il proprietario dell\'organizzazione') {
      return res.status(400).json({ error: error.message });
    }
    logger.error('Errore rimozione utente:', error);
    res.status(500).json({ error: 'Impossibile rimuovere l\'utente' });
  }
});

// Aggiorna ruolo utente
router.put('/:id/users/:userId/role', authenticate, async (req, res) => {
  try {
    const { role } = req.body;
    
    if (!['owner', 'admin', 'member'].includes(role)) {
      return res.status(400).json({ error: 'Ruolo non valido' });
    }

    const userOrganization = await organizationService.updateUserRole(
      req.params.id,
      req.params.userId,
      role,
      req.user.id
    );
    
    res.json(userOrganization);
  } catch (error) {
    if (error.message === 'Solo il proprietario può modificare i ruoli') {
      return res.status(403).json({ error: error.message });
    }
    logger.error('Errore aggiornamento ruolo:', error);
    res.status(500).json({ error: 'Impossibile aggiornare il ruolo' });
  }
});

module.exports = router;