// Import AWS SDK v3 S3 client and commands
const { S3Client } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');

// MinIO S3 Configuration from environment variables
const s3Config = {
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
  region: process.env.AWS_REGION || 'eu-south-1',
  endpoint: process.env.AWS_ENDPOINT,
  forcePathStyle: process.env.AWS_FORCE_PATH_STYLE === 'true', // Needed for MinIO
};

// Create S3 client instance
const s3Client = new S3Client(s3Config);

// Bucket configuration
const bucketConfig = {
  name: process.env.AWS_S3_BUCKET || 'astrameccanica',
  folders: {
    rawTiles: 'raw_tiles',
    processedMaps: 'processed_maps',
    cache: 'cache',
  }
};

/**
 * Upload a file to S3 storage
 * 
 * @param {Buffer|Stream} fileContent - The file content to upload
 * @param {string} fileName - The name to use for the file in S3
 * @param {string} folder - The folder within the bucket (from bucketConfig.folders)
 * @param {object} metadata - Optional metadata to store with the file
 * @returns {Promise<object>} - The S3 upload result with the file URL
 */
async function uploadToS3(fileContent, fileName, folder, metadata = {}) {
  // Import PutObjectCommand here to avoid circular dependencies
  const { PutObjectCommand } = require('@aws-sdk/client-s3');

  // If folder is not provided, use default
  if (!folder) {
    folder = bucketConfig.folders.processedMaps;
  }

  const key = `${folder}/${fileName}`;
  
  const params = {
    Bucket: bucketConfig.name,
    Key: key,
    Body: fileContent,
    Metadata: Object.keys(metadata).reduce((acc, key) => {
      // S3 metadata values must be strings
      acc[key] = metadata[key].toString();
      return acc;
    }, {}),
    ContentType: getContentType(fileName)
  };

  try {
    const command = new PutObjectCommand(params);
    const result = await s3Client.send(command);
    return {
      ...result,
      ETag: result.ETag,
      Key: key,
      Bucket: bucketConfig.name,
      Location: `${s3Config.endpoint}/${bucketConfig.name}/${key}`,
      url: `${s3Config.endpoint}/${bucketConfig.name}/${key}`
    };
  } catch (error) {
    console.error('Error uploading to S3:', error);
    throw error;
  }
}

/**
 * Get a file from S3 storage
 * 
 * @param {string} key - The full key of the file in S3
 * @param {boolean} asStream - Whether to return as stream (for large files)
 * @returns {Promise<object>} - The S3 object data
 */
async function getFromS3(key, asStream = false) {
  // Import GetObjectCommand here to avoid circular dependencies
  const { GetObjectCommand } = require('@aws-sdk/client-s3');
  
  const params = {
    Bucket: bucketConfig.name,
    Key: key
  };

  try {
    const command = new GetObjectCommand(params);
    const response = await s3Client.send(command);
    
    // If streaming is requested, return the response as-is
    if (asStream) {
      return response;
    }
    
    // Convert ReadableStream to Buffer if needed
    if (response.Body) {
      // For SDK v3, we need to handle the stream differently
      const chunks = [];
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      response.Body = Buffer.concat(chunks);
    }
    
    return response;
  } catch (error) {
    console.error('Error retrieving from S3:', error);
    throw error;
  }
}

/**
 * Helper function to determine content type from file name
 */
function getContentType(fileName) {
  if (fileName.endsWith('.tif') || fileName.endsWith('.tiff')) {
    return 'image/tiff';
  } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
    return 'image/jpeg';
  } else if (fileName.endsWith('.png')) {
    return 'image/png';
  } else if (fileName.endsWith('.json')) {
    return 'application/json';
  }
  return 'application/octet-stream'; // Default
}

/**
 * Check if the bucket exists, create it if it doesn't
 */
async function ensureBucketExists() {
  // Import commands here to avoid circular dependencies
  const { 
    HeadBucketCommand, 
    CreateBucketCommand,
    NotFound
  } = require('@aws-sdk/client-s3');

  try {
    const headCommand = new HeadBucketCommand({ Bucket: bucketConfig.name });
    await s3Client.send(headCommand);
    console.log(`Bucket ${bucketConfig.name} already exists`);
  } catch (error) {
    if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
      try {
        const createCommand = new CreateBucketCommand({ 
          Bucket: bucketConfig.name,
          CreateBucketConfiguration: {
            LocationConstraint: s3Config.region
          }
        });
        await s3Client.send(createCommand);
        console.log(`Bucket ${bucketConfig.name} created successfully`);
      } catch (createError) {
        console.error('Error creating bucket:', createError);
        throw createError;
      }
    } else {
      console.error('Error checking bucket:', error);
      throw error;
    }
  }
}

// Initialize - ensure bucket exists when module is loaded
(async () => {
  try {
    await ensureBucketExists();
  } catch (error) {
    console.error('Failed to initialize S3 storage:', error);
  }
})();

// Functions to create presigned URLs using AWS SDK v3
async function createPresignedGetUrl(key, expiresIn = 3600) {
  const { GetObjectCommand } = require('@aws-sdk/client-s3');
  const command = new GetObjectCommand({
    Bucket: bucketConfig.name,
    Key: key
  });
  
  return await getSignedUrl(s3Client, command, { expiresIn });
}

module.exports = {
  s3Client,
  uploadToS3,
  getFromS3,
  bucketConfig,
  createPresignedGetUrl,
  s3Config
};