const { PrismaClient } = require('../src/generated/prisma');
const prisma = new PrismaClient();

const geocodedCompaniesController = {
  // Get all geocoded companies with pagination and filters
  async getAll(req, res) {
    try {
      const {
        page = 1,
        limit = 50,
        search,
        provincia,
        citta,
        validationStatus,
        hasCoordinates,
        sortBy = 'ragioneSociale',
        sortOrder = 'asc',
        minLat,
        maxLat,
        minLng,
        maxLng
      } = req.query;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);

      // Build where clause
      const where = {};

      if (search) {
        where.OR = [
          { ragioneSociale: { contains: search, mode: 'insensitive' } },
          { piva: { contains: search, mode: 'insensitive' } },
          { indirizzoCompleto: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (provincia) {
        where.provincia = provincia;
      }

      if (citta) {
        where.citta = citta;
      }

      if (validationStatus) {
        where.validationStatus = validationStatus;
      }

      if (hasCoordinates === 'true') {
        where.AND = [
          { latitudine: { not: null } },
          { longitudine: { not: null } }
        ];
      } else if (hasCoordinates === 'false') {
        where.OR = [
          { latitudine: null },
          { longitudine: null }
        ];
      }

      // Add bounding box filter if provided
      if (minLat && maxLat && minLng && maxLng) {
        const latFilter = {
          latitudine: {
            gte: parseFloat(minLat),
            lte: parseFloat(maxLat)
          }
        };
        const lngFilter = {
          longitudine: {
            gte: parseFloat(minLng),
            lte: parseFloat(maxLng)
          }
        };
        
        if (where.AND) {
          where.AND.push(latFilter, lngFilter);
        } else {
          where.AND = [latFilter, lngFilter];
        }
      }

      // Get total count
      const total = await prisma.geocodedCompany.count({ where });

      // Get paginated results
      const companies = await prisma.geocodedCompany.findMany({
        where,
        skip,
        take,
        orderBy: {
          [sortBy]: sortOrder
        }
      });

      res.json({
        success: true,
        data: companies,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Error fetching geocoded companies:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch geocoded companies'
      });
    }
  },

  // Get single company by ID
  async getById(req, res) {
    try {
      const { id } = req.params;

      const company = await prisma.geocodedCompany.findUnique({
        where: { id }
      });

      if (!company) {
        return res.status(404).json({
          success: false,
          error: 'Company not found'
        });
      }

      res.json({
        success: true,
        data: company
      });
    } catch (error) {
      console.error('Error fetching company:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch company'
      });
    }
  },

  // Get statistics
  async getStatistics(req, res) {
    try {
      const [
        total,
        validated,
        withCoordinates,
        byProvince,
        byValidationStatus
      ] = await Promise.all([
        prisma.geocodedCompany.count(),
        prisma.geocodedCompany.count({
          where: { validationStatus: 'TROVATO' }
        }),
        prisma.geocodedCompany.count({
          where: {
            AND: [
              { latitudine: { not: null } },
              { longitudine: { not: null } }
            ]
          }
        }),
        prisma.geocodedCompany.groupBy({
          by: ['provincia'],
          _count: true,
          orderBy: {
            _count: {
              provincia: 'desc'
            }
          }
        }),
        prisma.geocodedCompany.groupBy({
          by: ['validationStatus'],
          _count: true
        })
      ]);

      res.json({
        success: true,
        data: {
          total,
          validated,
          withCoordinates,
          byProvince: byProvince.filter(p => p.provincia),
          byValidationStatus
        }
      });
    } catch (error) {
      console.error('Error fetching statistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch statistics'
      });
    }
  },

  // Get unique provinces
  async getProvinces(req, res) {
    try {
      const provinces = await prisma.geocodedCompany.findMany({
        where: {
          provincia: { not: null }
        },
        select: {
          provincia: true
        },
        distinct: ['provincia'],
        orderBy: {
          provincia: 'asc'
        }
      });

      res.json({
        success: true,
        data: provinces.map(p => p.provincia)
      });
    } catch (error) {
      console.error('Error fetching provinces:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch provinces'
      });
    }
  },

  // Get unique cities
  async getCities(req, res) {
    try {
      const { provincia } = req.query;
      
      const where = {
        citta: { not: null }
      };
      
      if (provincia) {
        where.provincia = provincia;
      }
      
      const cities = await prisma.geocodedCompany.findMany({
        where,
        select: {
          citta: true
        },
        distinct: ['citta'],
        orderBy: {
          citta: 'asc'
        }
      });

      res.json({
        success: true,
        data: cities.map(c => c.citta)
      });
    } catch (error) {
      console.error('Error fetching cities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch cities'
      });
    }
  },

  // Get companies for map view
  async getForMap(req, res) {
    try {
      const {
        bounds,
        provincia,
        validationStatus
      } = req.query;

      const where = {
        AND: [
          { latitudine: { not: null } },
          { longitudine: { not: null } }
        ]
      };

      if (provincia) {
        where.provincia = provincia;
      }

      if (validationStatus) {
        where.validationStatus = validationStatus;
      }

      if (bounds) {
        const [swLat, swLng, neLat, neLng] = bounds.split(',').map(Number);
        where.AND.push(
          { latitudine: { gte: swLat, lte: neLat } },
          { longitudine: { gte: swLng, lte: neLng } }
        );
      }

      const companies = await prisma.geocodedCompany.findMany({
        where,
        select: {
          id: true,
          ragioneSociale: true,
          latitudine: true,
          longitudine: true,
          indirizzoCompleto: true,
          validationStatus: true,
          businessStatus: true,
          rating: true
        },
        take: 1000 // Limit for performance
      });

      res.json({
        success: true,
        data: companies
      });
    } catch (error) {
      console.error('Error fetching companies for map:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch companies for map'
      });
    }
  }
};

module.exports = geocodedCompaniesController;