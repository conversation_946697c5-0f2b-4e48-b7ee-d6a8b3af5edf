const express = require('express');
const cors = require('cors');
const path = require('path');
const pino = require('pino-http');
const dotenv = require('dotenv');
const { createServer } = require('http');
const { WebSocketServer } = require('ws');
const compression = require('compression');
const { exec } = require('child_process');

// Load environment variables from backend/.env
dotenv.config({ path: path.join(__dirname, '.env') });
console.log('Environment loaded from:', path.join(__dirname, '.env'));

// Function to kill process using a specific port
async function killPortProcess(port) {
  return new Promise((resolve) => {
    exec(`lsof -ti:${port} | xargs kill -9 2>/dev/null || true`, (error) => {
      // Ignore errors - it's fine if no process is using the port
      resolve();
    });
  });
}

// Import prisma client
const prisma = require('./src/lib/prisma');

// Import routes
const mapRoutes = require('./src/routes/mapRoutes');
const s3Routes = require('./src/routes/s3Routes');
const databaseRoutes = require('./src/routes/databaseRoutes');
const geoRoutes = require('./src/routes/geoRoutes');
// const annotationRoutes = require('./src/routes/annotationRoutes'); // Removed - annotations handled by CVAT
const aiRoutes = require('./src/routes/aiRoutes');
const compressionRoutes = require('./src/routes/compressionRoutes');
const userRoutes = require('./src/routes/userRoutes');
const companyRoutes = require('./src/routes/companyRoutes');
const categoryRoutes = require('./src/routes/categoryRoutes');
const dataSourceRoutes = require('./src/routes/dataSourceRoutes');
const importRoutes = require('./src/routes/importRoutes');
const organizationRoutes = require('./src/routes/organizationRoutes');
const emailRoutes = require('./src/routes/emailRoutes');
const areaAnalyzerRoutes = require('./src/routes/areaAnalyzerRoutes');
const samSegmentationRoutes = require('./src/routes/samSegmentationRoutes');
const geocodedCompaniesRoutes = require('./routes/geocodedCompanies');

const app = express();
const PORT = process.env.PORT || 3000;

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = process.env.ALLOWED_ORIGINS 
      ? process.env.ALLOWED_ORIGINS.split(',') 
      : ['http://localhost:5173', 'http://localhost:3000'];
    
    // Allow requests with no origin (like mobile apps or Postman)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1 || process.env.NODE_ENV === 'development') {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

// Middleware
app.use(cors(corsOptions));
app.use(compression()); // Enable gzip compression for all responses
app.use(express.json());
app.use(pino({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  redact: ['req.headers.authorization'],
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true
    }
  }
}));

// Static files from output directory
app.use('/outputs', express.static(path.join(__dirname, '../outputs')));

// API Routes
app.use('/api', mapRoutes);
app.use('/api/s3', s3Routes);
app.use('/api/database', databaseRoutes);
app.use('/api/geo', geoRoutes);
// app.use('/api/annotations', annotationRoutes); // Removed - annotations handled by CVAT
app.use('/api/ai', aiRoutes);
app.use('/api/compression', compressionRoutes);
app.use('/api', userRoutes);
app.use('/api/user', require('./src/routes/userPreferencesRoutes'));
app.use('/api/sam-images', require('./src/routes/samImageRoutes'));
app.use('/api/organizations', organizationRoutes);
app.use('/api/email', emailRoutes);
app.use('/api/companies-db/categories', categoryRoutes);
app.use('/api/companies-db/data-sources', dataSourceRoutes);
app.use('/api/companies-db/import', importRoutes);
app.use('/api/geocoded-companies', geocodedCompaniesRoutes);
app.use('/api/companies-db', companyRoutes);
app.use('/api/area-analyzer', areaAnalyzerRoutes);
app.use('/api/sam', samSegmentationRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).send({ status: 'ok' });
});

// Error handling middleware for Prisma
app.use((err, req, res, next) => {
  if (err.name === 'PrismaClientKnownRequestError') {
    return res.status(400).json({
      error: 'Database error',
      message: err.message
    });
  }
  next(err);
});

// Create HTTP server
const server = createServer(app);

// Create WebSocket server
const wss = new WebSocketServer({ server });

// Store active WebSocket connections
const activeConnections = new Map();

wss.on('connection', (ws, req) => {
  const connectionId = Date.now().toString();
  activeConnections.set(connectionId, ws);
  
  console.log(`WebSocket client connected: ${connectionId}`);
  
  ws.on('close', () => {
    activeConnections.delete(connectionId);
    console.log(`WebSocket client disconnected: ${connectionId}`);
  });
  
  ws.on('error', (error) => {
    console.error(`WebSocket error for ${connectionId}:`, error);
    activeConnections.delete(connectionId);
  });
});

// Export function to broadcast progress updates
app.locals.broadcastProgress = (data) => {
  const message = JSON.stringify(data);
  activeConnections.forEach((ws) => {
    if (ws.readyState === ws.OPEN) {
      ws.send(message);
    }
  });
};

// Start server with port cleanup
(async () => {
  // Kill any process using the port before starting
  await killPortProcess(PORT);
  
  server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`WebSocket server ready`);
  });
})();

// Graceful shutdown to close Prisma connection
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

module.exports = app;