{"name": "astraai-backend", "version": "0.1.0", "description": "Express backend for AstraAI map generator", "main": "app.js", "scripts": {"dev": "nodemon app.js", "start": "node app.js", "lint": "eslint .", "import-geo": "node src/scripts/importGeoData.js", "download-quality-data": "node src/scripts/downloadISTATDirect.js", "import-quality-geo": "node src/scripts/importHighQualityData.js", "improve-boundaries": "node src/scripts/improveBoundaryQuality.js", "download-osm-data": "node src/scripts/downloadHighQualityData.js --osm", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate"}, "dependencies": {"@aws-sdk/client-s3": "^3.815.0", "@aws-sdk/s3-request-presigner": "^3.815.0", "@prisma/client": "^6.8.2", "@turf/area": "^7.2.0", "@turf/helpers": "^7.2.0", "@turf/intersect": "^7.2.0", "@turf/turf": "^7.2.0", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "nodemailer": "^6.9.15", "pg": "^8.16.0", "pino": "^8.15.0", "pino-http": "^8.5.0", "prisma": "^6.8.2", "replicate": "^1.0.1", "sharp": "^0.34.3", "ws": "^8.18.2"}, "devDependencies": {"eslint": "^8.44.0", "nodemon": "^3.1.10", "pino-pretty": "^10.3.1"}}