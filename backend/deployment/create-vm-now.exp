#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Create VM with GPU passthrough
send "qm create 200 --name sam-gpu-server --memory 8192 --cores 4 --cpu host --ostype l26 --scsihw virtio-scsi-pci --scsi0 local-lvm:50 --ide2 local:iso/ubuntu-24.04.1-live-server-amd64.iso,media=cdrom --net0 virtio,bridge=vmbr0 --boot order=scsi0 --machine q35 --bios ovmf --efidisk0 local-lvm:1\r"
expect "# "

# Add GPU passthrough
send "qm set 200 -hostpci0 07:00,pcie=1\r"
expect "# "

# Enable auto start
send "qm set 200 --onboot 1\r"
expect "# "

# Start VM
send "qm start 200\r"
expect "# "

# Show status
send "qm status 200\r"
expect "# "

# Show config
send "qm config 200 | grep -E 'hostpci|memory|cores'\r"
expect "# "

# Get VNC port
send "qm config 200 | grep vnc\r"
expect "# "

send "echo 'VM 200 created successfully!'\r"
expect "# "
send "echo 'Access VM console via: https://*************:8006 -> VM 200 -> Console'\r"
expect "# "

send "exit\r"
expect eof