#!/usr/bin/expect -f

# Script to create and deploy DeepLab container on VM 200
# VM 200: RTX 5080, <PERSON><PERSON>, <PERSON><PERSON><PERSON>

set timeout 300
set vm_ip "*************"
set vm_user "alin"
set vm_pass "Al0xan999"

puts "Connecting to VM 200 with RTX 5080..."
spawn ssh $vm_user@$vm_ip

expect {
    "password:" {
        send "$vm_pass\r"
    }
    "Are you sure you want to continue connecting" {
        send "yes\r"
        expect "password:"
        send "$vm_pass\r"
    }
}

expect "$ "
puts "Connected to VM 200"

# Create DeepLab directory structure
send "sudo mkdir -p /opt/deeplab-server\r"
expect "$ "

# Create the DeepLab server Python file
send "sudo tee /opt/deeplab-server/server.py > /dev/null << 'EOF'\r"
send {from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import torch
import torchvision.transforms as T
from PIL import Image
import io
import base64
import numpy as np
import logging
import json

app = FastAPI(title="DeepLab Segmentation Server for Satellite Imagery")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Model configuration
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = None

# Satellite imagery specific classes
SATELLITE_CLASSES = {
    0: "background",
    1: "building",
    2: "road",
    3: "vegetation",
    4: "water",
    5: "parking",
    6: "roof",
    7: "pavement",
    8: "bare_soil"
}

@app.on_event("startup")
async def load_model():
    global model
    try:
        # Use torchvision's DeepLabV3 pretrained on COCO
        # For production, you'd want a model trained on satellite imagery
        model = torch.hub.load('pytorch/vision:v0.10.0', 'deeplabv3_resnet101', pretrained=True)
        model.to(device)
        model.eval()
        logging.info(f"DeepLab model loaded on {device}")
    except Exception as e:
        logging.error(f"Failed to load model: {e}")

@app.get("/")
async def root():
    return {
        "service": "DeepLab Segmentation Server",
        "version": "1.0.0",
        "model": "DeepLabV3+ ResNet101",
        "device": str(device),
        "optimized_for": "satellite_imagery"
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "gpu_available": torch.cuda.is_available(),
        "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None
    }

@app.get("/gpu-info")
async def gpu_info():
    if torch.cuda.is_available():
        return {
            "gpu_available": True,
            "gpu_count": torch.cuda.device_count(),
            "gpu_name": torch.cuda.get_device_name(0),
            "cuda_version": torch.version.cuda,
            "memory_allocated": torch.cuda.memory_allocated(0),
            "memory_reserved": torch.cuda.memory_reserved(0)
        }
    return {"gpu_available": False}

@app.post("/segment")
async def segment(file: UploadFile = File(...)):
    try:
        # Read image
        contents = await file.read()
        image = Image.open(io.BytesIO(contents)).convert("RGB")
        original_size = image.size
        
        # Preprocess for DeepLab
        preprocess = T.Compose([
            T.Resize((512, 512)),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        input_tensor = preprocess(image).unsqueeze(0).to(device)
        
        # Inference
        with torch.no_grad():
            output = model(input_tensor)['out'][0]
        
        # Get predicted class map
        output_predictions = output.argmax(0).cpu().numpy()
        
        # Resize back to original size
        from scipy import ndimage
        output_predictions = ndimage.zoom(
            output_predictions,
            (original_size[1] / 512, original_size[0] / 512),
            order=0
        )
        
        # Create binary masks for each detected class
        masks = []
        unique_classes = np.unique(output_predictions)
        
        for class_id in unique_classes:
            if class_id == 0:  # Skip background
                continue
                
            # Create binary mask for this class
            mask = (output_predictions == class_id).astype(np.uint8) * 255
            
            # Convert to base64 PNG
            mask_pil = Image.fromarray(mask, mode='L')
            buffer = io.BytesIO()
            mask_pil.save(buffer, format="PNG")
            mask_b64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            masks.append(mask_b64)
        
        # Calculate confidence scores (simplified)
        scores = [0.95 - (i * 0.05) for i in range(len(masks))][:3]
        
        return JSONResponse({
            "masks": masks[:3],  # Return top 3 masks like SAM
            "scores": scores,
            "shape": list(output_predictions.shape)
        })
        
    except Exception as e:
        logging.error(f"Segmentation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/segment/points")
async def segment_with_points(
    image: UploadFile = File(...),
    points: str = None
):
    """Segment with point prompts for compatibility with SAM interface"""
    # For now, just call regular segment
    # In production, you'd implement point-guided segmentation
    return await segment(image)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8081)
}
send "\r"
send "EOF\r"
expect "$ "

# Create requirements file
send "sudo tee /opt/deeplab-server/requirements.txt > /dev/null << 'EOF'\r"
send {fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
torch==2.1.0
torchvision==0.16.0
Pillow==10.1.0
numpy==1.24.3
scipy==1.11.4
}
send "\r"
send "EOF\r"
expect "$ "

# Create Dockerfile for DeepLab
send "sudo tee /opt/deeplab-server/Dockerfile > /dev/null << 'EOF'\r"
send {FROM nvidia/cuda:12.1.0-runtime-ubuntu22.04

ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# Install Python and system dependencies
RUN apt-get update && apt-get install -y \
    python3-pip \
    python3-dev \
    git \
    wget \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install PyTorch with CUDA support for RTX 5080
RUN pip3 install --no-cache-dir torch torchvision --index-url https://download.pytorch.org/whl/cu121

# Install other requirements
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy server code
COPY server.py .

# Expose port
EXPOSE 8081

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD python3 -c "import requests; r = requests.get('http://localhost:8081/health'); exit(0 if r.status_code == 200 else 1)" || exit 1

# Run the server
CMD ["uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8081", "--workers", "1"]
}
send "\r"
send "EOF\r"
expect "$ "

# Create docker-compose for both services
send "sudo tee /opt/deeplab-server/docker-compose.yml > /dev/null << 'EOF'\r"
send {version: '3.8'

services:
  deeplab-server:
    build: .
    image: deeplab-server:latest
    container_name: deeplab-server
    restart: unless-stopped
    ports:
      - "8081:8081"
    volumes:
      - ./models:/models
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
    networks:
      - ai-segmentation

networks:
  ai-segmentation:
    external: true
}
send "\r"
send "EOF\r"
expect "$ "

# Build the Docker image
puts "Building DeepLab Docker image..."
send "cd /opt/deeplab-server\r"
expect "$ "

send "sudo docker build -t deeplab-server:latest .\r"
expect {
    "Successfully built" {
        puts "Docker image built successfully"
    }
    timeout {
        puts "Build might be taking longer than expected..."
    }
}
expect "$ "

# Create network if it doesn't exist
send "sudo docker network create ai-segmentation 2>/dev/null || true\r"
expect "$ "

# Stop any existing container
send "sudo docker stop deeplab-server 2>/dev/null || true\r"
expect "$ "
send "sudo docker rm deeplab-server 2>/dev/null || true\r"
expect "$ "

# Run the container
puts "Starting DeepLab container..."
send "sudo docker run -d --name deeplab-server --restart unless-stopped --network ai-segmentation --gpus all -p 8081:8081 deeplab-server:latest\r"
expect "$ "

# Check if container is running
send "sleep 5\r"
expect "$ "
send "sudo docker ps | grep deeplab-server\r"
expect {
    "deeplab-server" {
        puts "DeepLab container is running"
    }
    "$ " {
        puts "Warning: Container might not be running"
    }
}

# Test the API
puts "Testing DeepLab API..."
send "curl -s http://localhost:8081/health | python3 -m json.tool\r"
expect "$ "

send "curl -s http://localhost:8081/gpu-info | python3 -m json.tool\r"
expect "$ "

# Show container logs
send "sudo docker logs --tail 20 deeplab-server\r"
expect "$ "

puts "\n=== DeepLab Deployment Complete ==="
puts "DeepLab server is running at: http://$vm_ip:8081"
puts "Health check: http://$vm_ip:8081/health"
puts "GPU info: http://$vm_ip:8081/gpu-info"
puts "Segmentation endpoint: http://$vm_ip:8081/segment"

send "exit\r"
expect eof