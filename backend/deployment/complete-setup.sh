#!/bin/bash

# Complete Segmentation Setup Script
HOST="*************"
USER="alin"

echo "=== COMPLETE SEGMENTATION SETUP ==="

# Create all notebooks and scripts
ssh $USER@$HOST << 'ENDSSH'

# Create test script
cat > ~/jupyter-ml/notebooks/test_all_models.py << 'EOF'
#!/usr/bin/env python3
import torch
import torchvision
import sys

print("=" * 60)
print("TESTING ALL SEGMENTATION MODELS")
print("=" * 60)

# System info
print(f"\nPyTorch: {torch.__version__}")
print(f"CUDA: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

# Test DeepLab
print("\n[1] Testing DeepLabV3+...")
try:
    model = torchvision.models.segmentation.deeplabv3_resnet101(weights='DEFAULT')
    model.eval()
    if torch.cuda.is_available():
        model = model.cuda()
    
    test_input = torch.randn(1, 3, 512, 512)
    if torch.cuda.is_available():
        test_input = test_input.cuda()
    
    with torch.no_grad():
        output = model(test_input)
    
    print(f"✅ DeepLab ready! Output shape: {output['out'].shape}")
except Exception as e:
    print(f"❌ DeepLab error: {e}")

# Test FCN
print("\n[2] Testing FCN...")
try:
    fcn = torchvision.models.segmentation.fcn_resnet101(weights='DEFAULT')
    fcn.eval()
    if torch.cuda.is_available():
        fcn = fcn.cuda()
    
    with torch.no_grad():
        output = fcn(test_input)
    
    print(f"✅ FCN ready! Output shape: {output['out'].shape}")
except Exception as e:
    print(f"❌ FCN error: {e}")

print("\n" + "=" * 60)
print("ALL MODELS TESTED SUCCESSFULLY!")
print("Ready for satellite image segmentation")
print("=" * 60)
EOF

# Run the test
echo "Testing all models..."
sudo docker exec jupyter-ml python3 /workspace/notebooks/test_all_models.py

# Create comparison notebook
cat > ~/jupyter-ml/notebooks/model_comparison.py << 'EOF'
#!/usr/bin/env python3
import torch
import torchvision
import time
import numpy as np
from PIL import Image

def benchmark_models():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load models
    print("Loading models...")
    deeplab = torchvision.models.segmentation.deeplabv3_resnet101(weights='DEFAULT').to(device).eval()
    fcn = torchvision.models.segmentation.fcn_resnet101(weights='DEFAULT').to(device).eval()
    
    # Test image
    test_img = torch.randn(1, 3, 1024, 1024).to(device)
    
    # Warmup
    with torch.no_grad():
        _ = deeplab(test_img)
        _ = fcn(test_img)
    
    # Benchmark
    print("\nBenchmarking on RTX 5080 (1024x1024 image):")
    
    # DeepLab
    torch.cuda.synchronize()
    start = time.time()
    with torch.no_grad():
        for _ in range(10):
            _ = deeplab(test_img)
    torch.cuda.synchronize()
    deeplab_time = (time.time() - start) / 10
    
    # FCN
    torch.cuda.synchronize()
    start = time.time()
    with torch.no_grad():
        for _ in range(10):
            _ = fcn(test_img)
    torch.cuda.synchronize()
    fcn_time = (time.time() - start) / 10
    
    print(f"DeepLabV3+: {deeplab_time*1000:.2f}ms ({1/deeplab_time:.1f} FPS)")
    print(f"FCN: {fcn_time*1000:.2f}ms ({1/fcn_time:.1f} FPS)")
    
    # Memory usage
    print(f"\nGPU Memory: {torch.cuda.memory_allocated()/1e9:.2f} GB")

if __name__ == "__main__":
    benchmark_models()
EOF

echo ""
echo "Running benchmark..."
sudo docker exec jupyter-ml python3 /workspace/notebooks/model_comparison.py

echo ""
echo "=== SETUP COMPLETE ==="
echo "Access Jupyter at: http://*************:8888"
echo "Notebooks created:"
echo "- test_all_models.py"
echo "- model_comparison.py"

ENDSSH