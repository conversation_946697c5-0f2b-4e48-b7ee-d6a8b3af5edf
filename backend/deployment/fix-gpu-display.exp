#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Stop VM first
send "qm stop 202\r"
expect "# "

# Remove x-vga to keep Proxmox display active
send "qm set 202 -hostpci0 07:00,pcie=1\r"
expect "# "

# Add virtual display adapter
send "qm set 202 -vga std\r"
expect "# "

# Start VM
send "qm start 202\r"
expect "# "

send "echo 'VM configured with both GPU passthrough and virtual display'\r"
expect "# "
send "echo 'Access console via Proxmox web interface'\r"
expect "# "

send "exit\r"
expect eof