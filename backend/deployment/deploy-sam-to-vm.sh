#!/bin/bash

# Master script per deployare SAM sulla VM dopo installazione Ubuntu

set -e

echo "====================================="
echo "SAM GPU Server Deployment Script"
echo "====================================="

# Configuration
PROXMOX_HOST="*************"
PROXMOX_USER="root"
PROXMOX_PASS="Al0xan999"
VM_ID="202"

# Step 1: Wait for VM to be ready
echo "Step 1: Waiting for VM to be ready..."
./deployment/wait-vm-ready.exp

# Get VM IP
VM_IP=$(sshpass -p "$PROXMOX_PASS" ssh -o StrictHostKeyChecking=no $PROXMOX_USER@$PROXMOX_HOST \
    "qm agent $VM_ID network-get-interfaces 2>/dev/null | grep -A1 enp6s18 | grep '\"ip-address\"' | head -1 | cut -d'\"' -f4")

if [ -z "$VM_IP" ]; then
    echo "Error: Could not get VM IP address"
    exit 1
fi

echo "VM IP: $VM_IP"

# Step 2: Copy setup script to VM
echo "Step 2: Copying setup script to VM..."
sshpass -p "ubuntu" scp -o StrictHostKeyChecking=no ./deployment/setup-sam-vm.sh ubuntu@$VM_IP:/home/<USER>/

# Step 3: Execute setup script on VM
echo "Step 3: Executing SAM setup on VM..."
sshpass -p "ubuntu" ssh -o StrictHostKeyChecking=no ubuntu@$VM_IP "chmod +x /home/<USER>/setup-sam-vm.sh && sudo /home/<USER>/setup-sam-vm.sh"

# Step 4: Test SAM server
echo "Step 4: Testing SAM server..."
sleep 10
curl -s http://$VM_IP:8000/health | python3 -m json.tool

echo ""
echo "====================================="
echo "SAM GPU Server Deployment Complete!"
echo "====================================="
echo ""
echo "Server URL: http://$VM_IP:8000"
echo "Health Check: http://$VM_IP:8000/health"
echo ""
echo "Update frontend configuration:"
echo "  File: /frontend/src/services/samLocalService.ts"
echo "  Set: SERVER_URL = 'http://$VM_IP:8000'"
echo ""