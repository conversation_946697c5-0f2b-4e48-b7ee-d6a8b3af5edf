#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== CONFIGURING BOOT ORDER FOR UBUNTU INSTALLATION ===\n"

# Check current VM config
send "qm config 200 | grep -E '(ide2|boot|cdrom)'\r"
expect "# "

# Set boot order to CD-ROM first
send "qm set 200 -boot order=ide2\r"
expect "# "

# Restart VM to boot from ISO
send "qm stop 200\r"
expect "# "
sleep 2

send "qm start 200\r"
expect "# "

puts "\n=== VM RESTARTED ===\n"
send "echo 'VM should now boot from Ubuntu ISO'\r"
expect "# "
send "echo 'Go to Proxmox Console and you should see Ubuntu installer'\r"
expect "# "
send "echo 'If still in UEFI shell, type: exit'\r"
expect "# "
send "echo 'Then select Boot Manager -> UEFI QEMU DVD-ROM'\r"
expect "# "

send "exit\r"
expect eof