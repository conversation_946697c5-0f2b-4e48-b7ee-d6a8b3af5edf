#!/bin/bash

# Script per configurare SAM su Proxmox remotamente
PROXMOX_HOST="*************"
PROXMOX_USER="root"
PROXMOX_PASS="Al0xan999"

echo "=== Configurazione SAM su Proxmox ==="

# Funzione per eseguire comandi remoti
remote_exec() {
    sshpass -p "$PROXMOX_PASS" ssh -o StrictHostKeyChecking=no "$PROXMOX_USER@$PROXMOX_HOST" "$1"
}

# Step 1: Verifica connessione e GPU
echo "Step 1: Verifica GPU su Proxmox..."
remote_exec "lspci | grep -i nvidia"

# Step 2: Verifica IOMMU
echo "Step 2: Verifica IOMMU..."
remote_exec "dmesg | grep -e DMAR -e IOMMU | head -5"

# Step 3: Crea script di setup su Proxmox
echo "Step 3: Creazione script di configurazione..."
remote_exec "cat > /tmp/setup-sam-vm.sh << 'EOF'
#!/bin/bash

# Configurazione VM per SAM
VM_ID=200
VM_NAME=sam-gpu-server
VM_CORES=4
VM_RAM=8192
VM_DISK=50
STORAGE=local-lvm

echo 'Checking if VM exists...'
if qm status \$VM_ID 2>/dev/null; then
    echo 'VM \$VM_ID already exists. Stopping and removing...'
    qm stop \$VM_ID
    qm destroy \$VM_ID
fi

echo 'Finding NVIDIA GPU...'
GPU_ID=\$(lspci -nn | grep -i nvidia | grep VGA | cut -d' ' -f1)
echo \"GPU found: \$GPU_ID\"

echo 'Creating new VM...'
qm create \$VM_ID \
    --name \$VM_NAME \
    --memory \$VM_RAM \
    --cores \$VM_CORES \
    --cpu host \
    --ostype l26 \
    --scsihw virtio-scsi-pci \
    --scsi0 \$STORAGE:\$VM_DISK \
    --net0 virtio,bridge=vmbr0 \
    --boot order=scsi0 \
    --machine q35

echo 'Adding GPU to VM...'
qm set \$VM_ID -hostpci0 \$GPU_ID,pcie=1

echo 'VM created successfully!'
qm config \$VM_ID
EOF"

# Step 4: Esegui script
echo "Step 4: Esecuzione configurazione VM..."
remote_exec "chmod +x /tmp/setup-sam-vm.sh && /tmp/setup-sam-vm.sh"

# Step 5: Mostra status
echo "Step 5: Verifica configurazione..."
remote_exec "qm config 200"

echo ""
echo "=== Configurazione completata! ==="
echo ""
echo "Prossimi passi:"
echo "1. Installa Ubuntu Server nella VM 200"
echo "2. Configura SAM nel container Docker"
echo ""
echo "Per avviare la VM: qm start 200"
echo "Per la console: qm console 200"