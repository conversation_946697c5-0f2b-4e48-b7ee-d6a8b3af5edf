#!/bin/bash

# Script per configurare SAM GPU Server su Ubuntu VM
# Da eseguire DENTRO la VM Ubuntu dopo l'installazione

set -e

echo "=== SAM GPU Server Setup ==="
echo "Starting at $(date)"

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Step 1: Update system
log_info "Step 1: Updating system packages..."
sudo apt-get update
sudo apt-get upgrade -y

# Step 2: Install NVIDIA Driver
log_info "Step 2: Installing NVIDIA Driver..."
sudo apt-get install -y ubuntu-drivers-common
sudo ubuntu-drivers autoinstall
sudo apt-get install -y nvidia-driver-535

# Step 3: Install CUDA Toolkit
log_info "Step 3: Installing CUDA Toolkit..."
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2404/x86_64/cuda-keyring_1.1-1_all.deb
sudo dpkg -i cuda-keyring_1.1-1_all.deb
sudo apt-get update
sudo apt-get -y install cuda-toolkit-12-3

# Step 4: Install Docker
log_info "Step 4: Installing Docker..."
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# Step 5: Install NVIDIA Container Toolkit
log_info "Step 5: Installing NVIDIA Container Toolkit..."
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update
sudo apt-get install -y nvidia-container-toolkit
sudo nvidia-ctk runtime configure --runtime=docker
sudo systemctl restart docker

# Step 6: Verify GPU in Docker
log_info "Step 6: Verifying GPU in Docker..."
sudo docker run --rm --gpus all nvidia/cuda:11.8.0-base-ubuntu22.04 nvidia-smi

# Step 7: Create SAM Server directory
log_info "Step 7: Setting up SAM Server..."
sudo mkdir -p /opt/sam-server
cd /opt/sam-server

# Step 8: Create Dockerfile
sudo tee Dockerfile > /dev/null << 'EOF'
FROM nvidia/cuda:11.8.0-cudnn8-runtime-ubuntu22.04

# Install Python and dependencies
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3-pip \
    git \
    wget \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Install PyTorch with CUDA support
RUN pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Install SAM and dependencies
RUN pip3 install segment-anything opencv-python-headless
RUN pip3 install fastapi uvicorn[standard] python-multipart aiofiles websockets pillow numpy

# Download SAM models
RUN mkdir -p /models && cd /models && \
    wget -q https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth && \
    wget -q https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth && \
    wget -q https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth

WORKDIR /app
COPY main.py /app/

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
EOF

# Step 9: Create FastAPI application
sudo tee main.py > /dev/null << 'EOF'
from fastapi import FastAPI, UploadFile, File, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import torch
import numpy as np
from segment_anything import sam_model_registry, SamPredictor, SamAutomaticMaskGenerator
import cv2
import base64
import io
from PIL import Image
import json
import time
from typing import List, Dict, Any
import asyncio
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="SAM GPU Server", version="1.0.0")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# GPU configuration
device = "cuda" if torch.cuda.is_available() else "cpu"
logger.info(f"Using device: {device}")
if device == "cuda":
    logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
    logger.info(f"CUDA Version: {torch.version.cuda}")

# Load SAM model
sam_checkpoint = "/models/sam_vit_h_4b8939.pth"
model_type = "vit_h"

logger.info("Loading SAM model...")
sam = sam_model_registry[model_type](checkpoint=sam_checkpoint)
sam.to(device=device)
logger.info("SAM model loaded successfully")

# Initialize predictor and mask generator
predictor = SamPredictor(sam)
mask_generator = SamAutomaticMaskGenerator(
    model=sam,
    points_per_side=32,
    pred_iou_thresh=0.88,
    stability_score_thresh=0.95,
    crop_n_layers=1,
    crop_n_points_downscale_factor=2,
    min_mask_region_area=100,
)

# Cache for embeddings
embeddings_cache = {}

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: dict):
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except:
                pass

manager = ConnectionManager()

@app.get("/")
async def root():
    gpu_info = {}
    if torch.cuda.is_available():
        gpu_info = {
            "name": torch.cuda.get_device_name(0),
            "memory_allocated": f"{torch.cuda.memory_allocated(0) / 1024**3:.2f} GB",
            "memory_cached": f"{torch.cuda.memory_reserved(0) / 1024**3:.2f} GB",
            "memory_total": f"{torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB"
        }
    
    return {
        "status": "SAM GPU Server Running",
        "device": device,
        "gpu_info": gpu_info,
        "model": model_type,
        "endpoints": ["/embed", "/segment/points", "/segment/box", "/segment/auto", "/ws"]
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "gpu_available": torch.cuda.is_available(),
        "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
        "device": device,
        "model_loaded": sam is not None,
        "cache_size": len(embeddings_cache)
    }

@app.post("/embed")
async def create_embedding(file: UploadFile = File(...)):
    """Pre-compute image embedding for fast inference"""
    try:
        start_time = time.time()
        
        # Read and process image
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        image_np = np.array(image.convert("RGB"))
        
        # Generate unique ID
        embedding_id = f"img_{int(time.time() * 1000)}"
        
        # Compute embedding
        predictor.set_image(image_np)
        
        # Cache embedding
        embeddings_cache[embedding_id] = {
            "features": predictor.features,
            "input_size": predictor.input_size,
            "original_size": predictor.original_size,
            "image": image_np
        }
        
        elapsed = time.time() - start_time
        
        return {
            "embedding_id": embedding_id,
            "processing_time": elapsed,
            "image_size": image_np.shape[:2]
        }
        
    except Exception as e:
        logger.error(f"Embedding error: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.post("/segment/points")
async def segment_with_points(
    embedding_id: str,
    points: List[List[int]],
    labels: List[int]
):
    """Segment using point prompts with cached embedding"""
    try:
        if embedding_id not in embeddings_cache:
            return JSONResponse(status_code=404, content={"error": "Embedding not found"})
        
        start_time = time.time()
        
        # Restore predictor state
        cached = embeddings_cache[embedding_id]
        predictor.features = cached["features"]
        predictor.input_size = cached["input_size"]
        predictor.original_size = cached["original_size"]
        
        # Generate masks
        input_points = np.array(points)
        input_labels = np.array(labels)
        
        masks, scores, logits = predictor.predict(
            point_coords=input_points,
            point_labels=input_labels,
            multimask_output=True,
        )
        
        # Encode masks
        masks_encoded = []
        for mask in masks:
            mask_uint8 = (mask * 255).astype(np.uint8)
            _, buffer = cv2.imencode('.png', mask_uint8)
            mask_base64 = base64.b64encode(buffer).decode('utf-8')
            masks_encoded.append(mask_base64)
        
        elapsed = time.time() - start_time
        
        return {
            "masks": masks_encoded,
            "scores": scores.tolist(),
            "processing_time": elapsed
        }
        
    except Exception as e:
        logger.error(f"Point segmentation error: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.post("/segment/auto")
async def segment_automatic(file: UploadFile = File(...)):
    """Automatic mask generation"""
    try:
        start_time = time.time()
        
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        image_np = np.array(image.convert("RGB"))
        
        # Generate masks
        masks = mask_generator.generate(image_np)
        
        # Process results
        results = []
        for mask_data in masks:
            mask = mask_data['segmentation']
            mask_uint8 = (mask * 255).astype(np.uint8)
            _, buffer = cv2.imencode('.png', mask_uint8)
            mask_base64 = base64.b64encode(buffer).decode('utf-8')
            
            results.append({
                'mask': mask_base64,
                'area': int(mask_data['area']),
                'bbox': mask_data['bbox'],
                'predicted_iou': float(mask_data['predicted_iou']),
                'stability_score': float(mask_data['stability_score'])
            })
        
        elapsed = time.time() - start_time
        
        return {
            "masks": results,
            "total_masks": len(results),
            "processing_time": elapsed
        }
        
    except Exception as e:
        logger.error(f"Auto segmentation error: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket for real-time segmentation"""
    await manager.connect(websocket)
    embedding_id = None
    
    try:
        while True:
            data = await websocket.receive_json()
            
            if data["type"] == "set_image":
                # Process new image
                image_data = base64.b64decode(data["image"].split(',')[1])
                image = Image.open(io.BytesIO(image_data))
                image_np = np.array(image.convert("RGB"))
                
                embedding_id = f"ws_{id(websocket)}_{int(time.time() * 1000)}"
                
                predictor.set_image(image_np)
                embeddings_cache[embedding_id] = {
                    "features": predictor.features,
                    "input_size": predictor.input_size,
                    "original_size": predictor.original_size,
                    "image": image_np
                }
                
                await websocket.send_json({
                    "type": "embedding_ready",
                    "embedding_id": embedding_id
                })
                
            elif data["type"] == "add_point" and embedding_id:
                # Process point
                cached = embeddings_cache[embedding_id]
                predictor.features = cached["features"]
                predictor.input_size = cached["input_size"]
                predictor.original_size = cached["original_size"]
                
                points = np.array(data["points"])
                labels = np.array(data["labels"])
                
                masks, scores, _ = predictor.predict(
                    point_coords=points,
                    point_labels=labels,
                    multimask_output=True,
                )
                
                # Send best mask
                best_idx = np.argmax(scores)
                mask = masks[best_idx]
                mask_uint8 = (mask * 255).astype(np.uint8)
                _, buffer = cv2.imencode('.png', mask_uint8)
                mask_base64 = base64.b64encode(buffer).decode('utf-8')
                
                await websocket.send_json({
                    "type": "mask_update",
                    "mask": mask_base64,
                    "score": float(scores[best_idx])
                })
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        if embedding_id and embedding_id in embeddings_cache:
            del embeddings_cache[embedding_id]

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# Step 10: Build Docker image
log_info "Step 10: Building Docker image..."
sudo docker build -t sam-server .

# Step 11: Run SAM server
log_info "Step 11: Starting SAM server..."
sudo docker run -d \
    --name sam-server \
    --gpus all \
    -p 8000:8000 \
    --restart unless-stopped \
    sam-server

# Step 12: Wait for server to start
log_info "Waiting for server to start..."
sleep 10

# Step 13: Test the server
log_info "Step 13: Testing SAM server..."
curl http://localhost:8000/health

# Step 14: Get VM IP
VM_IP=$(ip addr show | grep inet | grep -v '127.0.0.1' | grep -v inet6 | awk '{print $2}' | cut -d'/' -f1 | head -1)

# Step 15: Create systemd service for auto-start
sudo tee /etc/systemd/system/sam-server.service > /dev/null << EOF
[Unit]
Description=SAM GPU Server
After=docker.service
Requires=docker.service

[Service]
Type=simple
Restart=always
ExecStart=/usr/bin/docker start -a sam-server
ExecStop=/usr/bin/docker stop sam-server

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable sam-server.service

# Final message
echo ""
echo "=========================================="
log_info "SAM GPU Server Setup Complete!"
echo "=========================================="
echo ""
echo "Server is running at: http://${VM_IP}:8000"
echo "Health check: http://${VM_IP}:8000/health"
echo ""
echo "API Endpoints:"
echo "  - POST /embed           - Pre-compute image embedding"
echo "  - POST /segment/points  - Segment with point prompts"
echo "  - POST /segment/auto    - Automatic segmentation"
echo "  - WS   /ws              - WebSocket for real-time"
echo ""
echo "Docker commands:"
echo "  - View logs:    sudo docker logs sam-server"
echo "  - Restart:      sudo docker restart sam-server"
echo "  - Stop:         sudo docker stop sam-server"
echo ""
log_info "Setup completed at $(date)"