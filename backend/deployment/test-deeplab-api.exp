#!/usr/bin/expect -f

# Test DeepLab API endpoints on VM 200
# RTX 5080 GPU

set timeout 30
set vm_ip "*************"
set deeplab_port "8081"

puts "Testing DeepLab Server at http://$vm_ip:$deeplab_port"
puts "=========================================="

# Test health endpoint
puts "\n1. Testing /health endpoint..."
spawn curl -s http://$vm_ip:$deeplab_port/health

expect {
    "healthy" {
        puts "✓ Health check passed"
    }
    timeout {
        puts "✗ Health check failed - timeout"
        exit 1
    }
}
expect eof

# Test GPU info endpoint
puts "\n2. Testing /gpu-info endpoint..."
spawn curl -s http://$vm_ip:$deeplab_port/gpu-info

expect {
    "RTX 5080" {
        puts "✓ GPU detected: NVIDIA RTX 5080"
    }
    "gpu_available" {
        puts "✓ GPU endpoint responding"
    }
    timeout {
        puts "✗ GPU info failed - timeout"
    }
}
expect eof

# Test root endpoint
puts "\n3. Testing root endpoint..."
spawn curl -s http://$vm_ip:$deeplab_port/

expect {
    "DeepLab Segmentation Server" {
        puts "✓ Server info retrieved"
    }
    timeout {
        puts "✗ Root endpoint failed"
    }
}
expect eof

# Test segmentation with a sample image
puts "\n4. Testing segmentation endpoint..."
puts "Creating test image..."

# Create a simple test image using Python
spawn python3 -c "
from PIL import Image
import numpy as np

# Create a simple test image (aerial view simulation)
img = Image.new('RGB', (512, 512))
pixels = img.load()

# Add some patterns to simulate buildings and roads
for i in range(512):
    for j in range(512):
        # Simulate buildings (rectangles)
        if (i // 50) % 2 == 0 and (j // 50) % 2 == 0:
            pixels[i, j] = (150, 150, 150)  # Gray for buildings
        # Simulate vegetation
        elif (i + j) % 100 < 30:
            pixels[i, j] = (34, 139, 34)  # Green for vegetation
        # Simulate roads
        elif i % 100 < 10 or j % 100 < 10:
            pixels[i, j] = (64, 64, 64)  # Dark gray for roads
        else:
            pixels[i, j] = (101, 67, 33)  # Brown for bare soil

img.save('/tmp/test_satellite.jpg')
print('Test image created: /tmp/test_satellite.jpg')
"
expect eof

# Upload and test segmentation
puts "\nUploading image for segmentation..."
spawn curl -s -X POST \
    -F "file=@/tmp/test_satellite.jpg" \
    http://$vm_ip:$deeplab_port/segment \
    | python3 -c "import sys, json; data = json.load(sys.stdin); print(f'Masks: {len(data.get(\"masks\", []))}, Scores: {data.get(\"scores\", [])[:3]}')"

expect {
    "Masks:" {
        puts "✓ Segmentation completed successfully"
    }
    timeout {
        puts "✗ Segmentation failed - timeout"
    }
}
expect eof

# Performance test
puts "\n5. Performance test..."
puts "Running 3 sequential segmentation requests..."

set start_time [clock milliseconds]

for {set i 1} {$i <= 3} {incr i} {
    spawn curl -s -X POST \
        -F "file=@/tmp/test_satellite.jpg" \
        http://$vm_ip:$deeplab_port/segment \
        -o /dev/null -w "%{time_total}s\n"
    
    expect {
        -re "(\[0-9.\]+)s" {
            puts "  Request $i: $expect_out(1,string) seconds"
        }
        timeout {
            puts "  Request $i: timeout"
        }
    }
    expect eof
}

set end_time [clock milliseconds]
set total_time [expr {($end_time - $start_time) / 1000.0}]
puts "Total time for 3 requests: $total_time seconds"
puts "Average time per request: [expr {$total_time / 3.0}] seconds"

# Check Docker container status
puts "\n6. Checking Docker container status..."
spawn ssh ubuntu@$vm_ip "sudo docker ps | grep deeplab-server"

expect {
    "password:" {
        send "ubuntu\r"
        expect {
            "deeplab-server" {
                puts "✓ DeepLab container is running"
            }
            timeout {
                puts "✗ Container not found"
            }
        }
    }
    "deeplab-server" {
        puts "✓ DeepLab container is running"
    }
    timeout {
        puts "✗ Could not check container status"
    }
}
expect eof

# Memory usage check
puts "\n7. Checking GPU memory usage..."
spawn ssh ubuntu@$vm_ip "nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader"

expect {
    "password:" {
        send "ubuntu\r"
        expect {
            -re "(\[0-9\]+) MiB, (\[0-9\]+) MiB" {
                puts "GPU Memory: $expect_out(1,string) MB used / $expect_out(2,string) MB total"
            }
        }
    }
    -re "(\[0-9\]+) MiB, (\[0-9\]+) MiB" {
        puts "GPU Memory: $expect_out(1,string) MB used / $expect_out(2,string) MB total"
    }
    timeout {
        puts "Could not get GPU memory info"
    }
}
expect eof

puts "\n=========================================="
puts "DeepLab API Test Complete!"
puts "Server URL: http://$vm_ip:$deeplab_port"
puts "==========================================\n"