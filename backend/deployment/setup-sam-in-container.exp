#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Enter container
send "pct enter 201\r"
expect "# "

# Update system
send "apt-get update && apt-get upgrade -y\r"
expect "# "

# Install Docker
send "curl -fsSL https://get.docker.com | sh\r"
expect "# "

# Install NVIDIA driver and toolkit
send "apt-get install -y nvidia-driver-535 nvidia-container-toolkit\r"
expect {
    "# " { }
    timeout { send "\r"; exp_continue }
}

# Configure Docker for NVIDIA
send "nvidia-ctk runtime configure --runtime=docker\r"
expect "# "

send "systemctl restart docker\r"
expect "# "

# Test GPU in Docker
send "docker run --rm --gpus all nvidia/cuda:11.8.0-base-ubuntu22.04 nvidia-smi\r"
expect "# "

# Create SAM directory
send "mkdir -p /opt/sam-server\r"
expect "# "

send "cd /opt/sam-server\r"
expect "# "

# Create SAM Dockerfile
send "cat > Dockerfile << 'EOFDOCKER'
FROM nvidia/cuda:11.8.0-cudnn8-runtime-ubuntu22.04

RUN apt-get update && apt-get install -y python3.10 python3-pip git wget && rm -rf /var/lib/apt/lists/*
RUN pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cu118
RUN pip3 install segment-anything opencv-python-headless
RUN pip3 install fastapi uvicorn[standard] python-multipart aiofiles websockets pillow numpy

RUN mkdir -p /models && cd /models && \
    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

WORKDIR /app
COPY main.py /app/

EXPOSE 8000
CMD [\"uvicorn\", \"main:app\", \"--host\", \"0.0.0.0\", \"--port\", \"8000\"]
EOFDOCKER\r"
expect "# "

# Create FastAPI server
send "cat > main.py << 'EOFPY'
from fastapi import FastAPI, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
import torch
from segment_anything import sam_model_registry, SamPredictor
import numpy as np
from PIL import Image
import io
import base64

app = FastAPI(title=\"SAM GPU Server\")

app.add_middleware(
    CORSMiddleware,
    allow_origins=[\"*\"],
    allow_credentials=True,
    allow_methods=[\"*\"],
    allow_headers=[\"*\"],
)

device = \"cuda\" if torch.cuda.is_available() else \"cpu\"
print(f\"Using device: {device}\")

sam = sam_model_registry[\"vit_h\"](checkpoint=\"/models/sam_vit_h_4b8939.pth\")
sam.to(device=device)
predictor = SamPredictor(sam)

embeddings_cache = {}

@app.get(\"/\")
async def root():
    return {\"status\": \"SAM GPU Server Running\", \"device\": device}

@app.get(\"/health\")
async def health():
    return {
        \"status\": \"healthy\",
        \"gpu_available\": torch.cuda.is_available(),
        \"gpu_name\": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
        \"device\": device
    }

@app.post(\"/embed\")
async def create_embedding(file: UploadFile = File(...)):
    contents = await file.read()
    image = Image.open(io.BytesIO(contents))
    image_np = np.array(image.convert(\"RGB\"))
    
    embedding_id = f\"img_{int(time.time() * 1000)}\"
    predictor.set_image(image_np)
    
    embeddings_cache[embedding_id] = {
        \"features\": predictor.features,
        \"input_size\": predictor.input_size,
        \"original_size\": predictor.original_size
    }
    
    return {\"embedding_id\": embedding_id, \"image_size\": image_np.shape[:2]}

import time
EOFPY\r"
expect "# "

# Build Docker image
send "docker build -t sam-server .\r"
expect {
    "Successfully" { }
    timeout { send "\r"; exp_continue }
}

expect "# "

# Run SAM server
send "docker run -d --name sam-server --gpus all -p 8000:8000 --restart unless-stopped sam-server\r"
expect "# "

# Check if running
send "docker ps\r"
expect "# "

# Test API
send "curl http://localhost:8000/health\r"
expect "# "

# Get container IP again
send "ip addr show eth0 | grep inet | grep -v inet6 | awk '{print \$2}' | cut -d'/' -f1\r"
expect "# "

send "echo 'SAM Server is running on port 8000!'\r"
expect "# "

send "exit\r"
expect "# "

send "exit\r"
expect eof