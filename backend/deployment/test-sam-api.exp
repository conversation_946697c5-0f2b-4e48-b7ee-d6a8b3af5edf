#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== TESTING SAM API ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Check container status
send "docker ps | grep sam-server\r"
expect "$ "

# Check container logs
send "docker logs sam-server --tail 20\r"
expect "$ "

# Test health endpoint
send "curl -s http://localhost:8080/health | python3 -m json.tool\r"
expect "$ "

send "echo ''\r"
expect "$ "
send "echo '=== SAM API STATUS ==='\r"
expect "$ "
send "echo 'Health endpoint: http://*************:8080/health'\r"
expect "$ "
send "echo 'Segment endpoint: POST http://*************:8080/segment'\r"
expect "$ "
send "echo 'Portainer: https://*************:9443'\r"
expect "$ "
send "echo ''\r"
expect "$ "

send "exit\r"
expect eof