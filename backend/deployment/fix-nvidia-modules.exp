#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== FIXING NVIDIA MODULES ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Load NVIDIA modules manually
send "sudo modprobe nvidia\r"
expect "*?assword*" { send "$password\r" }
expect "$ "

send "sudo modprobe nvidia-uvm\r"
expect "$ "

# Update initramfs
send "sudo update-initramfs -u\r"
expect "$ "

# Reboot for clean start
send "echo 'Rebooting system for driver activation...'\r"
expect "$ "

send "sudo reboot\r"
expect eof