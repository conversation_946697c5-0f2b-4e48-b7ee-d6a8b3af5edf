#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Check if KVM is enabled
send "lsmod | grep kvm\r"
expect "# "

# Check CPU virtualization support
send "egrep -c '(vmx|svm)' /proc/cpuinfo\r"
expect "# "

# Try to load KVM modules
send "modprobe kvm\r"
expect "# "
send "modprobe kvm_intel\r"
expect "# "

# Check BIOS virtualization
send "dmesg | grep -i 'virtualization'\r"
expect "# "

# Try removing cpu requirement and start VM without KVM acceleration
send "qm set 200 --cpu kvm64\r"
expect "# "

# Try to start VM again
send "qm start 200\r"
expect "# "

# Check status
send "qm status 200\r"
expect "# "

send "exit\r"
expect eof