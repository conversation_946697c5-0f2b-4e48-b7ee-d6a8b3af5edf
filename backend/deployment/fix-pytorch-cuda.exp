#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== FIXING PYTORCH FOR RTX 5080 ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Upgrade PyTorch to latest version for RTX 5080 support
puts "Upgrading PyTorch to version 2.5+ with CUDA 12.4 support..."

# First, uninstall old PyTorch
send "sudo docker exec jupyter-ml pip3 uninstall -y torch torchvision torchaudio\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    timeout {
        puts "Uninstalling old PyTorch..."
        exp_continue
    }
    "$ " {}
}

# Install latest PyTorch with CUDA 12.4 support (supports RTX 5080)
puts "\nInstalling PyTorch 2.5 with CUDA 12.4..."
send "sudo docker exec jupyter-ml pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124\r"
expect {
    timeout {
        puts "Installing PyTorch, this may take a few minutes..."
        exp_continue
    }
    "$ " {}
}

# Test GPU again
puts "\nTesting GPU with new PyTorch..."
send "sudo docker exec jupyter-ml python3 -c \"import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0)}' if torch.cuda.is_available() else 'No GPU'); print(f'CUDA version: {torch.version.cuda}')\"\r"
expect "$ "

# Restart container to apply changes
puts "\nRestarting Jupyter container..."
send "sudo docker restart jupyter-ml\r"
expect "$ "

send "sleep 5\r"
expect "$ "

# Check container status
send "sudo docker ps | grep jupyter-ml\r"
expect "$ "

puts "\n=== PYTORCH FIXED ==="
puts "PyTorch updated with RTX 5080 support"
puts "Access Jupyter at: http://$host:8888"

send "exit\r"
expect eof