#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Check container status
send "pct list | grep 201\r"
expect "# "

# Check container IP
send "pct exec 201 -- ip addr show eth0 | grep inet | grep -v inet6\r"
expect "# "

# Check if Docker is installed
send "pct exec 201 -- which docker 2>/dev/null || echo 'Docker not installed yet'\r"
expect "# "

# Check available memory
send "pct exec 201 -- free -h\r"
expect "# "

send "exit\r"
expect eof