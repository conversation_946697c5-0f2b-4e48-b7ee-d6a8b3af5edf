#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== VERIFYING ISO AND BOOT CONFIGURATION ===\n"

# Check if ISO exists
send "ls -la /var/lib/vz/template/iso/ | grep ubuntu\r"
expect "# "

# Check VM configuration
send "qm config 200\r"
expect "# "

# Check if VM can see the ISO
send "qm monitor 200\r"
expect "(qemu)"
send "info block\r"
expect "(qemu)"
send "quit\r"
expect "# "

# Check UEFI/BIOS settings
send "qm config 200 | grep -E '(bios|efidisk|boot)'\r"
expect "# "

# Try alternative boot configuration
puts "\n=== TRYING ALTERNATIVE BOOT FIX ===\n"

send "qm stop 200\r"
expect "# "
sleep 2

# Ensure EFI disk is properly configured
send "qm set 200 -efidisk0 local-lvm:1,efitype=4m,pre-enrolled-keys=1\r"
expect "# "

# Set boot order with multiple options
send "qm set 200 -boot order=ide2;scsi0\r"
expect "# "

# Add CDROM as primary boot
send "qm set 200 -ide2 local:iso/ubuntu-24.04.1-live-server-amd64.iso,media=cdrom\r"
expect "# "

send "qm start 200\r"
expect "# "

puts "\n=== VERIFICATION COMPLETE ===\n"
send "echo 'If still in UEFI Shell:'\r"
expect "# "
send "echo '1. Type: exit'\r"
expect "# "
send "echo '2. Select Boot Manager'\r"
expect "# "
send "echo '3. Choose UEFI QEMU DVD-ROM or Ubuntu'\r"
expect "# "

send "exit\r"
expect eof