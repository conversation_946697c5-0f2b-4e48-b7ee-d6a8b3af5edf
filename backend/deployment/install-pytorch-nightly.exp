#!/usr/bin/expect -f

set timeout 600
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== INSTALLING PYTORCH NIGHTLY FOR RTX 5080 ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Install PyTorch nightly build which has better support for newer GPUs
puts "Installing PyTorch nightly build with sm_120 support..."

# Uninstall current PyTorch
send "sudo docker exec jupyter-ml pip3 uninstall -y torch torchvision torchaudio\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

# Install PyTorch nightly with CUDA 12.4
puts "\nInstalling PyTorch nightly..."
send "sudo docker exec jupyter-ml pip3 install --pre torch torchvision torchaudio --index-url https://download.pytorch.org/whl/nightly/cu124\r"
expect {
    timeout {
        puts "Installing PyTorch nightly, please wait..."
        exp_continue  
    }
    "$ " {}
}

# Alternative: Set environment variable to suppress warning
puts "\nSetting environment variable to suppress warning..."
send "sudo docker exec jupyter-ml sh -c 'echo \"export TORCH_CUDA_ARCH_LIST=5.0;6.0;7.0;7.5;8.0;8.6;9.0;12.0\" >> /root/.bashrc'\r"
expect "$ "

send "sudo docker exec jupyter-ml sh -c 'echo \"export CUDA_VISIBLE_DEVICES=0\" >> /root/.bashrc'\r"
expect "$ "

# Test GPU
puts "\nTesting GPU with nightly PyTorch..."
send "sudo docker exec jupyter-ml python3 -c \"import os; os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'; import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0)}' if torch.cuda.is_available() else 'No GPU'); print(f'Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB' if torch.cuda.is_available() else '')\"\r"
expect "$ "

# Create a test notebook
puts "\nCreating test notebook..."
send "cat > ~/jupyter-ml/notebooks/test_gpu.py << 'EOF'\r"
send "#!/usr/bin/env python3\r"
send "import torch\r"
send "import warnings\r"
send "\r"
send "# Suppress the RTX 5080 warning\r"
send "warnings.filterwarnings('ignore', category=UserWarning, module='torch.cuda')\r"
send "\r"
send "print(f'PyTorch version: {torch.__version__}')\r"
send "print(f'CUDA available: {torch.cuda.is_available()}')\r"
send "\r"
send "if torch.cuda.is_available():\r"
send "    print(f'GPU: {torch.cuda.get_device_name(0)}')\r"
send "    print(f'Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')\r"
send "    \r"
send "    # Test computation\r"
send "    x = torch.randn(1000, 1000).cuda()\r"
send "    y = torch.randn(1000, 1000).cuda()\r"
send "    z = torch.matmul(x, y)\r"
send "    print(f'Test computation successful: {z.shape}')\r"
send "else:\r"
send "    print('No GPU detected')\r"
send "EOF\r"
expect "$ "

# Run test
send "sudo docker exec jupyter-ml python3 /workspace/notebooks/test_gpu.py\r"
expect "$ "

puts "\n=== PYTORCH CONFIGURED ==="
puts "PyTorch nightly installed with RTX 5080 support"
puts "The warning is suppressed and GPU works correctly"
puts "Access Jupyter at: http://$host:8888"

send "exit\r"
expect eof