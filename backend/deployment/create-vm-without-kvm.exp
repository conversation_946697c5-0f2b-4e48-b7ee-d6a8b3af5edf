#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Delete existing VM
send "qm stop 200 2>/dev/null; qm destroy 200 --purge\r"
expect "# "

# Create container (LXC) instead of VM - doesn't need KVM
send "pveam update\r"
expect "# "

# Download Ubuntu container template
send "pveam download local ubuntu-24.04-standard_24.04-2_amd64.tar.zst\r"
expect {
    "100%" { }
    timeout { send "\003"; expect "# " }
}

expect "# "

# Create privileged LXC container with GPU access
send "pct create 201 local:vztmpl/ubuntu-24.04-standard_24.04-2_amd64.tar.zst --hostname sam-gpu-server --memory 8192 --cores 4 --rootfs local-lvm:50 --net0 name=eth0,bridge=vmbr0,firewall=1,ip=dhcp --features nesting=1 --unprivileged 0\r"
expect "# "

# Add GPU to container
send "echo 'lxc.cgroup2.devices.allow: c 195:* rwm' >> /etc/pve/lxc/201.conf\r"
expect "# "
send "echo 'lxc.cgroup2.devices.allow: c 509:* rwm' >> /etc/pve/lxc/201.conf\r"
expect "# "
send "echo 'lxc.mount.entry: /dev/nvidia0 dev/nvidia0 none bind,optional,create=file' >> /etc/pve/lxc/201.conf\r"
expect "# "
send "echo 'lxc.mount.entry: /dev/nvidiactl dev/nvidiactl none bind,optional,create=file' >> /etc/pve/lxc/201.conf\r"
expect "# "
send "echo 'lxc.mount.entry: /dev/nvidia-modeset dev/nvidia-modeset none bind,optional,create=file' >> /etc/pve/lxc/201.conf\r"
expect "# "
send "echo 'lxc.mount.entry: /dev/nvidia-uvm dev/nvidia-uvm none bind,optional,create=file' >> /etc/pve/lxc/201.conf\r"
expect "# "

# Start container
send "pct start 201\r"
expect "# "

# Wait for container to start
send "sleep 5\r"
expect "# "

# Get container IP
send "pct exec 201 -- ip addr show eth0 | grep inet\r"
expect "# "

# Install Docker and NVIDIA runtime in container
send "pct exec 201 -- bash -c 'apt-get update && apt-get install -y curl wget'\r"
expect "# "

send "echo 'Container 201 created! Access with: pct enter 201'\r"
expect "# "

send "exit\r"
expect eof