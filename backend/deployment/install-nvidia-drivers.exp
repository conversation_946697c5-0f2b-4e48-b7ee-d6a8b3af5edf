#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== INSTALLING NVIDIA DRIVERS FOR RTX 5080 ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Update system first
send "sudo apt update\r"
expect "*?assword*" { send "$password\r" }
expect "$ "

# Install required packages
send "sudo apt install -y build-essential dkms\r"
expect "$ "

# Add NVIDIA PPA for latest drivers
send "sudo add-apt-repository -y ppa:graphics-drivers/ppa\r"
expect "$ "

send "sudo apt update\r"
expect "$ "

# Install NVIDIA driver (550 or newer for RTX 5080)
send "sudo apt install -y nvidia-driver-550\r"
expect {
    "$ " { puts "\nDriver installation complete" }
    timeout { puts "\nInstallation taking time..."; exp_continue }
}

# Install CUDA toolkit
send "sudo apt install -y nvidia-cuda-toolkit\r"
expect "$ "

# Configure driver
send "sudo nvidia-xconfig --no-xinerama --no-logo\r"
expect "$ "

# Check installation
send "echo 'Installation complete. System needs reboot.'\r"
expect "$ "

send "echo 'After reboot, run: nvidia-smi to verify'\r"
expect "$ "

send "exit\r"
expect eof