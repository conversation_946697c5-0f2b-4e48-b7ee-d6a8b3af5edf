#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== Finding VM IP by MAC Address ==="

# Get correct MAC
send "qm config 202 | grep net0\r"
expect "# "

# Check ARP table for MAC starting with BC:24:11
send "ip neigh | grep -i 'BC:24:11'\r"
expect "# "

# Check if any of the found IPs match our MAC
puts "\nChecking SSH hosts for Ubuntu..."
send "for ip in ************* ************* *************; do echo -n \"Testing \$ip: \"; ssh -o ConnectTimeout=2 -o StrictHostKeyChecking=no ubuntu@\$ip 'echo OK' 2>/dev/null || echo 'Not Ubuntu VM'; done\r"
expect "# "

# Try to force ARP discovery
send "for ip in 192.168.0.{100..120}; do ping -c 1 -W 1 \$ip >/dev/null 2>&1; done\r"
expect "# "
send "sleep 2\r"
expect "# "

# Check ARP again
send "arp -an | grep -i 'BC:24:11' || ip neigh | grep -i 'BC:24:11'\r"
expect "# "

# Alternative: check bridge for MAC
send "brctl showmacs vmbr0 2>/dev/null | grep -i 'BC:24:11' || bridge fdb show | grep -i 'BC:24:11'\r"
expect "# "

puts "\n========================================="
puts "Note: The VM console is displaying on the physical GPU."
puts "The installation might be waiting for input on the GPU monitor."
puts ""
puts "To fix console access, we can:"
puts "1. Add a virtual display alongside GPU"
puts "2. Remove GPU temporarily for installation"
puts "3. Use serial console"
puts "========================================="

send "exit\r"
expect eof