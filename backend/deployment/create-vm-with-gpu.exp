#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Verify KVM is now working
send "dmesg | grep -i svm | tail -5\r"
expect "# "

send "modprobe kvm_amd\r"
expect "# "

send "lsmod | grep kvm\r"
expect "# "

# Delete old VM if exists
send "qm stop 202 2>/dev/null; qm destroy 202 --purge 2>/dev/null\r"
expect "# "

# Create new VM with GPU passthrough
send "qm create 202 --name sam-gpu-vm --memory 8192 --cores 4 --cpu host --ostype l26 --scsihw virtio-scsi-pci --scsi0 local-lvm:50 --ide2 local:iso/ubuntu-24.04.1-live-server-amd64.iso,media=cdrom --net0 virtio,bridge=vmbr0 --boot order=scsi0 --machine q35 --bios ovmf --efidisk0 local-lvm:1\r"
expect "# "

# Add GPU passthrough
send "qm set 202 -hostpci0 07:00,pcie=1,x-vga=1\r"
expect "# "

# Enable NUMA if needed
send "qm set 202 --numa 1\r"
expect "# "

# Start VM
send "qm start 202\r"
expect "# "

# Check status
send "sleep 3\r"
expect "# "

send "qm status 202\r"
expect "# "

# Get VNC port for access
send "qm config 202 | grep vnc\r"
expect "# "

send "echo 'VM 202 created and started successfully!'\r"
expect "# "
send "echo 'Access via Proxmox web interface: https://*************:8006'\r"
expect "# "
send "echo 'VM ID: 202 - Click Console to access Ubuntu installer'\r"
expect "# "

send "exit\r"
expect eof