# SAM GPU Server Deployment Guide

## Overview
Complete deployment of SAM (Segment Anything Model) on Proxmox VM with RTX 3070 GPU.

## Current Status
- ✅ VM 202 created with GPU passthrough (RTX 3070)
- ✅ Ubuntu 24.04 ISO attached
- ⏳ Ubuntu installation in progress
- ⏳ Waiting for SAM deployment

## VM Specifications
- **VM ID**: 202
- **Name**: sam-gpu-vm
- **RAM**: 8GB
- **CPU**: 4 cores
- **GPU**: NVIDIA RTX 3070 (PCI passthrough)
- **Storage**: 50GB
- **Network**: Bridge vmbr0 (DHCP)

## Installation Steps

### 1. Ubuntu Installation (Manual via Console)
Access Proxmox web interface:
```
https://*************:8006
Username: root
Password: Al0xan999
```

1. Click on VM 202 in left panel
2. Click "Console" button
3. Follow Ubuntu Server installer:
   - Language: English
   - Keyboard: English (US)
   - Network: DHCP (automatic)
   - Storage: Use entire disk
   - Profile:
     - Name: Ubuntu User
     - Server: sam-gpu
     - Username: ubuntu
     - Password: ubuntu
   - SSH: Install OpenSSH server
   - Skip additional snaps

Installation takes ~10-15 minutes.

### 2. Wait for VM Ready
```bash
# Run from backend directory
./deployment/wait-vm-ready.exp
```

### 3. Deploy SAM Automatically
```bash
# Run complete deployment
./deployment/deploy-sam-to-vm.sh
```

This will:
1. Wait for VM to get IP
2. Copy setup script to VM
3. Install NVIDIA drivers
4. Install CUDA toolkit
5. Install Docker + NVIDIA runtime
6. Deploy SAM with FastAPI
7. Start the service

### 4. Manual Deployment (Alternative)
If automatic deployment fails:

```bash
# Get VM IP
ssh root@*************
qm agent 202 network-get-interfaces | grep ip-address

# SSH to VM
ssh ubuntu@<VM_IP>
# Password: ubuntu

# Download and run setup script
wget https://raw.githubusercontent.com/your-repo/setup-sam-vm.sh
chmod +x setup-sam-vm.sh
sudo ./setup-sam-vm.sh
```

## Testing

### Check GPU
```bash
ssh ubuntu@<VM_IP>
nvidia-smi
```

### Check SAM Server
```bash
# Health check
curl http://<VM_IP>:8000/health

# Server info
curl http://<VM_IP>:8000/
```

### Test with Python
```python
import requests
import base64
from PIL import Image
import io

# Upload image
with open("test.jpg", "rb") as f:
    files = {"file": f}
    response = requests.post("http://<VM_IP>:8000/embed", files=files)
    embedding_id = response.json()["embedding_id"]

# Segment with points
data = {
    "embedding_id": embedding_id,
    "points": [[100, 100], [200, 200]],
    "labels": [1, 1]
}
response = requests.post("http://<VM_IP>:8000/segment/points", json=data)
masks = response.json()["masks"]
```

## Frontend Integration

Update frontend configuration:
```typescript
// File: frontend/src/services/samLocalService.ts
const SERVER_URL = 'http://<VM_IP>:8000';
```

## Troubleshooting

### VM not starting
```bash
# Check VM status
ssh root@*************
qm status 202

# Check logs
qm showcmd 202
journalctl -xe | grep 202
```

### GPU not detected
```bash
# In VM
lspci | grep NVIDIA
dmesg | grep nvidia

# Reinstall drivers
sudo apt-get remove --purge nvidia-*
sudo ubuntu-drivers autoinstall
sudo reboot
```

### Docker issues
```bash
# Restart Docker
sudo systemctl restart docker

# Test GPU in Docker
sudo docker run --rm --gpus all nvidia/cuda:11.8.0-base-ubuntu22.04 nvidia-smi
```

### SAM server not starting
```bash
# Check logs
sudo docker logs sam-server

# Restart container
sudo docker restart sam-server

# Rebuild if needed
cd /opt/sam-server
sudo docker build -t sam-server .
sudo docker run -d --name sam-server --gpus all -p 8000:8000 sam-server
```

## Network Performance
The Proxmox server network has been optimized for 10Gbps:
- TCP buffers: 128MB
- Congestion control: BBR
- Large file transfers should now be much faster

## Files Created
- `setup-sam-vm.sh` - Main setup script for VM
- `ubuntu-autoinstall.yaml` - Ubuntu auto-installation config
- `wait-vm-ready.exp` - Wait for VM to be ready
- `deploy-sam-to-vm.sh` - Master deployment script
- `start-ubuntu-install.exp` - Start Ubuntu installation
- `connect-vm-console.exp` - Get console access info

## Next Steps
1. ✅ Complete Ubuntu installation
2. ⏳ Run deployment script
3. ⏳ Test GPU functionality
4. ⏳ Test SAM API endpoints
5. ⏳ Update frontend configuration
6. ⏳ Test real-time segmentation