#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Check BIOS virtualization settings
send "dmesg | grep -E 'VT-x|VMX|SVM'\r"
expect "# "

# Check if we can enable KVM without reboot
send "modprobe kvm_intel nested=1\r"
expect "# "

# Check if KVM is now loaded
send "lsmod | grep kvm\r"
expect "# "

# If KVM is loaded, create VM with GPU passthrough
send "if lsmod | grep -q kvm_intel; then echo 'KVM OK'; else echo 'KVM FAILED'; fi\r"
expect {
    "KVM OK" {
        send "echo 'Creating VM with GPU passthrough...'\r"
        expect "# "
        
        # Create VM with GPU
        send "qm create 202 --name sam-gpu-vm --memory 8192 --cores 4 --cpu host --ostype l26 --scsihw virtio-scsi-pci --scsi0 local-lvm:50 --ide2 local:iso/ubuntu-24.04.1-live-server-amd64.iso,media=cdrom --net0 virtio,bridge=vmbr0 --boot order=scsi0 --machine q35 --bios ovmf --efidisk0 local-lvm:1\r"
        expect "# "
        
        # Add GPU passthrough
        send "qm set 202 -hostpci0 07:00,pcie=1,x-vga=1\r"
        expect "# "
        
        # Start VM
        send "qm start 202\r"
        expect "# "
        
        send "qm status 202\r"
        expect "# "
    }
    "KVM FAILED" {
        send "echo 'KVM not available. Trying alternative...'\r"
        expect "# "
        
        # Try with emulated CPU instead
        send "qm create 202 --name sam-gpu-vm --memory 8192 --cores 4 --cpu qemu64 --ostype l26 --scsihw virtio-scsi-pci --scsi0 local-lvm:50 --ide2 local:iso/ubuntu-24.04.1-live-server-amd64.iso,media=cdrom --net0 virtio,bridge=vmbr0 --boot order=scsi0 --machine q35 --bios ovmf --efidisk0 local-lvm:1 --kvm 0\r"
        expect "# "
        
        send "qm set 202 -hostpci0 07:00,pcie=1,x-vga=1\r"
        expect "# "
        
        send "qm start 202\r"
        expect "# "
    }
}

expect "# "

send "exit\r"
expect eof