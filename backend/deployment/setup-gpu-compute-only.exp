#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== CONFIGURING GPU FOR COMPUTE ONLY (WITH VIRTUAL DISPLAY) ===\n"

# Stop VM first
send "qm stop 200\r"
expect "# "
sleep 2

# Remove old GPU config
send "qm set 200 -delete hostpci0\r"
expect "# "

# Add GPU WITHOUT x-vga (keeps virtual display active)
send "qm set 200 -hostpci0 07:00,pcie=1\r"
expect "# "

# Ensure virtual display is enabled
send "qm set 200 -vga std\r"
expect "# "

# Optional: Add more video memory for virtual display
send "qm set 200 -vga std,memory=32\r"
expect "# "

# Start VM
send "qm start 200\r"
expect "# "
sleep 3

send "qm status 200\r"
expect "# "

puts "\n=== CONFIGURATION COMPLETE ===\n"
send "echo 'GPU configured for compute (CUDA/ML) with virtual display'\r"
expect "# "
send "echo 'Benefits:'\r"
expect "# "
send "echo '- Access VM via Proxmox NoVNC console'\r"
expect "# "
send "echo '- GPU available for CUDA/SAM computations'\r"
expect "# "
send "echo '- No physical monitor needed'\r"
expect "# "
send "echo '- Desktop GUI works through virtual display'\r"
expect "# "

send "exit\r"
expect eof