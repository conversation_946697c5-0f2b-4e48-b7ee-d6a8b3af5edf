#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Verifica IOMMU
send "grep -q 'intel_iommu=on' /proc/cmdline && echo 'IOMMU OK' || echo 'IOMMU NOT ENABLED'\r"
expect "# "

# Crea VM con GPU passthrough
send "cat > /tmp/create-sam-vm.sh << 'EOF'
#!/bin/bash

VM_ID=200
VM_NAME=sam-gpu-server
VM_CORES=4
VM_RAM=8192
VM_DISK=50
STORAGE=local-lvm
ISO_STORAGE=local

# Download Ubuntu ISO if not exists
if \[ ! -f /var/lib/vz/template/iso/ubuntu-22.04.3-live-server-amd64.iso \]; then
    echo 'Downloading Ubuntu 22.04 ISO...'
    wget -P /var/lib/vz/template/iso/ https://releases.ubuntu.com/22.04.3/ubuntu-22.04.3-live-server-amd64.iso
fi

# Create VM
echo 'Creating VM...'
qm create \$VM_ID \\
    --name \$VM_NAME \\
    --memory \$VM_RAM \\
    --cores \$VM_CORES \\
    --cpu host \\
    --ostype l26 \\
    --scsihw virtio-scsi-pci \\
    --scsi0 \$STORAGE:\$VM_DISK \\
    --ide2 \$ISO_STORAGE:iso/ubuntu-22.04.3-live-server-amd64.iso,media=cdrom \\
    --net0 virtio,bridge=vmbr0 \\
    --boot order=scsi0 \\
    --machine q35 \\
    --bios ovmf \\
    --efidisk0 \$STORAGE:1

# Add GPU
GPU_ID='07:00'
echo \"Adding GPU \$GPU_ID to VM...\"
qm set \$VM_ID -hostpci0 \$GPU_ID,pcie=1,x-vga=1

# Enable start at boot
qm set \$VM_ID --onboot 1

echo 'VM created successfully!'
qm config \$VM_ID
EOF\r"

expect "# "
send "chmod +x /tmp/create-sam-vm.sh\r"
expect "# "
send "/tmp/create-sam-vm.sh\r"
expect "# "

# Start VM
send "qm start 200\r"
expect "# "

# Show VM status
send "qm status 200\r"
expect "# "

send "exit\r"
expect eof