#!/usr/bin/expect -f

set timeout 120
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== OPTIMIZING GPU PERFORMANCE ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Set GPU to maximum performance mode
puts "Setting GPU to max performance..."
send "sudo nvidia-smi -pm 1\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

# Check current GPU settings
send "nvidia-smi\r"
expect "$ "

# Optimize Docker for GPU
puts "\nOptimizing Docker GPU settings..."
send "sudo docker exec jupyter-ml nvidia-smi\r"
expect "$ "

# Set PyTorch optimizations
puts "\nConfiguring PyTorch optimizations..."
send {sudo docker exec jupyter-ml python3 -c "import torch; torch.backends.cudnn.benchmark = True; torch.backends.cuda.matmul.allow_tf32 = True; torch.backends.cudnn.allow_tf32 = True; print('PyTorch optimizations set'); print('CUDNN Benchmark:', torch.backends.cudnn.benchmark); print('TF32 enabled:', torch.backends.cuda.matmul.allow_tf32)"}
send "\r"
expect "$ "

# Test optimized performance
puts "\nTesting optimized performance..."
send {sudo docker exec jupyter-ml python3 -c "import torch; import time; x = torch.randn(8192, 8192, device='cuda'); torch.cuda.synchronize(); start = time.time(); y = torch.matmul(x, x); torch.cuda.synchronize(); print(f'8192x8192 matmul: {time.time()-start:.3f}s')"}
send "\r"
expect "$ "

# Create optimization script
puts "\nCreating permanent optimization script..."
send "cat > ~/jupyter-ml/notebooks/optimize_gpu.py << 'ENDFILE'\r"
send "import torch\r"
send "import os\r"
send "\r"
send "# Set environment variables for max performance\r"
send "os.environ['CUDA_LAUNCH_BLOCKING'] = '0'\r"
send "os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'\r"
send "\r"
send "# Enable CUDNN optimizations\r"
send "torch.backends.cudnn.benchmark = True\r"
send "torch.backends.cuda.matmul.allow_tf32 = True\r"
send "torch.backends.cudnn.allow_tf32 = True\r"
send "\r"
send "# Set to use all available GPU memory\r"
send "torch.cuda.set_per_process_memory_fraction(0.95)\r"
send "\r"
send "print('GPU Optimizations Applied:')\r"
send "print(f'- CUDNN Benchmark: {torch.backends.cudnn.benchmark}')\r"
send "print(f'- TF32 Enabled: {torch.backends.cuda.matmul.allow_tf32}')\r"
send "print(f'- GPU Memory Fraction: 95%')\r"
send "print(f'- Device: {torch.cuda.get_device_name(0)}')\r"
send "print(f'- Available Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')\r"
send "ENDFILE\r"
expect "$ "

# Run optimization script
send "sudo docker exec jupyter-ml python3 /workspace/notebooks/optimize_gpu.py\r"
expect "$ "

puts "\n=== GPU OPTIMIZATION COMPLETE ==="
puts "✅ GPU set to maximum performance mode"
puts "✅ CUDNN benchmarking enabled"
puts "✅ TF32 operations enabled for RTX 5080"
puts "✅ Memory allocation optimized"

send "exit\r"
expect eof