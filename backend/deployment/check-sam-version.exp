#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "alin"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

send "docker exec sam-server python3 -c \"import segment_anything; print('SAM Version: 1.0 (Original Facebook Research)'); from segment_anything import sam_model_registry; print(f'Available models: {list(sam_model_registry.keys())}');\"\r"
expect "$ "

send "docker exec sam-server ls -la /models/\r"
expect "$ "

send "exit\r"
expect eof