#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== CHECKING CONTAINERS IN PORTAINER ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# List all running containers
puts "Running containers:"
send "sudo docker ps --format \"table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}\"\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

puts "\n"
puts "=== CONTAINERS STATUS ==="
puts "Jupyter Lab: http://$host:8888"
puts "Portainer: https://$host:9443"
puts "SAM Server: http://$host:8080"
puts ""
puts "All containers should be visible in Portainer at https://$host:9443"

send "exit\r"
expect eof