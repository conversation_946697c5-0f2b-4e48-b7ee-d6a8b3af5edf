#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== VM Console & ISO Check ==="

# Check which ISO is mounted
send "qm config 202 | grep ide2\r"
expect "# "

# Check if it's server or desktop version
send "ls -lh /var/lib/vz/template/iso/ | grep ubuntu\r"
expect "# "

# Check VM display settings
send "qm config 202 | grep -E 'vga|hostpci'\r"
expect "# "

puts "\n=== Console Access Info ==="
puts "Ubuntu Server (CLI only) is installed."
puts "The console might be showing on the GPU output (physical monitor)."
puts ""
puts "To access:"
puts "1. NoVNC Console (might be black if output is on GPU):"
puts "   - Open: https://*************:8006"
puts "   - VM 202 -> Console"
puts ""
puts "2. Check physical monitor connected to RTX 3070"
puts ""
puts "3. Once installed, SSH will be available:"
puts "   ssh ubuntu@<VM_IP>"
puts ""

# Try to send Enter key to VM console to wake it up
send "qm sendkey 202 ret\r"
expect "# "

# Check if we can access terminal directly
send "qm terminal 202\r"
expect {
    "starting" {
        puts "Terminal starting - press Enter in console"
        send "\r"
        sleep 2
        send "\003"  # Ctrl+C to exit
    }
    timeout {
        puts "Terminal timeout"
    }
}

expect "# "

send "exit\r"
expect eof