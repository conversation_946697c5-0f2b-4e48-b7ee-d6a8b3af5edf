#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== TESTING SAM SERVER ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Quick status check
send "echo '=== SAM Server Status ==='\r"
expect "$ "

send "docker ps | grep sam-server\r"
expect "$ "

# PyTorch version
send "echo ''\r"
expect "$ "
send "echo '=== PyTorch Version ==='\r"
expect "$ "
send "docker exec sam-server python3 -c \"import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'RTX 5080: {torch.cuda.get_device_name(0)}'); print(f'Compute capability: {torch.cuda.get_device_capability(0)}')\"\r"
expect "$ "

# Health check
send "echo ''\r"
expect "$ "
send "echo '=== Health Check ==='\r"
expect "$ "
send "curl -s http://localhost:8080/health\r"
expect "$ "

# GPU status
send "echo ''\r"
expect "$ "
send "echo '=== GPU Status ==='\r"
expect "$ "
send "nvidia-smi --query-gpu=name,memory.used,memory.total,utilization.gpu --format=csv,noheader\r"
expect "$ "

# Container resource usage
send "echo ''\r"
expect "$ "
send "echo '=== Container Resources ==='\r"
expect "$ "
send "docker stats sam-server --no-stream --format \"table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\"\r"
expect "$ "

send "echo ''\r"
expect "$ "
send "echo '✓ SAM server is running with PyTorch 2.8 and RTX 5080 support'\r"
expect "$ "
send "echo '✓ Ready for segmentation tasks'\r"
expect "$ "

send "exit\r"
expect eof