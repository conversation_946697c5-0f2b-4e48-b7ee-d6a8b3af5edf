# AI Segmentation Server Deployment Guide

## Overview
Deployment di SAM e DeepLab su Proxmox VM con NVIDIA RTX 5080 usando Docker e Portainer.

## Infrastruttura Attuale

### VM Specifications
- **VM ID**: 200
- **Host**: Proxmox (*************:8006)
- **IP VM**: *************
- **RAM**: 16GB
- **CPU**: 8 cores
- **GPU**: NVIDIA RTX 5080 (PCI passthrough)
- **Storage**: 100GB
- **OS**: Ubuntu 24.04 LTS
- **Container Manager**: Docker + <PERSON>ainer

### Servizi Attivi
- **Portainer**: http://*************:9000
- **SAM Server**: http://*************:8080
- **DeepLab Server**: http://*************:8081 (da configurare)

## Accesso alla VM

### SSH
```bash
ssh ubuntu@*************
# Password: ubuntu (o quella configurata)
```

### Portainer Web UI
```
URL: http://*************:9000
Username: admin
Password: [configurata durante setup]
```

## Deployment DeepLab

### 1. Creazione Container DeepLab

Creare il file `deeplab-server/Dockerfile`:

```dockerfile
FROM nvidia/cuda:12.1.0-runtime-ubuntu22.04

# Install Python and dependencies
RUN apt-get update && apt-get install -y \
    python3-pip \
    python3-dev \
    git \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install PyTorch and TorchVision
RUN pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cu121

# Install DeepLab dependencies
RUN pip3 install \
    opencv-python-headless \
    pillow \
    numpy \
    fastapi \
    uvicorn \
    python-multipart \
    tensorflow \
    segmentation-models-pytorch

WORKDIR /app

# Copy server code
COPY server.py .

EXPOSE 8081

CMD ["uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8081"]
```

### 2. Deploy con Docker Compose

Creare `docker-compose.yml`:

```yaml
version: '3.8'

services:
  sam-server:
    image: sam-server:latest
    container_name: sam-server
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./models:/models
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    environment:
      - CUDA_VISIBLE_DEVICES=0

  deeplab-server:
    image: deeplab-server:latest
    container_name: deeplab-server
    restart: unless-stopped
    ports:
      - "8081:8081"
    volumes:
      - ./models:/models
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - MODEL_TYPE=deeplabv3plus
      - BACKBONE=resnet101

networks:
  default:
    name: ai-segmentation
    driver: bridge
```

### 3. Script di Deployment

Eseguire sulla VM:

```bash
cd /opt
sudo mkdir -p deeplab-server
cd deeplab-server

# Creare server.py (vedi sezione sotto)
sudo nano server.py

# Build e deploy
sudo docker-compose up -d
```

## DeepLab Server Implementation

File `server.py`:

```python
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
import torch
import torchvision.transforms as T
from PIL import Image
import io
import base64
import numpy as np
import segmentation_models_pytorch as smp
import logging

app = FastAPI(title="DeepLab Segmentation Server")

# Model configuration
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = None

@app.on_event("startup")
async def load_model():
    global model
    try:
        # Load DeepLabV3+ with ResNet101 backbone
        model = smp.DeepLabV3Plus(
            encoder_name="resnet101",
            encoder_weights="imagenet",
            in_channels=3,
            classes=21,  # PASCAL VOC classes
        )
        model.to(device)
        model.eval()
        logging.info(f"Model loaded on {device}")
    except Exception as e:
        logging.error(f"Failed to load model: {e}")

@app.get("/")
async def root():
    return {
        "service": "DeepLab Segmentation Server",
        "version": "1.0.0",
        "model": "DeepLabV3+",
        "device": str(device)
    }

@app.get("/health")
async def health():
    return {"status": "healthy", "gpu_available": torch.cuda.is_available()}

@app.post("/segment")
async def segment(file: UploadFile = File(...)):
    try:
        # Read image
        contents = await file.read()
        image = Image.open(io.BytesIO(contents)).convert("RGB")
        
        # Preprocess
        transform = T.Compose([
            T.Resize((512, 512)),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        input_tensor = transform(image).unsqueeze(0).to(device)
        
        # Inference
        with torch.no_grad():
            output = model(input_tensor)
        
        # Post-process
        pred = torch.argmax(output, dim=1).cpu().numpy()[0]
        
        # Convert to masks for each class
        masks = []
        for class_id in range(1, 21):  # Skip background
            mask = (pred == class_id).astype(np.uint8) * 255
            mask_pil = Image.fromarray(mask)
            
            # Convert to base64
            buffer = io.BytesIO()
            mask_pil.save(buffer, format="PNG")
            mask_b64 = base64.b64encode(buffer.getvalue()).decode()
            
            if mask.any():  # Only add non-empty masks
                masks.append({
                    "class_id": class_id,
                    "mask": mask_b64
                })
        
        return JSONResponse({
            "masks": masks,
            "shape": list(pred.shape)
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## Testing

### Test SAM Server
```bash
curl http://*************:8080/health
```

### Test DeepLab Server
```bash
curl http://*************:8081/health
```

### Test con Python
```python
import requests

# Test DeepLab
with open("test_image.jpg", "rb") as f:
    response = requests.post(
        "http://*************:8081/segment",
        files={"file": f}
    )
    print(response.json())
```

## Monitoring con Portainer

1. Accedi a Portainer: http://*************:9000
2. Vai su "Containers"
3. Verifica stato di:
   - sam-server
   - deeplab-server
4. Controlla logs per eventuali errori

## GPU Monitoring

```bash
# Sulla VM
nvidia-smi

# Monitor continuo
watch -n 1 nvidia-smi

# Docker containers GPU usage
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
```

## Troubleshooting

### Container non parte
```bash
# Check logs
docker logs sam-server
docker logs deeplab-server

# Restart
docker restart sam-server
docker restart deeplab-server
```

### GPU non disponibile
```bash
# Verifica driver
nvidia-smi

# Test CUDA in Docker
docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi
```

### Memory issues
```bash
# Libera memoria GPU
sudo fuser -v /dev/nvidia*
sudo kill -9 <PID>

# Restart Docker
sudo systemctl restart docker
```

## Frontend Configuration

Aggiornare i servizi nel frontend per supportare entrambi i modelli:

```typescript
// services/segmentationConfig.ts
export const SEGMENTATION_SERVERS = {
  SAM: 'http://*************:8080',
  DEEPLAB: 'http://*************:8081'
};
```

## Performance Optimization

### GPU Memory Management
- SAM usa ~4GB VRAM
- DeepLab usa ~2GB VRAM  
- RTX 5080 ha 16GB VRAM (sufficiente per entrambi)

### Network Optimization
- Usare compression per immagini grandi
- Implementare caching per embeddings frequenti
- Batch processing per multiple immagini

## Prossimi Steps

1. ✅ VM 200 configurata con Docker e Portainer
2. ✅ SAM Server operativo
3. ⏳ Deploy DeepLab Server
4. ⏳ Integrazione frontend con selettore modello
5. ⏳ Testing comparativo SAM vs DeepLab
6. ⏳ Ottimizzazione performance