#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== CHECKING SAM SERVER ERROR ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Check container logs for errors
send "docker logs sam-server --tail 50\r"
expect "$ "

# Check if container is still running
send "docker ps | grep sam-server\r"
expect "$ "

# Try to restart the container
send "docker restart sam-server\r"
expect "$ "

send "sleep 5\r"
expect "$ "

# Check logs again after restart
send "docker logs sam-server --tail 20\r"
expect "$ "

send "exit\r"
expect eof