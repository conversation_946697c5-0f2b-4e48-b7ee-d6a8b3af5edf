#!/usr/bin/expect -f

# Script expect per connettersi a Proxmox
set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

# Connessione SSH
spawn ssh $user@$host

expect {
    "yes/no" {
        send "yes\r"
        expect "*?assword:" { send "$password\r" }
    }
    "*?assword:" { send "$password\r" }
}

# Comandi da eseguire
expect "# " { send "lspci | grep -i nvidia\r" }
expect "# " { send "qm list\r" }
expect "# " { send "exit\r" }

expect eof