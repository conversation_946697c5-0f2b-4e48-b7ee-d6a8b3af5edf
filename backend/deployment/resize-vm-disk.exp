#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== RESIZING VM DISK TO 250GB ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Check current disk usage
puts "\n--- Current Disk Usage ---"
send "df -h /\r"
expect "$ "

# Check current disk size
send "lsblk\r"
expect "$ "

# Extend the logical volume to 250GB
puts "\n--- Extending disk to 250GB ---"
send "sudo lvextend -L 250G /dev/mapper/ubuntu--vg-ubuntu--lv\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "successfully resized" {
        puts "\n✓ Logical volume extended successfully"
    }
    "Insufficient free space" {
        puts "\n⚠ Need to extend physical volume first"
        
        # Check physical volume
        send "sudo pvdisplay\r"
        expect "$ "
        
        # Resize physical volume
        send "sudo pvresize /dev/sda3\r"
        expect "$ "
        
        # Try extending logical volume again
        send "sudo lvextend -L 250G /dev/mapper/ubuntu--vg-ubuntu--lv\r"
        expect "$ "
    }
    "$ " {
        puts "\nCommand completed"
    }
}

expect "$ "

# Resize the filesystem
puts "\n--- Resizing filesystem ---"
send "sudo resize2fs /dev/mapper/ubuntu--vg-ubuntu--lv\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "The filesystem" {
        puts "\n✓ Filesystem resized successfully"
    }
    "$ " {}
}

expect "$ "

# Clean up Docker space
puts "\n--- Cleaning Docker space ---"

# Remove unused Docker objects
send "sudo docker system prune -a --volumes -f\r"
expect "$ "

# Check Docker space usage
send "sudo docker system df\r"
expect "$ "

# Verify new disk usage
puts "\n--- New Disk Usage ---"
send "df -h /\r"
expect "$ "

# Check available space for Docker
send "sudo du -sh /var/lib/docker/ 2>/dev/null || echo 'Docker directory check'\r"
expect "$ "

puts "\n=== DISK RESIZE COMPLETE ==="
puts "Disk has been extended to 250GB"
puts "Docker and Portainer now have more space available"

send "exit\r"
expect eof