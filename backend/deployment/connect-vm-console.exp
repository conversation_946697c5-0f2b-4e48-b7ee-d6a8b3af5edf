#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Get VNC info
send "qm vnc 202\r"
expect "# "

# Alternative: use qm terminal for direct console access
send "echo '==========================================='\r"
expect "# "
send "echo 'To access VM console:'\r"
expect "# "
send "echo '1. Via Web: https://*************:8006'\r"
expect "# "
send "echo '2. Click on VM 202 -> Console'\r"
expect "# "
send "echo '3. Or use: qm terminal 202'\r"
expect "# "
send "echo '==========================================='\r"
expect "# "

# Try to get VM IP if guest agent is installed
send "qm agent 202 network-get-interfaces 2>/dev/null | grep '\"ip-address\"' | head -1 | cut -d'\"' -f4 || echo 'Guest agent not ready yet'\r"
expect "# "

send "exit\r"
expect eof