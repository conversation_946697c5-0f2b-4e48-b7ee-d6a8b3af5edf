#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== ADDING GPU INFO ENDPOINT TO SAM SERVER ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

send "cd ~/sam-server\r"
expect "$ "

# Update sam_server.py with GPU info endpoint
send "cat > sam_server_updated.py << 'EOF'\r"
send "#!/usr/bin/env python3\r"
send "import torch\r"
send "from segment_anything import sam_model_registry, SamPredictor, SamAutomaticMaskGenerator\r"
send "from flask import Flask, request, jsonify\r"
send "from flask_cors import CORS\r"
send "import base64\r"
send "import numpy as np\r"
send "from PIL import Image\r"
send "import io\r"
send "import traceback\r"
send "import sys\r"
send "\r"
send "app = Flask(__name__)\r"
send "CORS(app, origins='*', methods=['GET', 'POST', 'OPTIONS'])\r"
send "\r"
send "# Initialize variables\r"
send "predictor = None\r"
send "mask_generator = None\r"
send "device = None\r"
send "\r"
send "def init_model():\r"
send "    global predictor, mask_generator, device\r"
send "    try:\r"
send "        print(\"Initializing SAM model...\", flush=True)\r"
send "        sam_checkpoint = \"/models/sam_vit_h_4b8939.pth\"\r"
send "        model_type = \"vit_h\"\r"
send "        device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\r"
send "        print(f\"Using device: {device}\", flush=True)\r"
send "        \r"
send "        if device == \"cuda\":\r"
send "            print(f\"GPU: {torch.cuda.get_device_name(0)}\", flush=True)\r"
send "            print(f\"PyTorch: {torch.__version__}\", flush=True)\r"
send "        \r"
send "        sam = sam_model_registry[model_type](checkpoint=sam_checkpoint)\r"
send "        sam.to(device=device)\r"
send "        \r"
send "        predictor = SamPredictor(sam)\r"
send "        mask_generator = SamAutomaticMaskGenerator(sam)\r"
send "        print(\"Model loaded successfully!\", flush=True)\r"
send "        return True\r"
send "    except Exception as e:\r"
send "        print(f\"Failed to load model: {str(e)}\", flush=True)\r"
send "        traceback.print_exc()\r"
send "        return False\r"
send "\r"
send "@app.route('/health', methods=['GET'])\r"
send "def health():\r"
send "    return jsonify({\"status\": \"healthy\", \"device\": device if device else \"not initialized\"})\r"
send "\r"
send "@app.route('/gpu-info', methods=['GET'])\r"
send "def gpu_info():\r"
send "    if torch.cuda.is_available():\r"
send "        return jsonify({\r"
send "            \"available\": True,\r"
send "            \"name\": torch.cuda.get_device_name(0),\r"
send "            \"memory_allocated\": torch.cuda.memory_allocated(0),\r"
send "            \"memory_reserved\": torch.cuda.memory_reserved(0),\r"
send "            \"memory_total\": torch.cuda.get_device_properties(0).total_memory,\r"
send "            \"pytorch_version\": torch.__version__,\r"
send "            \"cuda_version\": torch.version.cuda,\r"
send "            \"compute_capability\": list(torch.cuda.get_device_capability(0))\r"
send "        })\r"
send "    else:\r"
send "        return jsonify({\"available\": False, \"device\": \"CPU\"})\r"
send "\r"
send "@app.route('/segment', methods=['POST', 'OPTIONS'])\r"
send "def segment():\r"
send "    if request.method == 'OPTIONS':\r"
send "        return '', 200\r"
send "    \r"
send "    try:\r"
send "        if not predictor:\r"
send "            return jsonify({\"error\": \"Model not initialized\"}), 500\r"
send "        \r"
send "        data = request.json\r"
send "        if not data or 'image' not in data:\r"
send "            return jsonify({\"error\": \"No image provided\"}), 400\r"
send "        \r"
send "        print(f\"Processing segmentation request...\", flush=True)\r"
send "        \r"
send "        # Decode image\r"
send "        image_b64 = data['image']\r"
send "        image_data = base64.b64decode(image_b64)\r"
send "        image = Image.open(io.BytesIO(image_data)).convert('RGB')\r"
send "        image_np = np.array(image)\r"
send "        \r"
send "        print(f\"Image shape: {image_np.shape}\", flush=True)\r"
send "        \r"
send "        # Set image for predictor\r"
send "        predictor.set_image(image_np)\r"
send "        \r"
send "        # Get points if provided\r"
send "        points = data.get('points', [])\r"
send "        boxes = data.get('boxes', [])\r"
send "        \r"
send "        if points and len(points) > 0:\r"
send "            print(f\"Using {len(points)} points for segmentation\", flush=True)\r"
send "            input_points = np.array([[p['x'], p['y']] for p in points])\r"
send "            input_labels = np.array([p.get('label', 1) for p in points])\r"
send "            \r"
send "            # Add box support\r"
send "            input_box = None\r"
send "            if boxes and len(boxes) > 0:\r"
send "                box = boxes[0]\r"
send "                input_box = np.array([box['x1'], box['y1'], box['x2'], box['y2']])\r"
send "            \r"
send "            masks, scores, logits = predictor.predict(\r"
send "                point_coords=input_points if len(input_points) > 0 else None,\r"
send "                point_labels=input_labels if len(input_labels) > 0 else None,\r"
send "                box=input_box,\r"
send "                multimask_output=True,\r"
send "            )\r"
send "        else:\r"
send "            print(\"Generating automatic masks\", flush=True)\r"
send "            # Use center point as default\r"
send "            h, w = image_np.shape[:2]\r"
send "            input_points = np.array([[w//2, h//2]])\r"
send "            input_labels = np.array([1])\r"
send "            \r"
send "            masks, scores, logits = predictor.predict(\r"
send "                point_coords=input_points,\r"
send "                point_labels=input_labels,\r"
send "                multimask_output=True,\r"
send "            )\r"
send "        \r"
send "        print(f\"Generated {len(masks)} masks\", flush=True)\r"
send "        \r"
send "        # Convert masks to base64\r"
send "        masks_b64 = []\r"
send "        for i, mask in enumerate(masks):\r"
send "            mask_img = Image.fromarray((mask * 255).astype(np.uint8))\r"
send "            buffer = io.BytesIO()\r"
send "            mask_img.save(buffer, format='PNG')\r"
send "            mask_b64 = base64.b64encode(buffer.getvalue()).decode()\r"
send "            masks_b64.append(mask_b64)\r"
send "        \r"
send "        return jsonify({\r"
send "            \"masks\": masks_b64,\r"
send "            \"scores\": scores.tolist()\r"
send "        })\r"
send "        \r"
send "    except Exception as e:\r"
send "        error_msg = f\"Segmentation error: {str(e)}\"\r"
send "        print(error_msg, flush=True)\r"
send "        traceback.print_exc()\r"
send "        return jsonify({\"error\": error_msg}), 500\r"
send "\r"
send "@app.route('/segment-auto', methods=['POST', 'OPTIONS'])\r"
send "def segment_auto():\r"
send "    if request.method == 'OPTIONS':\r"
send "        return '', 200\r"
send "    \r"
send "    try:\r"
send "        if not mask_generator:\r"
send "            return jsonify({\"error\": \"Model not initialized\"}), 500\r"
send "        \r"
send "        data = request.json\r"
send "        if not data or 'image' not in data:\r"
send "            return jsonify({\"error\": \"No image provided\"}), 400\r"
send "        \r"
send "        print(f\"Processing automatic segmentation...\", flush=True)\r"
send "        \r"
send "        # Decode image\r"
send "        image_b64 = data['image']\r"
send "        image_data = base64.b64decode(image_b64)\r"
send "        image = Image.open(io.BytesIO(image_data)).convert('RGB')\r"
send "        image_np = np.array(image)\r"
send "        \r"
send "        # Generate all masks\r"
send "        masks = mask_generator.generate(image_np)\r"
send "        \r"
send "        print(f\"Generated {len(masks)} automatic masks\", flush=True)\r"
send "        \r"
send "        # Sort by area and take top N\r"
send "        masks = sorted(masks, key=lambda x: x['area'], reverse=True)[:10]\r"
send "        \r"
send "        # Convert masks to base64\r"
send "        result_masks = []\r"
send "        for mask_data in masks:\r"
send "            mask = mask_data['segmentation']\r"
send "            mask_img = Image.fromarray((mask * 255).astype(np.uint8))\r"
send "            buffer = io.BytesIO()\r"
send "            mask_img.save(buffer, format='PNG')\r"
send "            mask_b64 = base64.b64encode(buffer.getvalue()).decode()\r"
send "            \r"
send "            result_masks.append({\r"
send "                \"mask\": mask_b64,\r"
send "                \"area\": mask_data['area'],\r"
send "                \"bbox\": mask_data['bbox'],\r"
send "                \"predicted_iou\": mask_data['predicted_iou'],\r"
send "                \"stability_score\": mask_data['stability_score']\r"
send "            })\r"
send "        \r"
send "        return jsonify({\"masks\": result_masks})\r"
send "        \r"
send "    except Exception as e:\r"
send "        error_msg = f\"Auto segmentation error: {str(e)}\"\r"
send "        print(error_msg, flush=True)\r"
send "        traceback.print_exc()\r"
send "        return jsonify({\"error\": error_msg}), 500\r"
send "\r"
send "if __name__ == '__main__':\r"
send "    if init_model():\r"
send "        app.run(host='0.0.0.0', port=8080, debug=False)\r"
send "    else:\r"
send "        print(\"Failed to initialize model, exiting...\", flush=True)\r"
send "        sys.exit(1)\r"
send "EOF\r"
expect "$ "

# Backup old file and replace with new
send "mv sam_server.py sam_server_old.py\r"
expect "$ "
send "mv sam_server_updated.py sam_server.py\r"
expect "$ "

# Rebuild and restart container
send "docker stop sam-server\r"
expect "$ "
send "docker rm sam-server\r"
expect "$ "

send "docker build -t sam-server:pytorch28 .\r"
expect "$ "

send "docker run -d --gpus all -p 8080:8080 --name sam-server --restart=always sam-server:pytorch28\r"
expect "$ "

send "sleep 10\r"
expect "$ "

# Test new endpoints
send "echo ''\r"
expect "$ "
send "echo '=== Testing GPU Info Endpoint ==='\r"
expect "$ "
send "curl -s http://localhost:8080/gpu-info | python3 -m json.tool\r"
expect "$ "

send "echo ''\r"
expect "$ "
send "echo '=== Server Updated Successfully ==='\r"
expect "$ "

send "exit\r"
expect eof