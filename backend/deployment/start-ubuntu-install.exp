#!/usr/bin/expect -f

# Script per avviare installazione Ubuntu nella VM
set timeout 120
set host "*************"
set user "root"
set password "Al0xan999"

puts "Connecting to Proxmox server..."
spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\nStarting Ubuntu installation process..."

# Send boot command to VM to start installer
send "qm sendkey 202 ret\r"
expect "# "
sleep 2

# Check VM status
send "qm status 202\r"
expect "# "

puts "\n=========================================="
puts "Ubuntu Installation Started!"
puts "=========================================="
puts ""
puts "NEXT STEPS:"
puts "1. Open browser: https://*************:8006"
puts "2. Login with: root / Al0xan999"
puts "3. Click on VM 202 in the left panel"
puts "4. Click 'Console' button"
puts "5. Follow Ubuntu installer:"
puts "   - Language: English"
puts "   - Keyboard: English (US)"
puts "   - Network: DHCP (automatic)"
puts "   - Storage: Use entire disk"
puts "   - Username: ubuntu"
puts "   - Password: ubuntu"
puts "   - Hostname: sam-gpu"
puts "   - Install OpenSSH: YES"
puts ""
puts "Installation will take ~10-15 minutes"
puts "=========================================="

send "exit\r"
expect eof