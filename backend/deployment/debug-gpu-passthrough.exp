#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== CHECKING GPU PASSTHROUGH STATUS ===\n"

# Check IOMMU groups
send "find /sys/kernel/iommu_groups/ -type l | grep '07:00'\r"
expect "# "

# Check if GPU is bound to vfio
send "lspci -nnk -d 10de: | grep -A 3 '07:00'\r"
expect "# "

# Check VM configuration
send "qm config 202 | grep -E '(hostpci|vga|display)'\r"
expect "# "

# Check if VM can see the GPU
send "qm monitor 202\r"
expect "(qemu)"
send "info pci\r"
expect "(qemu)"
send "quit\r"
expect "# "

# Check blacklist status
send "cat /etc/modprobe.d/blacklist.conf | grep -E '(nvidia|nouveau)'\r"
expect "# "

# Check VFIO modules
send "lsmod | grep vfio\r"
expect "# "

# Check dmesg for errors
send "dmesg | grep -i 'vfio\\|iommu\\|07:00' | tail -20\r"
expect "# "

puts "\n=== TROUBLESHOOTING TIPS ===\n"
send "echo '1. Make sure IOMMU is enabled in BIOS'\r"
expect "# "
send "echo '2. Verify GPU is in its own IOMMU group'\r"
expect "# "
send "echo '3. Check if nouveau/nvidia drivers are blacklisted on host'\r"
expect "# "
send "echo '4. Try connecting a physical monitor to GPU'\r"
expect "# "
send "echo '5. Use NoVNC console in Proxmox web UI'\r"
expect "# "

send "exit\r"
expect eof