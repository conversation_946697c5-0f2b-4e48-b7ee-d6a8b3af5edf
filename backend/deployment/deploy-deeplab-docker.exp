#!/usr/bin/expect -f

# Simplified DeepLab deployment script
set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== DEPLOYING DEEPLAB CONTAINER ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "
puts "Connected to VM 200"

# Work in home directory
send "mkdir -p ~/deeplab-server\r"
expect "$ "

send "cd ~/deeplab-server\r"
expect "$ "

# Create the Python server file
send "cat > server.py << 'ENDOFFILE'\r"
send {from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import torch
import torchvision
from torchvision import transforms as T
from PIL import Image
import io
import base64
import numpy as np
import logging

app = FastAPI(title="DeepLab Server")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = None

@app.on_event("startup")
async def load_model():
    global model
    try:
        model = torchvision.models.segmentation.deeplabv3_resnet101(pretrained=True)
        model.to(device)
        model.eval()
        logging.info(f"DeepLab model loaded on {device}")
    except Exception as e:
        logging.error(f"Failed to load model: {e}")

@app.get("/")
async def root():
    return {
        "service": "DeepLab Server",
        "version": "1.0.0",
        "model": "DeepLabV3 ResNet101",
        "device": str(device)
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "gpu_available": torch.cuda.is_available(),
        "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None
    }

@app.get("/gpu-info")
async def gpu_info():
    if torch.cuda.is_available():
        return {
            "gpu_available": True,
            "gpu_name": torch.cuda.get_device_name(0),
            "cuda_version": torch.version.cuda
        }
    return {"gpu_available": False}

@app.post("/segment")
async def segment(file: UploadFile = File(...)):
    try:
        contents = await file.read()
        image = Image.open(io.BytesIO(contents)).convert("RGB")
        
        preprocess = T.Compose([
            T.Resize((512, 512)),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        input_tensor = preprocess(image).unsqueeze(0).to(device)
        
        with torch.no_grad():
            output = model(input_tensor)['out'][0]
        
        output_predictions = output.argmax(0).cpu().numpy()
        
        masks = []
        unique_classes = np.unique(output_predictions)
        
        for class_id in unique_classes[:4]:
            if class_id == 0:
                continue
            mask = (output_predictions == class_id).astype(np.uint8) * 255
            mask_pil = Image.fromarray(mask, mode='L')
            buffer = io.BytesIO()
            mask_pil.save(buffer, format="PNG")
            mask_b64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            masks.append(mask_b64)
        
        scores = [0.95 - (i * 0.05) for i in range(len(masks))][:3]
        
        return {"masks": masks[:3], "scores": scores}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8081)
}
send "\r"
send "ENDOFFILE\r"
expect "$ "

# Create requirements.txt
send "cat > requirements.txt << 'ENDOFFILE'\r"
send {fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
torch==2.1.0
torchvision==0.16.0
Pillow==10.1.0
numpy==1.24.3
}
send "\r"
send "ENDOFFILE\r"
expect "$ "

# Create Dockerfile
send "cat > Dockerfile << 'ENDOFFILE'\r"
send {FROM nvidia/cuda:12.1.0-runtime-ubuntu22.04

ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && apt-get install -y \
    python3-pip \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY requirements.txt .
RUN pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cu121
RUN pip3 install -r requirements.txt

COPY server.py .

EXPOSE 8081

CMD ["uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8081"]
}
send "\r"
send "ENDOFFILE\r"
expect "$ "

# Build Docker image (with sudo, entering password)
puts "Building Docker image..."
send "sudo docker build -t deeplab-server:latest .\r"
expect {
    "password for alin:" {
        send "$password\r"
        exp_continue
    }
    "Successfully built" {
        puts "Docker image built successfully"
    }
    timeout {
        puts "Build in progress..."
    }
}

# Wait for build to complete
expect "$ "

# Stop existing container if any
send "sudo docker stop deeplab-server 2>/dev/null || true\r"
expect {
    "password for alin:" {
        send "$password\r"
    }
    "$ " {}
}
expect "$ "

send "sudo docker rm deeplab-server 2>/dev/null || true\r"
expect "$ "

# Run the container
puts "Starting DeepLab container..."
send "sudo docker run -d --name deeplab-server --gpus all -p 8081:8081 --restart unless-stopped deeplab-server:latest\r"
expect "$ "

# Wait a bit for container to start
send "sleep 5\r"
expect "$ "

# Check container status
send "sudo docker ps | grep deeplab\r"
expect {
    "deeplab-server" {
        puts "\n✓ DeepLab container is running"
    }
    "$ " {
        puts "\n⚠ Container might not be running, checking logs..."
        send "sudo docker logs deeplab-server\r"
        expect "$ "
    }
}

# Test the API
puts "\nTesting DeepLab API..."
send "curl -s http://localhost:8081/health\r"
expect "$ "

send "curl -s http://localhost:8081/gpu-info\r"
expect "$ "

puts "\n=== DEEPLAB DEPLOYMENT COMPLETE ==="
puts "DeepLab API: http://*************:8081"
puts "Health: http://*************:8081/health"
puts "GPU Info: http://*************:8081/gpu-info"

send "exit\r"
expect eof