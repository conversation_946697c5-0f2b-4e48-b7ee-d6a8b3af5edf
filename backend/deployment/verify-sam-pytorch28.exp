#!/usr/bin/expect -f

set timeout 120
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== VERIFYING SAM SERVER WITH PYTORCH 2.8 ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Check if container is running
send "docker ps | grep sam-server\r"
expect "$ "

# Check PyTorch version in container
puts "\n--- Checking PyTorch Version ---"
send "docker exec sam-server python3 -c \"import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else None}')\"\r"
expect "$ "

# Check container logs
puts "\n--- Container Logs ---"
send "docker logs sam-server --tail 20\r"
expect "$ "

# Test health endpoint
puts "\n--- Testing Health Endpoint ---"
send "curl -s http://localhost:8080/health | python3 -m json.tool\r"
expect "$ "

# Create a test image and test segmentation
puts "\n--- Testing Segmentation ---"
send "cat > test_segmentation.py << 'EOF'\r"
send "#!/usr/bin/env python3\r"
send "import requests\r"
send "import base64\r"
send "import json\r"
send "from PIL import Image, ImageDraw\r"
send "import io\r"
send "\r"
send "# Create a simple test image\r"
send "img = Image.new('RGB', (400, 300), 'white')\r"
send "draw = ImageDraw.Draw(img)\r"
send "draw.rectangle([100, 75, 300, 225], fill='blue')\r"
send "draw.ellipse([150, 100, 250, 200], fill='red')\r"
send "\r"
send "# Convert to base64\r"
send "buffer = io.BytesIO()\r"
send "img.save(buffer, format='PNG')\r"
send "img_b64 = base64.b64encode(buffer.getvalue()).decode()\r"
send "\r"
send "# Test health\r"
send "print('Testing health endpoint...')\r"
send "try:\r"
send "    response = requests.get('http://localhost:8080/health')\r"
send "    print(f'Health: {response.json()}')\r"
send "except Exception as e:\r"
send "    print(f'Health check failed: {e}')\r"
send "\r"
send "# Test segmentation\r"
send "print('\\nTesting segmentation...')\r"
send "try:\r"
send "    response = requests.post(\r"
send "        'http://localhost:8080/segment',\r"
send "        json={\r"
send "            'image': img_b64,\r"
send "            'points': [[200, 150]]  # Center point\r"
send "        }\r"
send "    )\r"
send "    if response.status_code == 200:\r"
send "        data = response.json()\r"
send "        print(f'Success! Generated {len(data.get(\"masks\", []))} masks')\r"
send "        if 'scores' in data:\r"
send "            print(f'Scores: {data[\"scores\"]}')\r"
send "    else:\r"
send "        print(f'Error {response.status_code}: {response.text}')\r"
send "except Exception as e:\r"
send "    print(f'Segmentation failed: {e}')\r"
send "EOF\r"
expect "$ "

send "python3 test_segmentation.py\r"
expect "$ "

# Check GPU memory usage
puts "\n--- GPU Status ---"
send "nvidia-smi --query-gpu=name,memory.used,memory.total,utilization.gpu --format=csv\r"
expect "$ "

# Check Docker container resource usage
puts "\n--- Container Resources ---"
send "docker stats sam-server --no-stream\r"
expect "$ "

send "echo ''\r"
expect "$ "
send "echo '=== VERIFICATION COMPLETE ==='\r"
expect "$ "
send "echo 'SAM server is running with PyTorch 2.8 and RTX 5080 support'\r"
expect "$ "

send "exit\r"
expect eof