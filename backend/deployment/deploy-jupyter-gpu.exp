#!/usr/bin/expect -f

# Deploy Jupyter Lab with GPU support for ML experiments
set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== DEPLOYING JUPYTER LAB WITH GPU ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "
puts "Connected to VM 200"

# Create Jupyter directory
send "mkdir -p ~/jupyter-ml\r"
expect "$ "

send "cd ~/jupyter-ml\r"
expect "$ "

# Create custom Dockerfile for Jupyter with ML libraries
send "cat > Dockerfile << 'ENDOFFILE'\r"
send {FROM jupyter/tensorflow-notebook:latest

USER root

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    wget \
    curl \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Switch back to jovyan user
US<PERSON> jovyan

# Install PyTorch with CUDA support
RUN pip install --no-cache-dir \
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Install computer vision and ML libraries
RUN pip install --no-cache-dir \
    opencv-python \
    albumentations \
    segmentation-models-pytorch \
    transformers \
    timm \
    pytorch-lightning \
    wandb \
    tensorboard

# Install satellite imagery specific libraries
RUN pip install --no-cache-dir \
    rasterio \
    geopandas \
    folium \
    earthengine-api \
    sentinelsat \
    planetary-computer \
    pystac-client

# Install FastAPI for testing
RUN pip install --no-cache-dir \
    fastapi \
    uvicorn \
    python-multipart

# Install additional useful libraries
RUN pip install --no-cache-dir \
    scikit-learn \
    pandas \
    matplotlib \
    seaborn \
    plotly \
    ipywidgets \
    tqdm

# Set environment variables
ENV JUPYTER_ENABLE_LAB=yes
ENV GRANT_SUDO=yes

# Create workspace directories
RUN mkdir -p /home/<USER>/work/notebooks \
    /home/<USER>/work/data \
    /home/<USER>/work/models \
    /home/<USER>/work/outputs

WORKDIR /home/<USER>/work

# Expose Jupyter port
EXPOSE 8888

CMD ["start-notebook.sh", "--NotebookApp.token=''", "--NotebookApp.password=''"]
}
send "\r"
send "ENDOFFILE\r"
expect "$ "

# Create docker-compose.yml
send "cat > docker-compose.yml << 'ENDOFFILE'\r"
send {version: '3.8'

services:
  jupyter-gpu:
    build: .
    image: jupyter-ml-gpu:latest
    container_name: jupyter-ml-gpu
    restart: unless-stopped
    ports:
      - "8888:8888"
    volumes:
      - ./notebooks:/home/<USER>/work/notebooks
      - ./data:/home/<USER>/work/data
      - ./models:/home/<USER>/work/models
      - ./outputs:/home/<USER>/work/outputs
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - GRANT_SUDO=yes
      - NB_UID=1000
      - NB_GID=100
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - jupyter-net

networks:
  jupyter-net:
    driver: bridge
}
send "\r"
send "ENDOFFILE\r"
expect "$ "

# Create initial test notebook
send "mkdir -p notebooks\r"
expect "$ "

send "cat > notebooks/test_gpu_segmentation.ipynb << 'ENDOFFILE'\r"
send {{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# GPU Test and Segmentation Models\n",
    "Test GPU availability and compare segmentation models"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import torch\n",
    "import torchvision\n",
    "import cv2\n",
    "import numpy as np\n",
    "from PIL import Image\n",
    "import matplotlib.pyplot as plt\n",
    "\n",
    "# Check GPU\n",
    "print(f\"PyTorch version: {torch.__version__}\")\n",
    "print(f\"CUDA available: {torch.cuda.is_available()}\")\n",
    "if torch.cuda.is_available():\n",
    "    print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n",
    "    print(f\"Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test DeepLab\n",
    "model = torchvision.models.segmentation.deeplabv3_resnet101(pretrained=True)\n",
    "model.eval()\n",
    "if torch.cuda.is_available():\n",
    "    model = model.cuda()\n",
    "print(\"DeepLab model loaded successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test segmentation on sample image\n",
    "def segment_image(image_path):\n",
    "    from torchvision import transforms\n",
    "    \n",
    "    # Load and preprocess image\n",
    "    image = Image.open(image_path).convert('RGB')\n",
    "    preprocess = transforms.Compose([\n",
    "        transforms.ToTensor(),\n",
    "        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n",
    "    ])\n",
    "    \n",
    "    input_tensor = preprocess(image)\n",
    "    input_batch = input_tensor.unsqueeze(0)\n",
    "    \n",
    "    if torch.cuda.is_available():\n",
    "        input_batch = input_batch.cuda()\n",
    "    \n",
    "    # Perform segmentation\n",
    "    with torch.no_grad():\n",
    "        output = model(input_batch)['out'][0]\n",
    "    \n",
    "    output_predictions = output.argmax(0).cpu().numpy()\n",
    "    \n",
    "    # Visualize\n",
    "    plt.figure(figsize=(15, 5))\n",
    "    plt.subplot(1, 3, 1)\n",
    "    plt.imshow(image)\n",
    "    plt.title('Original Image')\n",
    "    plt.axis('off')\n",
    "    \n",
    "    plt.subplot(1, 3, 2)\n",
    "    plt.imshow(output_predictions, cmap='jet')\n",
    "    plt.title('Segmentation Mask')\n",
    "    plt.axis('off')\n",
    "    \n",
    "    plt.subplot(1, 3, 3)\n",
    "    plt.imshow(image)\n",
    "    plt.imshow(output_predictions, alpha=0.5, cmap='jet')\n",
    "    plt.title('Overlay')\n",
    "    plt.axis('off')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    return output_predictions\n",
    "\n",
    "# You can test with your own image\n",
    "# mask = segment_image('path/to/your/image.jpg')"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "name": "python",
   "version": "3.10.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
}
send "\r"
send "ENDOFFILE\r"
expect "$ "

# Build the Docker image
puts "\nBuilding Jupyter Docker image with ML libraries..."
send "sudo docker-compose build\r"
expect {
    "password for alin:" {
        send "$password\r"
        exp_continue
    }
    "Successfully built" {
        puts "Docker image built successfully"
    }
    timeout {
        puts "Build in progress... this may take several minutes"
    }
}

# Wait for build completion (longer timeout for ML libraries)
set timeout 600
expect "$ "

# Stop any existing Jupyter containers
send "sudo docker-compose down 2>/dev/null || true\r"
expect "$ "

# Start the container
puts "\nStarting Jupyter container..."
send "sudo docker-compose up -d\r"
expect "$ "

# Wait for container to start
send "sleep 10\r"
expect "$ "

# Check if container is running
send "sudo docker ps | grep jupyter-ml-gpu\r"
expect {
    "jupyter-ml-gpu" {
        puts "\n✓ Jupyter container is running"
    }
    "$ " {
        puts "\n⚠ Container might not be running, checking logs..."
        send "sudo docker logs jupyter-ml-gpu --tail 20\r"
        expect "$ "
    }
}

# Get the Jupyter token (if needed)
puts "\nGetting Jupyter access info..."
send "sudo docker logs jupyter-ml-gpu 2>&1 | grep -E '(token=|http://127)' | tail -1\r"
expect "$ "

# Test GPU access in container
puts "\nTesting GPU access in Jupyter container..."
send "sudo docker exec jupyter-ml-gpu python -c \"import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0)}' if torch.cuda.is_available() else 'No GPU')\"\r"
expect "$ "

puts "\n=== JUPYTER DEPLOYMENT COMPLETE ==="
puts "Access Jupyter Lab at: http://$host:8888"
puts "No password required (configured for development)"
puts ""
puts "Features installed:"
puts "- PyTorch with CUDA support"
puts "- Computer Vision libraries (OpenCV, Albumentations)"
puts "- Segmentation models (DeepLab, U-Net via segmentation-models-pytorch)"
puts "- Satellite imagery tools (Rasterio, GeoPandas)"
puts "- FastAPI for API testing"
puts ""
puts "Directories:"
puts "- Notebooks: ~/jupyter-ml/notebooks"
puts "- Data: ~/jupyter-ml/data"
puts "- Models: ~/jupyter-ml/models"
puts "- Outputs: ~/jupyter-ml/outputs"

send "exit\r"
expect eof