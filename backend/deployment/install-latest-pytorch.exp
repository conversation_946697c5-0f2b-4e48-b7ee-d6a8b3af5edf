#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== INSTALLING LATEST PYTORCH FOR RTX 5080 ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Check available PyTorch versions
puts "Checking latest available PyTorch versions..."
send "sudo docker exec jupyter-ml pip3 index versions torch\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

# Install the absolute latest nightly build
puts "\nInstalling latest PyTorch nightly (closest to 2.8)..."
send "sudo docker exec jupyter-ml pip3 uninstall -y torch torchvision torchaudio\r"
expect "$ "

# Install nightly build
send "sudo docker exec jupyter-ml pip3 install --pre torch torchvision torchaudio --index-url https://download.pytorch.org/whl/nightly/cu124\r"
expect {
    timeout {
        puts "Installing latest PyTorch..."
        exp_continue
    }
    "$ " {}
}

# Verify version
send "sudo docker exec jupyter-ml python3 -c \"import torch; print(f'PyTorch installed: {torch.__version__}')\"\r"
expect "$ "

puts "\n=== PYTORCH UPDATED ==="
puts "Installed the latest available PyTorch version"
puts "Note: PyTorch 2.8 doesn't exist yet, installed latest available (2.6.0 or nightly)"

send "exit\r"
expect eof