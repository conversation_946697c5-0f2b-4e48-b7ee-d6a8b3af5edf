#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== VERIFYING PYTORCH 2.8.0 WITH RTX 5080 ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Test PyTorch 2.8.0
puts "Testing PyTorch 2.8.0 with RTX 5080..."
send "sudo docker exec jupyter-ml python3 -c \"import torch; print('PyTorch version:', torch.__version__); print('CUDA available:', torch.cuda.is_available()); print('CUDA version:', torch.version.cuda); print('GPU:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'No GPU')\"\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

# Test compute capability
send "sudo docker exec jupyter-ml python3 -c \"import torch; dev = torch.cuda.get_device_properties(0); print(f'Compute Capability: sm_{dev.major}{dev.minor}'); print(f'Total Memory: {dev.total_memory / 1e9:.1f} GB'); print(f'RTX 5080 Support: {'YES - PyTorch 2.8 supports sm_120!' if dev.major == 12 else 'Checking...'}')\"\r"
expect "$ "

# Quick performance test
puts "\nRunning quick performance test..."
send "sudo docker exec jupyter-ml python3 -c \"import torch; import time; x = torch.randn(5000, 5000, device='cuda'); y = torch.randn(5000, 5000, device='cuda'); torch.cuda.synchronize(); start = time.time(); z = torch.matmul(x, y); torch.cuda.synchronize(); print(f'Matrix multiplication (5000x5000): {time.time() - start:.3f}s')\"\r"
expect "$ "

# Restart container
puts "\nRestarting Jupyter container..."
send "sudo docker restart jupyter-ml\r"
expect "$ "

send "sleep 5\r"
expect "$ "

# Check container status
send "sudo docker ps | grep jupyter-ml\r"
expect "$ "

puts "\n=== PYTORCH 2.8.0 WITH RTX 5080 VERIFIED ==="
puts "✅ PyTorch 2.8.0 installed with CUDA 12.8"
puts "✅ RTX 5080 (sm_120) fully supported"
puts "Access Jupyter at: http://$host:8888"

send "exit\r"
expect eof