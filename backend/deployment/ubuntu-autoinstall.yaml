#cloud-config
autoinstall:
  version: 1
  locale: en_US.UTF-8
  keyboard:
    layout: us
  
  network:
    network:
      version: 2
      ethernets:
        enp6s18:
          dhcp4: true
  
  storage:
    layout:
      name: direct
  
  identity:
    hostname: sam-gpu-server
    username: ubuntu
    password: "$6$rounds=4096$8dkfN3JqM$R5B5F5XZ7RoHPGnT/7Y3NDa6yNj1Yw0Q7h4L0yI5bB4N1kE7xK4lC7K2Js5jX9L3kF1zQ0H6I4mN5J7L8K9M0/"  # password: ubuntu
  
  ssh:
    install-server: true
    allow-pw: true
  
  packages:
    - build-essential
    - curl
    - wget
    - git
    - vim
    - htop
    - net-tools
    - software-properties-common
    - python3-pip
    - python3-dev
  
  late-commands:
    - echo 'ubuntu ALL=(ALL) NOPASSWD:ALL' > /target/etc/sudoers.d/ubuntu
    - curtin in-target --target=/target -- chmod 440 /etc/sudoers.d/ubuntu