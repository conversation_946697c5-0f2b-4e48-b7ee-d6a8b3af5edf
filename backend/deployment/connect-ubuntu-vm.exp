#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== CONNECTING TO UBUNTU VM ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
    timeout { puts "Connection timeout"; exit 1 }
}

expect {
    "$ " { puts "\nConnected successfully!\n" }
    timeout { puts "Login failed"; exit 1 }
}

# Test connection
send "hostname\r"
expect "$ "

send "uname -a\r"
expect "$ "

send "ip a | grep 192.168\r"
expect "$ "

# Check GPU passthrough
send "lspci | grep -i nvidia\r"
expect "$ "

send "echo 'Connection test successful!'\r"
expect "$ "

send "exit\r"
expect eof