#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Check network interfaces
send "ip link show | grep 'state UP'\r"
expect "# "

# Check current network speed
send "ethtool eth0 2>/dev/null | grep -i speed || ethtool eno1 2>/dev/null | grep -i speed || ethtool enp0s31f6 2>/dev/null | grep -i speed\r"
expect "# "

# Check network configuration
send "cat /proc/net/dev | grep -E 'eth|eno|enp'\r"
expect "# "

# Test download speed with wget
send "wget -O /dev/null --timeout=10 https://speed.hetzner.de/100MB.bin 2>&1 | grep -E 'MB/s|KB/s'\r"
expect "# "

# Check if there's bandwidth limiting
send "tc qdisc show\r"
expect "# "

# Check DNS
send "cat /etc/resolv.conf | grep nameserver\r"
expect "# "

# Check routing
send "ip route | head -5\r"
expect "# "

send "exit\r"
expect eof