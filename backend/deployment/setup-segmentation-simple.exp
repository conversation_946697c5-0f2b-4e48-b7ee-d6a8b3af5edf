#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== SETTING UP SEGMENTATION IN JUPYTER ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Install required packages in Jupyter container
puts "Installing segmentation packages..."
send "sudo docker exec jupyter-ml pip3 install segment-anything opencv-python rasterio geopandas fastapi uvicorn python-multipart gradio -q\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

# Create segmentation test script
puts "\nCreating segmentation test script..."
send "cat > ~/jupyter-ml/notebooks/test_segmentation.py << 'ENDFILE'\r"
send "#!/usr/bin/env python3\r"
send "import torch\r"
send "import torchvision\r"
send "print('PyTorch:', torch.__version__)\r"
send "print('CUDA:', torch.cuda.is_available())\r"
send "if torch.cuda.is_available():\r"
send "    print('GPU:', torch.cuda.get_device_name(0))\r"
send "\r"
send "# Test DeepLab\r"
send "print('\\nLoading DeepLab...')\r"
send "model = torchvision.models.segmentation.deeplabv3_resnet101(pretrained=True)\r"
send "model.eval()\r"
send "if torch.cuda.is_available():\r"
send "    model = model.cuda()\r"
send "print('DeepLab ready!')\r"
send "\r"
send "# Quick test\r"
send "test_input = torch.randn(1, 3, 224, 224)\r"
send "if torch.cuda.is_available():\r"
send "    test_input = test_input.cuda()\r"
send "with torch.no_grad():\r"
send "    output = model(test_input)\r"
send "print('Output shape:', output['out'].shape)\r"
send "print('\\nAll models ready for satellite segmentation!')\r"
send "ENDFILE\r"
expect "$ "

# Run test
send "sudo docker exec jupyter-ml python3 /workspace/notebooks/test_segmentation.py\r"
expect "$ "

# Create DeepLab API server
puts "\nCreating DeepLab API server..."
send "cat > ~/jupyter-ml/notebooks/deeplab_api.py << 'ENDFILE'\r"
send "from fastapi import FastAPI, File, UploadFile\r"
send "from fastapi.middleware.cors import CORSMiddleware\r"
send "import torch\r"
send "import torchvision\r"
send "from PIL import Image\r"
send "import io\r"
send "import base64\r"
send "import numpy as np\r"
send "\r"
send "app = FastAPI()\r"
send "app.add_middleware(CORSMiddleware, allow_origins=['*'], allow_methods=['*'], allow_headers=['*'])\r"
send "\r"
send "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\r"
send "model = torchvision.models.segmentation.deeplabv3_resnet101(pretrained=True).to(device)\r"
send "model.eval()\r"
send "\r"
send "@app.get('/health')\r"
send "def health():\r"
send "    return {'status': 'healthy', 'gpu': str(device)}\r"
send "\r"
send "@app.post('/segment')\r"
send "async def segment(file: UploadFile):\r"
send "    from torchvision import transforms\r"
send "    contents = await file.read()\r"
send "    image = Image.open(io.BytesIO(contents)).convert('RGB')\r"
send "    \r"
send "    preprocess = transforms.Compose([\r"
send "        transforms.ToTensor(),\r"
send "        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])\r"
send "    ])\r"
send "    \r"
send "    input_tensor = preprocess(image).unsqueeze(0).to(device)\r"
send "    \r"
send "    with torch.no_grad():\r"
send "        output = model(input_tensor)['out'][0]\r"
send "    \r"
send "    mask = output.argmax(0).cpu().numpy()\r"
send "    mask_img = Image.fromarray((mask * 10).astype(np.uint8))\r"
send "    \r"
send "    buffered = io.BytesIO()\r"
send "    mask_img.save(buffered, format='PNG')\r"
send "    mask_b64 = base64.b64encode(buffered.getvalue()).decode()\r"
send "    \r"
send "    return {'mask': mask_b64, 'classes': int(mask.max()) + 1}\r"
send "\r"
send "if __name__ == '__main__':\r"
send "    import uvicorn\r"
send "    uvicorn.run(app, host='0.0.0.0', port=8081)\r"
send "ENDFILE\r"
expect "$ "

# Start DeepLab server
puts "\nStarting DeepLab server..."
send "sudo docker exec -d jupyter-ml sh -c 'cd /workspace/notebooks && python3 deeplab_api.py > deeplab.log 2>&1'\r"
expect "$ "

send "sleep 5\r"
expect "$ "

# Check if server is running
send "curl -s http://localhost:8081/health 2>/dev/null || echo 'Server starting...'\r"
expect "$ "

# List all services
puts "\n"
send "sudo docker ps --format 'table {{.Names}}\t{{.Ports}}'\r"
expect "$ "

puts "\n=== SEGMENTATION SETUP COMPLETE ==="
puts "Services running:"
puts "- Jupyter Lab: http://$host:8888"
puts "- SAM Server: http://$host:8080"
puts "- DeepLab API: http://$host:8081"
puts ""
puts "All models ready for satellite image segmentation!"

send "exit\r"
expect eof