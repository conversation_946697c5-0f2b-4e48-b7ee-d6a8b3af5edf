#!/usr/bin/expect -f

set timeout 1800
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Access VM console via qm terminal
send "qm terminal 202\r"
expect {
    "starting" { 
        send "\r"
        exp_continue 
    }
    "Ubuntu" {
        send "\r"
    }
    timeout {
        send "\r"
    }
}

# Wait for GRUB menu and select Install Ubuntu Server
sleep 2
send "\r"

# The installer should auto-detect and use autoinstall.yaml if present
# Otherwise proceed with manual installation

expect {
    "Continue in" {
        # Language selection - English
        send "\r"
    }
    timeout {
        send "\r"
    }
}

sleep 2

expect {
    "Keyboard" {
        # Keyboard layout
        send "\r"
    }
    timeout {
        send "\r"
    }
}

sleep 2

expect {
    "Network connections" {
        # Network - use DHCP
        send "\r"
    }
    timeout {
        send "\r"
    }
}

sleep 2

expect {
    "Configure proxy" {
        # No proxy
        send "\r"
    }
    timeout {
        send "\r"
    }
}

sleep 2

expect {
    "Mirror address" {
        # Default mirror
        send "\r"
    }
    timeout {
        send "\r"
    }
}

sleep 2

expect {
    "Guided storage" {
        # Use entire disk
        send "\r"
        sleep 1
        send "\r"
        sleep 1
        send "\t\t\r"  # Confirm destructive action
    }
    timeout {
        send "\r"
    }
}

sleep 2

expect {
    "Profile setup" {
        # Your name
        send "Ubuntu User\r"
        sleep 1
        # Server name
        send "sam-gpu\r"
        sleep 1
        # Username
        send "ubuntu\r"
        sleep 1
        # Password
        send "ubuntu\r"
        sleep 1
        # Confirm password
        send "ubuntu\r"
    }
    timeout {
        send "\r"
    }
}

sleep 2

expect {
    "SSH Setup" {
        # Install OpenSSH server
        send " \r"  # Space to select, then enter
    }
    timeout {
        send "\r"
    }
}

sleep 2

expect {
    "Featured Server Snaps" {
        # Skip snaps
        send "\r"
    }
    timeout {
        send "\r"
    }
}

# Installation will now proceed
# Wait for completion (this takes a while)
set timeout 3600
expect {
    "Installation complete" {
        send "\r"
    }
    "Reboot Now" {
        send "\r"
    }
    timeout {
        puts "Installation taking longer than expected..."
    }
}

# Exit from qm terminal (Ctrl+O)
send "\017"
expect "# "

send "echo 'Ubuntu installation initiated. Please wait for completion...'\r"
expect "# "

send "exit\r"
expect eof