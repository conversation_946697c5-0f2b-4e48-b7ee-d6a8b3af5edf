#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Check interface speed
send "ethtool enp4s0d1 | grep -E 'Speed|Duplex|Link'\r"
expect "# "

# Check if MTU is optimal
send "ip link show enp4s0d1 | grep mtu\r"
expect "# "

# Test with speedtest-cli if available
send "which speedtest-cli && speedtest-cli --simple || echo 'speedtest-cli not installed'\r"
expect "# "

# Test download from fast CDN
send "curl -o /dev/null -w 'Download Speed: %{speed_download} bytes/sec\\n' https://cdn.kernel.org/pub/linux/kernel/v6.x/linux-6.6.tar.xz 2>/dev/null | head -20\r"
expect "# "

# Check if any firewall rules might be limiting
send "iptables -L -n | grep -i limit | head -5\r"
expect "# "

# Check network buffer sizes
send "sysctl net.core.rmem_max net.core.wmem_max | head -2\r"
expect "# "

# Check active connections
send "ss -s | head -10\r"
expect "# "

# Kill the ongoing download if still running
send "pkill -f wget.*ubuntu\r"
expect "# "

# Try alternative mirror for Ubuntu ISO
send "wget -O /dev/null --timeout=10 http://mirror.math.princeton.edu/pub/ubuntu-iso/22.04/ubuntu-22.04.5-live-server-amd64.iso 2>&1 | head -20 &\r"
expect "# "

send "sleep 5\r"
expect "# "

send "pkill wget\r"
expect "# "

send "exit\r"
expect eof