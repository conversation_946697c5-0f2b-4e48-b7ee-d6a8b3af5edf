#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== CHECKING NVIDIA STATUS ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Check if NVIDIA driver is installed
send "dpkg -l | grep nvidia-driver\r"
expect "$ "

# Check if nvidia-smi works
send "nvidia-smi 2>&1\r"
expect "$ "

# Check if GPU is visible
send "lspci | grep -i nvidia\r"
expect "$ "

# Check kernel modules
send "lsmod | grep nvidia\r"
expect "$ "

# Check CUDA version if installed
send "nvcc --version 2>&1 | head -5\r"
expect "$ "

# Check if system needs reboot
send "ls /var/run/reboot-required 2>/dev/null && echo 'REBOOT REQUIRED' || echo 'No reboot needed'\r"
expect "$ "

send "exit\r"
expect eof