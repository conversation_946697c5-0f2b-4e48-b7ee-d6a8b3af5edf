#!/usr/bin/expect -f

set timeout 600
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== INSTALLING PYTORCH 2.8.0 WITH CUDA 12.8 FOR RTX 5080 ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

puts "Installing PyTorch 2.8.0 with CUDA 12.8 support for RTX 5080 (sm_120)..."

# First uninstall current PyTorch
send "sudo docker exec jupyter-ml pip3 uninstall -y torch torchvision torchaudio\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

# Install PyTorch 2.8.0 with CUDA 12.8 support
puts "\nInstalling PyTorch 2.8.0 with CUDA 12.8..."
send "sudo docker exec jupyter-ml pip3 install torch==2.8.0 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128\r"
expect {
    timeout {
        puts "Installing PyTorch 2.8.0, please wait..."
        exp_continue
    }
    "ERROR" {
        puts "\nCUDA 12.8 index not found, trying alternative..."
        send "sudo docker exec jupyter-ml pip3 install torch==2.8.0 torchvision torchaudio\r"
        exp_continue
    }
    "$ " {}
}

# Verify installation
puts "\nVerifying PyTorch 2.8.0 installation..."
send "sudo docker exec jupyter-ml python3 -c \"import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}'); print(f'GPU: {torch.cuda.get_device_name(0)}' if torch.cuda.is_available() else 'No GPU'); print(f'Compute capability: sm_{torch.cuda.get_device_capability(0)[0]}{torch.cuda.get_device_capability(0)[1]}' if torch.cuda.is_available() else '')\"\r"
expect "$ "

# Create test script to verify RTX 5080 works
puts "\nCreating RTX 5080 test script..."
send "cat > ~/jupyter-ml/notebooks/test_rtx5080.py << 'EOF'\r"
send "#!/usr/bin/env python3\r"
send "import torch\r"
send "import warnings\r"
send "\r"
send "# PyTorch 2.8 should support RTX 5080 natively\r"
send "print('=' * 50)\r"
send "print('RTX 5080 PyTorch 2.8 Test')\r"
send "print('=' * 50)\r"
send "\r"
send "print(f'PyTorch version: {torch.__version__}')\r"
send "print(f'CUDA available: {torch.cuda.is_available()}')\r"
send "\r"
send "if torch.cuda.is_available():\r"
send "    device_props = torch.cuda.get_device_properties(0)\r"
send "    print(f'GPU: {torch.cuda.get_device_name(0)}')\r"
send "    print(f'Compute Capability: sm_{device_props.major}{device_props.minor}')\r"
send "    print(f'Total Memory: {device_props.total_memory / 1e9:.1f} GB')\r"
send "    print(f'CUDA Cores: {device_props.multi_processor_count * 128}')\r"
send "    \r"
send "    # Test GPU computation\r"
send "    print('\\nRunning GPU benchmark...')\r"
send "    x = torch.randn(10000, 10000, device='cuda')\r"
send "    y = torch.randn(10000, 10000, device='cuda')\r"
send "    \r"
send "    # Warm up\r"
send "    _ = torch.matmul(x, y)\r"
send "    torch.cuda.synchronize()\r"
send "    \r"
send "    # Benchmark\r"
send "    import time\r"
send "    start = time.time()\r"
send "    z = torch.matmul(x, y)\r"
send "    torch.cuda.synchronize()\r"
send "    elapsed = time.time() - start\r"
send "    \r"
send "    print(f'Matrix multiplication (10000x10000): {elapsed:.3f} seconds')\r"
send "    print(f'TFLOPS: {2 * 10000**3 / elapsed / 1e12:.2f}')\r"
send "    print('\\n✅ RTX 5080 working perfectly with PyTorch 2.8!')\r"
send "else:\r"
send "    print('❌ GPU not detected')\r"
send "EOF\r"
expect "$ "

# Run the test
send "sudo docker exec jupyter-ml python3 /workspace/notebooks/test_rtx5080.py\r"
expect "$ "

# Restart container to ensure all changes take effect
puts "\nRestarting Jupyter container..."
send "sudo docker restart jupyter-ml\r"
expect "$ "

send "sleep 5\r"
expect "$ "

puts "\n=== PYTORCH 2.8.0 INSTALLATION COMPLETE ==="
puts "PyTorch 2.8.0 with CUDA 12.8 installed for RTX 5080 support"
puts "Access Jupyter at: http://$host:8888"

send "exit\r"
expect eof