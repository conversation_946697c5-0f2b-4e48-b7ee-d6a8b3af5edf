#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== Deleting All VMs and Containers ==="

# List all VMs and containers
send "qm list\r"
expect "# "
send "pct list\r"
expect "# "

# Delete VM 202
send "qm stop 202 2>/dev/null; qm destroy 202 --purge 2>/dev/null && echo 'VM 202 deleted' || echo 'VM 202 not found'\r"
expect "# "

# Delete container 201 if exists
send "pct stop 201 2>/dev/null; pct destroy 201 --purge 2>/dev/null && echo 'Container 201 deleted' || echo 'Container 201 not found'\r"
expect "# "

# Clean up any other test VMs (200-210 range)
send "for id in {200..210}; do qm stop \$id 2>/dev/null; qm destroy \$id --purge 2>/dev/null; done\r"
expect "# "

send "echo 'Cleanup complete'\r"
expect "# "

send "exit\r"
expect eof