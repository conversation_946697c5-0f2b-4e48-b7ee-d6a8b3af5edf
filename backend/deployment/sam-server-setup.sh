#!/bin/bash

# Script per configurare SAM Server su Ubuntu con GPU
# Da eseguire DENTRO la VM Ubuntu dopo l'installazione

set -e

echo "=== SAM GPU Server Setup ==="

# Step 1: Aggiorna sistema
echo "Step 1: Aggiornamento sistema..."
apt-get update && apt-get upgrade -y

# Step 2: Installa driver NVIDIA
echo "Step 2: Installazione driver NVIDIA..."
apt-get install -y ubuntu-drivers-common
ubuntu-drivers autoinstall
apt-get install -y nvidia-cuda-toolkit

# Step 3: Installa Docker
echo "Step 3: Installazione Docker..."
apt-get install -y apt-transport-https ca-certificates curl software-properties-common
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | apt-key add -
add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
apt-get update
apt-get install -y docker-ce docker-ce-cli containerd.io

# Step 4: Installa NVIDIA Container Toolkit
echo "Step 4: Installazione NVIDIA Container Toolkit..."
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | tee /etc/apt/sources.list.d/nvidia-docker.list
apt-get update
apt-get install -y nvidia-container-toolkit
systemctl restart docker

# Step 5: Verifica GPU in Docker
echo "Step 5: Verifica GPU in Docker..."
docker run --rm --gpus all nvidia/cuda:11.8.0-base-ubuntu22.04 nvidia-smi

# Step 6: Crea directory per SAM
echo "Step 6: Preparazione ambiente SAM..."
mkdir -p /opt/sam-server
cd /opt/sam-server

# Step 7: Crea Dockerfile per SAM
cat > Dockerfile << 'EOF'
FROM nvidia/cuda:11.8.0-cudnn8-runtime-ubuntu22.04

# Installa Python e dipendenze
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3-pip \
    git \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Installa PyTorch con CUDA
RUN pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# Installa SAM
RUN pip3 install segment-anything opencv-python-headless

# Installa FastAPI e dipendenze
RUN pip3 install fastapi uvicorn[standard] python-multipart aiofiles websockets pillow numpy

# Download modelli SAM
RUN mkdir -p /models && \
    cd /models && \
    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth && \
    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth && \
    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth

WORKDIR /app

# Copia codice server
COPY . /app

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
EOF

# Step 8: Crea server FastAPI per SAM
cat > main.py << 'EOF'
from fastapi import FastAPI, UploadFile, File, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import torch
import numpy as np
from segment_anything import sam_model_registry, SamPredictor, SamAutomaticMaskGenerator
import cv2
import base64
import io
from PIL import Image
import json
import asyncio
from typing import List, Dict, Any
import time

app = FastAPI(title="SAM GPU Server")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configurazione GPU
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

# Carica modello SAM (vit_h per massima qualità)
sam_checkpoint = "/models/sam_vit_h_4b8939.pth"
model_type = "vit_h"

print("Loading SAM model...")
sam = sam_model_registry[model_type](checkpoint=sam_checkpoint)
sam.to(device=device)

# Predictor per interactive segmentation
predictor = SamPredictor(sam)

# Generator per automatic masks
mask_generator = SamAutomaticMaskGenerator(
    model=sam,
    points_per_side=32,
    pred_iou_thresh=0.88,
    stability_score_thresh=0.95,
    crop_n_layers=1,
    crop_n_points_downscale_factor=2,
    min_mask_region_area=100,
)

# Cache per embeddings
embeddings_cache = {}

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: dict):
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except:
                pass

manager = ConnectionManager()

@app.get("/")
async def root():
    return {"status": "SAM GPU Server Running", "device": device}

@app.get("/health")
async def health():
    gpu_available = torch.cuda.is_available()
    gpu_name = torch.cuda.get_device_name(0) if gpu_available else None
    return {
        "status": "healthy",
        "gpu_available": gpu_available,
        "gpu_name": gpu_name,
        "device": device,
        "model_loaded": sam is not None
    }

@app.post("/embed")
async def create_embedding(file: UploadFile = File(...)):
    """Pre-calcola embedding per un'immagine"""
    try:
        start_time = time.time()
        
        # Leggi immagine
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        image_np = np.array(image.convert("RGB"))
        
        # Genera ID univoco per l'embedding
        embedding_id = f"img_{int(time.time() * 1000)}"
        
        # Calcola embedding con SAM
        predictor.set_image(image_np)
        
        # Salva in cache (in produzione usare Redis)
        embeddings_cache[embedding_id] = {
            "features": predictor.features,
            "input_size": predictor.input_size,
            "original_size": predictor.original_size,
            "image": image_np
        }
        
        elapsed = time.time() - start_time
        
        return {
            "embedding_id": embedding_id,
            "processing_time": elapsed,
            "image_size": image_np.shape[:2]
        }
        
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.post("/segment/points")
async def segment_with_points(
    embedding_id: str,
    points: List[List[int]],
    labels: List[int]
):
    """Segmenta usando punti con embedding pre-calcolato"""
    try:
        if embedding_id not in embeddings_cache:
            return JSONResponse(status_code=404, content={"error": "Embedding not found"})
        
        start_time = time.time()
        
        # Recupera embedding dalla cache
        cached = embeddings_cache[embedding_id]
        
        # Ripristina stato predictor
        predictor.features = cached["features"]
        predictor.input_size = cached["input_size"]
        predictor.original_size = cached["original_size"]
        
        # Genera maschere
        input_points = np.array(points)
        input_labels = np.array(labels)
        
        masks, scores, logits = predictor.predict(
            point_coords=input_points,
            point_labels=input_labels,
            multimask_output=True,
        )
        
        # Converti maschere in formato inviabile
        masks_encoded = []
        for mask in masks:
            mask_uint8 = (mask * 255).astype(np.uint8)
            _, buffer = cv2.imencode('.png', mask_uint8)
            mask_base64 = base64.b64encode(buffer).decode('utf-8')
            masks_encoded.append(mask_base64)
        
        elapsed = time.time() - start_time
        
        return {
            "masks": masks_encoded,
            "scores": scores.tolist(),
            "processing_time": elapsed
        }
        
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.post("/segment/box")
async def segment_with_box(
    embedding_id: str,
    box: List[int]  # [x1, y1, x2, y2]
):
    """Segmenta usando bounding box"""
    try:
        if embedding_id not in embeddings_cache:
            return JSONResponse(status_code=404, content={"error": "Embedding not found"})
        
        start_time = time.time()
        
        cached = embeddings_cache[embedding_id]
        
        # Ripristina predictor
        predictor.features = cached["features"]
        predictor.input_size = cached["input_size"]
        predictor.original_size = cached["original_size"]
        
        # Genera maschera
        input_box = np.array(box)
        
        masks, scores, logits = predictor.predict(
            box=input_box,
            multimask_output=False,
        )
        
        # Converti maschera
        mask_uint8 = (masks[0] * 255).astype(np.uint8)
        _, buffer = cv2.imencode('.png', mask_uint8)
        mask_base64 = base64.b64encode(buffer).decode('utf-8')
        
        elapsed = time.time() - start_time
        
        return {
            "mask": mask_base64,
            "score": float(scores[0]),
            "processing_time": elapsed
        }
        
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.post("/segment/auto")
async def segment_automatic(file: UploadFile = File(...)):
    """Segmentazione automatica completa"""
    try:
        start_time = time.time()
        
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        image_np = np.array(image.convert("RGB"))
        
        # Genera tutte le maschere
        masks = mask_generator.generate(image_np)
        
        # Converti maschere
        results = []
        for mask_data in masks:
            mask = mask_data['segmentation']
            mask_uint8 = (mask * 255).astype(np.uint8)
            _, buffer = cv2.imencode('.png', mask_uint8)
            mask_base64 = base64.b64encode(buffer).decode('utf-8')
            
            results.append({
                'mask': mask_base64,
                'area': int(mask_data['area']),
                'bbox': mask_data['bbox'],
                'predicted_iou': float(mask_data['predicted_iou']),
                'stability_score': float(mask_data['stability_score'])
            })
        
        elapsed = time.time() - start_time
        
        return {
            "masks": results,
            "total_masks": len(results),
            "processing_time": elapsed
        }
        
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket per segmentazione real-time"""
    await manager.connect(websocket)
    embedding_id = None
    
    try:
        while True:
            data = await websocket.receive_json()
            
            if data["type"] == "set_image":
                # Ricevi immagine e calcola embedding
                image_data = base64.b64decode(data["image"].split(',')[1])
                image = Image.open(io.BytesIO(image_data))
                image_np = np.array(image.convert("RGB"))
                
                embedding_id = f"ws_{id(websocket)}_{int(time.time() * 1000)}"
                
                predictor.set_image(image_np)
                embeddings_cache[embedding_id] = {
                    "features": predictor.features,
                    "input_size": predictor.input_size,
                    "original_size": predictor.original_size,
                    "image": image_np
                }
                
                await websocket.send_json({
                    "type": "embedding_ready",
                    "embedding_id": embedding_id
                })
                
            elif data["type"] == "add_point" and embedding_id:
                # Segmenta con nuovo punto
                cached = embeddings_cache[embedding_id]
                predictor.features = cached["features"]
                predictor.input_size = cached["input_size"]
                predictor.original_size = cached["original_size"]
                
                points = np.array(data["points"])
                labels = np.array(data["labels"])
                
                masks, scores, _ = predictor.predict(
                    point_coords=points,
                    point_labels=labels,
                    multimask_output=True,
                )
                
                # Invia migliore maschera
                best_idx = np.argmax(scores)
                mask = masks[best_idx]
                mask_uint8 = (mask * 255).astype(np.uint8)
                _, buffer = cv2.imencode('.png', mask_uint8)
                mask_base64 = base64.b64encode(buffer).decode('utf-8')
                
                await websocket.send_json({
                    "type": "mask_update",
                    "mask": mask_base64,
                    "score": float(scores[best_idx])
                })
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        if embedding_id and embedding_id in embeddings_cache:
            del embeddings_cache[embedding_id]

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# Step 9: Crea docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  sam-server:
    build: .
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
    ports:
      - "8000:8000"
    volumes:
      - ./models:/models
      - ./data:/data
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
EOF

# Step 10: Build e avvia
echo "Step 10: Build Docker image..."
docker-compose build

echo "Step 11: Avvio servizio..."
docker-compose up -d

# Step 12: Verifica
echo "Step 12: Verifica servizio..."
sleep 5
curl http://localhost:8000/health

echo ""
echo "=== Setup completato! ==="
echo "SAM Server è disponibile su http://$(hostname -I | cut -d' ' -f1):8000"
echo ""
echo "Endpoints disponibili:"
echo "  - GET  /health          - Status del server"
echo "  - POST /embed           - Pre-calcola embedding"
echo "  - POST /segment/points  - Segmenta con punti"
echo "  - POST /segment/box     - Segmenta con box"
echo "  - POST /segment/auto    - Segmentazione automatica"
echo "  - WS   /ws              - WebSocket per real-time"
echo ""
echo "Per vedere i log: docker-compose logs -f"