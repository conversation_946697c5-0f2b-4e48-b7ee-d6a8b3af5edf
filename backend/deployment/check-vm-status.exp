#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Check VM status
send "qm list\r"
expect "# "

# Check VM config
send "qm config 200 2>/dev/null | head -20\r"
expect "# "

# Check if ISO download completed
send "ls -lh /var/lib/vz/template/iso/ | grep ubuntu\r"
expect "# "

# If VM exists, start it
send "qm status 200 2>/dev/null\r"
expect "# "

# Get VM IP if running
send "qm guest cmd 200 network-get-interfaces 2>/dev/null | grep ip-address || echo 'VM not running or agent not installed'\r"
expect "# "

send "exit\r"
expect eof