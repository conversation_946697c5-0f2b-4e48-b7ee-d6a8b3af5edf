#!/usr/bin/expect -f

set timeout 300
set host "*************"  
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Download Ubuntu ISO da mirror veloce
send "cd /var/lib/vz/template/iso/\r"
expect "# "

# Usa un mirror più veloce e versione più recente
send "wget -c https://releases.ubuntu.com/24.04.1/ubuntu-24.04.1-live-server-amd64.iso\r"
expect {
    "100%" { }
    timeout { send "\003"; expect "# " }
}

expect "# "

# Crea VM
send "qm create 200 --name sam-gpu-server --memory 8192 --cores 4 --cpu host --ostype l26 --scsihw virtio-scsi-pci --scsi0 local-lvm:50 --ide2 local:iso/ubuntu-24.04.1-live-server-amd64.iso,media=cdrom --net0 virtio,bridge=vmbr0 --boot order=scsi0 --machine q35 --bios ovmf --efidisk0 local-lvm:1\r"
expect "# "

# Aggiungi GPU
send "qm set 200 -hostpci0 07:00,pcie=1,x-vga=1\r"
expect "# "

# Start VM
send "qm start 200\r"
expect "# "

# Mostra status
send "qm status 200\r"
expect "# "

send "exit\r"
expect eof