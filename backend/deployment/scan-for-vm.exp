#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== Scanning for VM on Network ==="

# Get VM MAC address
send "qm config 202 | grep net0 | cut -d'=' -f2 | cut -d',' -f1\r"
expect -re "(\[0-9A-F:\]+)"
set mac $expect_out(1,string)
expect "# "
puts "VM MAC Address: $mac"

# Scan network for this MAC
send "arp-scan -l 2>/dev/null | grep -i '$mac' || ip neigh | grep -i '$mac'\r"
expect "# "

# Try common VM IPs
puts "\nTrying common IPs..."
send "for ip in 192.168.0.{100..120}; do ping -c 1 -W 1 \$ip >/dev/null 2>&1 && echo \"Found: \$ip\"; done\r"
expect "# "

# Check DHCP leases
send "cat /etc/dnsmasq.d/* 2>/dev/null | grep -A2 -B2 '52:54' || echo 'No DHCP config found'\r"
expect "# "

# Try to scan with nmap for SSH
send "which nmap >/dev/null 2>&1 && nmap -p22 *************-120 --open -oG - | grep 22/open || echo 'nmap not available'\r"
expect "# "

puts "\n========================================="
puts "Installation Status:"
puts "- Ubuntu Server ISO is mounted (no GUI)"
puts "- Video output is on physical GPU (RTX 3070)"
puts "- If installation completed, VM should have IP via DHCP"
puts ""
puts "Options:"
puts "1. Check monitor connected to RTX 3070"
puts "2. Wait and re-run this script to find VM IP"
puts "3. Reinstall with standard VGA for console access"
puts "========================================="

send "exit\r"
expect eof