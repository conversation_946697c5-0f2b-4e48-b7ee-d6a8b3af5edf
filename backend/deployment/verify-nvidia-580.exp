#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== VERIFYING NVIDIA DRIVER 580 ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Check nvidia-smi
send "nvidia-smi\r"
expect "$ "

# Check driver version
send "nvidia-smi --query-gpu=name,driver_version --format=csv\r"
expect "$ "

# Check CUDA availability
send "nvidia-smi -L\r"
expect "$ "

send "exit\r"
expect eof