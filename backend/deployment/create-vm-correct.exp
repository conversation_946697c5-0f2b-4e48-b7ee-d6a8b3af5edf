#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== CREATING VM 200 WITH CORRECT SYNTAX ==="

# Create VM with correct boot syntax
send "qm create 200 --name ubuntu-sam --memory 16384 --cores 6 --cpu host --ostype l26 --scsihw virtio-scsi-pci --scsi0 local-lvm:100 --ide2 local:iso/ubuntu-24.04.1-live-server-amd64.iso,media=cdrom --net0 virtio,bridge=vmbr0 --boot c --bootdisk scsi0 --machine q35 --bios ovmf --efidisk0 local-lvm:1\r"
expect "# "

# Add GPU passthrough
send "qm set 200 -hostpci0 07:00,pcie=1,x-vga=1\r"
expect "# "

# Set boot priority to CD first
send "qm set 200 --boot order=ide2\\;scsi0\r"
expect "# "

# Optimize settings
send "qm set 200 --numa 1\r"
expect "# "
send "qm set 200 --balloon 0\r"
expect "# "

# Start VM
send "qm start 200\r"
expect "# "

# Check status
send "qm status 200\r"
expect "# "

puts "\n========================================="
puts "✅ VM 200 CREATED AND STARTED!"
puts "========================================="
puts ""
puts "VM is now running with GPU as primary display"
puts "Ubuntu installer should appear on GPU monitor"
puts ""
puts "If you need to reboot Proxmox to see output:"
puts "  ssh root@*************"
puts "  reboot"
puts "========================================="

send "exit\r"
expect eof