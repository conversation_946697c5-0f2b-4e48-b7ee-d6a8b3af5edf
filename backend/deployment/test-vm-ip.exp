#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== Testing VM at ************* ==="

# Check if our MAC matches
send "qm config 202 | grep net0 | grep -o 'BC:24:11:46:8C:0C'\r"
expect "# "

# Try SSH to *************
send "ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no ubuntu@************* 'hostname' || echo 'SSH failed'\r"
expect {
    "password:" {
        puts "\n✅ FOUND VM! Ubuntu is installed at *************"
        puts "Username: ubuntu"
        puts "Password: ubuntu (if you set it during installation)"
        send "\003"  # Ctrl+C
    }
    "SSH failed" {
        puts "\n❌ Not our VM or SSH not ready"
    }
    timeout {
        puts "\n⏳ Connection timeout"
    }
}

expect "# "

# Check if port 22 is open
send "nc -zv ************* 22 2>&1\r"
expect "# "

puts "\n========================================="
puts "VM Status:"
puts "IP: ************* (if MAC matches)"
puts "Next step: SSH ubuntu@*************"
puts "========================================="

send "exit\r"
expect eof