#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== CREATING SAM SEGMENTATION CONTAINER ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Create directory for SAM
send "mkdir -p ~/sam-server\r"
expect "$ "

send "cd ~/sam-server\r"
expect "$ "

# Create Dockerfile for SAM
send "cat > Dockerfile << 'EOF'\r"
send "FROM nvidia/cuda:12.0.0-runtime-ubuntu22.04\r"
send "\r"
send "# Install Python and dependencies\r"
send "RUN apt-get update && apt-get install -y \\\r"
send "    python3-pip \\\r"
send "    python3-dev \\\r"
send "    git \\\r"
send "    wget \\\r"
send "    && rm -rf /var/lib/apt/lists/*\r"
send "\r"
send "# Install PyTorch with CUDA support\r"
send "RUN pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cu118\r"
send "\r"
send "# Install SAM\r"
send "RUN pip3 install git+https://github.com/facebookresearch/segment-anything.git\r"
send "RUN pip3 install opencv-python-headless pillow flask flask-cors\r"
send "\r"
send "# Download SAM models\r"
send "RUN mkdir -p /models && cd /models && \\\r"
send "    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth && \\\r"
send "    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth && \\\r"
send "    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth\r"
send "\r"
send "# Create app directory\r"
send "WORKDIR /app\r"
send "\r"
send "# Copy server script (will create next)\r"
send "COPY sam_server.py /app/\r"
send "\r"
send "EXPOSE 8080\r"
send "\r"
send "CMD \[\"python3\", \"sam_server.py\"\]\r"
send "EOF\r"
expect "$ "

# Create SAM server script
send "cat > sam_server.py << 'EOF'\r"
send "#!/usr/bin/env python3\r"
send "import torch\r"
send "from segment_anything import sam_model_registry, SamPredictor\r"
send "from flask import Flask, request, jsonify\r"
send "from flask_cors import CORS\r"
send "import base64\r"
send "import numpy as np\r"
send "from PIL import Image\r"
send "import io\r"
send "\r"
send "app = Flask(__name__)\r"
send "CORS(app)\r"
send "\r"
send "# Load SAM model\r"
send "print(\"Loading SAM model...\")\r"
send "sam_checkpoint = \"/models/sam_vit_h_4b8939.pth\"\r"
send "model_type = \"vit_h\"\r"
send "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\r"
send "print(f\"Using device: {device}\")\r"
send "\r"
send "sam = sam_model_registry\[model_type\](checkpoint=sam_checkpoint)\r"
send "sam.to(device=device)\r"
send "predictor = SamPredictor(sam)\r"
send "print(\"Model loaded successfully!\")\r"
send "\r"
send "@app.route('/health', methods=\['GET'\])\r"
send "def health():\r"
send "    return jsonify({\"status\": \"healthy\", \"device\": device})\r"
send "\r"
send "@app.route('/segment', methods=\['POST'\])\r"
send "def segment():\r"
send "    try:\r"
send "        data = request.json\r"
send "        image_b64 = data\['image'\]\r"
send "        points = data.get('points', \[\])\r"
send "        \r"
send "        # Decode image\r"
send "        image_data = base64.b64decode(image_b64)\r"
send "        image = Image.open(io.BytesIO(image_data))\r"
send "        image_np = np.array(image)\r"
send "        \r"
send "        # Set image\r"
send "        predictor.set_image(image_np)\r"
send "        \r"
send "        # Predict masks\r"
send "        if points:\r"
send "            input_points = np.array(points)\r"
send "            input_labels = np.ones(len(points))\r"
send "            masks, scores, logits = predictor.predict(\r"
send "                point_coords=input_points,\r"
send "                point_labels=input_labels,\r"
send "                multimask_output=True,\r"
send "            )\r"
send "        else:\r"
send "            # Auto-generate masks\r"
send "            masks, scores, logits = predictor.predict(\r"
send "                point_coords=None,\r"
send "                point_labels=None,\r"
send "                multimask_output=True,\r"
send "            )\r"
send "        \r"
send "        # Convert masks to base64\r"
send "        masks_b64 = \[\]\r"
send "        for mask in masks:\r"
send "            mask_img = Image.fromarray((mask * 255).astype(np.uint8))\r"
send "            buffer = io.BytesIO()\r"
send "            mask_img.save(buffer, format='PNG')\r"
send "            mask_b64 = base64.b64encode(buffer.getvalue()).decode()\r"
send "            masks_b64.append(mask_b64)\r"
send "        \r"
send "        return jsonify({\r"
send "            \"masks\": masks_b64,\r"
send "            \"scores\": scores.tolist()\r"
send "        })\r"
send "    except Exception as e:\r"
send "        return jsonify({\"error\": str(e)}), 500\r"
send "\r"
send "if __name__ == '__main__':\r"
send "    app.run(host='0.0.0.0', port=8080)\r"
send "EOF\r"
expect "$ "

# Build Docker image
send "docker build -t sam-server:latest .\r"
expect {
    "$ " { puts "\nDocker image built successfully" }
    timeout { puts "\nBuild in progress..."; exp_continue }
}

# Run container
send "docker run -d --gpus all -p 8080:8080 --name sam-server --restart=always sam-server:latest\r"
expect "$ "

send "sleep 5\r"
expect "$ "

# Check if running
send "docker ps | grep sam-server\r"
expect "$ "

send "echo ''\r"
expect "$ "
send "echo '=== SAM SERVER DEPLOYED ==='\r"
expect "$ "
send "echo 'API endpoint: http://*************:8080'\r"
expect "$ "
send "echo 'Health check: http://*************:8080/health'\r"
expect "$ "
send "echo 'Segment endpoint: POST http://*************:8080/segment'\r"
expect "$ "

send "exit\r"
expect eof