#!/usr/bin/expect -f

# Script per aspettare che la VM sia pronta dopo installazione
set timeout 1800
set host "*************"
set user "root"
set password "Al0xan999"

puts "Waiting for Ubuntu installation to complete..."
puts "This may take 10-15 minutes..."

while {1} {
    spawn ssh $user@$host
    
    expect {
        "yes/no" { send "yes\r"; exp_continue }
        "*?assword:" { send "$password\r" }
    }
    
    expect "# "
    
    # Try to get VM IP via guest agent
    send "qm agent 202 network-get-interfaces 2>/dev/null | grep -A1 enp6s18 | grep '\"ip-address\"' | head -1 | cut -d'\"' -f4\r"
    expect {
        -re "(\[0-9\]+\\.\[0-9\]+\\.\[0-9\]+\\.\[0-9\]+)" {
            set vm_ip $expect_out(1,string)
            puts "\n=========================================="
            puts "VM IS READY!"
            puts "VM IP Address: $vm_ip"
            puts "=========================================="
            send "exit\r"
            expect eof
            break
        }
        "# " {
            puts -nonewline "."
            flush stdout
        }
    }
    
    send "exit\r"
    expect eof
    
    # Wait 30 seconds before next check
    sleep 30
}

puts "\nNow you can SSH to the VM:"
puts "ssh ubuntu@$vm_ip"
puts "Password: ubuntu"