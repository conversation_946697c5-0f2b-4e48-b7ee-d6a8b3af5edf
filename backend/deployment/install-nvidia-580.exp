#!/usr/bin/expect -f

set timeout 600
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== INSTALLING NVIDIA DRIVER 580.76.05 FOR RTX 5080 ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Remove old drivers first
send "sudo apt remove --purge -y nvidia-* libnvidia-*\r"
expect "*?assword*" { send "$password\r" }
expect "$ "

send "sudo apt autoremove -y\r"
expect "$ "

# Install prerequisites
send "sudo apt update\r"
expect "$ "

send "sudo apt install -y build-essential dkms linux-headers-\$(uname -r) pkg-config libglvnd-dev\r"
expect "$ "

# Download NVIDIA driver
send "cd /tmp\r"
expect "$ "

send "wget https://us.download.nvidia.com/XFree86/Linux-x86_64/580.76.05/NVIDIA-Linux-x86_64-580.76.05.run\r"
expect {
    "$ " { puts "\nDownload complete" }
    timeout { puts "\nDownload in progress..."; exp_continue }
}

# Make it executable
send "chmod +x NVIDIA-Linux-x86_64-580.76.05.run\r"
expect "$ "

# Disable nouveau driver
send "sudo bash -c 'echo blacklist nouveau > /etc/modprobe.d/blacklist-nouveau.conf'\r"
expect "$ "

send "sudo bash -c 'echo options nouveau modeset=0 >> /etc/modprobe.d/blacklist-nouveau.conf'\r"
expect "$ "

send "sudo update-initramfs -u\r"
expect "$ "

# Install the driver
send "sudo ./NVIDIA-Linux-x86_64-580.76.05.run --silent --dkms\r"
expect {
    "$ " { puts "\nDriver installation complete" }
    timeout { puts "\nInstallation in progress..."; exp_continue }
}

# Reboot
send "echo 'Driver installed! Rebooting...'\r"
expect "$ "

send "sudo reboot\r"
expect eof