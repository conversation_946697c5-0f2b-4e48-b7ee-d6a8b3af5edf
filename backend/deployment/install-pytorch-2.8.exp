#!/usr/bin/expect -f

set timeout 600
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== INSTALLING PYTORCH 2.8 WITH NATIVE RTX 5080 SUPPORT ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Stop any running containers
send "docker stop sam-server 2>/dev/null || true\r"
expect "$ "
send "docker rm sam-server 2>/dev/null || true\r"
expect "$ "

send "cd ~/sam-server\r"
expect "$ "

# Create optimized Dockerfile with PyTorch 2.8
send "cat > Dockerfile << 'EOF'\r"
send "# Use CUDA 12.8 for RTX 5080 support\r"
send "FROM nvidia/cuda:12.8.0-cudnn-runtime-ubuntu22.04\r"
send "\r"
send "# Install Python 3.11 and dependencies\r"
send "RUN apt-get update && apt-get install -y \\\r"
send "    python3.11 \\\r"
send "    python3.11-dev \\\r"
send "    python3-pip \\\r"
send "    git \\\r"
send "    wget \\\r"
send "    curl \\\r"
send "    && rm -rf /var/lib/apt/lists/*\r"
send "\r"
send "# Set Python 3.11 as default\r"
send "RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1\r"
send "\r"
send "# Upgrade pip\r"
send "RUN python3 -m pip install --upgrade pip\r"
send "\r"
send "# Install PyTorch 2.8 with CUDA 12.8 support\r"
send "# Using the latest stable release that supports RTX 5080 (sm_120)\r"
send "RUN pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128\r"
send "\r"
send "# Install SAM and dependencies\r"
send "RUN pip3 install git+https://github.com/facebookresearch/segment-anything.git\r"
send "RUN pip3 install opencv-python-headless pillow flask flask-cors numpy scipy\r"
send "\r"
send "# Download SAM models\r"
send "RUN mkdir -p /models && cd /models && \\\r"
send "    wget -q https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth && \\\r"
send "    echo 'Model downloaded successfully'\r"
send "\r"
send "# Create app directory\r"
send "WORKDIR /app\r"
send "\r"
send "# Copy optimized server script\r"
send "COPY sam_server.py /app/\r"
send "\r"
send "EXPOSE 8080\r"
send "\r"
send "ENV PYTHONUNBUFFERED=1\r"
send "ENV TORCH_CUDA_ARCH_LIST='8.0;8.6;8.9;9.0;12.0'\r"
send "ENV CUDA_VISIBLE_DEVICES=0\r"
send "\r"
send "CMD \[\"python3\", \"sam_server.py\"\]\r"
send "EOF\r"
expect "$ "

# Create test script to verify PyTorch version
send "cat > test_pytorch.py << 'EOF'\r"
send "#!/usr/bin/env python3\r"
send "import torch\r"
send "print(f\"PyTorch version: {torch.__version__}\")\r"
send "print(f\"CUDA available: {torch.cuda.is_available()}\")\r"
send "if torch.cuda.is_available():\r"
send "    print(f\"CUDA version: {torch.version.cuda}\")\r"
send "    print(f\"GPU: {torch.cuda.get_device_name(0)}\")\r"
send "    print(f\"GPU compute capability: {torch.cuda.get_device_capability(0)}\")\r"
send "    # Test tensor operation on GPU\r"
send "    x = torch.randn(1000, 1000).cuda()\r"
send "    y = torch.randn(1000, 1000).cuda()\r"
send "    z = torch.matmul(x, y)\r"
send "    print(f\"Test tensor operation successful: {z.shape}\")\r"
send "EOF\r"
expect "$ "

# Build the optimized image
send "echo 'Building Docker image with PyTorch 2.8...'\r"
expect "$ "

send "docker build -t sam-server:pytorch28 --no-cache .\r"
expect {
    "$ " { puts "\nDocker build completed" }
    timeout { puts "\nBuild in progress..."; exp_continue }
}

# Test PyTorch in container first
send "docker run --rm --gpus all sam-server:pytorch28 python3 -c \"import torch; print(f'PyTorch {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else None}')\"\r"
expect "$ "

# Run the SAM server
send "docker run -d --gpus all -p 8080:8080 --name sam-server --restart=always sam-server:pytorch28\r"
expect "$ "

send "sleep 10\r"
expect "$ "

# Check logs
send "docker logs sam-server --tail 30\r"
expect "$ "

# Test health endpoint
send "curl -s http://localhost:8080/health | python3 -m json.tool\r"
expect "$ "

send "echo ''\r"
expect "$ "
send "echo '=== PYTORCH 2.8 WITH RTX 5080 SUPPORT INSTALLED ==='\r"
expect "$ "

send "exit\r"
expect eof