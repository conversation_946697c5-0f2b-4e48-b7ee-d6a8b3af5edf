#!/usr/bin/expect -f

set timeout 10
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Check download status
send "ps aux | grep wget | grep -v grep\r"
expect "# "

# Check file size
send "ls -lh /var/lib/vz/template/iso/ | grep ubuntu-24\r"
expect "# "

# Check VM exists
send "qm list\r"
expect "# "

send "exit\r"
expect eof