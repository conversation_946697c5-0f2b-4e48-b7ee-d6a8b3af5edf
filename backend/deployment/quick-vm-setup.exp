#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== QUICK VM SETUP WITH GPU ==="

# Create VM 200
send "qm create 200 --name ubuntu-sam --memory 16384 --cores 6 --cpu host --ostype l26 --scsihw virtio-scsi-pci --scsi0 local-lvm:100 --ide2 local:iso/ubuntu-24.04.1-live-server-amd64.iso,media=cdrom --net0 virtio,bridge=vmbr0 --boot order=ide2,scsi0 --machine q35 --bios ovmf --efidisk0 local-lvm:1\r"
expect "# "

# Add GPU
send "qm set 200 -hostpci0 07:00,pcie=1,x-vga=1\r"
expect "# "

# Optimize
send "qm set 200 --numa 1 --balloon 0\r"
expect "# "

# Start VM
send "qm start 200\r"
expect "# "

puts "\n✅ VM 200 Created!"
puts "\nREBOOTING PROXMOX NOW..."
puts "After reboot, Ubuntu installer will appear on GPU monitor"

# Reboot immediately
send "reboot\r"
expect eof