#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "alin"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Check Docker images
send "sudo docker images | grep jupyter\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

# Check running containers
send "sudo docker ps -a | grep jupyter\r"
expect "$ "

# Check build processes
send "ps aux | grep docker | grep build\r"
expect "$ "

# Check disk usage
send "df -h /\r"
expect "$ "

send "exit\r"
expect eof