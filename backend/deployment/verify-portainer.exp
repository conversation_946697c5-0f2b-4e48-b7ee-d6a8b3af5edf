#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== VERIFYING PORTAINER AND GPU SETUP ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Check Portainer is running
send "docker ps | grep portainer\r"
expect "$ "

# Test GPU in Docker
send "docker run --rm --gpus all nvidia/cuda:12.0.0-base-ubuntu22.04 nvidia-smi\r"
expect "$ "

# Get Portainer URL
send "echo ''\r"
expect "$ "
send "echo '=== ACCESS PORTAINER ==='\r"
expect "$ "
send "echo 'Portainer WebUI: https://*************:9443'\r"
expect "$ "
send "echo 'First access: Create admin user'\r"
expect "$ "
send "echo 'GPU containers: Use --gpus all flag'\r"
expect "$ "

send "exit\r"
expect eof