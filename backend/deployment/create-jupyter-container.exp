#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== CREATING JUPYTER ML CONTAINER ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Create directory for Jupy<PERSON>
send "mkdir -p ~/jupyter-ml\r"
expect "$ "

send "cd ~/jupyter-ml\r"
expect "$ "

# Create Dockerfile for Jupyter with ML libraries
send "cat > Dockerfile << 'EOF'\r"
send "FROM nvidia/cuda:12.0.0-cudnn8-runtime-ubuntu22.04\r"
send "\r"
send "# Install Python and system dependencies\r"
send "RUN apt-get update && apt-get install -y \\\r"
send "    python3-pip \\\r"
send "    python3-dev \\\r"
send "    git \\\r"
send "    wget \\\r"
send "    curl \\\r"
send "    && rm -rf /var/lib/apt/lists/*\r"
send "\r"
send "# Install Jupyter\r"
send "RUN pip3 install jupyter jupyterlab notebook ipywidgets\r"
send "\r"
send "# Install PyTorch with CUDA support for RTX 5080\r"
send "RUN pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121\r"
send "\r"
send "# Install ML and Computer Vision libraries\r"
send "RUN pip3 install numpy pandas matplotlib seaborn plotly\r"
send "RUN pip3 install opencv-python-headless pillow scikit-learn\r"
send "RUN pip3 install segmentation-models-pytorch albumentations\r"
send "RUN pip3 install transformers datasets huggingface-hub\r"
send "\r"
send "# Install FastAPI for testing\r"
send "RUN pip3 install fastapi uvicorn python-multipart\r"
send "\r"
send "# Install satellite imagery libraries\r"
send "RUN pip3 install rasterio geopandas folium\r"
send "\r"
send "# Create workspace\r"
send "RUN mkdir -p /workspace/notebooks /workspace/data /workspace/models\r"
send "WORKDIR /workspace\r"
send "\r"
send "# Expose Jupyter port\r"
send "EXPOSE 8888\r"
send "\r"
send "# Start Jupyter Lab\r"
send "CMD \[\"jupyter\", \"lab\", \"--ip=0.0.0.0\", \"--port=8888\", \"--no-browser\", \"--allow-root\", \"--NotebookApp.token=''\", \"--NotebookApp.password=''\"\]\r"
send "EOF\r"
expect "$ "

# Build the Docker image
puts "Building Jupyter Docker image..."
send "sudo docker build -t jupyter-ml:latest .\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "Successfully built" {
        puts "Docker image built successfully!"
    }
    timeout {
        puts "Build is taking time, please wait..."
        exp_continue
    }
}

# Wait for build to complete
expect "$ "

# Stop any existing Jupyter container
send "sudo docker stop jupyter-ml 2>/dev/null || true\r"
expect "$ "

send "sudo docker rm jupyter-ml 2>/dev/null || true\r"
expect "$ "

# Create directories for volumes
send "mkdir -p notebooks data models outputs\r"
expect "$ "

# Run the Jupyter container with GPU
puts "Starting Jupyter container with GPU support..."
send "sudo docker run -d \\\r"
send "  --name jupyter-ml \\\r"
send "  --gpus all \\\r"
send "  --restart unless-stopped \\\r"
send "  -p 8888:8888 \\\r"
send "  -v \$(pwd)/notebooks:/workspace/notebooks \\\r"
send "  -v \$(pwd)/data:/workspace/data \\\r"
send "  -v \$(pwd)/models:/workspace/models \\\r"
send "  jupyter-ml:latest\r"
expect "$ "

# Wait for container to start
send "sleep 5\r"
expect "$ "

# Check if container is running
send "sudo docker ps | grep jupyter-ml\r"
expect {
    "jupyter-ml" {
        puts "\n✓ Jupyter container is running!"
    }
    "$ " {
        puts "\n⚠ Container might not be running"
        send "sudo docker logs jupyter-ml\r"
        expect "$ "
    }
}

# Test GPU access
puts "\nTesting GPU access in Jupyter container..."
send "sudo docker exec jupyter-ml python3 -c \"import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0)}' if torch.cuda.is_available() else 'No GPU detected')\"\r"
expect "$ "

# Show container info
send "sudo docker inspect jupyter-ml | grep IPAddress\r"
expect "$ "

puts "\n=== JUPYTER ML CONTAINER READY ==="
puts "Access Jupyter Lab at: http://$host:8888"
puts "No password required (development mode)"
puts ""
puts "Features:"
puts "- PyTorch with CUDA support for RTX 5080"
puts "- Computer Vision libraries (OpenCV, Albumentations)"
puts "- Segmentation models (DeepLab, U-Net)"
puts "- Transformers for modern ML models"
puts "- Satellite imagery tools (Rasterio, GeoPandas)"
puts ""
puts "You can also manage this container in Portainer at:"
puts "https://$host:9443"

send "exit\r"
expect eof