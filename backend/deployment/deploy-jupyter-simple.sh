#!/bin/bash

# Simple Jupyter deployment script for VM 200
HOST="*************"
USER="alin"

echo "=== DEPLOYING JUPYTER WITH GPU ==="

# SSH command to execute on remote
ssh $USER@$HOST << 'ENDSSH'

# Create directory
mkdir -p ~/jupyter-ml
cd ~/jupyter-ml

# Stop and remove existing container
sudo docker stop jupyter-ml 2>/dev/null || true
sudo docker rm jupyter-ml 2>/dev/null || true

# Create notebooks directory
mkdir -p notebooks data models outputs

# Run Jupyter with GPU support using official image
echo "Starting Jupyter container..."
sudo docker run -d \
  --name jupyter-ml \
  --restart unless-stopped \
  --gpus all \
  -p 8888:8888 \
  -v $(pwd)/notebooks:/home/<USER>/work/notebooks \
  -v $(pwd)/data:/home/<USER>/work/data \
  -v $(pwd)/models:/home/<USER>/work/models \
  -v $(pwd)/outputs:/home/<USER>/work/outputs \
  -e JUPYTER_ENABLE_LAB=yes \
  -e GRANT_SUDO=yes \
  jupyter/tensorflow-notebook \
  start-notebook.sh --NotebookApp.token='' --NotebookApp.password=''

# Wait for container to start
sleep 10

# Install additional packages in running container
echo "Installing ML packages..."
sudo docker exec jupyter-ml pip install --no-cache-dir \
  torch torchvision --index-url https://download.pytorch.org/whl/cu121 \
  opencv-python \
  albumentations \
  segmentation-models-pytorch \
  transformers \
  fastapi \
  uvicorn

# Check status
echo ""
echo "Checking container status..."
sudo docker ps | grep jupyter-ml

# Test GPU
echo ""
echo "Testing GPU access..."
sudo docker exec jupyter-ml python -c "import torch; print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0)}' if torch.cuda.is_available() else 'No GPU')"

echo ""
echo "=== JUPYTER READY ==="
echo "Access at: http://$HOST:8888"
echo "No password required"

ENDSSH