#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== TESTING DEEPLAB IN JUPYTER ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Test DeepLab
puts "Testing DeepLab model..."
send {sudo docker exec jupyter-ml python3 -c "import torch; import torchvision; print('PyTorch:', torch.__version__); print('CUDA:', torch.cuda.is_available()); model = torchvision.models.segmentation.deeplabv3_resnet101(weights='DEFAULT'); model.eval(); print('DeepLab loaded successfully!'); test = torch.randn(1,3,224,224); out = model(test); print('Test passed! Output shape:', dict(out)['out'].shape)"}
send "\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

puts "\n=== DEEPLAB TEST COMPLETE ==="

send "exit\r"
expect eof