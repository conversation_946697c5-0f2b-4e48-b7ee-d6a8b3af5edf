#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== SETTING UP COMPLETE SEGMENTATION SYSTEM ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Create comprehensive segmentation notebook
puts "Creating complete segmentation setup notebook..."

send "cat > ~/jupyter-ml/notebooks/complete_segmentation_setup.py << 'EOF'\r"
send "#!/usr/bin/env python3\r"
send "\"\"\"\r"
send "Complete Satellite Image Segmentation Setup\r"
send "RTX 5080 + PyTorch 2.8 + CUDA 12.8\r"
send "Includes: SAM, DeepLab, U-Net for satellite imagery\r"
send "\"\"\"\r"
send "\r"
send "import os\r"
send "import sys\r"
send "import torch\r"
send "import warnings\r"
send "warnings.filterwarnings('ignore')\r"
send "\r"
send "print('=' * 60)\r"
send "print('SATELLITE IMAGE SEGMENTATION SETUP')\r"
send "print('=' * 60)\r"
send "\r"
send "# Check GPU\r"
send "print(f'\\nPyTorch: {torch.__version__}')\r"
send "print(f'CUDA: {torch.cuda.is_available()}')\r"
send "if torch.cuda.is_available():\r"
send "    print(f'GPU: {torch.cuda.get_device_name(0)}')\r"
send "    print(f'Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')\r"
send "\r"
send "# Install required packages\r"
send "print('\\nInstalling required packages...')\r"
send "import subprocess\r"
send "\r"
send "packages = [\r"
send "    'segment-anything',\r"
send "    'opencv-python',\r"
send "    'rasterio',\r"
send "    'geopandas',\r"
send "    'matplotlib',\r"
send "    'ipywidgets',\r"
send "    'gradio',\r"
send "    'fastapi',\r"
send "    'uvicorn'\r"
send "]\r"
send "\r"
send "for package in packages:\r"
send "    try:\r"
send "        __import__(package.replace('-', '_'))\r"
send "        print(f'✓ {package} already installed')\r"
send "    except ImportError:\r"
send "        print(f'Installing {package}...')\r"
send "        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package, '-q'])\r"
send "\r"
send "print('\\n✅ All packages installed!')\r"
send "EOF\r"
expect "$ "

# Run the setup
send "sudo docker exec jupyter-ml python3 /workspace/notebooks/complete_segmentation_setup.py\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

# Create the main segmentation notebook
puts "\nCreating main segmentation notebook..."

send "cat > ~/jupyter-ml/notebooks/satellite_segmentation.ipynb << 'EOF'\r"
send "{\r"
send " \"cells\": [\r"
send "  {\r"
send "   \"cell_type\": \"markdown\",\r"
send "   \"metadata\": {},\r"
send "   \"source\": [\r"
send "    \"# Satellite Image Segmentation System\\n\",\r"
send "    \"Complete setup for SAM, DeepLab, and U-Net on RTX 5080\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"code\",\r"
send "   \"execution_count\": null,\r"
send "   \"metadata\": {},\r"
send "   \"outputs\": [],\r"
send "   \"source\": [\r"
send "    \"import torch\\n\",\r"
send "    \"import torchvision\\n\",\r"
send "    \"import numpy as np\\n\",\r"
send "    \"from PIL import Image\\n\",\r"
send "    \"import matplotlib.pyplot as plt\\n\",\r"
send "    \"import cv2\\n\",\r"
send "    \"\\n\",\r"
send "    \"# GPU Check\\n\",\r"
send "    \"device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\\n\",\r"
send "    \"print(f'Using device: {device}')\\n\",\r"
send "    \"if device.type == 'cuda':\\n\",\r"
send "    \"    print(f'GPU: {torch.cuda.get_device_name(0)}')\\n\",\r"
send "    \"    print(f'Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"markdown\",\r"
send "   \"metadata\": {},\r"
send "   \"source\": [\r"
send "    \"## 1. DeepLabV3+ for Satellite Segmentation\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"code\",\r"
send "   \"execution_count\": null,\r"
send "   \"metadata\": {},\r"
send "   \"outputs\": [],\r"
send "   \"source\": [\r"
send "    \"# Load DeepLabV3+\\n\",\r"
send "    \"model_deeplabv3 = torchvision.models.segmentation.deeplabv3_resnet101(\\n\",\r"
send "    \"    pretrained=True\\n\",\r"
send "    \").to(device)\\n\",\r"
send "    \"model_deeplabv3.eval()\\n\",\r"
send "    \"\\n\",\r"
send "    \"print('DeepLabV3+ loaded successfully!')\\n\",\r"
send "    \"\\n\",\r"
send "    \"def segment_with_deeplab(image_path):\\n\",\r"
send "    \"    from torchvision import transforms\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    # Load and preprocess\\n\",\r"
send "    \"    image = Image.open(image_path).convert('RGB')\\n\",\r"
send "    \"    preprocess = transforms.Compose([\\n\",\r"
send "    \"        transforms.ToTensor(),\\n\",\r"
send "    \"        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\\n\",\r"
send "    \"    ])\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    input_tensor = preprocess(image).unsqueeze(0).to(device)\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    # Inference\\n\",\r"
send "    \"    with torch.no_grad():\\n\",\r"
send "    \"        output = model_deeplabv3(input_tensor)['out'][0]\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    output_predictions = output.argmax(0).cpu().numpy()\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    # Visualize\\n\",\r"
send "    \"    fig, axes = plt.subplots(1, 3, figsize=(15, 5))\\n\",\r"
send "    \"    axes[0].imshow(image)\\n\",\r"
send "    \"    axes[0].set_title('Original')\\n\",\r"
send "    \"    axes[0].axis('off')\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    axes[1].imshow(output_predictions, cmap='jet')\\n\",\r"
send "    \"    axes[1].set_title('DeepLab Segmentation')\\n\",\r"
send "    \"    axes[1].axis('off')\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    axes[2].imshow(image)\\n\",\r"
send "    \"    axes[2].imshow(output_predictions, alpha=0.5, cmap='jet')\\n\",\r"
send "    \"    axes[2].set_title('Overlay')\\n\",\r"
send "    \"    axes[2].axis('off')\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    plt.tight_layout()\\n\",\r"
send "    \"    plt.show()\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    return output_predictions\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"markdown\",\r"
send "   \"metadata\": {},\r"
send "   \"source\": [\r"
send "    \"## 2. SAM Integration (Already Running on Port 8080)\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"code\",\r"
send "   \"execution_count\": null,\r"
send "   \"metadata\": {},\r"
send "   \"outputs\": [],\r"
send "   \"source\": [\r"
send "    \"import requests\\n\",\r"
send "    \"import base64\\n\",\r"
send "    \"from io import BytesIO\\n\",\r"
send "    \"\\n\",\r"
send "    \"def segment_with_sam(image_path, points=None):\\n\",\r"
send "    \"    # Read image\\n\",\r"
send "    \"    with open(image_path, 'rb') as f:\\n\",\r"
send "    \"        image_bytes = f.read()\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    # Encode to base64\\n\",\r"
send "    \"    image_b64 = base64.b64encode(image_bytes).decode()\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    # Call SAM server\\n\",\r"
send "    \"    response = requests.post(\\n\",\r"
send "    \"        'http://localhost:8080/segment',\\n\",\r"
send "    \"        json={'image': image_b64, 'points': points or []}\\n\",\r"
send "    \"    )\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    if response.status_code == 200:\\n\",\r"
send "    \"        result = response.json()\\n\",\r"
send "    \"        masks = result['masks']\\n\",\r"
send "    \"        scores = result['scores']\\n\",\r"
send "    \"        \\n\",\r"
send "    \"        # Decode first mask\\n\",\r"
send "    \"        mask_bytes = base64.b64decode(masks[0])\\n\",\r"
send "    \"        mask_image = Image.open(BytesIO(mask_bytes))\\n\",\r"
send "    \"        \\n\",\r"
send "    \"        return np.array(mask_image), scores\\n\",\r"
send "    \"    else:\\n\",\r"
send "    \"        print(f'SAM Error: {response.status_code}')\\n\",\r"
send "    \"        return None, None\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"markdown\",\r"
send "   \"metadata\": {},\r"
send "   \"source\": [\r"
send "    \"## 3. Custom U-Net for Satellite Images\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"code\",\r"
send "   \"execution_count\": null,\r"
send "   \"metadata\": {},\r"
send "   \"outputs\": [],\r"
send "   \"source\": [\r"
send "    \"import torch.nn as nn\\n\",\r"
send "    \"import torch.nn.functional as F\\n\",\r"
send "    \"\\n\",\r"
send "    \"class UNet(nn.Module):\\n\",\r"
send "    \"    def __init__(self, n_channels=3, n_classes=21):\\n\",\r"
send "    \"        super(UNet, self).__init__()\\n\",\r"
send "    \"        # Encoder\\n\",\r"
send "    \"        self.enc1 = self.conv_block(n_channels, 64)\\n\",\r"
send "    \"        self.enc2 = self.conv_block(64, 128)\\n\",\r"
send "    \"        self.enc3 = self.conv_block(128, 256)\\n\",\r"
send "    \"        self.enc4 = self.conv_block(256, 512)\\n\",\r"
send "    \"        \\n\",\r"
send "    \"        # Decoder\\n\",\r"
send "    \"        self.dec1 = self.conv_block(512 + 256, 256)\\n\",\r"
send "    \"        self.dec2 = self.conv_block(256 + 128, 128)\\n\",\r"
send "    \"        self.dec3 = self.conv_block(128 + 64, 64)\\n\",\r"
send "    \"        self.final = nn.Conv2d(64, n_classes, 1)\\n\",\r"
send "    \"        \\n\",\r"
send "    \"    def conv_block(self, in_ch, out_ch):\\n\",\r"
send "    \"        return nn.Sequential(\\n\",\r"
send "    \"            nn.Conv2d(in_ch, out_ch, 3, padding=1),\\n\",\r"
send "    \"            nn.BatchNorm2d(out_ch),\\n\",\r"
send "    \"            nn.ReLU(inplace=True),\\n\",\r"
send "    \"            nn.Conv2d(out_ch, out_ch, 3, padding=1),\\n\",\r"
send "    \"            nn.BatchNorm2d(out_ch),\\n\",\r"
send "    \"            nn.ReLU(inplace=True)\\n\",\r"
send "    \"        )\\n\",\r"
send "    \"        \\n\",\r"
send "    \"    def forward(self, x):\\n\",\r"
send "    \"        # Encoder\\n\",\r"
send "    \"        e1 = self.enc1(x)\\n\",\r"
send "    \"        e2 = self.enc2(F.max_pool2d(e1, 2))\\n\",\r"
send "    \"        e3 = self.enc3(F.max_pool2d(e2, 2))\\n\",\r"
send "    \"        e4 = self.enc4(F.max_pool2d(e3, 2))\\n\",\r"
send "    \"        \\n\",\r"
send "    \"        # Decoder\\n\",\r"
send "    \"        d1 = self.dec1(torch.cat([F.interpolate(e4, scale_factor=2), e3], 1))\\n\",\r"
send "    \"        d2 = self.dec2(torch.cat([F.interpolate(d1, scale_factor=2), e2], 1))\\n\",\r"
send "    \"        d3 = self.dec3(torch.cat([F.interpolate(d2, scale_factor=2), e1], 1))\\n\",\r"
send "    \"        \\n\",\r"
send "    \"        return self.final(d3)\\n\",\r"
send "    \"\\n\",\r"
send "    \"# Initialize U-Net\\n\",\r"
send "    \"unet = UNet(n_channels=3, n_classes=10).to(device)\\n\",\r"
send "    \"print('U-Net initialized for satellite imagery!')\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"markdown\",\r"
send "   \"metadata\": {},\r"
send "   \"source\": [\r"
send "    \"## 4. Model Comparison & Benchmarking\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"code\",\r"
send "   \"execution_count\": null,\r"
send "   \"metadata\": {},\r"
send "   \"outputs\": [],\r"
send "   \"source\": [\r"
send "    \"import time\\n\",\r"
send "    \"\\n\",\r"
send "    \"def benchmark_models(image_path):\\n\",\r"
send "    \"    results = {}\\n\",\r"
send "    \"    image = Image.open(image_path).convert('RGB')\\n\",\r"
send "    \"    img_tensor = transforms.ToTensor()(image).unsqueeze(0).to(device)\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    # DeepLab benchmark\\n\",\r"
send "    \"    torch.cuda.synchronize()\\n\",\r"
send "    \"    start = time.time()\\n\",\r"
send "    \"    with torch.no_grad():\\n\",\r"
send "    \"        _ = model_deeplabv3(img_tensor)\\n\",\r"
send "    \"    torch.cuda.synchronize()\\n\",\r"
send "    \"    results['DeepLab'] = time.time() - start\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    # U-Net benchmark\\n\",\r"
send "    \"    torch.cuda.synchronize()\\n\",\r"
send "    \"    start = time.time()\\n\",\r"
send "    \"    with torch.no_grad():\\n\",\r"
send "    \"        _ = unet(img_tensor)\\n\",\r"
send "    \"    torch.cuda.synchronize()\\n\",\r"
send "    \"    results['U-Net'] = time.time() - start\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    print('Inference Speed on RTX 5080:')\\n\",\r"
send "    \"    for model, time_taken in results.items():\\n\",\r"
send "    \"        print(f'{model}: {time_taken*1000:.2f}ms ({1/time_taken:.1f} FPS)')\\n\",\r"
send "    \"    \\n\",\r"
send "    \"    return results\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"markdown\",\r"
send "   \"metadata\": {},\r"
send "   \"source\": [\r"
send "    \"## 5. FastAPI Server for All Models\"\r"
send "   ]\r"
send "  },\r"
send "  {\r"
send "   \"cell_type\": \"code\",\r"
send "   \"execution_count\": null,\r"
send "   \"metadata\": {},\r"
send "   \"outputs\": [],\r"
send "   \"source\": [\r"
send "    \"# Save this as api_server.py and run with uvicorn\\n\",\r"
send "    \"api_code = '''\\n\",\r"
send "    \"from fastapi import FastAPI, File, UploadFile\\n\",\r"
send "    \"from fastapi.responses import JSONResponse\\n\",\r"
send "    \"import torch\\n\",\r"
send "    \"import torchvision\\n\",\r"
send "    \"from PIL import Image\\n\",\r"
send "    \"import io\\n\",\r"
send "    \"import base64\\n\",\r"
send "    \"\\n\",\r"
send "    \"app = FastAPI()\\n\",\r"
send "    \"\\n\",\r"
send "    \"# Load models\\n\",\r"
send "    \"device = torch.device(\\'cuda\\' if torch.cuda.is_available() else \\'cpu\\')\\n\",\r"
send "    \"deeplab = torchvision.models.segmentation.deeplabv3_resnet101(pretrained=True).to(device)\\n\",\r"
send "    \"deeplab.eval()\\n\",\r"
send "    \"\\n\",\r"
send "    \"@app.post(\\'/segment/deeplab\\')\\n\",\r"
send "    \"async def segment_deeplab(file: UploadFile = File(...)):\\n\",\r"
send "    \"    image = Image.open(io.BytesIO(await file.read())).convert(\\'RGB\\')\\n\",\r"
send "    \"    # Process with DeepLab\\n\",\r"
send "    \"    # Return segmentation mask\\n\",\r"
send "    \"    return JSONResponse({\\'status\\': \\'success\\', \\'model\\': \\'deeplab\\'})\\n\",\r"
send "    \"\\n\",\r"
send "    \"@app.get(\\'/health\\')\\n\",\r"
send "    \"def health():\\n\",\r"
send "    \"    return {\\'status\\': \\'healthy\\', \\'gpu\\': torch.cuda.is_available()}\\n\",\r"
send "    \"'''\\n\",\r"
send "    \"\\n\",\r"
send "    \"with open('/workspace/notebooks/api_server.py', 'w') as f:\\n\",\r"
send "    \"    f.write(api_code)\\n\",\r"
send "    \"\\n\",\r"
send "    \"print('API server code saved to api_server.py')\\n\",\r"
send "    \"print('Run with: uvicorn api_server:app --host 0.0.0.0 --port 8081')\"\r"
send "   ]\r"
send "  }\r"
send " ],\r"
send " \"metadata\": {\r"
send "  \"kernelspec\": {\r"
send "   \"display_name\": \"Python 3\",\r"
send "   \"language\": \"python\",\r"
send "   \"name\": \"python3\"\r"
send "  }\r"
send " },\r"
send " \"nbformat\": 4,\r"
send " \"nbformat_minor\": 4\r"
send "}\r"
send "EOF\r"
expect "$ "

puts "\nCreating FastAPI server for DeepLab..."

# Create FastAPI server
send "cat > ~/jupyter-ml/notebooks/deeplab_server.py << 'EOF'\r"
send "#!/usr/bin/env python3\r"
send "from fastapi import FastAPI, File, UploadFile, HTTPException\r"
send "from fastapi.middleware.cors import CORSMiddleware\r"
send "from fastapi.responses import JSONResponse\r"
send "import torch\r"
send "import torchvision\r"
send "from torchvision import transforms\r"
send "from PIL import Image\r"
send "import io\r"
send "import base64\r"
send "import numpy as np\r"
send "import uvicorn\r"
send "\r"
send "app = FastAPI(title=\"DeepLab Segmentation Server\")\r"
send "\r"
send "# CORS\r"
send "app.add_middleware(\r"
send "    CORSMiddleware,\r"
send "    allow_origins=[\"*\"],\r"
send "    allow_credentials=True,\r"
send "    allow_methods=[\"*\"],\r"
send "    allow_headers=[\"*\"],\r"
send ")\r"
send "\r"
send "# Load model\r"
send "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\r"
send "print(f\"Using device: {device}\")\r"
send "\r"
send "model = torchvision.models.segmentation.deeplabv3_resnet101(pretrained=True)\r"
send "model = model.to(device)\r"
send "model.eval()\r"
send "print(\"DeepLab model loaded!\")\r"
send "\r"
send "@app.get(\"/health\")\r"
send "async def health():\r"
send "    return {\r"
send "        \"status\": \"healthy\",\r"
send "        \"model\": \"DeepLabV3+\",\r"
send "        \"device\": str(device),\r"
send "        \"gpu\": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None\r"
send "    }\r"
send "\r"
send "@app.post(\"/segment\")\r"
send "async def segment(file: UploadFile = File(...)):\r"
send "    try:\r"
send "        # Read image\r"
send "        contents = await file.read()\r"
send "        image = Image.open(io.BytesIO(contents)).convert('RGB')\r"
send "        \r"
send "        # Preprocess\r"
send "        preprocess = transforms.Compose([\r"
send "            transforms.ToTensor(),\r"
send "            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\r"
send "        ])\r"
send "        \r"
send "        input_tensor = preprocess(image).unsqueeze(0).to(device)\r"
send "        \r"
send "        # Inference\r"
send "        with torch.no_grad():\r"
send "            output = model(input_tensor)['out'][0]\r"
send "        \r"
send "        output_predictions = output.argmax(0).cpu().numpy()\r"
send "        \r"
send "        # Convert to base64\r"
send "        mask_img = Image.fromarray((output_predictions * 10).astype(np.uint8))\r"
send "        buffered = io.BytesIO()\r"
send "        mask_img.save(buffered, format=\"PNG\")\r"
send "        mask_b64 = base64.b64encode(buffered.getvalue()).decode()\r"
send "        \r"
send "        return {\r"
send "            \"status\": \"success\",\r"
send "            \"mask\": mask_b64,\r"
send "            \"shape\": list(output_predictions.shape),\r"
send "            \"classes\": int(output_predictions.max()) + 1\r"
send "        }\r"
send "        \r"
send "    except Exception as e:\r"
send "        raise HTTPException(status_code=500, detail=str(e))\r"
send "\r"
send "if __name__ == \"__main__\":\r"
send "    uvicorn.run(app, host=\"0.0.0.0\", port=8081)\r"
send "EOF\r"
expect "$ "

# Install FastAPI and uvicorn in container
send "sudo docker exec jupyter-ml pip3 install fastapi uvicorn python-multipart -q\r"
expect "$ "

# Start DeepLab server in background
puts "\nStarting DeepLab server on port 8081..."
send "sudo docker exec -d jupyter-ml python3 /workspace/notebooks/deeplab_server.py\r"
expect "$ "

send "sleep 3\r"
expect "$ "

# Test DeepLab server
send "curl -s http://localhost:8081/health | python3 -m json.tool\r"
expect "$ "

puts "\n=== COMPLETE SEGMENTATION SYSTEM READY ==="
puts "✅ Jupyter Notebook: http://$host:8888"
puts "✅ SAM Server: http://$host:8080"  
puts "✅ DeepLab Server: http://$host:8081"
puts "✅ All models configured for RTX 5080"
puts ""
puts "Open Jupyter and run 'satellite_segmentation.ipynb' to test everything!"

send "exit\r"
expect eof