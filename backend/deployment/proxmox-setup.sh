#!/bin/bash

# Script per configurare VM con GPU passthrough su Proxmox per SAM
# Da eseguire sul nodo Proxmox

echo "=== Configurazione VM per SAM con GPU Passthrough ==="

# Variabili
VM_ID=200
VM_NAME="sam-gpu-server"
VM_CORES=4
VM_RAM=8192
VM_DISK=50
STORAGE="local-lvm"
ISO_PATH="/var/lib/vz/template/iso/ubuntu-22.04.3-live-server-amd64.iso"

# Step 1: Abilita IOMMU (se non già fatto)
echo "Step 1: Verifica IOMMU..."
if ! grep -q "intel_iommu=on" /etc/default/grub; then
    echo "Abilitando IOMMU..."
    sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT="quiet"/GRUB_CMDLINE_LINUX_DEFAULT="quiet intel_iommu=on iommu=pt"/' /etc/default/grub
    update-grub
    echo "RIAVVIO NECESSARIO! Esegui 'reboot' e rilancia questo script."
    exit 1
fi

# Step 2: Carica moduli VFIO
echo "Step 2: Configurazione moduli VFIO..."
cat > /etc/modules-load.d/vfio.conf << EOF
vfio
vfio_iommu_type1
vfio_pci
vfio_virqfd
EOF

# Step 3: Blacklist driver NVIDIA su host
echo "Step 3: Blacklist driver NVIDIA su host..."
cat > /etc/modprobe.d/blacklist-nvidia.conf << EOF
blacklist nouveau
blacklist nvidia
blacklist nvidiafb
blacklist nvidia_drm
EOF

# Step 4: Trova GPU NVIDIA
echo "Step 4: Identificazione GPU..."
GPU_ID=$(lspci -nn | grep -i nvidia | grep VGA | cut -d' ' -f1)
GPU_VENDOR_ID=$(lspci -nn | grep -i nvidia | grep VGA | grep -oP '\[\K[0-9a-f]{4}:[0-9a-f]{4}')

echo "GPU trovata: $GPU_ID (Vendor ID: $GPU_VENDOR_ID)"

# Step 5: Crea VM
echo "Step 5: Creazione VM..."
qm create $VM_ID \
    --name $VM_NAME \
    --memory $VM_RAM \
    --cores $VM_CORES \
    --cpu host \
    --ostype l26 \
    --scsihw virtio-scsi-pci \
    --scsi0 $STORAGE:$VM_DISK \
    --ide2 $ISO_PATH,media=cdrom \
    --net0 virtio,bridge=vmbr0 \
    --boot order=scsi0 \
    --machine q35

# Step 6: Aggiungi GPU alla VM
echo "Step 6: Aggiunta GPU alla VM..."
qm set $VM_ID -hostpci0 $GPU_ID,pcie=1,x-vga=1

# Step 7: Configurazioni aggiuntive per GPU
qm set $VM_ID \
    --vga none \
    --bios ovmf \
    --efidisk0 $STORAGE:1 \
    --args "-cpu host,+kvm_pv_unhalt,+kvm_pv_eoi,hv_vendor_id=proxmox,hv_spinlocks=0x1fff,hv_vapic,hv_time,hv_reset,hv_vpindex,hv_runtime,hv_relaxed,hv_synic,hv_stimer,hv_tlbflush,hv_ipi,kvm=off"

echo "=== Configurazione completata ==="
echo ""
echo "Prossimi passi:"
echo "1. Avvia la VM: qm start $VM_ID"
echo "2. Installa Ubuntu Server"
echo "3. Esegui lo script di setup SAM nella VM"
echo ""
echo "Per connetterti alla console: qm console $VM_ID"