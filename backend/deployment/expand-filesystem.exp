#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== EXPANDING FILESYSTEM TO USE ALL AVAILABLE SPACE ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Check current disk usage and partitions
puts "\n--- Current Disk Status ---"
send "df -h /\r"
expect "$ "

send "lsblk\r"
expect "$ "

# Check volume group info
puts "\n--- Volume Group Information ---"
send "sudo vgdisplay\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

# Check for free space in volume group
send "sudo vgs\r"
expect "$ "

# Extend logical volume to use all available space
puts "\n--- Extending logical volume to use all available space ---"
send "sudo lvextend -l +100%FREE /dev/mapper/ubuntu--vg-ubuntu--lv\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "Size of logical volume" {
        puts "\n✓ Logical volume extended successfully"
    }
    "New size" {
        puts "\n✓ Volume extended to new size"
    }
    "$ " {}
}

expect "$ "

# Resize the filesystem to use the new space
puts "\n--- Resizing filesystem to use all space ---"
send "sudo resize2fs /dev/mapper/ubuntu--vg-ubuntu--lv\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "The filesystem on" {
        puts "\n✓ Filesystem resized successfully"
    }
    "$ " {}
}

expect "$ "

# Clean up Docker to free space
puts "\n--- Cleaning Docker to free up space ---"

# Remove stopped containers
send "sudo docker container prune -f\r"
expect "$ "

# Remove unused images
send "sudo docker image prune -a -f\r"
expect "$ "

# Remove unused volumes
send "sudo docker volume prune -f\r"
expect "$ "

# Remove build cache
send "sudo docker builder prune -a -f\r"
expect "$ "

# Final disk check
puts "\n--- Final Disk Status ---"
send "df -h /\r"
expect "$ "

# Check Docker space
send "sudo docker system df\r"
expect "$ "

puts "\n=== FILESYSTEM EXPANSION COMPLETE ==="
puts "All available space from the 300GB VM disk is now usable"

send "exit\r"
expect eof