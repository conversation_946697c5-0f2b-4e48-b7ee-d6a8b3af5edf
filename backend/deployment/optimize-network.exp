#!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

# Ottimizza buffer TCP per 10Gbps
send "sysctl -w net.core.rmem_max=134217728\r"
expect "# "

send "sysctl -w net.core.wmem_max=134217728\r"
expect "# "

send "sysctl -w net.ipv4.tcp_rmem='4096 87380 134217728'\r"
expect "# "

send "sysctl -w net.ipv4.tcp_wmem='4096 65536 134217728'\r"
expect "# "

send "sysctl -w net.core.netdev_max_backlog=5000\r"
expect "# "

# Abilita TCP BBR per congestione moderna
send "sysctl -w net.ipv4.tcp_congestion_control=bbr\r"
expect "# "

# Aumenta MTU per jumbo frames (se supportato)
send "ip link set dev enp4s0d1 mtu 9000 2>/dev/null || echo 'Jumbo frames not supported'\r"
expect "# "

send "ip link set dev vmbr0 mtu 9000 2>/dev/null || echo 'Bridge MTU unchanged'\r"
expect "# "

# Salva configurazione permanente
send "cat >> /etc/sysctl.d/99-network-performance.conf << 'EOF'
# Network optimizations for 10Gbps
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_congestion_control = bbr
EOF\r"
expect "# "

# Verifica nuove impostazioni
send "sysctl net.core.rmem_max net.core.wmem_max\r"
expect "# "

# Test velocità con nuovo download
send "wget -O /dev/null https://mirror.init7.net/ubuntu/pool/main/l/linux/linux-source-5.15.0.tar.bz2 2>&1 | head -20 &\r"
expect "# "

send "sleep 5\r"
expect "# "

send "pkill wget\r"
expect "# "

send "exit\r"
expect eof