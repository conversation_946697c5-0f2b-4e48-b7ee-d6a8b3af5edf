#!/usr/bin/expect -f

set timeout 300
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== INSTALLING DOCKER WITH GPU SUPPORT AND PORTAINER ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Install Docker
send "curl -fsSL https://get.docker.com | sudo sh\r"
expect "*?assword*" { send "$password\r" }
expect "$ "

# Add user to docker group
send "sudo usermod -aG docker alin\r"
expect "$ "

# Install NVIDIA Container Toolkit
send "curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg\r"
expect "$ "

send "curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | sed 's#deb https://#deb \[signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg\] https://#g' | sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list\r"
expect "$ "

send "sudo apt update\r"
expect "$ "

send "sudo apt install -y nvidia-container-toolkit\r"
expect "$ "

# Configure Docker for NVIDIA
send "sudo nvidia-ctk runtime configure --runtime=docker\r"
expect "$ "

send "sudo systemctl restart docker\r"
expect "$ "

# Test GPU in Docker
send "sudo docker run --rm --gpus all nvidia/cuda:12.0.0-base-ubuntu22.04 nvidia-smi\r"
expect "$ "

# Install Portainer with GPU support
send "sudo docker volume create portainer_data\r"
expect "$ "

send "sudo docker run -d -p 8000:8000 -p 9443:9443 --name portainer --restart=always -v /var/run/docker.sock:/var/run/docker.sock -v portainer_data:/data portainer/portainer-ce:latest\r"
expect "$ "

# Get IP for access
send "echo ''\r"
expect "$ "
send "echo '=== INSTALLATION COMPLETE ==='\r"
expect "$ "
send "echo 'Portainer WebUI: https://*************:9443'\r"
expect "$ "
send "echo 'GPU is available in Docker containers with --gpus all flag'\r"
expect "$ "
send "echo ''\r"
expect "$ "

send "exit\r"
expect eof