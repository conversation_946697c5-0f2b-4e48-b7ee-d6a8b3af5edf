#!/usr/bin/expect -f

set timeout 600
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== UPGRADING PYTORCH FOR RTX 5080 ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Stop and remove old container
send "docker stop sam-server\r"
expect "$ "
send "docker rm sam-server\r"
expect "$ "

send "cd ~/sam-server\r"
expect "$ "

# Create new Dockerfile with CUDA 12.8 and PyTorch 2.7
send "cat > Dockerfile << 'EOF'\r"
send "# Use CUDA 12.8 for RTX 5080 support\r"
send "FROM nvidia/cuda:12.8.0-runtime-ubuntu22.04\r"
send "\r"
send "# Install Python and dependencies\r"
send "RUN apt-get update && apt-get install -y \\\r"
send "    python3-pip \\\r"
send "    python3-dev \\\r"
send "    git \\\r"
send "    wget \\\r"
send "    && rm -rf /var/lib/apt/lists/*\r"
send "\r"
send "# Upgrade pip\r"
send "RUN pip3 install --upgrade pip\r"
send "\r"
send "# Install PyTorch 2.7.0 with CUDA 12.8 support for RTX 5080\r"
send "RUN pip3 install torch==2.7.0 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128\r"
send "\r"
send "# Install SAM and dependencies\r"
send "RUN pip3 install git+https://github.com/facebookresearch/segment-anything.git\r"
send "RUN pip3 install opencv-python-headless pillow flask flask-cors numpy\r"
send "\r"
send "# Download SAM models\r"
send "RUN mkdir -p /models && cd /models && \\\r"
send "    wget -q https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth && \\\r"
send "    wget -q https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth && \\\r"
send "    wget -q https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth\r"
send "\r"
send "# Create app directory\r"
send "WORKDIR /app\r"
send "\r"
send "# Copy server script\r"
send "COPY sam_server.py /app/\r"
send "\r"
send "EXPOSE 8080\r"
send "\r"
send "ENV PYTHONUNBUFFERED=1\r"
send "ENV CUDA_VISIBLE_DEVICES=0\r"
send "\r"
send "CMD \[\"python3\", \"sam_server.py\"\]\r"
send "EOF\r"
expect "$ "

# Build new image
send "echo 'Building Docker image with PyTorch 2.7.0 and CUDA 12.8...'\r"
expect "$ "

send "docker build -t sam-server:pytorch27 .\r"
expect {
    "$ " { puts "\nDocker build completed" }
    timeout { puts "\nBuild in progress..."; exp_continue }
}

# Run new container
send "docker run -d --gpus all -p 8080:8080 --name sam-server --restart=always sam-server:pytorch27\r"
expect "$ "

send "sleep 15\r"
expect "$ "

# Check logs
send "docker logs sam-server --tail 30\r"
expect "$ "

# Test health
send "curl -s http://localhost:8080/health | python3 -m json.tool\r"
expect "$ "

send "exit\r"
expect eof