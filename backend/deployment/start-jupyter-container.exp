#!/usr/bin/expect -f

set timeout 60
set host "*************"
set user "alin"
set password "Al0xan999"

puts "\n=== STARTING JUPYTER CONTAINER ===\n"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "$ "

# Go to jupyter-ml directory
send "cd ~/jupyter-ml\r"
expect "$ "

# Stop any existing container
send "sudo docker stop jupyter-ml 2>/dev/null || true\r"
expect {
    "*?assword*" { 
        send "$password\r"
        exp_continue
    }
    "$ " {}
}

send "sudo docker rm jupyter-ml 2>/dev/null || true\r"
expect "$ "

# Create directories for volumes
send "mkdir -p notebooks data models outputs\r"
expect "$ "

# Run the container with GPU support
puts "\nStarting Jupyter container..."
send "sudo docker run -d \\\r"
send "  --name jupyter-ml \\\r"
send "  --gpus all \\\r"
send "  --restart unless-stopped \\\r"
send "  -p 8888:8888 \\\r"
send "  -v \$(pwd)/notebooks:/workspace/notebooks \\\r"
send "  -v \$(pwd)/data:/workspace/data \\\r"
send "  -v \$(pwd)/models:/workspace/models \\\r"
send "  jupyter-ml:latest\r"
expect "$ "

# Wait for container to start
send "sleep 5\r"
expect "$ "

# Check if container is running
send "sudo docker ps | grep jupyter-ml\r"
expect "$ "

# Show logs
send "sudo docker logs jupyter-ml --tail 20\r"
expect "$ "

# Test GPU
puts "\nTesting GPU access..."
send "sudo docker exec jupyter-ml python3 -c \"import torch; print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0)}' if torch.cuda.is_available() else 'No GPU')\"\r"
expect "$ "

puts "\n=== JUPYTER READY ==="
puts "Access at: http://$host:8888"
puts "No password required"

send "exit\r"
expect eof