#!/usr/bin/expect -f

set timeout 120
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== COMPLETE VM SETUP WITH GPU ==="

# Step 1: Delete ALL containers and VMs
puts "\nStep 1: Cleaning up all existing VMs and containers..."
send "pct stop 100 2>/dev/null; pct destroy 100 --purge 2>/dev/null\r"
expect "# "
send "pct stop 201 2>/dev/null; pct destroy 201 --purge 2>/dev/null\r"
expect "# "
send "for id in {100..210}; do qm stop \$id 2>/dev/null; qm destroy \$id --purge 2>/dev/null; pct stop \$id 2>/dev/null; pct destroy \$id --purge 2>/dev/null; done\r"
expect "# "

# Step 2: Create new VM with optimal settings
puts "\nStep 2: Creating new VM 200 with GPU passthrough..."
send "qm create 200 --name ubuntu-sam-gpu --memory 16384 --cores 6 --cpu host --ostype l26 --scsihw virtio-scsi-pci --scsi0 local-lvm:100 --ide2 local:iso/ubuntu-24.04.1-live-server-amd64.iso,media=cdrom --net0 virtio,bridge=vmbr0 --boot order=ide2,scsi0 --machine q35 --bios ovmf --efidisk0 local-lvm:1\r"
expect "# "

# Step 3: Add GPU passthrough with primary VGA
puts "\nStep 3: Adding RTX 3070 GPU passthrough..."
send "qm set 200 -hostpci0 07:00,pcie=1,x-vga=1\r"
expect "# "

# Step 4: Enable NUMA and optimize
puts "\nStep 4: Optimizing VM settings..."
send "qm set 200 --numa 1\r"
expect "# "
send "qm set 200 --balloon 0\r"
expect "# "

# Step 5: Start the VM
puts "\nStep 5: Starting VM 200..."
send "qm start 200\r"
expect "# "
sleep 3

# Check status
send "qm status 200\r"
expect "# "

puts "\n=========================================="
puts "VM CREATED SUCCESSFULLY!"
puts "=========================================="
puts ""
puts "VM Details:"
puts "  ID: 200"
puts "  Name: ubuntu-sam-gpu"
puts "  RAM: 16GB"
puts "  CPU: 6 cores"
puts "  GPU: RTX 3070 (Primary VGA)"
puts "  Disk: 100GB"
puts ""
puts "IMPORTANT: The VM is now using the GPU as primary display!"
puts "You need to reboot Proxmox to see the output on the GPU monitor."
puts ""
puts "Rebooting Proxmox in 10 seconds..."
puts "After reboot:"
puts "1. The GPU monitor will show Ubuntu installer"
puts "2. Install Ubuntu manually from the GPU screen"
puts "3. Use settings:"
puts "   - Username: ubuntu"
puts "   - Password: ubuntu"
puts "   - Hostname: sam-gpu"
puts "   - Enable SSH server"
puts ""

# Wait before reboot
sleep 10

# Reboot Proxmox
send "reboot\r"
expect {
    "# " {
        puts "Rebooting now..."
    }
    eof {
        puts "Proxmox is rebooting..."
    }
}

expect eof