#\!/usr/bin/expect -f

set timeout 30
set host "*************"
set user "root"
set password "Al0xan999"

spawn ssh $user@$host

expect {
    "yes/no" { send "yes\r"; exp_continue }
    "*?assword:" { send "$password\r" }
}

expect "# "

puts "\n=== Checking VM Installation Status ==="

# Check if VM is running
send "qm status 202\r"
expect "# "

# Try to get VM IP (if guest agent is installed, Ubuntu is ready)
send "qm agent 202 network-get-interfaces 2>/dev/null | grep -A1 enp6s18 | grep '\"ip-address\"' | head -1 | cut -d'\"' -f4\r"
expect {
    -re "\[0-9\]+\\.\[0-9\]+\\.\[0-9\]+\\.\[0-9\]+" {
        puts "\n✅ UBUNTU INSTALLATION COMPLETE!"
        puts "VM IP Address: $expect_out(0,string)"
        puts "\nVM is ready for SAM deployment"
    }
    "# " {
        puts "\n⏳ Ubuntu installation still in progress..."
        puts "Guest agent not responding yet"
    }
}

# Check VM uptime to estimate progress
send "qm monitor 202\r"
expect "(qemu)"
send "info status\r"
expect "(qemu)"
send "quit\r"
expect "# "

# Check console output for clues
send "qm showcmd 202 | grep -c vnc\r"
expect "# "

send "exit\r"
expect eof
