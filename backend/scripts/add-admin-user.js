const { PrismaClient } = require('../src/generated/prisma');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function addAdminUser() {
  try {
    // Hash della password
    const hashedPassword = await bcrypt.hash('Al0xan999', 10);
    
    // Crea l'utente admin
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: '<PERSON><PERSON>',
        password: hashedPassword,
        role: 'admin'
      }
    });
    
    console.log('✅ Utente admin creato con successo:', {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    });
    
    // Opzionale: crea anche un'organizzazione di default e associa l'utente
    const organization = await prisma.organization.create({
      data: {
        name: 'Admin Organization',
        slug: 'admin-org',
        plan: 'premium',
        users: {
          create: {
            userId: user.id,
            role: 'owner'
          }
        }
      }
    });
    
    console.log('✅ Organizzazione creata e utente associato come owner:', {
      orgId: organization.id,
      orgName: organization.name
    });
    
  } catch (error) {
    if (error.code === 'P2002') {
      console.error('❌ Errore: Un utente con questa email esiste già');
    } else {
      console.error('❌ Errore durante la creazione dell\'utente:', error);
    }
  } finally {
    await prisma.$disconnect();
  }
}

addAdminUser();