const { PrismaClient } = require('../src/generated/prisma');
const fs = require('fs').promises;
const path = require('path');

const prisma = new PrismaClient();

async function loadGeocodedData() {
  try {
    console.log('Loading geocoded data...');
    
    // Read JSON file
    const filePath = path.join(__dirname, '../../DATI_GEOCODIFICATI_VALIDATI.json');
    let fileContent = await fs.readFile(filePath, 'utf-8');
    
    // Replace NaN with null in the string before parsing
    fileContent = fileContent.replace(/:\s*NaN/g, ': null');
    
    const data = JSON.parse(fileContent);
    
    console.log(`Found ${data.length} records to import`);
    
    // Clear existing data
    await prisma.geocodedCompany.deleteMany();
    console.log('Cleared existing geocoded companies');
    
    // Process data in batches
    const batchSize = 100;
    let imported = 0;
    
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, Math.min(i + batchSize, data.length));
      
      const companies = batch.map(record => ({
        piva: record.piva || null,
        ragioneSociale: record.ragione_sociale || 'N/A',
        toponimo: record.toponimo || null,
        indirizzo: record.indirizzo || null,
        civico: record.civico || null,
        cap: record.cap || null,
        citta: record.citta || null,
        provincia: record.provincia || null,
        regione: record.regione || null,
        geocodeId: record.id || null,
        latitudine: record.latitudine,
        longitudine: record.longitudine,
        indirizzoCompleto: record.indirizzo_completo || null,
        geocodingStatus: record.geocoding_status || null,
        googlePlaceId: record.google_place_id || null,
        googleFormattedAddress: record.google_formatted_address || null,
        googlePlaceIdVerified: record.google_place_id_verified || null,
        businessNameGoogle: record.business_name_google || null,
        businessStatus: record.business_status || null,
        businessTypes: record.business_types || null,
        addressValidated: record.address_validated || null,
        isPermanentlyClosed: record.is_permanently_closed,
        hasOpeningHours: record.has_opening_hours,
        website: record.website || null,
        phoneNumber: record.phone_number || null,
        rating: record.rating,
        totalRatings: record.total_ratings,
        validationTimestamp: record.validation_timestamp ? new Date(record.validation_timestamp) : null,
        validationStatus: record.validation_status || null
      }));
      
      await prisma.geocodedCompany.createMany({
        data: companies,
        skipDuplicates: true
      });
      
      imported += batch.length;
      console.log(`Imported ${imported}/${data.length} records`);
    }
    
    console.log('Data import completed successfully!');
    
    // Show statistics
    const count = await prisma.geocodedCompany.count();
    const validated = await prisma.geocodedCompany.count({
      where: { validationStatus: 'TROVATO' }
    });
    
    console.log(`\nStatistics:`);
    console.log(`Total companies: ${count}`);
    console.log(`Validated companies: ${validated}`);
    
  } catch (error) {
    console.error('Error loading geocoded data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

loadGeocodedData();