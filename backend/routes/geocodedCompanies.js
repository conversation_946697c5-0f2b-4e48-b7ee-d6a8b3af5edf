const express = require('express');
const router = express.Router();
const geocodedCompaniesController = require('../controllers/geocodedCompaniesController');

// Optional authentication middleware - allows both authenticated and unauthenticated requests
const optionalAuthMiddleware = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (token) {
    // In production, verify JWT token here and set req.user
    req.authenticated = true;
  } else {
    req.authenticated = false;
  }
  next();
};

// Get all companies with pagination and filters - with optional auth
router.get('/', optionalAuthMiddleware, geocodedCompaniesController.getAll);

// Get statistics
router.get('/statistics', optionalAuthMiddleware, geocodedCompaniesController.getStatistics);

// Get unique provinces
router.get('/provinces', optionalAuthMiddleware, geocodedCompaniesController.getProvinces);

// Get unique cities
router.get('/cities', optionalAuthMiddleware, geocodedCompaniesController.getCities);

// Get companies for map view
router.get('/map', optionalAuthMiddleware, geocodedCompaniesController.getForMap);

// Get single company by ID
router.get('/:id', optionalAuthMiddleware, geocodedCompaniesController.getById);

module.exports = router;