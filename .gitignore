# OS files
.DS_Store
Thumbs.db

# Node
node_modules/
/backend/node_modules/
/frontend/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
.venv
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Logs
*.log
logs/
backend_output.log
frontend.log
ai_server.log
ai_server_real.log

# Environment variables
.env
.env.*
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env
frontend/.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*.swn
*.bak

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.cache/
.parcel-cache/

# Generated files
/backend/src/generated/

# Temporary files
/backend/temp/
/temp/
tmp/
*.tmp

# Database
*.sqlite
*.sqlite3
*.db

# AWS/S3
.aws/

# Machine Learning models (keep only necessary ones)
*.pth
*.pkl
*.h5
*.model
!models/sam_vit_h_4b8939.pth

# Large data files
*.tif
*.tiff
*.jpg
*.jpeg
*.png
!public/images/
!src/assets/

# Geospatial data
*.shp
*.shx
*.dbf
*.prj
# Esclude i file geodati ISTAT pesanti
/backend/src/data/high_quality/
# Ma mantieni il backup compresso
!geodata_backup.tar.gz

# Prisma
/backend/prisma/migrations/*/
!/backend/prisma/migrations/migration_lock.toml
!/backend/prisma/migrations/*/migration.sql

# Frontend build
/frontend/dist/
/frontend/.vite/

# Package lock files (choose one)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Testing
coverage/
.nyc_output/

# Misc
.eslintcache
.stylelintcache
*.pid
*.seed
*.pid.lock

# Windows temporary files
nul
*/nul

# Claude local settings
.claude/settings.local.json