# 🏠 Sistema di Classificazione Materiali Tetti

Sistema completo end-to-end per rilevare e classificare materiali di copertura dei tetti da immagini satellitari.

## 🎯 Funzionalità

- **Download automatico** di immagini satellitari da Google Maps
- **Segmentazione AI** per identificare tetti nelle immagini
- **Tool di annotazione** web-based per classificare materiali
- **Training del modello** con deep learning (Transfer Learning)
- **Testing in tempo reale** del modello addestrato
- **Dashboard completa** per gestire l'intero workflow

## 🚀 Quick Start

### 1. Setup Backend Python

```bash
cd python
./setup_ml.sh
```

### 2. Avvia AI Server

```bash
cd python/ai_server
source ../venv/bin/activate
uvicorn main:app --reload --port 8000
```

### 3. Avvia Frontend

```bash
cd frontend
pnpm install
pnpm dev
```

### 4. Apri l'applicazione

Naviga a: http://localhost:5173/roof-classifier

## 📋 Workflow Completo

### Step 1: Raccolta Dataset
1. Vai alla tab **Dataset**
2. Inserisci coordinate GPS o usa quelle di esempio
3. Clicca **Download & Process Images**
4. Il sistema automaticamente:
   - Scarica immagini satellitari
   - Rileva i tetti con SAM
   - Estrae ritagli individuali

### Step 2: Annotazione
1. Vai alla tab **Annotation**
2. Classifica ogni tetto con il materiale corrispondente:
   - Terracotta Tiles (tegole terracotta)
   - Cement Tiles (tegole cemento)
   - Slate Tiles (ardesia)
   - Metal Sheet (lamiera)
   - Asphalt Shingles (tegole bituminose)
   - Flat Concrete (cemento piano)
   - Green Roof (tetto verde)
   - Solar Panels (pannelli solari)
   - Mixed Materials (materiali misti)

### Step 3: Training
1. Vai alla tab **Training**
2. Configura i parametri:
   - Epochs: 50-100
   - Batch Size: 32
   - Learning Rate: 0.0001
3. Clicca **Start Training**
4. Monitora progress e metriche in tempo reale

### Step 4: Testing
1. Vai alla tab **Testing**
2. Carica un'immagine di tetto
3. Ottieni predizioni con confidence scores

## 🏗️ Architettura

### Backend (Python)
- **FastAPI** per API REST
- **SAM** (Segment Anything Model) per segmentazione
- **PyTorch** + **EfficientNet** per classificazione
- **Albumentations** per data augmentation

### Frontend (React)
- **React** + **TypeScript**
- **TanStack Router** per routing
- **Tailwind CSS** per styling
- **Recharts** per visualizzazioni

### Struttura File
```
AstraMeccanica/
├── python/
│   ├── roof_dataset_pipeline.py    # Pipeline creazione dataset
│   ├── train_roof_classifier.py    # Script training
│   ├── ai_server/
│   │   ├── main.py                # Server FastAPI
│   │   └── routers/
│   │       └── roof_training.py   # API endpoints
│   └── dataset/roofs/             # Dataset generato
│       ├── raw_images/            # Immagini satellitari
│       ├── segmented/             # Maschere segmentazione
│       ├── cropped/               # Ritagli tetti
│       └── annotations/           # Annotazioni JSON
├── frontend/
│   └── src/
│       ├── routes/
│       │   └── roof-classifier.tsx
│       └── features/roof-classifier/
│           ├── RoofClassifierDashboard.tsx
│           └── components/
│               ├── DatasetManager.tsx
│               ├── AnnotationTool.tsx
│               ├── TrainingMonitor.tsx
│               └── ModelTester.tsx
└── models/
    └── roof_classifier/
        └── best_model.pth         # Modello addestrato
```

## 📊 Performance Attese

- **Accuratezza**: 85-95% su classi principali
- **Velocità inferenza**: <500ms per immagine
- **Dataset minimo**: 100 immagini per classe
- **Dataset ottimale**: 500+ immagini per classe

## 🛠️ Configurazione Avanzata

### Parametri Download Tiles
- **Zoom**: 18-20 (19 consigliato)
- **Size**: 640x640 pixels
- **Scale**: 2 (alta risoluzione)

### Parametri Segmentazione
- **Min Area**: 0.5% dell'immagine
- **Max Area**: 40% dell'immagine
- **IoU Threshold**: 0.88
- **Stability Score**: 0.95

### Hyperparameters Training
- **Model**: EfficientNet-B3
- **Optimizer**: AdamW
- **Scheduler**: CosineAnnealingWarmRestarts
- **Augmentation**: Heavy (weather, perspective, noise)

## 🐛 Troubleshooting

### Errore: "SAM model not loaded"
```bash
cd python
wget -O ai_server/models/sam_vit_h_4b8939.pth \
    https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
```

### Errore: "Not enough annotated samples"
Devi annotare almeno 10 campioni prima di poter avviare il training.

### GPU non rilevata
Il sistema funziona anche su CPU ma sarà più lento. Per usare GPU:
```bash
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

## 📝 Note

- Il sistema salva automaticamente checkpoint durante il training
- Le annotazioni sono salvate in JSON e possono essere esportate
- Il modello migliore viene salvato automaticamente
- Supporta resume del training da checkpoint

## 🔮 Sviluppi Futuri

- [ ] Export modello in ONNX per deployment
- [ ] Active learning per annotazione semi-automatica
- [ ] Multi-label classification per tetti misti
- [ ] Integration con drone per acquisizione real-time
- [ ] API per inference batch su larga scala