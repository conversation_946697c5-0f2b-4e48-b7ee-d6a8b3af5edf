# AstraMeccanica - Sistema AI per Rilevamento Tetti

## 🚀 Panoramica

AstraMeccanica combina il meglio di due progetti:
- **Frontend moderno** con Shadcn UI (da Def-AstraMeccanica)
- **Backend robusto** con Express/PostgreSQL/PostGIS (da astraai)
- **AI Server** con SAM 2 per rilevamento tetti (da astraai)

## 📁 Struttura Progetto

```
AstraMeccanica/
├── frontend/          # React + Vite + Shadcn UI
├── backend/           # Express + PostgreSQL + MinIO + Prisma ORM
├── python/            # AI Server con SAM 2
├── config/            # Configurazioni
├── start.sh           # Script avvio completo
├── stop.sh            # Script stop servizi
└── geodata_backup.tar.gz  # Backup dati geografici ISTAT (93MB)
```

## 🛠️ Setup Iniziale

### 1. Installazione dipendenze

```bash
# Frontend
cd frontend && pnpm install

# Backend
cd ../backend && npm install

# Python AI Server
cd ../python/ai_server
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 2. Dati Geografici ISTAT

I dati geografici ISTAT (shapefile dei confini amministrativi) sono esclusi dal repository per le dimensioni.

```bash
# Estrai il backup compresso incluso
tar -xzf geodata_backup.tar.gz -C backend/src/data/

# Oppure scarica i dati aggiornati
cd backend
node src/scripts/downloadHighQualityData.js
```

Vedi `backend/src/data/GEODATA_README.md` per maggiori dettagli.

### 3. Configurazione

Crea il file `.env` in `config/`:
```env
# Google Maps
GOOGLE_MAPS_API_KEY=your_key_here

# Database
DATABASE_URL=******************************************/astrameccanica

# MinIO Storage
MINIO_ENDPOINT=**************
MINIO_PORT=9000
MINIO_ACCESS_KEY=your_key
MINIO_SECRET_KEY=your_secret
```

### 4. Database Setup

```bash
# Genera il client Prisma
cd backend
npx prisma generate

# Esegui le migrazioni
npx prisma migrate deploy
```

### 5. Avvio sistema

```bash
# Avvia tutti i servizi
./start.sh

# Oppure avvia singolarmente:
# Backend: cd backend && npm start
# Frontend: cd frontend && pnpm dev
# AI Server: cd python/ai_server && python main.py
```

Per fermare tutti i servizi:
```bash
./stop.sh
```

## 📋 TODO - Configurazione Frontend

### Priorità Alta
- [ ] Aggiornare vite.config.ts con proxy per backend (porta 3000)
- [ ] Configurare router per le pagine principali
- [ ] Rimuovere pagine non necessarie
- [ ] Integrare API calls al backend

### Pagine da Mantenere/Adattare
1. **Dashboard** - Overview del sistema
2. **Map Downloader** - Download mappe satellitari
3. **Area Analyzer** - Analisi tetti con AI
4. **Companies DB** - Database aziende
5. **Settings** - Impostazioni sistema

### Pagine da Rimuovere
- Sign-in/Sign-up (per ora)
- Chats
- Tasks
- Apps generico

## 🔌 API Endpoints

### Backend (porta 3000)
- `/api/generate-map` - Download mappe
- `/api/generate-map-async` - Download asincrono
- `/api/s3/*` - Gestione storage
- `/api/database/*` - Database operations
- `/api/geo/*` - Dati geografici

### AI Server (porta 8001)
- `/detect` - Rilevamento tetti
- `/classify` - Classificazione materiali
- `/segment` - Segmentazione precisa
- `/docs` - Documentazione API

## 🚦 Porte Servizi

- **Frontend**: http://localhost:5173 (Vite dev server)
- **Backend**: http://localhost:3000 (Express API)
- **AI Server**: http://localhost:8001 (FastAPI)
- **AI Docs**: http://localhost:8001/docs (Swagger UI)

## 📊 Flusso Dati

1. **Frontend** → Richiesta download mappa
2. **Backend** → Scarica tiles da Google Maps
3. **Storage** → Salva in MinIO
4. **AI Server** → Analizza immagine per tetti
5. **Database** → Salva risultati analisi
6. **Frontend** → Mostra risultati utente

## 🎯 Prossimi Passi

1. Configurare il routing del frontend
2. Collegare frontend al backend API
3. Testare il flusso completo download → AI → risultati
4. Ottimizzare UI per il caso d'uso specifico

## 📝 Note Tecniche

### Stack Tecnologico
- **Frontend**: React 18, TypeScript, Vite, TanStack Router, Shadcn UI, TailwindCSS
- **Backend**: Node.js, Express, Prisma ORM, PostgreSQL + PostGIS
- **AI**: Python, FastAPI, SAM 2 (Segment Anything Model), PyTorch
- **Storage**: MinIO (S3-compatible object storage)

### Requisiti Sistema
- Node.js 18+
- Python 3.8+
- PostgreSQL 14+ con estensione PostGIS
- 8GB RAM minimo (16GB consigliato per AI)
- GPU CUDA-compatible (opzionale ma consigliato per AI)