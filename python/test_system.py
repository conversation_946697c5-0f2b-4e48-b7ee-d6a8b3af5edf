#!/usr/bin/env python3
"""
Script di test per verificare il funzionamento del sistema classificatore tetti
"""

import os
import sys
import json
from pathlib import Path
import numpy as np
from PIL import Image
import requests
import time

# Test configurazione base
def test_directories():
    """Verifica che le directory necessarie esistano"""
    print("🔍 Verificando struttura directory...")
    
    required_dirs = [
        "dataset/roofs/raw_images",
        "dataset/roofs/segmented", 
        "dataset/roofs/cropped",
        "dataset/roofs/annotations",
        "models/roof_classifier"
    ]
    
    for dir_path in required_dirs:
        path = Path(dir_path)
        if not path.exists():
            print(f"  ❌ Directory mancante: {dir_path}")
            path.mkdir(parents=True, exist_ok=True)
            print(f"  ✅ Creata: {dir_path}")
        else:
            print(f"  ✅ OK: {dir_path}")
    
    return True

def test_imports():
    """Verifica che le librerie necessarie siano installate"""
    print("\n📦 Verificando dipendenze Python...")
    
    modules = {
        "torch": "PyTorch",
        "torchvision": "TorchVision",
        "cv2": "OpenCV",
        "PIL": "Pillow",
        "numpy": "NumPy",
        "fastapi": "FastAPI",
        "albumentations": "Albumentations"
    }
    
    missing = []
    for module, name in modules.items():
        try:
            __import__(module)
            print(f"  ✅ {name} installato")
        except ImportError:
            print(f"  ❌ {name} mancante")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️  Installare le dipendenze mancanti con:")
        print(f"   pip install {' '.join(missing)}")
        return False
    
    return True

def test_downloader():
    """Test del downloader di immagini"""
    print("\n🌍 Testando downloader immagini...")
    
    try:
        from downloader_async import TileDownloader
        
        # Test con coordinate di esempio
        lat, lon = 45.4408, 12.3155  # Venezia
        
        # Crea un piccolo test
        output_dir = Path("dataset/roofs/raw_images")
        test_file = output_dir / f"test_{lat}_{lon}.jpg"
        
        if test_file.exists():
            print(f"  ✅ File test già esistente: {test_file.name}")
        else:
            print(f"  ⏳ Download test da {lat}, {lon}...")
            # Il download richiederebbe un'implementazione async
            print(f"  ⚠️  Download richiede ambiente async")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Errore: {e}")
        return False

def test_sam_model():
    """Verifica la presenza del modello SAM"""
    print("\n🤖 Verificando modello SAM...")
    
    checkpoint_path = Path("ai_server/models/sam_vit_h_4b8939.pth")
    
    if checkpoint_path.exists():
        size_mb = checkpoint_path.stat().st_size / (1024 * 1024)
        print(f"  ✅ Checkpoint SAM trovato ({size_mb:.1f} MB)")
        return True
    else:
        print(f"  ❌ Checkpoint SAM mancante")
        print(f"  ℹ️  Scaricalo con:")
        print(f"     wget -O {checkpoint_path} https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth")
        return False

def test_api_endpoints():
    """Test degli endpoint API"""
    print("\n🌐 Testando API endpoints...")
    
    base_url = "http://localhost:8000"
    
    # Controlla se il server è attivo
    try:
        response = requests.get(f"{base_url}/health", timeout=2)
        if response.status_code == 200:
            print(f"  ✅ Server AI attivo su {base_url}")
        else:
            print(f"  ⚠️  Server risponde con status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print(f"  ❌ Server AI non raggiungibile su {base_url}")
        print(f"  ℹ️  Avvialo con: cd ai_server && uvicorn main:app --reload")
        return False
    except Exception as e:
        print(f"  ❌ Errore: {e}")
        return False
    
    # Test endpoint dataset status
    try:
        response = requests.get(f"{base_url}/api/roof-training/dataset/status")
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Dataset status API funzionante")
            print(f"     - Immagini: {data.get('total_images', 0)}")
            print(f"     - Tetti: {data.get('total_roofs', 0)}")
            print(f"     - Annotati: {data.get('annotated', 0)}")
        else:
            print(f"  ⚠️  Dataset status risponde con {response.status_code}")
    except Exception as e:
        print(f"  ❌ Errore dataset status: {e}")
    
    return True

def test_frontend():
    """Verifica che il frontend sia configurato"""
    print("\n💻 Verificando frontend...")
    
    frontend_path = Path("../frontend")
    
    if not frontend_path.exists():
        print(f"  ❌ Directory frontend non trovata")
        return False
    
    # Verifica file componenti
    components = [
        "src/features/roof-classifier/RoofClassifierDashboard.tsx",
        "src/features/roof-classifier/components/DatasetManager.tsx",
        "src/features/roof-classifier/components/AnnotationTool.tsx",
        "src/features/roof-classifier/components/TrainingMonitor.tsx",
        "src/features/roof-classifier/components/ModelTester.tsx",
        "src/routes/roof-classifier.tsx"
    ]
    
    all_exist = True
    for component in components:
        path = frontend_path / component
        if path.exists():
            print(f"  ✅ {component.split('/')[-1]}")
        else:
            print(f"  ❌ Mancante: {component}")
            all_exist = False
    
    if all_exist:
        print(f"\n  ℹ️  Avvia il frontend con:")
        print(f"     cd ../frontend && pnpm dev")
    
    return all_exist

def create_sample_data():
    """Crea dati di esempio per test"""
    print("\n📝 Creando dati di esempio...")
    
    # Crea immagine di test
    test_img = Image.new('RGB', (640, 640), color='blue')
    test_path = Path("dataset/roofs/raw_images/sample_test.jpg")
    test_path.parent.mkdir(parents=True, exist_ok=True)
    test_img.save(test_path)
    print(f"  ✅ Creata immagine test: {test_path}")
    
    # Crea annotazione di esempio
    annotation = {
        "id": "sample_001",
        "filename": "sample_test.jpg",
        "source_image": str(test_path),
        "bbox": [100, 100, 300, 300],
        "area": 40000,
        "material": "unknown",
        "needs_annotation": True,
        "created_at": "2024-01-01T00:00:00"
    }
    
    ann_path = Path("dataset/roofs/annotations/annotations.json")
    ann_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Carica esistenti o crea nuovo
    if ann_path.exists():
        with open(ann_path, 'r') as f:
            annotations = json.load(f)
    else:
        annotations = []
    
    # Aggiungi se non esiste già
    if not any(a['id'] == 'sample_001' for a in annotations):
        annotations.append(annotation)
        with open(ann_path, 'w') as f:
            json.dump(annotations, f, indent=2)
        print(f"  ✅ Creata annotazione di esempio")
    else:
        print(f"  ℹ️  Annotazione di esempio già esistente")
    
    return True

def main():
    """Esegue tutti i test"""
    print("=" * 50)
    print("🚀 TEST SISTEMA CLASSIFICATORE TETTI")
    print("=" * 50)
    
    results = {
        "Directory": test_directories(),
        "Dipendenze": test_imports(),
        "Downloader": test_downloader(),
        "Modello SAM": test_sam_model(),
        "Frontend": test_frontend(),
        "Dati esempio": create_sample_data()
    }
    
    # Test API solo se possibile
    if results["Dipendenze"]:
        results["API"] = test_api_endpoints()
    
    # Riepilogo
    print("\n" + "=" * 50)
    print("📊 RIEPILOGO TEST")
    print("=" * 50)
    
    for test, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {test}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 Tutti i test passati! Il sistema è pronto.")
    else:
        print("\n⚠️  Alcuni test falliti. Correggere i problemi sopra indicati.")
    
    print("\n📚 PROSSIMI PASSI:")
    print("1. Avvia il server AI: cd ai_server && uvicorn main:app --reload")
    print("2. Avvia il frontend: cd ../frontend && pnpm dev")
    print("3. Apri browser: http://localhost:5173/roof-classifier")
    
    return all_passed

if __name__ == "__main__":
    sys.exit(0 if main() else 1)