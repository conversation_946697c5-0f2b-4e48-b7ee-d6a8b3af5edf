#!/usr/bin/env python3
"""
Optimized Roof Classifier Training for AMD Ryzen Z1 Extreme
Optimizations:
- Mixed precision training
- CPU optimizations for AMD
- Efficient data loading
- Model quantization for inference
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms, models
import numpy as np
from PIL import Image
import time
from pathlib import Path
import json

# AMD Ryzen Z1 Extreme optimizations
def setup_amd_optimizations():
    """Configure optimizations for AMD Ryzen Z1 Extreme"""
    
    # Set thread count for optimal performance
    # Z1 Extreme has 8 cores, 16 threads
    torch.set_num_threads(12)  # Leave some threads for system
    
    # Enable MKL-DNN optimizations if available
    if hasattr(torch, 'backends'):
        if hasattr(torch.backends, 'mkldnn'):
            torch.backends.mkldnn.enabled = True
    
    # Set memory pinning for faster data transfer
    torch.backends.cudnn.benchmark = True
    
    print("AMD Ryzen Z1 Extreme Optimizations:")
    print(f"  - CPU threads: {torch.get_num_threads()}")
    print(f"  - MKL-DNN: {'Enabled' if torch.backends.mkldnn.enabled else 'Disabled'}")
    
    # Check for AVX2 support (Z1 Extreme supports it)
    import platform
    if platform.processor():
        print(f"  - Processor: {platform.processor()}")
    
    return True

class EfficientRoofDataset(Dataset):
    """Memory-efficient dataset for roof images"""
    
    def __init__(self, root_dir, transform=None, cache_size=100):
        self.root_dir = Path(root_dir)
        self.transform = transform
        self.cache_size = cache_size
        self.cache = {}
        
        # Roof material classes
        self.classes = ['tile', 'metal', 'shingle', 'concrete', 'solar']
        self.class_to_idx = {cls: i for i, cls in enumerate(self.classes)}
        
        # Collect all image paths
        self.samples = []
        for class_name in self.classes:
            class_dir = self.root_dir / class_name
            if class_dir.exists():
                for img_path in class_dir.glob('*.jpg'):
                    self.samples.append((img_path, self.class_to_idx[class_name]))
        
        print(f"Dataset: {len(self.samples)} images across {len(self.classes)} classes")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label = self.samples[idx]
        
        # Use cache for frequently accessed images
        if idx in self.cache:
            image = self.cache[idx]
        else:
            image = Image.open(img_path).convert('RGB')
            # Update cache (FIFO)
            if len(self.cache) >= self.cache_size:
                self.cache.pop(next(iter(self.cache)))
            self.cache[idx] = image
        
        if self.transform:
            image = self.transform(image)
        
        return image, label

class LightweightRoofClassifier(nn.Module):
    """Lightweight CNN optimized for CPU inference"""
    
    def __init__(self, num_classes=5):
        super().__init__()
        
        # Use depthwise separable convolutions for efficiency
        self.features = nn.Sequential(
            # Block 1
            nn.Conv2d(3, 32, kernel_size=3, stride=2, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            
            # Block 2 - Depthwise separable
            nn.Conv2d(32, 32, kernel_size=3, padding=1, groups=32),
            nn.Conv2d(32, 64, kernel_size=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            # Block 3 - Depthwise separable
            nn.Conv2d(64, 64, kernel_size=3, padding=1, groups=64),
            nn.Conv2d(64, 128, kernel_size=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            # Block 4 - Depthwise separable
            nn.Conv2d(128, 128, kernel_size=3, padding=1, groups=128),
            nn.Conv2d(128, 256, kernel_size=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d(1)
        )
        
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(256, num_classes)
        )
    
    def forward(self, x):
        x = self.features(x)
        x = x.view(x.size(0), -1)
        x = self.classifier(x)
        return x

def train_with_mixed_precision(model, train_loader, val_loader, num_epochs=10):
    """Train with mixed precision for better performance"""
    
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
    
    # Mixed precision training (if available)
    use_amp = hasattr(torch.cuda.amp, 'autocast')
    scaler = torch.cuda.amp.GradScaler() if use_amp else None
    
    best_acc = 0.0
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        epoch_start = time.time()
        
        for batch_idx, (images, labels) in enumerate(train_loader):
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(images)
            loss = criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            # Statistics
            train_loss += loss.item()
            _, predicted = outputs.max(1)
            train_total += labels.size(0)
            train_correct += predicted.eq(labels).sum().item()
            
            if batch_idx % 10 == 0:
                print(f'  Batch {batch_idx}/{len(train_loader)}, '
                      f'Loss: {loss.item():.4f}, '
                      f'Acc: {100.*train_correct/train_total:.2f}%')
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for images, labels in val_loader:
                outputs = model(images)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = outputs.max(1)
                val_total += labels.size(0)
                val_correct += predicted.eq(labels).sum().item()
        
        # Calculate metrics
        train_acc = 100. * train_correct / train_total
        val_acc = 100. * val_correct / val_total
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        epoch_time = time.time() - epoch_start
        
        print(f'\nEpoch {epoch+1}/{num_epochs}:')
        print(f'  Time: {epoch_time:.2f}s')
        print(f'  Train Loss: {avg_train_loss:.4f}, Acc: {train_acc:.2f}%')
        print(f'  Val Loss: {avg_val_loss:.4f}, Acc: {val_acc:.2f}%')
        
        # Save history
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(val_acc)
        
        # Save best model
        if val_acc > best_acc:
            best_acc = val_acc
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_acc': best_acc,
                'history': history
            }, 'best_roof_model_z1.pth')
            print(f'  ✓ Saved best model (Acc: {best_acc:.2f}%)')
        
        scheduler.step()
    
    return history

def quantize_model_for_inference(model_path):
    """Quantize model for faster CPU inference"""
    
    # Load model
    checkpoint = torch.load(model_path, map_location='cpu')
    model = LightweightRoofClassifier()
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Dynamic quantization for CPU
    quantized_model = torch.quantization.quantize_dynamic(
        model,
        {nn.Linear, nn.Conv2d},
        dtype=torch.qint8
    )
    
    # Save quantized model
    torch.save(quantized_model, 'roof_model_quantized_z1.pth')
    
    # Compare model sizes
    original_size = os.path.getsize(model_path) / (1024 * 1024)
    quantized_size = os.path.getsize('roof_model_quantized_z1.pth') / (1024 * 1024)
    
    print(f"Model Quantization:")
    print(f"  Original size: {original_size:.2f} MB")
    print(f"  Quantized size: {quantized_size:.2f} MB")
    print(f"  Reduction: {(1 - quantized_size/original_size)*100:.1f}%")
    
    return quantized_model

def benchmark_inference(model, test_loader):
    """Benchmark inference performance"""
    
    model.eval()
    
    # Warmup
    with torch.no_grad():
        dummy_input = torch.randn(1, 3, 224, 224)
        for _ in range(10):
            _ = model(dummy_input)
    
    # Actual benchmark
    total_time = 0
    num_samples = 0
    
    with torch.no_grad():
        for images, _ in test_loader:
            start = time.time()
            _ = model(images)
            total_time += time.time() - start
            num_samples += images.size(0)
    
    avg_time = total_time / num_samples * 1000  # ms
    throughput = num_samples / total_time  # images/sec
    
    print(f"\nInference Performance:")
    print(f"  Average time: {avg_time:.2f} ms/image")
    print(f"  Throughput: {throughput:.1f} images/sec")
    print(f"  Total samples: {num_samples}")
    
    return avg_time, throughput

def main():
    print("="*60)
    print("Roof Classifier Training - Optimized for Ryzen Z1 Extreme")
    print("="*60)
    
    # Setup optimizations
    setup_amd_optimizations()
    
    # Data transforms
    transform_train = transforms.Compose([
        transforms.RandomResizedCrop(224),
        transforms.RandomHorizontalFlip(),
        transforms.ColorJitter(brightness=0.2, contrast=0.2),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    transform_val = transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    # Create synthetic dataset for testing
    # In real use, point this to your actual roof images
    print("\nNote: Using synthetic data for demonstration")
    print("Replace 'dataset/roofs' with your actual dataset path")
    
    # Model
    print("\nInitializing lightweight model...")
    model = LightweightRoofClassifier(num_classes=5)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    # Create synthetic data for demonstration
    print("\nCreating synthetic training data...")
    batch_size = 32  # Optimal for Z1 Extreme
    
    # Synthetic data loaders
    train_data = torch.randn(200, 3, 224, 224)
    train_labels = torch.randint(0, 5, (200,))
    train_dataset = torch.utils.data.TensorDataset(train_data, train_labels)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, 
                            shuffle=True, num_workers=4, pin_memory=True)
    
    val_data = torch.randn(50, 3, 224, 224)
    val_labels = torch.randint(0, 5, (50,))
    val_dataset = torch.utils.data.TensorDataset(val_data, val_labels)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, 
                          shuffle=False, num_workers=4, pin_memory=True)
    
    # Train
    print("\nStarting training...")
    history = train_with_mixed_precision(model, train_loader, val_loader, num_epochs=5)
    
    # Benchmark
    print("\nBenchmarking inference...")
    benchmark_inference(model, val_loader)
    
    # Quantize for deployment
    if os.path.exists('best_roof_model_z1.pth'):
        print("\nQuantizing model for deployment...")
        quantized_model = quantize_model_for_inference('best_roof_model_z1.pth')
        benchmark_inference(quantized_model, val_loader)
    
    print("\n" + "="*60)
    print("Training complete!")
    print("For production use:")
    print("  1. Replace synthetic data with real roof images")
    print("  2. Organize images in folders by material type")
    print("  3. Adjust batch_size based on available RAM")
    print("  4. Use the quantized model for deployment")
    print("="*60)

if __name__ == "__main__":
    main()