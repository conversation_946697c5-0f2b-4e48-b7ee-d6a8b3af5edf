#!/usr/bin/env python3
"""
ML Benchmark Script for Ryzen Z1 Extreme
Tests various ML workloads relevant to roof classification
"""

import os
import sys
import time
import psutil
import numpy as np
import json
from datetime import datetime
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Check for required libraries
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
    print(f"✓ PyTorch {torch.__version__} available")
    
    # Check for CUDA
    if torch.cuda.is_available():
        print(f"✓ CUDA available: {torch.cuda.get_device_name(0)}")
        print(f"  CUDA version: {torch.version.cuda}")
        print(f"  cuDNN version: {torch.backends.cudnn.version()}")
        device = torch.device("cuda")
    else:
        print("✗ CUDA not available, using CPU")
        device = torch.device("cpu")
except ImportError:
    TORCH_AVAILABLE = False
    print("✗ PyTorch not installed")
    device = None

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.neural_network import MLPClassifier
    import cv2
    SKLEARN_AVAILABLE = True
    print(f"✓ scikit-learn available")
    print(f"✓ OpenCV {cv2.__version__} available")
except ImportError:
    SKLEARN_AVAILABLE = False
    print("✗ scikit-learn or OpenCV not installed")

# System info
def get_system_info():
    """Get system specifications"""
    info = {
        "cpu": {
            "model": "AMD Ryzen Z1 Extreme",  # You mentioned this
            "cores": psutil.cpu_count(logical=False),
            "threads": psutil.cpu_count(logical=True),
            "frequency": psutil.cpu_freq().max if psutil.cpu_freq() else "N/A"
        },
        "memory": {
            "total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
            "available_gb": round(psutil.virtual_memory().available / (1024**3), 2)
        },
        "gpu": None
    }
    
    if TORCH_AVAILABLE and torch.cuda.is_available():
        info["gpu"] = {
            "name": torch.cuda.get_device_name(0),
            "memory_gb": round(torch.cuda.get_device_properties(0).total_memory / (1024**3), 2)
        }
    
    return info

# Simple CNN for roof classification
class RoofClassifierCNN(nn.Module):
    """Simple CNN for roof material classification"""
    def __init__(self, num_classes=5):
        super(RoofClassifierCNN, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.pool = nn.MaxPool2d(2, 2)
        self.fc1 = nn.Linear(128 * 28 * 28, 256)
        self.fc2 = nn.Linear(256, num_classes)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.5)
        
    def forward(self, x):
        x = self.pool(self.relu(self.conv1(x)))
        x = self.pool(self.relu(self.conv2(x)))
        x = self.pool(self.relu(self.conv3(x)))
        x = x.view(x.size(0), -1)
        x = self.dropout(self.relu(self.fc1(x)))
        x = self.fc2(x)
        return x

def benchmark_numpy_operations(size=1000):
    """Benchmark NumPy operations"""
    print("\n" + "="*60)
    print("NUMPY BENCHMARK")
    print("="*60)
    
    results = {}
    
    # Matrix multiplication
    print(f"Matrix multiplication ({size}x{size})...", end=" ")
    A = np.random.randn(size, size).astype(np.float32)
    B = np.random.randn(size, size).astype(np.float32)
    
    start = time.time()
    C = np.dot(A, B)
    elapsed = time.time() - start
    results["matrix_mult"] = elapsed
    print(f"{elapsed:.4f}s")
    
    # FFT for image processing
    print(f"FFT (2D, {size}x{size})...", end=" ")
    img = np.random.randn(size, size).astype(np.float32)
    
    start = time.time()
    fft_result = np.fft.fft2(img)
    elapsed = time.time() - start
    results["fft_2d"] = elapsed
    print(f"{elapsed:.4f}s")
    
    # Convolution (image filtering)
    print(f"Convolution (100 filters on {size}x{size})...", end=" ")
    kernel = np.random.randn(5, 5).astype(np.float32)
    
    start = time.time()
    for _ in range(100):
        from scipy.signal import convolve2d
        filtered = convolve2d(img, kernel, mode='same')
    elapsed = time.time() - start
    results["convolution"] = elapsed
    print(f"{elapsed:.4f}s")
    
    return results

def benchmark_pytorch_cnn(batch_sizes=[16, 32, 64], num_epochs=5):
    """Benchmark PyTorch CNN training"""
    if not TORCH_AVAILABLE:
        return {"error": "PyTorch not available"}
    
    print("\n" + "="*60)
    print("PYTORCH CNN BENCHMARK")
    print("="*60)
    print(f"Device: {device}")
    
    results = {}
    
    # Create synthetic dataset (224x224 RGB images, 5 classes)
    num_samples = 500
    num_classes = 5
    
    for batch_size in batch_sizes:
        print(f"\nBatch size: {batch_size}")
        
        # Generate synthetic data
        X = torch.randn(num_samples, 3, 224, 224)
        y = torch.randint(0, num_classes, (num_samples,))
        
        dataset = TensorDataset(X, y)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # Initialize model
        model = RoofClassifierCNN(num_classes=num_classes).to(device)
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        
        # Training benchmark
        start_time = time.time()
        
        for epoch in range(num_epochs):
            epoch_loss = 0.0
            for batch_X, batch_y in dataloader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
            
            print(f"  Epoch {epoch+1}/{num_epochs}, Loss: {epoch_loss/len(dataloader):.4f}")
        
        training_time = time.time() - start_time
        
        # Inference benchmark
        model.eval()
        with torch.no_grad():
            test_batch = torch.randn(batch_size, 3, 224, 224).to(device)
            
            # Warmup
            for _ in range(10):
                _ = model(test_batch)
            
            # Actual benchmark
            start_time = time.time()
            num_inferences = 100
            for _ in range(num_inferences):
                _ = model(test_batch)
            
            inference_time = (time.time() - start_time) / num_inferences
        
        results[f"batch_{batch_size}"] = {
            "training_time": training_time,
            "avg_epoch_time": training_time / num_epochs,
            "inference_time_ms": inference_time * 1000,
            "images_per_second": batch_size / inference_time
        }
        
        print(f"  Training time: {training_time:.2f}s")
        print(f"  Inference: {inference_time*1000:.2f}ms per batch")
        print(f"  Throughput: {batch_size/inference_time:.1f} images/sec")
    
    return results

def benchmark_sklearn_models():
    """Benchmark scikit-learn models for feature-based classification"""
    if not SKLEARN_AVAILABLE:
        return {"error": "scikit-learn not available"}
    
    print("\n" + "="*60)
    print("SCIKIT-LEARN BENCHMARK")
    print("="*60)
    
    results = {}
    
    # Generate synthetic feature data (e.g., extracted features from roof images)
    n_samples = 10000
    n_features = 100  # Simulating extracted features (color histograms, texture, etc.)
    n_classes = 5
    
    X = np.random.randn(n_samples, n_features).astype(np.float32)
    y = np.random.randint(0, n_classes, n_samples)
    
    # Random Forest
    print("Random Forest Classifier...", end=" ")
    rf = RandomForestClassifier(n_estimators=100, n_jobs=-1)
    
    start = time.time()
    rf.fit(X, y)
    training_time = time.time() - start
    
    start = time.time()
    predictions = rf.predict(X[:1000])
    inference_time = time.time() - start
    
    results["random_forest"] = {
        "training_time": training_time,
        "inference_time_1000": inference_time
    }
    print(f"Train: {training_time:.3f}s, Inference: {inference_time:.3f}s")
    
    # Neural Network
    print("MLP Classifier...", end=" ")
    mlp = MLPClassifier(hidden_layer_sizes=(256, 128), max_iter=100)
    
    start = time.time()
    mlp.fit(X, y)
    training_time = time.time() - start
    
    start = time.time()
    predictions = mlp.predict(X[:1000])
    inference_time = time.time() - start
    
    results["mlp"] = {
        "training_time": training_time,
        "inference_time_1000": inference_time
    }
    print(f"Train: {training_time:.3f}s, Inference: {inference_time:.3f}s")
    
    return results

def benchmark_image_processing():
    """Benchmark image processing operations"""
    if not SKLEARN_AVAILABLE:  # cv2 is imported with sklearn
        return {"error": "OpenCV not available"}
    
    print("\n" + "="*60)
    print("IMAGE PROCESSING BENCHMARK")
    print("="*60)
    
    results = {}
    
    # Create synthetic image (1024x1024 RGB)
    img = np.random.randint(0, 255, (1024, 1024, 3), dtype=np.uint8)
    
    # Resize operations (common in preprocessing)
    print("Image resizing (100 iterations)...", end=" ")
    start = time.time()
    for _ in range(100):
        resized = cv2.resize(img, (224, 224))
    elapsed = time.time() - start
    results["resize_100"] = elapsed
    print(f"{elapsed:.3f}s")
    
    # Edge detection (for roof boundary detection)
    print("Edge detection (Canny, 100 iterations)...", end=" ")
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    start = time.time()
    for _ in range(100):
        edges = cv2.Canny(gray, 100, 200)
    elapsed = time.time() - start
    results["canny_100"] = elapsed
    print(f"{elapsed:.3f}s")
    
    # Feature extraction (ORB)
    print("Feature extraction (ORB)...", end=" ")
    orb = cv2.ORB_create()
    start = time.time()
    keypoints, descriptors = orb.detectAndCompute(gray, None)
    elapsed = time.time() - start
    results["orb_features"] = elapsed
    print(f"{elapsed:.3f}s, found {len(keypoints)} keypoints")
    
    return results

def calculate_performance_score(results: Dict) -> float:
    """Calculate overall performance score"""
    score = 100.0
    
    # Weights for different benchmarks
    weights = {
        "numpy": 0.2,
        "pytorch": 0.4,
        "sklearn": 0.2,
        "image": 0.2
    }
    
    # Reference times (based on typical mid-range hardware)
    # These are rough estimates, adjust based on your needs
    if "numpy" in results:
        numpy_score = 100
        if results["numpy"]["matrix_mult"] > 0.5:
            numpy_score -= (results["numpy"]["matrix_mult"] - 0.5) * 50
        score *= (numpy_score / 100) * weights["numpy"]
    
    if "pytorch" in results and "batch_32" in results["pytorch"]:
        pytorch_score = 100
        if results["pytorch"]["batch_32"]["inference_time_ms"] > 50:
            pytorch_score -= (results["pytorch"]["batch_32"]["inference_time_ms"] - 50) * 2
        score *= (pytorch_score / 100) * weights["pytorch"]
    
    return max(0, min(100, score))

def main():
    """Main benchmark function"""
    print("\n" + "="*60)
    print("ML PERFORMANCE BENCHMARK FOR ROOF CLASSIFICATION")
    print("AMD Ryzen Z1 Extreme Test Suite")
    print("="*60)
    
    # Get system info
    system_info = get_system_info()
    print("\nSystem Information:")
    print(f"  CPU: {system_info['cpu']['model']}")
    print(f"  Cores: {system_info['cpu']['cores']} physical, {system_info['cpu']['threads']} logical")
    print(f"  RAM: {system_info['memory']['total_gb']} GB total, {system_info['memory']['available_gb']} GB available")
    if system_info['gpu']:
        print(f"  GPU: {system_info['gpu']['name']} ({system_info['gpu']['memory_gb']} GB)")
    
    results = {
        "timestamp": datetime.now().isoformat(),
        "system": system_info,
        "benchmarks": {}
    }
    
    # Run benchmarks
    try:
        # NumPy benchmarks
        print("\nStarting NumPy benchmarks...")
        results["benchmarks"]["numpy"] = benchmark_numpy_operations(size=1000)
        
        # PyTorch benchmarks
        if TORCH_AVAILABLE:
            print("\nStarting PyTorch benchmarks...")
            results["benchmarks"]["pytorch"] = benchmark_pytorch_cnn(
                batch_sizes=[16, 32],
                num_epochs=3
            )
        
        # Scikit-learn benchmarks
        if SKLEARN_AVAILABLE:
            print("\nStarting scikit-learn benchmarks...")
            results["benchmarks"]["sklearn"] = benchmark_sklearn_models()
            
            print("\nStarting image processing benchmarks...")
            results["benchmarks"]["image_processing"] = benchmark_image_processing()
    
    except Exception as e:
        print(f"\nError during benchmark: {e}")
        results["error"] = str(e)
    
    # Summary
    print("\n" + "="*60)
    print("BENCHMARK SUMMARY")
    print("="*60)
    
    if "pytorch" in results["benchmarks"] and "batch_32" in results["benchmarks"]["pytorch"]:
        pytorch_results = results["benchmarks"]["pytorch"]["batch_32"]
        print(f"\n📊 Deep Learning Performance:")
        print(f"  Training speed: {pytorch_results['avg_epoch_time']:.2f}s per epoch")
        print(f"  Inference speed: {pytorch_results['inference_time_ms']:.2f}ms per batch")
        print(f"  Throughput: {pytorch_results['images_per_second']:.1f} images/second")
        
        # Performance assessment
        if pytorch_results['images_per_second'] > 100:
            print("  ✅ Excellent performance for roof classification!")
        elif pytorch_results['images_per_second'] > 50:
            print("  ✅ Good performance, suitable for small-medium datasets")
        elif pytorch_results['images_per_second'] > 20:
            print("  ⚠️ Moderate performance, consider smaller models or batch sizes")
        else:
            print("  ❌ Limited performance, may need GPU acceleration")
    
    # Save results
    output_file = f"benchmark_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Results saved to: {output_file}")
    
    # Recommendations
    print("\n" + "="*60)
    print("RECOMMENDATIONS FOR ROOF CLASSIFICATION")
    print("="*60)
    
    if TORCH_AVAILABLE and torch.cuda.is_available():
        print("✅ GPU acceleration available - use it for training")
    else:
        print("⚠️ No GPU detected - consider:")
        print("  - Using smaller models (MobileNet, EfficientNet-B0)")
        print("  - Reducing batch size")
        print("  - Using mixed precision training")
        print("  - Pre-training on cloud and fine-tuning locally")
    
    if system_info['memory']['available_gb'] < 8:
        print("⚠️ Limited RAM - consider:")
        print("  - Using data generators instead of loading all data")
        print("  - Reducing image resolution")
        print("  - Using model checkpointing")
    
    print("\nOptimal settings for your system:")
    if pytorch_results['images_per_second'] > 50:
        print("  - Batch size: 32")
        print("  - Image size: 224x224")
        print("  - Model: ResNet18 or custom CNN")
    else:
        print("  - Batch size: 16")
        print("  - Image size: 128x128")
        print("  - Model: MobileNetV2 or simple CNN")
    
    return results

if __name__ == "__main__":
    results = main()