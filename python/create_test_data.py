#!/usr/bin/env python3
"""
Crea dati di test per il sistema di classificazione tetti
"""

import json
import numpy as np
from pathlib import Path
from PIL import Image, ImageDraw
import random

def create_test_images_and_annotations():
    """Crea immagini e annotazioni di test"""
    
    # Directory
    raw_dir = Path("dataset/roofs/raw_images")
    cropped_dir = Path("dataset/roofs/cropped")
    annotations_dir = Path("dataset/roofs/annotations")
    
    # Crea directory
    for d in [raw_dir, cropped_dir, annotations_dir]:
        d.mkdir(parents=True, exist_ok=True)
    
    # Materiali disponibili
    materials = [
        'terracotta_tiles',
        'cement_tiles',
        'slate_tiles',
        'metal_sheet',
        'asphalt_shingles',
        'flat_concrete',
        'green_roof',
        'solar_panels',
        'mixed_materials'
    ]
    
    # Colori per simulare diversi materiali
    material_colors = {
        'terracotta_tiles': (180, 100, 50),
        'cement_tiles': (150, 150, 150),
        'slate_tiles': (70, 70, 90),
        'metal_sheet': (200, 200, 210),
        'asphalt_shingles': (50, 50, 50),
        'flat_concrete': (180, 180, 180),
        'green_roof': (50, 150, 50),
        'solar_panels': (30, 30, 100),
        'mixed_materials': (120, 100, 80)
    }
    
    annotations = []
    
    # Crea 20 immagini di test
    for i in range(20):
        # Scegli un materiale random
        material = random.choice(materials)
        color = material_colors[material]
        
        # Crea immagine con pattern che simula tetto
        img = Image.new('RGB', (256, 256), color='lightblue')
        draw = ImageDraw.Draw(img)
        
        # Aggiungi texture base
        for y in range(0, 256, 20):
            for x in range(0, 256, 30):
                # Varia leggermente il colore
                var_color = tuple(c + random.randint(-20, 20) for c in color)
                var_color = tuple(max(0, min(255, c)) for c in var_color)
                
                # Disegna "tegole" o pattern
                if material == 'terracotta_tiles':
                    draw.rectangle([x, y, x+28, y+18], fill=var_color, outline=(0,0,0))
                elif material == 'solar_panels':
                    draw.rectangle([x, y, x+28, y+18], fill=var_color, outline='white')
                    # Aggiungi griglia
                    draw.line([x+14, y, x+14, y+18], fill='white')
                    draw.line([x, y+9, x+28, y+9], fill='white')
                elif material == 'metal_sheet':
                    draw.rectangle([x, y, x+28, y+18], fill=var_color)
                    # Aggiungi nervature
                    draw.line([x+7, y, x+7, y+18], fill='gray')
                    draw.line([x+21, y, x+21, y+18], fill='gray')
                else:
                    draw.rectangle([x, y, x+28, y+18], fill=var_color)
        
        # Aggiungi un po' di rumore
        pixels = img.load()
        for y in range(256):
            for x in range(256):
                if random.random() < 0.1:
                    noise = random.randint(-30, 30)
                    r, g, b = pixels[x, y]
                    pixels[x, y] = (
                        max(0, min(255, r + noise)),
                        max(0, min(255, g + noise)),
                        max(0, min(255, b + noise))
                    )
        
        # Salva immagine
        filename = f"test_roof_{i:03d}.jpg"
        filepath = cropped_dir / filename
        img.save(filepath, quality=90)
        
        # Crea annotazione
        needs_annotation = i >= 10  # Metà annotate, metà no
        annotation = {
            "id": f"test_{i:03d}",
            "filename": filename,
            "source_image": f"dataset/roofs/raw_images/source_{i:03d}.jpg",
            "bbox": [10, 10, 246, 246],
            "mask_file": f"test_roof_{i:03d}_mask.png",
            "area": 60000 + random.randint(-10000, 10000),
            "stability_score": random.uniform(0.85, 0.99),
            "material": "unknown" if needs_annotation else material,
            "needs_annotation": needs_annotation,
            "confidence": 1.0 if not needs_annotation else None,
            "created_at": "2024-01-01T00:00:00"
        }
        
        annotations.append(annotation)
        
        print(f"✅ Creata immagine test {i+1}/20: {filename} ({material})")
    
    # Salva annotazioni
    ann_file = annotations_dir / "annotations.json"
    with open(ann_file, 'w') as f:
        json.dump(annotations, f, indent=2)
    
    print(f"\n📝 Salvate {len(annotations)} annotazioni in {ann_file}")
    
    # Crea split train/val/test
    random.shuffle(annotations)
    n_train = int(len(annotations) * 0.7)
    n_val = int(len(annotations) * 0.15)
    
    splits = {
        "train": [a for a in annotations[:n_train] if a['material'] != 'unknown'],
        "val": [a for a in annotations[n_train:n_train+n_val] if a['material'] != 'unknown'],
        "test": [a for a in annotations[n_train+n_val:] if a['material'] != 'unknown']
    }
    
    for split_name, split_data in splits.items():
        split_file = annotations_dir / f"{split_name}.json"
        with open(split_file, 'w') as f:
            json.dump(split_data, f, indent=2)
        print(f"✅ Creato split {split_name}: {len(split_data)} campioni")
    
    # Aggiorna statistiche
    stats = {
        "total_images": 20,
        "total_roofs": len(annotations),
        "annotated": len([a for a in annotations if not a['needs_annotation']]),
        "pending_annotation": len([a for a in annotations if a['needs_annotation']]),
        "last_updated": "2024-01-01T00:00:00"
    }
    
    print(f"\n📊 Statistiche dataset:")
    print(f"   - Immagini totali: {stats['total_images']}")
    print(f"   - Tetti totali: {stats['total_roofs']}")  
    print(f"   - Annotati: {stats['annotated']}")
    print(f"   - Da annotare: {stats['pending_annotation']}")
    
    return stats

if __name__ == "__main__":
    create_test_images_and_annotations()
    print("\n✅ Dati di test creati con successo!")
    print("   Ora puoi aprire http://localhost:5173/roof-classifier per testare il sistema")