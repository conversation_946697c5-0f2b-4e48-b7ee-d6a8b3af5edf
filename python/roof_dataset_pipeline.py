#!/usr/bin/env python3
"""
Pipeline completa per la creazione del dataset di classificazione materiali tetti.
Integra il downloader Google Maps con SAM per segmentazione automatica.
"""

import os
import sys
import json
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import cv2
from PIL import Image
import torch
from tqdm import tqdm
import argparse
import hashlib
from datetime import datetime
import logging
import asyncio
import aiohttp

# Aggiungi il percorso per importare i moduli esistenti
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ai_server'))

# Import del downloader async esistente
from downloader_async import TileDownloader

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RoofDatasetPipeline:
    """Pipeline per creare dataset di tetti segmentati per training."""
    
    def __init__(self, output_dir: str = "dataset/roofs"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Directory struttura dataset
        self.raw_images_dir = self.output_dir / "raw_images"
        self.segmented_dir = self.output_dir / "segmented"
        self.cropped_dir = self.output_dir / "cropped"
        self.annotations_dir = self.output_dir / "annotations"
        
        for dir_path in [self.raw_images_dir, self.segmented_dir, 
                        self.cropped_dir, self.annotations_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # SAM verrà caricato quando necessario
        self.sam_predictor = None
        
        # Metadata del dataset
        self.metadata = {
            "created_at": datetime.now().isoformat(),
            "images": [],
            "statistics": {
                "total_images": 0,
                "total_roofs": 0,
                "avg_roofs_per_image": 0
            }
        }
    
    def download_satellite_images(self, 
                                 locations: List[Tuple[float, float]], 
                                 zoom: int = 19,
                                 size: Tuple[int, int] = (640, 640)) -> List[str]:
        """
        Scarica immagini satellitari per le location specificate.
        
        Args:
            locations: Lista di tuple (lat, lon)
            zoom: Livello zoom Google Maps (19-20 per dettagli tetti)
            size: Dimensione immagine in pixel
            
        Returns:
            Lista dei percorsi delle immagini scaricate
        """
        downloaded_paths = []
        
        for i, (lat, lon) in enumerate(tqdm(locations, desc="Downloading satellite images")):
            # Genera nome univoco basato su coordinate
            hash_id = hashlib.md5(f"{lat}_{lon}_{zoom}".encode()).hexdigest()[:8]
            filename = f"sat_{lat:.6f}_{lon:.6f}_{zoom}_{hash_id}.jpg"
            output_path = self.raw_images_dir / filename
            
            if output_path.exists():
                logger.info(f"Image already exists: {filename}")
                downloaded_paths.append(str(output_path))
                continue
            
            try:
                # Usa il downloader esistente
                downloader = GoogleMapsDownloader(
                    lat=lat,
                    lng=lon,
                    zoom=zoom,
                    size_x=size[0],
                    size_y=size[1],
                    scale=2,  # Alta risoluzione
                    maptype='satellite',
                    format='jpg'
                )
                
                # Scarica l'immagine
                downloader.save_image(str(output_path))
                downloaded_paths.append(str(output_path))
                logger.info(f"Downloaded: {filename}")
                
            except Exception as e:
                logger.error(f"Error downloading image for {lat}, {lon}: {e}")
        
        return downloaded_paths
    
    def segment_roofs(self, image_path: str) -> Dict:
        """
        Segmenta i tetti in un'immagine usando SAM.
        
        Args:
            image_path: Percorso dell'immagine da segmentare
            
        Returns:
            Dizionario con maschere e metadata
        """
        try:
            # Carica immagine
            image = Image.open(image_path)
            image_np = np.array(image)
            
            # Prepara richiesta per SAM
            request = SegmentationRequest(
                image=image_np.tolist(),
                mode=SegmentationMode.AUTOMATIC
            )
            
            # Esegui segmentazione
            result = self.segmentation_service.segment(request)
            
            # Filtra maschere per identificare probabili tetti
            roof_masks = self._filter_roof_candidates(result.masks, image_np)
            
            return {
                "image_path": image_path,
                "masks": roof_masks,
                "num_roofs": len(roof_masks),
                "image_size": image_np.shape[:2]
            }
            
        except Exception as e:
            logger.error(f"Error segmenting {image_path}: {e}")
            return None
    
    def _filter_roof_candidates(self, masks: List[Dict], image: np.ndarray) -> List[Dict]:
        """
        Filtra le maschere per identificare i probabili tetti.
        
        Criteri:
        - Dimensione appropriata (non troppo piccoli né troppo grandi)
        - Forma relativamente regolare
        - Posizione centrale nell'immagine
        - Colore/texture coerenti
        """
        height, width = image.shape[:2]
        min_area = (height * width) * 0.005  # Min 0.5% dell'immagine
        max_area = (height * width) * 0.4    # Max 40% dell'immagine
        
        filtered_masks = []
        
        for mask in masks:
            mask_array = np.array(mask['segmentation'])
            area = np.sum(mask_array)
            
            # Filtro per dimensione
            if area < min_area or area > max_area:
                continue
            
            # Calcola bounding box
            coords = np.argwhere(mask_array)
            if len(coords) == 0:
                continue
                
            y_min, x_min = coords.min(axis=0)
            y_max, x_max = coords.max(axis=0)
            bbox_width = x_max - x_min
            bbox_height = y_max - y_min
            
            # Filtro per aspect ratio (i tetti sono generalmente regolari)
            aspect_ratio = bbox_width / max(bbox_height, 1)
            if aspect_ratio < 0.3 or aspect_ratio > 3.0:
                continue
            
            # Calcola compattezza (quanto la forma riempie il bounding box)
            bbox_area = bbox_width * bbox_height
            compactness = area / max(bbox_area, 1)
            if compactness < 0.4:  # Forme troppo irregolari
                continue
            
            # Aggiungi metadata
            mask['roof_confidence'] = self._calculate_roof_confidence(
                mask_array, image, (x_min, y_min, x_max, y_max)
            )
            
            if mask['roof_confidence'] > 0.3:
                filtered_masks.append(mask)
        
        # Ordina per confidence
        filtered_masks.sort(key=lambda x: x['roof_confidence'], reverse=True)
        
        return filtered_masks
    
    def _calculate_roof_confidence(self, mask: np.ndarray, image: np.ndarray, 
                                  bbox: Tuple[int, int, int, int]) -> float:
        """
        Calcola la probabilità che una maschera rappresenti un tetto.
        """
        x_min, y_min, x_max, y_max = bbox
        roi = image[y_min:y_max, x_min:x_max]
        mask_roi = mask[y_min:y_max, x_min:x_max]
        
        # Estrai features
        features = []
        
        # 1. Texture uniformity (i tetti hanno texture regolari)
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_RGB2GRAY)
        texture_std = np.std(gray_roi[mask_roi > 0]) if np.any(mask_roi > 0) else 100
        features.append(1.0 / (1.0 + texture_std / 50.0))
        
        # 2. Edge strength (i tetti hanno bordi definiti)
        edges = cv2.Canny(gray_roi, 50, 150)
        edge_density = np.mean(edges[mask_roi > 0]) if np.any(mask_roi > 0) else 0
        features.append(edge_density / 255.0)
        
        # 3. Color consistency (i tetti hanno colori uniformi)
        if np.any(mask_roi > 0):
            masked_pixels = roi[mask_roi > 0]
            color_std = np.mean([np.std(masked_pixels[:, i]) for i in range(3)])
            features.append(1.0 / (1.0 + color_std / 30.0))
        else:
            features.append(0)
        
        # Media pesata delle features
        confidence = np.mean(features)
        return float(confidence)
    
    def extract_roof_crops(self, segmentation_result: Dict) -> List[Dict]:
        """
        Estrae ritagli dei singoli tetti dall'immagine.
        """
        if not segmentation_result:
            return []
        
        image = Image.open(segmentation_result['image_path'])
        image_np = np.array(image)
        crops = []
        
        base_name = Path(segmentation_result['image_path']).stem
        
        for i, mask in enumerate(segmentation_result['masks']):
            mask_array = np.array(mask['segmentation']).astype(np.uint8)
            
            # Trova bounding box
            coords = np.argwhere(mask_array)
            if len(coords) == 0:
                continue
                
            y_min, x_min = coords.min(axis=0)
            y_max, x_max = coords.max(axis=0)
            
            # Aggiungi padding
            padding = 10
            y_min = max(0, y_min - padding)
            x_min = max(0, x_min - padding)
            y_max = min(image_np.shape[0], y_max + padding)
            x_max = min(image_np.shape[1], x_max + padding)
            
            # Ritaglia immagine e maschera
            crop_img = image_np[y_min:y_max, x_min:x_max]
            crop_mask = mask_array[y_min:y_max, x_min:x_max]
            
            # Salva ritaglio
            crop_filename = f"{base_name}_roof_{i:03d}.jpg"
            crop_path = self.cropped_dir / crop_filename
            Image.fromarray(crop_img).save(crop_path)
            
            # Salva maschera
            mask_filename = f"{base_name}_roof_{i:03d}_mask.png"
            mask_path = self.segmented_dir / mask_filename
            Image.fromarray(crop_mask * 255).save(mask_path)
            
            # Crea annotazione
            annotation = {
                "filename": crop_filename,
                "source_image": segmentation_result['image_path'],
                "bbox": [x_min, y_min, x_max, y_max],
                "mask_file": mask_filename,
                "confidence": mask.get('roof_confidence', 0),
                "area": int(np.sum(crop_mask)),
                "material": "unknown",  # Da annotare manualmente
                "needs_review": True
            }
            
            crops.append(annotation)
            
        return crops
    
    def create_dataset_from_locations(self, locations: List[Tuple[float, float]], 
                                     zoom: int = 19) -> str:
        """
        Pipeline completa per creare dataset da lista di coordinate.
        """
        logger.info(f"Starting dataset creation for {len(locations)} locations")
        
        # 1. Scarica immagini satellitari
        image_paths = self.download_satellite_images(locations, zoom)
        logger.info(f"Downloaded {len(image_paths)} images")
        
        # 2. Segmenta tetti in ogni immagine
        all_annotations = []
        for image_path in tqdm(image_paths, desc="Segmenting roofs"):
            segmentation = self.segment_roofs(image_path)
            if segmentation:
                # 3. Estrai ritagli dei tetti
                crops = self.extract_roof_crops(segmentation)
                all_annotations.extend(crops)
                
                # Aggiorna metadata
                self.metadata['images'].append({
                    "path": image_path,
                    "num_roofs": len(crops),
                    "timestamp": datetime.now().isoformat()
                })
        
        # 4. Salva annotazioni
        annotations_file = self.annotations_dir / "annotations.json"
        with open(annotations_file, 'w') as f:
            json.dump(all_annotations, f, indent=2)
        
        # 5. Aggiorna e salva metadata
        self.metadata['statistics']['total_images'] = len(image_paths)
        self.metadata['statistics']['total_roofs'] = len(all_annotations)
        self.metadata['statistics']['avg_roofs_per_image'] = (
            len(all_annotations) / max(len(image_paths), 1)
        )
        
        metadata_file = self.output_dir / "metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(self.metadata, f, indent=2)
        
        logger.info(f"Dataset created successfully!")
        logger.info(f"Total images: {len(image_paths)}")
        logger.info(f"Total roofs extracted: {len(all_annotations)}")
        logger.info(f"Dataset saved to: {self.output_dir}")
        
        return str(self.output_dir)
    
    def create_training_splits(self, train_ratio: float = 0.7, 
                              val_ratio: float = 0.15) -> Dict:
        """
        Divide il dataset in train/val/test sets.
        """
        annotations_file = self.annotations_dir / "annotations.json"
        with open(annotations_file, 'r') as f:
            annotations = json.load(f)
        
        # Shuffle
        np.random.shuffle(annotations)
        
        n_total = len(annotations)
        n_train = int(n_total * train_ratio)
        n_val = int(n_total * val_ratio)
        
        splits = {
            "train": annotations[:n_train],
            "val": annotations[n_train:n_train + n_val],
            "test": annotations[n_train + n_val:]
        }
        
        # Salva splits
        for split_name, split_data in splits.items():
            split_file = self.annotations_dir / f"{split_name}.json"
            with open(split_file, 'w') as f:
                json.dump(split_data, f, indent=2)
        
        logger.info(f"Dataset split: Train={len(splits['train'])}, "
                   f"Val={len(splits['val'])}, Test={len(splits['test'])}")
        
        return splits


def main():
    parser = argparse.ArgumentParser(description="Create roof dataset from satellite images")
    parser.add_argument("--locations-file", type=str, 
                       help="JSON file with list of [lat, lon] coordinates")
    parser.add_argument("--output-dir", type=str, default="dataset/roofs",
                       help="Output directory for dataset")
    parser.add_argument("--zoom", type=int, default=19,
                       help="Google Maps zoom level (19-20 recommended)")
    parser.add_argument("--sample-locations", action="store_true",
                       help="Use sample locations for testing")
    
    args = parser.parse_args()
    
    # Inizializza pipeline
    pipeline = RoofDatasetPipeline(output_dir=args.output_dir)
    
    # Carica o genera locations
    if args.sample_locations:
        # Locations di esempio (Italia - zone con vari tipi di tetti)
        locations = [
            (45.4408, 12.3155),  # Venezia - tegole terracotta
            (41.9028, 12.4964),  # Roma - mix materiali
            (45.0703, 7.6869),   # Torino - zone industriali
            (40.8518, 14.2681),  # Napoli - vari stili
            (45.4642, 9.1900),   # Milano - moderno e tradizionale
            (43.7696, 11.2558),  # Firenze - storico
            (44.4949, 11.3426),  # Bologna - tegole rosse
            (45.4064, 11.8768),  # Padova - mix
            (37.5079, 15.0830),  # Catania - mediterraneo
            (38.1157, 13.3615),  # Palermo - vari stili
        ]
    elif args.locations_file:
        with open(args.locations_file, 'r') as f:
            locations = json.load(f)
    else:
        logger.error("Please provide --locations-file or use --sample-locations")
        return
    
    # Crea dataset
    dataset_path = pipeline.create_dataset_from_locations(locations, zoom=args.zoom)
    
    # Crea splits per training
    pipeline.create_training_splits()
    
    print(f"\nDataset created successfully at: {dataset_path}")
    print("\nNext steps:")
    print("1. Review and annotate materials in dataset/roofs/annotations/annotations.json")
    print("2. Run training script: python train_roof_classifier.py")


if __name__ == "__main__":
    main()