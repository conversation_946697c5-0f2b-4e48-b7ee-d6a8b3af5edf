#!/bin/bash

# Setup script per il sistema di classificazione materiali tetti

echo "🚀 Setting up Roof Material Classifier..."

# Naviga alla directory Python
cd "$(dirname "$0")"

# Crea virtual environment se non esiste
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Attiva virtual environment
source venv/bin/activate

# Aggiorna pip
pip install --upgrade pip

echo "📦 Installing ML dependencies..."

# Installa dipendenze base
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# Installa altre dipendenze ML
pip install \
    albumentations>=1.3.0 \
    opencv-python>=4.8.0 \
    Pillow>=10.0.0 \
    numpy>=1.24.0 \
    scipy>=1.10.0 \
    scikit-learn>=1.3.0 \
    matplotlib>=3.7.0 \
    seaborn>=0.12.0 \
    tqdm>=4.65.0

# Installa SAM se non già presente
if ! python -c "import segment_anything" 2>/dev/null; then
    echo "Installing Segment Anything Model..."
    pip install git+https://github.com/facebookresearch/segment-anything.git
fi

# Scarica checkpoint SAM se non presente
if [ ! -f "ai_server/models/sam_vit_h_4b8939.pth" ]; then
    echo "Downloading SAM checkpoint..."
    mkdir -p ai_server/models
    wget -O ai_server/models/sam_vit_h_4b8939.pth \
        https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
fi

# Crea directory per dataset e modelli
echo "Creating directories..."
mkdir -p dataset/roofs/{raw_images,segmented,cropped,annotations}
mkdir -p models/roof_classifier

echo "✅ Setup completed!"
echo ""
echo "To start using the system:"
echo "1. Activate the virtual environment: source venv/bin/activate"
echo "2. Start the AI server: cd ai_server && python main.py"
echo "3. Open the frontend at http://localhost:5173/roof-classifier"
echo ""
echo "For a quick test with sample data:"
echo "python roof_dataset_pipeline.py --sample-locations"