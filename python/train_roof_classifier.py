#!/usr/bin/env python3
"""
Script di training per il classificatore di materiali per tetti.
Utilizza transfer learning con EfficientNet e augmentation avanzata.
"""

import os
import json
import numpy as np
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms, models
import torchvision.transforms.functional as TF
from PIL import Image
import random
from tqdm import tqdm
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns
import logging
import argparse
from typing import Dict, List, Tuple, Optional
import albumentations as A
from albumentations.pytorch import ToTensorV2
import wandb

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Classi di materiali per tetti
ROOF_MATERIALS = {
    0: 'terracotta_tiles',
    1: 'cement_tiles', 
    2: 'slate_tiles',
    3: 'metal_sheet',
    4: 'asphalt_shingles',
    5: 'flat_concrete',
    6: 'green_roof',
    7: 'solar_panels',
    8: 'mixed_materials',
    9: 'unknown'
}

class RoofMaterialDataset(Dataset):
    """Dataset per classificazione materiali tetti con augmentation avanzata."""
    
    def __init__(self, 
                 annotations_file: str,
                 images_dir: str,
                 transform=None,
                 augment: bool = False):
        """
        Args:
            annotations_file: Path al file JSON con annotazioni
            images_dir: Directory con immagini ritagliate
            transform: Trasformazioni da applicare
            augment: Se True, applica augmentation pesante
        """
        with open(annotations_file, 'r') as f:
            self.annotations = json.load(f)
        
        self.images_dir = Path(images_dir)
        self.transform = transform
        self.augment = augment
        
        # Filtra solo annotazioni con materiale assegnato
        self.annotations = [a for a in self.annotations 
                           if a.get('material', 'unknown') != 'unknown']
        
        # Crea mapping materiale -> indice
        self.material_to_idx = {v: k for k, v in ROOF_MATERIALS.items()}
        
        # Augmentation pipeline con Albumentations
        self.augmentation = A.Compose([
            # Trasformazioni geometriche
            A.RandomRotate90(p=0.5),
            A.Flip(p=0.5),
            A.Transpose(p=0.3),
            A.ShiftScaleRotate(
                shift_limit=0.1, 
                scale_limit=0.2, 
                rotate_limit=45, 
                p=0.5
            ),
            
            # Trasformazioni fotometriche
            A.OneOf([
                A.RandomBrightnessContrast(
                    brightness_limit=0.3,
                    contrast_limit=0.3,
                    p=1
                ),
                A.HueSaturationValue(
                    hue_shift_limit=20,
                    sat_shift_limit=30,
                    val_shift_limit=20,
                    p=1
                ),
            ], p=0.8),
            
            # Simulazione condizioni meteo
            A.OneOf([
                A.RandomRain(
                    slant_lower=-10,
                    slant_upper=10,
                    drop_length=20,
                    drop_width=1,
                    drop_color=(200, 200, 200),
                    p=1
                ),
                A.RandomFog(
                    fog_coef_lower=0.1,
                    fog_coef_upper=0.3,
                    p=1
                ),
                A.RandomSunFlare(
                    flare_roi=(0, 0, 1, 0.5),
                    angle_lower=0,
                    angle_upper=1,
                    p=1
                ),
            ], p=0.3),
            
            # Rumore e blur
            A.OneOf([
                A.GaussNoise(var_limit=(10.0, 50.0), p=1),
                A.ISONoise(p=1),
                A.MultiplicativeNoise(p=1),
            ], p=0.3),
            
            A.OneOf([
                A.MotionBlur(blur_limit=7, p=1),
                A.MedianBlur(blur_limit=5, p=1),
                A.GaussianBlur(blur_limit=5, p=1),
            ], p=0.3),
            
            # Distorsioni prospettiche (simula angoli di vista diversi)
            A.Perspective(scale=(0.05, 0.15), p=0.3),
            
            # Ritagli casuali per variabilità
            A.RandomCrop(height=224, width=224, p=0.3),
            
            # Normalizzazione finale
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])
        
        # Transform standard senza augmentation
        self.basic_transform = A.Compose([
            A.Resize(256, 256),
            A.CenterCrop(224, 224),
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])
    
    def __len__(self):
        return len(self.annotations)
    
    def __getitem__(self, idx):
        annotation = self.annotations[idx]
        
        # Carica immagine
        img_path = self.images_dir / annotation['filename']
        image = Image.open(img_path).convert('RGB')
        image = np.array(image)
        
        # Applica trasformazioni
        if self.augment and random.random() > 0.1:  # 90% augmentation
            augmented = self.augmentation(image=image)
            image = augmented['image']
        else:
            augmented = self.basic_transform(image=image)
            image = augmented['image']
        
        # Ottieni label
        material = annotation.get('material', 'unknown')
        label = self.material_to_idx.get(material, 9)  # 9 = unknown
        
        return image, label

class RoofMaterialClassifier(nn.Module):
    """Modello per classificazione materiali tetti usando EfficientNet."""
    
    def __init__(self, 
                 num_classes: int = 10,
                 model_name: str = 'efficientnet_b3',
                 pretrained: bool = True,
                 dropout: float = 0.3):
        super().__init__()
        
        # Carica modello pre-addestrato
        if model_name.startswith('efficientnet'):
            self.backbone = models.efficientnet_b3(pretrained=pretrained)
            num_features = self.backbone.classifier[1].in_features
            
            # Sostituisci classifier
            self.backbone.classifier = nn.Sequential(
                nn.Dropout(dropout),
                nn.Linear(num_features, 512),
                nn.ReLU(),
                nn.BatchNorm1d(512),
                nn.Dropout(dropout/2),
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.BatchNorm1d(256),
                nn.Dropout(dropout/2),
                nn.Linear(256, num_classes)
            )
        else:
            # ResNet come alternativa
            self.backbone = models.resnet50(pretrained=pretrained)
            num_features = self.backbone.fc.in_features
            self.backbone.fc = nn.Sequential(
                nn.Dropout(dropout),
                nn.Linear(num_features, 512),
                nn.ReLU(),
                nn.BatchNorm1d(512),
                nn.Dropout(dropout/2),
                nn.Linear(512, num_classes)
            )
    
    def forward(self, x):
        return self.backbone(x)
    
    def extract_features(self, x):
        """Estrae features per visualizzazione."""
        if hasattr(self.backbone, 'features'):
            features = self.backbone.features(x)
            features = self.backbone.avgpool(features)
            features = torch.flatten(features, 1)
            return features
        else:
            # Per ResNet
            x = self.backbone.conv1(x)
            x = self.backbone.bn1(x)
            x = self.backbone.relu(x)
            x = self.backbone.maxpool(x)
            x = self.backbone.layer1(x)
            x = self.backbone.layer2(x)
            x = self.backbone.layer3(x)
            x = self.backbone.layer4(x)
            x = self.backbone.avgpool(x)
            return torch.flatten(x, 1)

class Trainer:
    """Classe per gestire il training del modello."""
    
    def __init__(self,
                 model: nn.Module,
                 device: torch.device,
                 learning_rate: float = 1e-4,
                 weight_decay: float = 1e-4):
        self.model = model.to(device)
        self.device = device
        
        # Optimizer e scheduler
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=10,
            T_mult=2
        )
        
        # Loss con pesi per classi sbilanciate
        self.criterion = nn.CrossEntropyLoss()
        
        # Tracking
        self.train_losses = []
        self.val_losses = []
        self.val_accuracies = []
        self.best_val_acc = 0
    
    def train_epoch(self, dataloader: DataLoader) -> float:
        """Esegue un'epoca di training."""
        self.model.train()
        total_loss = 0
        correct = 0
        total = 0
        
        pbar = tqdm(dataloader, desc="Training")
        for images, labels in pbar:
            images = images.to(self.device)
            labels = labels.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(images)
            loss = self.criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()
            
            # Update progress bar
            pbar.set_postfix({
                'loss': loss.item(),
                'acc': 100. * correct / total
            })
        
        return total_loss / len(dataloader)
    
    def validate(self, dataloader: DataLoader) -> Tuple[float, float]:
        """Valida il modello."""
        self.model.eval()
        total_loss = 0
        correct = 0
        total = 0
        
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for images, labels in tqdm(dataloader, desc="Validation"):
                images = images.to(self.device)
                labels = labels.to(self.device)
                
                outputs = self.model(images)
                loss = self.criterion(outputs, labels)
                
                total_loss += loss.item()
                _, predicted = outputs.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()
                
                all_preds.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        accuracy = 100. * correct / total
        avg_loss = total_loss / len(dataloader)
        
        return avg_loss, accuracy, all_preds, all_labels
    
    def train(self,
              train_loader: DataLoader,
              val_loader: DataLoader,
              epochs: int = 50,
              save_dir: str = "models/roof_classifier"):
        """Training loop completo."""
        
        Path(save_dir).mkdir(parents=True, exist_ok=True)
        
        for epoch in range(epochs):
            logger.info(f"\nEpoch {epoch+1}/{epochs}")
            
            # Training
            train_loss = self.train_epoch(train_loader)
            self.train_losses.append(train_loss)
            
            # Validation
            val_loss, val_acc, preds, labels = self.validate(val_loader)
            self.val_losses.append(val_loss)
            self.val_accuracies.append(val_acc)
            
            # Learning rate scheduling
            self.scheduler.step()
            
            logger.info(f"Train Loss: {train_loss:.4f}")
            logger.info(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")
            
            # Save best model
            if val_acc > self.best_val_acc:
                self.best_val_acc = val_acc
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_acc': val_acc,
                    'val_loss': val_loss,
                }, f"{save_dir}/best_model.pth")
                logger.info(f"Saved best model with accuracy: {val_acc:.2f}%")
            
            # Save checkpoint
            if (epoch + 1) % 10 == 0:
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                }, f"{save_dir}/checkpoint_epoch_{epoch+1}.pth")
        
        # Plot training curves
        self.plot_training_curves(save_dir)
        
        # Generate classification report
        self.generate_classification_report(val_loader, save_dir)
    
    def plot_training_curves(self, save_dir: str):
        """Visualizza curve di training."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Loss curves
        ax1.plot(self.train_losses, label='Train Loss')
        ax1.plot(self.val_losses, label='Val Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.legend()
        ax1.grid(True)
        
        # Accuracy curve
        ax2.plot(self.val_accuracies, label='Val Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy (%)')
        ax2.set_title('Validation Accuracy')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(f"{save_dir}/training_curves.png")
        plt.close()
    
    def generate_classification_report(self, dataloader: DataLoader, save_dir: str):
        """Genera report dettagliato di classificazione."""
        self.model.eval()
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for images, labels in tqdm(dataloader, desc="Generating report"):
                images = images.to(self.device)
                outputs = self.model(images)
                _, predicted = outputs.max(1)
                
                all_preds.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        # Classification report
        target_names = [ROOF_MATERIALS[i] for i in range(len(ROOF_MATERIALS))]
        report = classification_report(all_labels, all_preds, 
                                     target_names=target_names)
        
        with open(f"{save_dir}/classification_report.txt", 'w') as f:
            f.write(report)
        
        # Confusion matrix
        cm = confusion_matrix(all_labels, all_preds)
        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=target_names,
                   yticklabels=target_names)
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')
        plt.tight_layout()
        plt.savefig(f"{save_dir}/confusion_matrix.png")
        plt.close()
        
        logger.info(f"\nClassification Report:\n{report}")


def main():
    parser = argparse.ArgumentParser(description="Train roof material classifier")
    parser.add_argument("--dataset-dir", type=str, default="dataset/roofs",
                       help="Dataset directory")
    parser.add_argument("--model-name", type=str, default="efficientnet_b3",
                       help="Model architecture")
    parser.add_argument("--epochs", type=int, default=50,
                       help="Number of training epochs")
    parser.add_argument("--batch-size", type=int, default=32,
                       help="Batch size")
    parser.add_argument("--learning-rate", type=float, default=1e-4,
                       help="Learning rate")
    parser.add_argument("--num-workers", type=int, default=4,
                       help="Number of data loader workers")
    parser.add_argument("--use-wandb", action="store_true",
                       help="Use Weights & Biases for tracking")
    
    args = parser.parse_args()
    
    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    # Initialize wandb if requested
    if args.use_wandb:
        wandb.init(
            project="roof-material-classification",
            config=vars(args)
        )
    
    # Create datasets
    dataset_dir = Path(args.dataset_dir)
    
    train_dataset = RoofMaterialDataset(
        annotations_file=dataset_dir / "annotations" / "train.json",
        images_dir=dataset_dir / "cropped",
        augment=True
    )
    
    val_dataset = RoofMaterialDataset(
        annotations_file=dataset_dir / "annotations" / "val.json",
        images_dir=dataset_dir / "cropped",
        augment=False
    )
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=args.num_workers,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers,
        pin_memory=True
    )
    
    logger.info(f"Train samples: {len(train_dataset)}")
    logger.info(f"Val samples: {len(val_dataset)}")
    
    # Create model
    model = RoofMaterialClassifier(
        num_classes=len(ROOF_MATERIALS),
        model_name=args.model_name,
        pretrained=True
    )
    
    # Create trainer and train
    trainer = Trainer(
        model=model,
        device=device,
        learning_rate=args.learning_rate
    )
    
    trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=args.epochs,
        save_dir="models/roof_classifier"
    )
    
    logger.info("Training completed!")


if __name__ == "__main__":
    main()