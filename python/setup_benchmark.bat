@echo off
echo ===========================================
echo Setup for ML Benchmark on Ryzen Z1 Extreme
echo ===========================================

REM Check Python version
python --version

REM Create virtual environment
echo Creating virtual environment...
python -m venv benchmark_env

REM Activate environment
call benchmark_env\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install CPU-optimized packages
echo Installing CPU-optimized packages...
pip install numpy scipy scikit-learn opencv-python-headless psutil

REM Install PyTorch for AMD CPU
echo Installing PyTorch (CPU optimized)...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

REM Install additional ML libraries
echo Installing additional ML libraries...
pip install pillow matplotlib tqdm pandas

echo.
echo ===========================================
echo Setup complete!
echo ===========================================
echo.
echo To run the benchmark:
echo   1. Activate the environment: benchmark_env\Scripts\activate.bat
echo   2. Run the benchmark: python benchmark_ml.py
echo.
echo Note for Ryzen Z1 Extreme users:
echo   - This CPU has 8 cores/16 threads - great for parallel processing
echo   - The integrated RDNA3 GPU (12 CUs) isn't supported by ROCm yet
echo   - For best performance, close other applications during benchmark
echo   - Consider using DirectML for GPU acceleration on Windows
echo.
pause