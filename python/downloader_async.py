#!/usr/bin/env python3
"""
Async Map Tile Downloader with Progress Reporting

Enhanced version using asyncio for better concurrent downloading and real-time progress reporting.
"""

import os
import sys
import json
import time
import math
import logging
import argparse
import io
import asyncio
import aiohttp
import certifi
import ssl
from pathlib import Path
from PIL import Image
import numpy as np
import cv2
from osgeo import gdal, osr

# Configure GDAL to use exceptions
gdal.UseExceptions()
from datetime import datetime

# Configure logging with structured output for progress parsing
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',  # Simple format for easy parsing
)
logger = logging.getLogger('async_downloader')

# Constants
TILE_SIZE = 256  # Google Maps tile size in pixels
MAX_CONCURRENT_DOWNLOADS = 20  # Maximum concurrent downloads
PROGRESS_UPDATE_INTERVAL = 0.5  # Progress update interval in seconds

# Progress tracking
class ProgressTracker:
    def __init__(self, total_tiles):
        self.total_tiles = total_tiles
        self.downloaded = 0
        self.failed = 0
        self.start_time = time.time()
        self.last_update = 0
        
    def increment(self, success=True):
        if success:
            self.downloaded += 1
        else:
            self.failed += 1
        
        # Send progress update if enough time has passed
        current_time = time.time()
        if current_time - self.last_update > PROGRESS_UPDATE_INTERVAL:
            self.send_progress()
            self.last_update = current_time
    
    def send_progress(self):
        elapsed = time.time() - self.start_time
        completed = self.downloaded + self.failed
        
        if completed > 0:
            rate = completed / elapsed
            eta = (self.total_tiles - completed) / rate if rate > 0 else 0
        else:
            rate = 0
            eta = 0
        
        progress = {
            "type": "progress",
            "total": self.total_tiles,
            "downloaded": self.downloaded,
            "failed": self.failed,
            "completed": completed,
            "percentage": (completed / self.total_tiles * 100) if self.total_tiles > 0 else 0,
            "rate": rate,
            "eta": eta,
            "elapsed": elapsed
        }
        
        # Output as JSON for easy parsing by the backend
        print(json.dumps(progress), flush=True)
    
    def final_report(self):
        elapsed = time.time() - self.start_time
        progress = {
            "type": "complete",
            "total": self.total_tiles,
            "downloaded": self.downloaded,
            "failed": self.failed,
            "elapsed": elapsed,
            "success": self.failed == 0
        }
        print(json.dumps(progress), flush=True)

# Reuse coordinate conversion functions from original
def wgs_to_mercator(x, y):
    y = 85.0511287798 if y > 85.0511287798 else y
    y = -85.0511287798 if y < -85.0511287798 else y
    x2 = x * 20037508.34 / 180
    y2 = math.log(math.tan((90 + y) * math.pi / 360)) / (math.pi / 180)
    y2 = y2 * 20037508.34 / 180
    return x2, y2

def mercator_to_wgs(x, y):
    x2 = x / 20037508.34 * 180
    y2 = y / 20037508.34 * 180
    y2 = 180 / math.pi * (2 * math.atan(math.exp(y2 * math.pi / 180)) - math.pi / 2)
    return x2, y2

def lat_lon_to_tile(lat, lon, zoom):
    """Convert latitude, longitude to tile coordinates at given zoom level."""
    lat = min(max(lat, -85.0511), 85.0511)
    n = 2.0 ** zoom
    x_tile = math.floor((lon + 180) / 360 * n)
    y_tile = math.floor((1 - math.log(math.tan(lat * math.pi / 180) + 1 / math.cos(lat * math.pi / 180)) / math.pi) / 2 * n)
    return x_tile, y_tile

def get_tile_bounds(bbox, zoom):
    """Calculate tile coordinates needed to cover the bounding box."""
    min_lon, min_lat, max_lon, max_lat = bbox
    min_x, max_y = lat_lon_to_tile(min_lat, min_lon, zoom)
    max_x, min_y = lat_lon_to_tile(max_lat, max_lon, zoom)
    return min_x, min_y, max_x + 1, max_y + 1

def pixls_to_mercator(zb):
    """Get the web Mercator projection coordinates."""
    inx, iny = zb["LT"]
    inx2, iny2 = zb["RB"]
    length = 20037508.3427892
    sum = 2 ** zb["z"]
    LTx = inx / sum * length * 2 - length
    LTy = -(iny / sum * length * 2) + length
    RBx = (inx2 + 1) / sum * length * 2 - length
    RBy = -((iny2 + 1) / sum * length * 2) + length
    res = {'LT': (LTx, LTy), 'RB': (RBx, RBy),
           'LB': (LTx, RBy), 'RT': (RBx, LTy)}
    return res

def getExtent(x1, y1, x2, y2, z, source="Google"):
    """Get the geographic extent of the area covered by tiles."""
    if x1 == x2:
        x2 += 0.000001
    if y1 == y2:
        y2 += 0.000001
        
    pos1x, pos1y = lat_lon_to_tile(y1, x1, z)
    pos2x, pos2y = lat_lon_to_tile(y2, x2, z)
    
    if pos1x == pos2x:
        pos2x += 1
    if pos1y == pos2y:
        pos2y += 1
        
    Xframe = pixls_to_mercator(
        {"LT": (pos1x, pos1y), "RT": (pos2x, pos1y), "LB": (pos1x, pos2y), "RB": (pos2x, pos2y), "z": z})
    for i in ["LT", "LB", "RT", "RB"]:
        Xframe[i] = mercator_to_wgs(*Xframe[i])
    return Xframe

def get_url(source, x, y, z, style):
    """Get the URL for a specific map tile."""
    api_key = os.environ.get('GOOGLE_MAPS_API_KEY', 'AIzaSyBxpMSp5E3JtWbe7TsPDm7mHR6u2O_gXYY')
    
    if source == 'Google':
        subdomain = f"mt{x % 4}"
        url = f"https://{subdomain}.google.com/vt/lyrs={style}&x={x}&y={y}&z={z}&key={api_key}"
    else:
        raise Exception(f"Unknown Map Source: {source}")
    return url

async def download_tile(session, url, tile_info, progress_tracker, semaphore):
    """Download a single tile with retry logic."""
    async with semaphore:  # Limit concurrent downloads
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'image/webp,*/*',
            'Accept-Language': 'en-US,en;q=0.5',
            'Referer': 'https://www.google.com/maps/',
        }
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    # Exponential backoff with jitter
                    await asyncio.sleep(2 ** attempt + 0.1 * attempt)
                
                async with session.get(url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        data = await response.read()
                        progress_tracker.increment(success=True)
                        return tile_info, data
                    else:
                        logger.warning(f"HTTP {response.status} for tile {tile_info}")
                        
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"Failed to download tile {tile_info} after {max_retries} attempts: {e}")
                    progress_tracker.increment(success=False)
                    # Return placeholder tile
                    img = Image.new('RGBA', (256, 256), (255, 255, 255, 128))
                    img_byte_arr = io.BytesIO()
                    img.save(img_byte_arr, format='PNG')
                    return tile_info, img_byte_arr.getvalue()

async def download_all_tiles(tile_list, progress_tracker, max_concurrent=MAX_CONCURRENT_DOWNLOADS):
    """Download all tiles concurrently."""
    # Create SSL context with certifi
    ssl_context = ssl.create_default_context(cafile=certifi.where())
    
    # Create semaphore for limiting concurrent connections
    semaphore = asyncio.Semaphore(max_concurrent)
    
    # Create connector with connection limit
    connector = aiohttp.TCPConnector(
        limit=max_concurrent,
        ssl=ssl_context
    )
    
    async with aiohttp.ClientSession(connector=connector) as session:
        tasks = []
        for tile_info in tile_list:
            url = tile_info['url']
            task = download_tile(session, url, tile_info, progress_tracker, semaphore)
            tasks.append(task)
        
        # Download all tiles concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        tile_data = {}
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Task exception: {result}")
            else:
                tile_info, data = result
                tile_data[tile_info['index']] = data
        
        return tile_data

def merge_tiles(tile_data, lenx, leny):
    """Merge tile data into a single image."""
    outpic = Image.new('RGBA', (lenx * 256, leny * 256), (0, 0, 0, 0))
    
    for i in range(lenx * leny):
        if i in tile_data and tile_data[i] is not None:
            try:
                picio = io.BytesIO(tile_data[i])
                small_pic = Image.open(picio)
                y, x = i // lenx, i % lenx
                outpic.paste(small_pic, (x * 256, y * 256))
            except Exception as e:
                logger.error(f"Error merging tile {i}: {e}")
    
    return outpic

def saveTiff(r, g, b, gt, filePath):
    """Save RGB arrays as a GeoTIFF."""
    driver = gdal.GetDriverByName('GTiff')
    dset_output = driver.Create(filePath, r.shape[1], r.shape[0], 3, gdal.GDT_Byte)
    dset_output.SetGeoTransform(gt)
    try:
        proj = osr.SpatialReference()
        proj.ImportFromEPSG(4326)
        dset_output.SetSpatialRef(proj)
    except Exception as e:
        logger.error(f"Error setting coordinate system: {e}")
    dset_output.GetRasterBand(1).WriteArray(r)
    dset_output.GetRasterBand(2).WriteArray(g)
    dset_output.GetRasterBand(3).WriteArray(b)
    dset_output.FlushCache()
    dset_output = None

def create_world_file(image_path, bbox, zoom, tile_bounds):
    """Create a world file for georeferencing."""
    min_lon, min_lat, max_lon, max_lat = bbox
    min_x, min_y, max_x, max_y = tile_bounds
    
    width_px = max(1, (max_x - min_x) * TILE_SIZE)
    height_px = max(1, (max_y - min_y) * TILE_SIZE)
    
    lon_diff = max(0.000001, max_lon - min_lon)
    lat_diff = max(0.000001, max_lat - min_lat)
    
    lon_per_pixel = lon_diff / width_px
    lat_per_pixel = lat_diff / height_px
    
    world_file_path = image_path.replace('.jpg', '.jgw').replace('.jpeg', '.jgw').replace('.tif', '.tfw')
    
    with open(world_file_path, 'w') as f:
        f.write(f"{lon_per_pixel}\n")
        f.write("0.0\n")
        f.write("0.0\n")
        f.write(f"{-lat_per_pixel}\n")
        f.write(f"{min_lon}\n")
        f.write(f"{max_lat}\n")
    
    metadata_path = image_path + '.meta'
    with open(metadata_path, 'w') as f:
        metadata = {
            'bbox': bbox,
            'zoom': zoom,
            'created': time.strftime('%Y-%m-%d %H:%M:%S'),
            'projection': 'EPSG:4326',
            'tile_bounds': {
                'min_x': min_x,
                'min_y': min_y,
                'max_x': max_x,
                'max_y': max_y
            }
        }
        json.dump(metadata, f, indent=2)

async def create_composite_image_async(bbox, zoom, style, output_path, server="Google"):
    """Create a composite image from tiles downloaded concurrently."""
    min_lon, min_lat, max_lon, max_lat = bbox
    
    # Ensure minimum bounding box size
    min_area_degrees = 0.001
    if abs(max_lon - min_lon) < min_area_degrees:
        center_x = (min_lon + max_lon) / 2
        min_lon = center_x - min_area_degrees
        max_lon = center_x + min_area_degrees
    
    if abs(max_lat - min_lat) < min_area_degrees:
        center_y = (min_lat + max_lat) / 2
        min_lat = center_y - min_area_degrees
        max_lat = center_y + min_area_degrees
    
    # Get tile coordinates
    min_tile_x, max_tile_y = lat_lon_to_tile(min_lat, min_lon, zoom)
    max_tile_x, min_tile_y = lat_lon_to_tile(max_lat, max_lon, zoom)
    
    # Ensure minimum tile grid
    min_tiles = 5
    current_width = max_tile_x - min_tile_x + 1
    current_height = max_tile_y - min_tile_y + 1
    
    if current_width < min_tiles:
        tiles_to_add = min_tiles - current_width
        tiles_left = tiles_to_add // 2
        tiles_right = tiles_to_add - tiles_left
        min_tile_x = max(0, min_tile_x - tiles_left)
        max_tile_x = max_tile_x + tiles_right
    
    if current_height < min_tiles:
        tiles_to_add = min_tiles - current_height
        tiles_top = tiles_to_add // 2
        tiles_bottom = tiles_to_add - tiles_top
        min_tile_y = max(0, min_tile_y - tiles_top)
        max_tile_y = max_tile_y + tiles_bottom
    
    lenx = max_tile_x - min_tile_x + 1
    leny = max_tile_y - min_tile_y + 1
    total_tiles = lenx * leny
    
    # Send initial status
    print(json.dumps({
        "type": "start",
        "total_tiles": total_tiles,
        "grid_size": {"width": lenx, "height": leny},
        "zoom": zoom,
        "bbox": bbox
    }), flush=True)
    
    # Create tile list with metadata
    tile_list = []
    index = 0
    for j in range(min_tile_y, max_tile_y + 1):
        for i in range(min_tile_x, max_tile_x + 1):
            tile_list.append({
                'x': i,
                'y': j,
                'index': index,
                'url': get_url(server, i, j, zoom, style)
            })
            index += 1
    
    # Create progress tracker
    progress_tracker = ProgressTracker(total_tiles)
    
    # Download all tiles
    tile_data = await download_all_tiles(tile_list, progress_tracker)
    
    # Send final progress
    progress_tracker.final_report()
    
    # Merge tiles
    print(json.dumps({"type": "status", "message": "Merging tiles into composite image"}), flush=True)
    outpic = merge_tiles(tile_data, lenx, leny)
    
    # Convert to RGB for GeoTIFF
    outpic = outpic.convert('RGB')
    r, g, b = cv2.split(np.array(outpic))
    
    # Get extent information
    extent = getExtent(min_lon, min_lat, max_lon, max_lat, zoom)
    
    # Calculate geotransform
    top_left_x_wgs = extent['LT'][0]
    top_left_y_wgs = extent['LT'][1]
    
    width_px = max(1, lenx * 256)
    height_px = max(1, leny * 256)
    
    pixel_width_deg = (extent['RB'][0] - extent['LT'][0]) / width_px
    pixel_height_deg = (extent['RB'][1] - extent['LT'][1]) / height_px
    
    gt = (top_left_x_wgs, pixel_width_deg, 0, top_left_y_wgs, 0, pixel_height_deg)
    
    # Save as GeoTIFF
    print(json.dumps({"type": "status", "message": "Saving GeoTIFF"}), flush=True)
    saveTiff(r, g, b, gt, output_path)
    
    # Create world file
    tile_bounds = (min_tile_x, min_tile_y, max_tile_x, max_tile_y)
    create_world_file(output_path, bbox, zoom, tile_bounds)
    
    return output_path

def main():
    """Main entry point for the async downloader."""
    parser = argparse.ArgumentParser(description='Download and compose map tiles with async support.')
    parser.add_argument('--left', type=float, required=True, help='Left longitude')
    parser.add_argument('--bottom', type=float, required=True, help='Bottom latitude')
    parser.add_argument('--right', type=float, required=True, help='Right longitude')
    parser.add_argument('--top', type=float, required=True, help='Top latitude')
    parser.add_argument('--zoom', type=int, required=True, help='Zoom level (0-21)')
    parser.add_argument('--output', type=str, required=True, help='Output file path')
    parser.add_argument('--style', type=str, default='s', 
                        choices=['m', 's', 'y', 't', 'p', 'h'], 
                        help='Map style')
    
    args = parser.parse_args()
    
    # Validate zoom level
    if args.zoom < 0 or args.zoom > 21:
        print(json.dumps({"type": "error", "message": "Zoom level must be between 0 and 21"}), flush=True)
        sys.exit(1)
        
    # Ensure output directory exists
    output_dir = os.path.dirname(args.output)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # Ensure the output file has the correct extension
    if not args.output.lower().endswith('.tif'):
        args.output += '.tif'
    
    # Set up bounding box
    bbox = [args.left, args.bottom, args.right, args.top]
    
    try:
        # Run async download
        asyncio.run(create_composite_image_async(
            bbox, args.zoom, args.style, args.output, "Google"
        ))
        
        print(json.dumps({
            "type": "success",
            "output_file": args.output
        }), flush=True)
        
    except Exception as e:
        print(json.dumps({
            "type": "error",
            "message": str(e)
        }), flush=True)
        sys.exit(1)

if __name__ == "__main__":
    main()