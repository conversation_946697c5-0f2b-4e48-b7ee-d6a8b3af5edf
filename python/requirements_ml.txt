# Requirements per il sistema di classificazione materiali tetti

# Deep Learning
torch>=2.0.0
torchvision>=0.15.0
albumentations>=1.3.0

# Computer Vision
opencv-python>=4.8.0
segment-anything>=1.0
Pillow>=10.0.0

# Scientific Computing
numpy>=1.24.0
scipy>=1.10.0
scikit-learn>=1.3.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0

# Progress & Logging
tqdm>=4.65.0
wandb>=0.15.0

# Existing dependencies (già presenti nel progetto)
# fastapi
# uvicorn
# aiohttp
# asyncio
# psycopg2-binary
# geopandas