# Sistema di Classificazione Materiali Tetti

Sistema completo per rilevare tetti da immagini satellitari e classificarne i materiali usando deep learning.

## Componenti Principali

### 1. Pipeline Raccolta Dataset (`roof_dataset_pipeline.py`)
- **Scarica immagini satellitari** da Google Maps
- **Rileva automaticamente i tetti** usando SAM (Segment Anything Model)
- **Estrae e salva ritagli** dei singoli tetti
- **Genera annotazioni** in formato JSON

### 2. Script Training (`train_roof_classifier.py`)
- **Transfer learning** con EfficientNet-B3
- **Data augmentation avanzata** (meteo, prospettiva, rumore)
- **10 classi di materiali** supportate
- **Metriche dettagliate** e confusion matrix

## Setup Rapido

### 1. Installa dipendenze
```bash
pip install -r requirements_ml.txt
```

### 2. Crea dataset di esempio
```bash
# Usa location di esempio italiane
python roof_dataset_pipeline.py --sample-locations --zoom 19

# O fornisci tue coordinate
python roof_dataset_pipeline.py --locations-file coords.json --zoom 19
```

### 3. Annota i materiali
Modifica `dataset/roofs/annotations/annotations.json` aggiungendo il campo `material` per ogni tetto:
```json
{
  "filename": "sat_45.440800_12.315500_roof_001.jpg",
  "material": "terracotta_tiles",  // <-- Aggiungi questo
  ...
}
```

### 4. Addestra il modello
```bash
python train_roof_classifier.py --dataset-dir dataset/roofs --epochs 50
```

## Materiali Supportati

1. **terracotta_tiles** - Tegole in terracotta (tipiche italiane)
2. **cement_tiles** - Tegole in cemento
3. **slate_tiles** - Ardesia
4. **metal_sheet** - Lamiera metallica
5. **asphalt_shingles** - Tegole bituminose
6. **flat_concrete** - Cemento piano
7. **green_roof** - Tetto verde/giardino
8. **solar_panels** - Pannelli solari
9. **mixed_materials** - Materiali misti
10. **unknown** - Non identificato

## Workflow Completo

```mermaid
graph LR
    A[Google Maps] --> B[Download Tiles]
    B --> C[SAM Segmentation]
    C --> D[Estrai Tetti]
    D --> E[Annotazione Manuale]
    E --> F[Training Model]
    F --> G[Deploy API]
```

## File Generati

```
dataset/roofs/
├── raw_images/          # Immagini satellitari originali
├── segmented/           # Maschere di segmentazione
├── cropped/            # Ritagli dei singoli tetti
├── annotations/        # Annotazioni JSON
│   ├── annotations.json
│   ├── train.json
│   ├── val.json
│   └── test.json
└── metadata.json       # Statistiche dataset

models/roof_classifier/
├── best_model.pth      # Miglior modello
├── training_curves.png # Grafici training
├── confusion_matrix.png # Matrice confusione
└── classification_report.txt
```

## API Integration

Il modello addestrato si integra automaticamente con il server FastAPI esistente:

```python
# Endpoint già configurato
POST /api/classification/roof-material
{
  "image": "base64_encoded_image"
}

# Response
{
  "material": "terracotta_tiles",
  "confidence": 0.95,
  "all_predictions": {...}
}
```

## Performance Attese

- **Accuracy**: 85-95% su classi principali
- **Speed**: <500ms per immagine
- **Min dataset**: 100 immagini per classe
- **Optimal dataset**: 500+ immagini per classe

## Miglioramenti Futuri

- [ ] Active learning per annotazione semi-automatica
- [ ] Ensemble di modelli per maggiore accuratezza
- [ ] Fine-tuning specifico per regioni geografiche
- [ ] Export ONNX per deployment edge
- [ ] Integration con CVAT per annotazione web-based