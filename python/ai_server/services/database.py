import os
import psycopg2
from psycopg2.extras import RealDictCursor
import logging
from typing import List, Dict, Any, Optional
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class Database:
    """Database connection manager"""
    
    def __init__(self):
        self.connection_params = {
            'host': os.getenv('DB_HOST', '**************'),
            'port': os.getenv('DB_PORT', '5432'),
            'database': os.getenv('DB_NAME', 'astraai'),
            'user': os.getenv('DB_USER', 'alins'),
            'password': os.getenv('DB_PASSWORD', 'astra1245\!')
        }
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    def get_connection(self):
        """Get database connection"""
        return psycopg2.connect(**self.connection_params)
    
    async def execute(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute query and return results"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self._execute_sync,
            query,
            params
        )
    
    def _execute_sync(self, query: str, params: tuple = None) -> List[Dict]:
        """Synchronous query execution"""
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute(query, params)
                    if cursor.description:
                        return cursor.fetchall()
                    conn.commit()
                    return []
        except Exception as e:
            logger.error(f"Database error: {e}")
            raise
    
    async def insert_annotation(self, annotation_data: Dict) -> str:
        """Insert new annotation"""
        query = """
            INSERT INTO roof_annotations (
                tile_id, roof_id, geometry, confidence, 
                material, roof_type, has_solar_panels,
                annotator_type, status
            ) VALUES (
                %s, %s, ST_GeomFromGeoJSON(%s), %s,
                %s, %s, %s, %s, %s
            ) RETURNING id
        """
        
        result = await self.execute(query, (
            annotation_data['tile_id'],
            annotation_data['roof_id'],
            annotation_data['geometry'],
            annotation_data['confidence'],
            annotation_data.get('material'),
            annotation_data.get('roof_type'),
            annotation_data.get('has_solar_panels', False),
            annotation_data.get('annotator_type', 'ai'),
            annotation_data.get('status', 'pending')
        ))
        
        return result[0]['id'] if result else None
    
    async def get_annotations_for_tile(self, tile_id: str) -> List[Dict]:
        """Get all annotations for a tile"""
        query = """
            SELECT 
                id, roof_id, ST_AsGeoJSON(geometry) as geometry,
                confidence, material, roof_type, has_solar_panels,
                annotator_type, status, created_at
            FROM roof_annotations
            WHERE tile_id = %s
            ORDER BY created_at DESC
        """
        
        return await self.execute(query, (tile_id,))
    
    async def cleanup(self):
        """Cleanup resources"""
        self.executor.shutdown(wait=True)

# Global database instance
db = Database()
EOF < /dev/null