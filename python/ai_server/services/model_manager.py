import torch
TORCH_AVAILABLE = True
import logging
from typing import Dict, Any, Optional
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Import SAM
from segment_anything import sam_model_registry, SamPredictor
from segment_anything.automatic_mask_generator import SamAutomaticMaskGenerator

try:
    from transformers import AutoModelForImageClassification, AutoImageProcessor
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    AutoModelForImageClassification = None
    AutoImageProcessor = None
import numpy as np

logger = logging.getLogger(__name__)

class ModelManager:
    """Manages AI models for inference"""
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.models = {}
        self.processors = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        logger.info(f"Model manager initialized. Using device: {self.device}")
        
    async def load_sam2(self):
        """Load SAM model"""
        try:
            # Use the downloaded checkpoint
            checkpoint_path = Path("models/sam_vit_h_4b8939.pth")
            
            if not checkpoint_path.exists():
                logger.error(f"SAM checkpoint not found at {checkpoint_path}")
                raise FileNotFoundError(f"SAM checkpoint not found at {checkpoint_path}")
            
            logger.info(f"Loading SAM model from {checkpoint_path}")
            
            # Load model
            model = sam_model_registry["vit_h"](checkpoint=str(checkpoint_path))
            model.to(self.device)
            model.eval()
            
            # Create predictor
            self.models['sam2_predictor'] = SamPredictor(model)
            
            # Create automatic mask generator
            self.models['sam2_generator'] = SamAutomaticMaskGenerator(
                model=model,
                points_per_side=32,
                pred_iou_thresh=0.88,
                stability_score_thresh=0.95,
                crop_n_layers=1,
                crop_n_points_downscale_factor=2,
                min_mask_region_area=100,
            )
            
            # Store the model reference
            self.models['sam2'] = model
            
            logger.info("SAM model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading SAM: {e}")
            raise
    
    async def load_roof_classifier(self):
        """Load roof classification model"""
        try:
            # Use a pre-trained model that we'll fine-tune
            model_name = "microsoft/swin-tiny-patch4-window7-224"
            
            logger.info(f"Loading roof classifier: {model_name}")
            
            self.processors['roof_classifier'] = AutoImageProcessor.from_pretrained(model_name)
            model = AutoModelForImageClassification.from_pretrained(
                model_name,
                num_labels=12,  # 6 materials + 6 roof types
                ignore_mismatched_sizes=True
            )
            model.to(self.device)
            model.eval()
            
            self.models['roof_classifier'] = model
            logger.info("Roof classifier loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading roof classifier: {e}")
            raise
    
    def get_loaded_models(self) -> list:
        """Get list of loaded models"""
        return list(self.models.keys())
    
    def get_model_status(self) -> Dict[str, str]:
        """Get status of all models"""
        status = {}
        for name, model in self.models.items():
            if hasattr(model, '__class__'):
                status[name] = "loaded" if model is not None else "not_loaded"
            else:
                status[name] = "error"
        return status
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get detailed model information"""
        info = {}
        for name, model in self.models.items():
            model_info = {
                "loaded": model is not None,
                "type": type(model).__name__,
                "device": str(self.device)
            }
            
            if hasattr(model, 'num_parameters'):
                model_info["parameters"] = sum(p.numel() for p in model.parameters())
            
            info[name] = model_info
        
        return info
    
    @property
    def sam_model(self):
        """Get SAM model reference"""
        return self.models.get('sam2')
    
    @property
    def classifier(self):
        """Get classifier model reference"""
        return self.models.get('roof_classifier')
    
    def predict_sam(self, image: np.ndarray, 
                   point_coords: Optional[np.ndarray] = None,
                   point_labels: Optional[np.ndarray] = None, 
                   boxes: Optional[np.ndarray] = None,
                   mask_input: Optional[np.ndarray] = None,
                   multimask_output: bool = True):
        """Run SAM prediction with various prompts"""
        if 'sam2_predictor' not in self.models:
            raise RuntimeError("SAM model not loaded")
            
        predictor = self.models['sam2_predictor']
        
        # Set image
        predictor.set_image(image)
        
        # Run prediction
        masks, scores, logits = predictor.predict(
            point_coords=point_coords,
            point_labels=point_labels,
            box=boxes[0] if boxes is not None and len(boxes) > 0 else None,
            mask_input=mask_input,
            multimask_output=multimask_output,
        )
        
        return masks, scores, logits
    
    def generate_masks(self, image: np.ndarray,
                      points_per_side: int = 32,
                      pred_iou_thresh: float = 0.88,
                      stability_score_thresh: float = 0.95,
                      min_mask_region_area: int = 100):
        """Generate masks automatically"""
        if 'sam2' not in self.models:
            raise RuntimeError("SAM model not loaded")
            
        # Create a new generator with custom params
        generator = SamAutomaticMaskGenerator(
            model=self.models['sam2'],
            points_per_side=points_per_side,
            pred_iou_thresh=pred_iou_thresh,
            stability_score_thresh=stability_score_thresh,
            crop_n_layers=1,
            crop_n_points_downscale_factor=2,
            min_mask_region_area=min_mask_region_area,
        )
        
        # Generate masks
        masks = generator.generate(image)
        return masks
    
    async def segment_roofs(self, image: np.ndarray, prompts: Optional[Dict] = None):
        """Segment roofs in image using SAM"""
        if 'sam2_predictor' not in self.models:
            raise RuntimeError("SAM model not loaded")
            
        predictor = self.models['sam2_predictor']
        
        # Set image
        predictor.set_image(image)
        
        if prompts:
            # Use point prompts
            masks, scores, logits = predictor.predict(
                point_coords=prompts.get('points'),
                point_labels=prompts.get('labels'),
                multimask_output=True,
            )
            # Return masks with metadata
            results = []
            for i, (mask, score) in enumerate(zip(masks, scores)):
                results.append({
                    'segmentation': mask,
                    'predicted_iou': float(score),
                    'area': int(np.sum(mask)),
                    'stability_score': float(score)  # Use IoU as stability proxy
                })
            return results
        else:
            # Use automatic mask generation
            generator = self.models.get('sam2_generator')
            if generator:
                masks = generator.generate(image)
                return masks
            else:
                raise RuntimeError("Automatic mask generator not available")
    
    def classify_roof(self, image: np.ndarray, mask: Optional[np.ndarray] = None):
        """Classify roof attributes with multi-task model"""
        if 'roof_classifier' not in self.models:
            raise RuntimeError("Roof classifier not loaded")
            
        # Apply mask if provided
        if mask is not None:
            image = image * mask[:, :, np.newaxis]
        
        # Simulated classification (replace with real model inference)
        # In production, this would use the trained multi-task model
        import random
        
        material_class = random.randint(0, 9)
        building_class = random.randint(0, 5)
        has_solar = random.random() > 0.8
        
        return {
            'material_class': material_class,
            'material_confidence': random.uniform(0.7, 0.95),
            'building_class': building_class,
            'building_confidence': random.uniform(0.7, 0.95),
            'has_solar_panels': has_solar,
            'solar_confidence': random.uniform(0.8, 0.99),
            'condition': random.choice(['excellent', 'good', 'fair', 'poor']),
            'age_estimate': f"{random.randint(5, 30)} years"
        }
    
    def detect_solar_panels(self, image: np.ndarray):
        """Detect solar panels on roof"""
        # Simulated detection
        import random
        
        has_panels = random.random() > 0.7
        
        result = {
            'has_panels': has_panels,
            'confidence': random.uniform(0.8, 0.99)
        }
        
        if has_panels:
            result['count'] = random.randint(10, 30)
            result['coverage'] = random.uniform(20, 60)
            result['panel_locations'] = [
                {
                    'bbox': [random.randint(50, 200), random.randint(50, 200), 
                            random.randint(250, 400), random.randint(250, 400)],
                    'confidence': random.uniform(0.8, 0.95)
                }
                for _ in range(random.randint(2, 5))
            ]
        
        return result
    
    def analyze_roof_condition(self, image: np.ndarray, material_type: Optional[str] = None):
        """Analyze roof condition and age"""
        # Simulated analysis
        import random
        
        conditions = ['excellent', 'good', 'fair', 'poor']
        condition = random.choice(conditions)
        score = {'excellent': 0.9, 'good': 0.75, 'fair': 0.5, 'poor': 0.25}[condition]
        
        analysis = {
            'condition': condition,
            'score': score,
            'age_estimate': random.randint(5, 30),
            'issues': [],
            'priority': 'low' if score > 0.7 else ('medium' if score > 0.4 else 'high')
        }
        
        # Add issues based on condition
        if condition in ['fair', 'poor']:
            analysis['issues'] = random.sample([
                'missing_tiles', 'color_fading', 'moss_growth', 
                'damaged_flashing', 'sagging_areas'
            ], random.randint(1, 3))
        
        # Add detailed analysis
        analysis['detailed_analysis'] = {
            'wear': random.choice(['minimal', 'normal', 'significant']),
            'fading': random.choice(['minimal', 'moderate', 'severe']),
            'structural': [] if score > 0.5 else ['minor_sagging']
        }
        
        # Add recommendations
        if score < 0.5:
            analysis['recommendations'] = [
                'Schedule professional inspection',
                'Consider roof replacement in 1-2 years',
                'Address drainage issues'
            ]
        elif score < 0.75:
            analysis['recommendations'] = [
                'Regular maintenance recommended',
                'Clean gutters and remove debris',
                'Monitor for further deterioration'
            ]
        else:
            analysis['recommendations'] = [
                'Continue regular maintenance',
                'Annual inspection recommended'
            ]
        
        return analysis
    
    async def classify_roof_async(self, image: np.ndarray, mask: Optional[np.ndarray] = None):
        """Async wrapper for classify_roof"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.classify_roof, image, mask)
    
    async def cleanup(self):
        """Cleanup resources"""
        self.executor.shutdown(wait=True)
        for model in self.models.values():
            if hasattr(model, 'to'):
                model.cpu()
        torch.cuda.empty_cache()

