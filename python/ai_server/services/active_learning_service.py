import logging
from typing import List, Dict, Optional, Any
import numpy as np
from datetime import datetime
import asyncio
import json

logger = logging.getLogger(__name__)

class ActiveLearningService:
    """Service for managing active learning workflows"""
    
    def __init__(self, model_manager):
        self.model_manager = model_manager
        self.cvat_enabled = False  # Can be enabled when <PERSON>VA<PERSON> is configured
        self.iterations = {}
        
    async def select_samples(
        self, 
        unlabeled_samples: List[Dict], 
        sample_size: int, 
        strategy: str = "uncertainty"
    ) -> List[Dict]:
        """
        Select most informative samples for annotation
        
        Strategies:
        - uncertainty: Select samples with highest prediction uncertainty
        - diversity: Select diverse samples to cover data distribution
        - hybrid: Combine uncertainty and diversity
        """
        
        if strategy == "uncertainty":
            return await self._uncertainty_sampling(unlabeled_samples, sample_size)
        elif strategy == "diversity":
            return await self._diversity_sampling(unlabeled_samples, sample_size)
        elif strategy == "hybrid":
            return await self._hybrid_sampling(unlabeled_samples, sample_size)
        else:
            raise ValueError(f"Unknown sampling strategy: {strategy}")
    
    async def _uncertainty_sampling(self, samples: List[Dict], k: int) -> List[Dict]:
        """Select samples with highest uncertainty"""
        
        # Calculate uncertainty scores for each sample
        scored_samples = []
        
        for sample in samples:
            # In production, this would load the actual image and run inference
            # For now, we'll use the predicted_confidence as inverse uncertainty
            uncertainty = 1.0 - sample.get('predicted_confidence', 0.5)
            
            scored_samples.append({
                **sample,
                'uncertainty_score': uncertainty,
                'priority': uncertainty
            })
        
        # Sort by uncertainty (highest first)
        scored_samples.sort(key=lambda x: x['uncertainty_score'], reverse=True)
        
        # Return top k samples
        return scored_samples[:k]
    
    async def _diversity_sampling(self, samples: List[Dict], k: int) -> List[Dict]:
        """Select diverse samples using spatial diversity"""
        
        selected = []
        remaining = samples.copy()
        
        # Select first sample randomly
        if remaining:
            idx = np.random.randint(len(remaining))
            first_sample = remaining.pop(idx)
            first_sample['uncertainty_score'] = 0.5
            first_sample['priority'] = 1.0
            selected.append(first_sample)
        
        # Select remaining samples based on distance from already selected
        while len(selected) < k and remaining:
            max_min_dist = 0
            best_idx = 0
            
            for i, sample in enumerate(remaining):
                # Calculate minimum distance to selected samples
                min_dist = float('inf')
                
                for sel_sample in selected:
                    # Use geographic distance
                    dist = self._calculate_distance(
                        sample['bbox'], 
                        sel_sample['bbox']
                    )
                    min_dist = min(min_dist, dist)
                
                if min_dist > max_min_dist:
                    max_min_dist = min_dist
                    best_idx = i
            
            # Add sample with maximum minimum distance
            best_sample = remaining.pop(best_idx)
            best_sample['uncertainty_score'] = 0.5
            best_sample['priority'] = 1.0 - len(selected) / k
            selected.append(best_sample)
        
        return selected
    
    async def _hybrid_sampling(self, samples: List[Dict], k: int) -> List[Dict]:
        """Combine uncertainty and diversity sampling"""
        
        # Get uncertainty scores
        uncertainty_samples = await self._uncertainty_sampling(samples, len(samples))
        
        # Select top uncertain samples (2x the desired amount)
        uncertain_pool = uncertainty_samples[:k * 2]
        
        # Apply diversity sampling on the uncertain pool
        diverse_samples = await self._diversity_sampling(uncertain_pool, k)
        
        # Combine scores
        for sample in diverse_samples:
            sample['priority'] = (
                0.7 * sample.get('uncertainty_score', 0.5) + 
                0.3 * sample.get('priority', 0.5)
            )
        
        return diverse_samples
    
    def _calculate_distance(self, bbox1: List[float], bbox2: List[float]) -> float:
        """Calculate distance between two bounding boxes"""
        # Use center points
        center1 = [(bbox1[0] + bbox1[2]) / 2, (bbox1[1] + bbox1[3]) / 2]
        center2 = [(bbox2[0] + bbox2[2]) / 2, (bbox2[1] + bbox2[3]) / 2]
        
        # Euclidean distance
        return np.sqrt(
            (center1[0] - center2[0])**2 + 
            (center1[1] - center2[1])**2
        )
    
    async def create_cvat_task(
        self, 
        iteration_id: str, 
        samples: List[Dict]
    ) -> Optional[str]:
        """Create CVAT annotation task"""
        
        if not self.cvat_enabled:
            logger.info("CVAT integration not enabled")
            return None
        
        # In production, this would:
        # 1. Create a new CVAT task
        # 2. Upload images for selected samples
        # 3. Add pre-annotations from model predictions
        # 4. Return the CVAT task ID
        
        task_id = f"cvat_task_{iteration_id}"
        logger.info(f"Created CVAT task: {task_id}")
        
        return task_id
    
    async def update_model(
        self, 
        iteration_id: str, 
        annotations: List[Dict]
    ) -> Dict[str, Any]:
        """Update model with new annotations"""
        
        logger.info(f"Updating model with {len(annotations)} annotations")
        
        # In production, this would:
        # 1. Convert annotations to training format
        # 2. Fine-tune the model
        # 3. Evaluate on validation set
        # 4. Deploy if performance improved
        
        # Mock response
        return {
            'success': True,
            'version': f"v1.{iteration_id}",
            'metrics_improvement': {
                'iou': 0.02,
                'accuracy': 0.015,
                'f1_score': 0.018
            }
        }
    
    async def get_iteration_status(self, iteration_id: str) -> Optional[Dict]:
        """Get status of an iteration"""
        
        if iteration_id not in self.iterations:
            return None
        
        iteration = self.iterations[iteration_id]
        
        # Mock status
        return {
            'iteration_id': iteration_id,
            'status': 'in_progress',
            'created_at': iteration.get('created_at'),
            'samples_total': iteration.get('samples_total', 0),
            'samples_completed': iteration.get('samples_completed', 0),
            'cvat_task_id': iteration.get('cvat_task_id'),
            'model_updated': iteration.get('model_updated', False)
        }
    
    async def get_batch_statistics(self, batch_id: str) -> Dict[str, Any]:
        """Get statistics for a batch"""
        
        # Mock statistics
        return {
            'total_iterations': 5,
            'total_samples_annotated': 500,
            'performance_trend': [
                {'iteration': 1, 'iou': 0.82, 'accuracy': 0.88},
                {'iteration': 2, 'iou': 0.84, 'accuracy': 0.89},
                {'iteration': 3, 'iou': 0.85, 'accuracy': 0.90},
                {'iteration': 4, 'iou': 0.86, 'accuracy': 0.91},
                {'iteration': 5, 'iou': 0.87, 'accuracy': 0.92}
            ],
            'confidence_distribution': {
                '0.0-0.2': 5,
                '0.2-0.4': 15,
                '0.4-0.6': 25,
                '0.6-0.8': 35,
                '0.8-1.0': 20
            },
            'annotation_time_stats': {
                'avg_time_per_sample': 0.5,
                'total_time_hours': 4.2,
                'efficiency_trend': 'improving'
            }
        }
    
    def save_iteration(self, iteration_id: str, data: Dict):
        """Save iteration data"""
        self.iterations[iteration_id] = {
            **data,
            'created_at': datetime.now().isoformat()
        }