#!/usr/bin/env python3
"""Start the AI server with proper module resolution"""

import sys
import os
from pathlib import Path

# Add the ai_server directory to Python path
ai_server_dir = Path(__file__).parent
sys.path.insert(0, str(ai_server_dir))

# Now we can import the app
if __name__ == "__main__":
    import uvicorn
    from main import app
    
    print("Starting AstraMeccanica AI Server...")
    print(f"Python path: {sys.path[0]}")
    
    uvicorn.run(app, host="0.0.0.0", port=8001, reload=False)