import os
import sys
from pathlib import Path
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import torch
from dotenv import load_dotenv
import logging

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables
load_dotenv(Path(__file__).parent.parent.parent / "config" / ".env")

# Import routers
from routers import segmentation, classification, osm, active_learning, roof_training

# Import model manager
from services.model_manager import ModelManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global model manager
model_manager = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup and shutdown events"""
    global model_manager
    
    logger.info("Starting AI server...")
    
    # Initialize model manager
    model_manager = ModelManager()
    
    # Load models
    logger.info("Loading SAM 2 model...")
    await model_manager.load_sam2()
    
    logger.info("Loading roof classifier...")
    await model_manager.load_roof_classifier()
    
    logger.info("AI server started successfully!")
    
    yield
    
    # Cleanup
    logger.info("Shutting down AI server...")
    if model_manager:
        await model_manager.cleanup()

# Create FastAPI app
app = FastAPI(
    title="AstraMeccanica AI Server",
    description="AI inference server for roof annotation system",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(segmentation.router, prefix="/api/segmentation", tags=["segmentation"])
app.include_router(classification.router, prefix="/api/classification", tags=["classification"])
app.include_router(osm.router, prefix="/api/osm", tags=["osm"])
app.include_router(active_learning.router, prefix="/api/active-learning", tags=["active-learning"])
app.include_router(roof_training.router, prefix="/api/roof-training", tags=["roof-training"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "AstraMeccanica AI Server",
        "status": "running",
        "device": "cuda" if torch.cuda.is_available() else "cpu",
        "models_loaded": model_manager.get_loaded_models() if model_manager else []
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="Model manager not initialized")
    
    return {
        "status": "healthy",
        "models": model_manager.get_model_status()
    }

@app.get("/api/models")
async def list_models():
    """List available models and their status"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="Model manager not initialized")
    
    return model_manager.get_model_info()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)