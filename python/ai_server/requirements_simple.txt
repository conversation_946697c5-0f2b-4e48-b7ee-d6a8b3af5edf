# FastAPI and server
fastapi==0.109.0
uvicorn[standard]==0.27.0
python-multipart==0.0.6
pydantic==2.5.3

# AI/ML frameworks (using compatible versions)
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.36.0
# segment-anything-2==0.1.0  # Will install manually if available
opencv-python>=4.8.0
scikit-image>=0.22.0

# Geospatial
geopandas>=0.14.0
shapely>=2.0.0
pyproj>=3.6.0
rasterio>=1.3.0
osmnx>=1.8.0

# Database
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0
geoalchemy2>=0.14.0

# Utils
numpy>=1.24.0
pillow>=10.0.0
tqdm>=4.66.0
python-dotenv>=1.0.0
aiofiles>=23.0.0
httpx>=0.26.0

# CVAT integration (if available)
# cvat-sdk>=2.9.0

# Monitoring
prometheus-client>=0.19.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.23.0