from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from typing import List, Optional, Dict, Any
import numpy as np
import cv2
import base64
import io
from PIL import Image
import torch
import json
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# Get model manager at runtime
def get_model_manager():
    """Get model manager from app state"""
    from main import model_manager
    return model_manager

# Roof material classes
MATERIAL_CLASSES = {
    0: "terracotta_tiles",
    1: "cement_tiles",
    2: "slate_tiles",
    3: "metal_sheet",
    4: "asphalt_shingles",
    5: "flat_concrete",
    6: "green_roof",
    7: "solar_panels",
    8: "mixed_materials",
    9: "unknown"
}

# Building type classes
BUILDING_TYPES = {
    0: "residential",
    1: "industrial",
    2: "commercial",
    3: "agricultural",
    4: "institutional",
    5: "mixed_use"
}

def decode_image(image_data: str) -> np.ndarray:
    """Decode base64 image to numpy array"""
    try:
        # Remove data URL prefix if present
        if ',' in image_data:
            image_data = image_data.split(',')[1]
        
        # Decode base64
        image_bytes = base64.b64decode(image_data)
        
        # Convert to PIL Image
        image = Image.open(io.BytesIO(image_bytes))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Convert to numpy array
        return np.array(image)
    except Exception as e:
        logger.error(f"Error decoding image: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid image data: {str(e)}")

def extract_roof_crop(image: np.ndarray, polygon: List[List[float]]) -> np.ndarray:
    """Extract roof region from image using polygon coordinates"""
    try:
        # Create mask from polygon
        pts = np.array(polygon, dtype=np.int32)
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        cv2.fillPoly(mask, [pts], 255)
        
        # Get bounding box
        x, y, w, h = cv2.boundingRect(pts)
        
        # Crop image and mask
        cropped_image = image[y:y+h, x:x+w]
        cropped_mask = mask[y:y+h, x:x+w]
        
        # Apply mask
        result = cv2.bitwise_and(cropped_image, cropped_image, mask=cropped_mask)
        
        # Set background to white
        background = np.ones_like(result) * 255
        background[cropped_mask > 0] = result[cropped_mask > 0]
        
        return background
    except Exception as e:
        logger.error(f"Error extracting roof crop: {e}")
        return image

@router.post("/classify-roof")
async def classify_roof(
    image: str = Form(..., description="Base64 encoded image"),
    polygon: str = Form(..., description="JSON array of polygon coordinates"),
    include_confidence: bool = Form(True, description="Include confidence scores")
):
    """
    Classify roof material and building type
    
    - **image**: Base64 encoded image
    - **polygon**: Polygon coordinates defining the roof region
    - **include_confidence**: Whether to include confidence scores
    """
    try:
        mm = get_model_manager()
        if not mm or not mm.classifier:
            raise HTTPException(status_code=503, detail="Classifier model not loaded")
        
        # Decode image
        image_np = decode_image(image)
        
        # Parse polygon
        polygon_coords = json.loads(polygon)
        
        # Extract roof region
        roof_crop = extract_roof_crop(image_np, polygon_coords)
        
        # Run classification
        predictions = mm.classify_roof(roof_crop)
        
        # Process results
        result = {
            "material": {
                "class": MATERIAL_CLASSES.get(predictions["material_class"], "unknown"),
                "class_id": predictions["material_class"]
            },
            "building_type": {
                "class": BUILDING_TYPES.get(predictions["building_class"], "unknown"),
                "class_id": predictions["building_class"]
            },
            "has_solar_panels": predictions["has_solar_panels"],
            "roof_condition": predictions.get("condition", "unknown"),
            "estimated_age": predictions.get("age_estimate", "unknown")
        }
        
        if include_confidence:
            result["material"]["confidence"] = float(predictions["material_confidence"])
            result["building_type"]["confidence"] = float(predictions["building_confidence"])
            result["solar_panel_confidence"] = float(predictions["solar_confidence"])
        
        # Add additional attributes
        if "color_dominant" in predictions:
            result["dominant_color"] = predictions["color_dominant"]
        
        if "texture_features" in predictions:
            result["texture_complexity"] = predictions["texture_features"].get("complexity", "medium")
        
        return {
            "status": "success",
            "classification": result
        }
        
    except Exception as e:
        logger.error(f"Classification error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/classify-batch")
async def classify_batch(
    images: List[str] = Form(..., description="List of base64 encoded images"),
    polygons: str = Form(..., description="JSON array of polygons for each image"),
    include_confidence: bool = Form(True, description="Include confidence scores")
):
    """
    Classify multiple roofs in batch
    
    - **images**: List of base64 encoded images
    - **polygons**: JSON array containing polygon coordinates for each image
    - **include_confidence**: Whether to include confidence scores
    """
    try:
        mm = get_model_manager()
        if not mm or not mm.classifier:
            raise HTTPException(status_code=503, detail="Classifier model not loaded")
        
        # Parse polygons
        polygons_list = json.loads(polygons)
        
        if len(images) != len(polygons_list):
            raise HTTPException(
                status_code=400,
                detail="Number of images and polygons must match"
            )
        
        results = []
        
        for image_data, polygon_coords in zip(images, polygons_list):
            # Decode image
            image_np = decode_image(image_data)
            
            # Extract roof region
            roof_crop = extract_roof_crop(image_np, polygon_coords)
            
            # Run classification
            predictions = mm.classify_roof(roof_crop)
            
            # Process results
            result = {
                "material": {
                    "class": MATERIAL_CLASSES.get(predictions["material_class"], "unknown"),
                    "class_id": predictions["material_class"]
                },
                "building_type": {
                    "class": BUILDING_TYPES.get(predictions["building_class"], "unknown"),
                    "class_id": predictions["building_class"]
                },
                "has_solar_panels": predictions["has_solar_panels"],
                "roof_condition": predictions.get("condition", "unknown")
            }
            
            if include_confidence:
                result["material"]["confidence"] = float(predictions["material_confidence"])
                result["building_type"]["confidence"] = float(predictions["building_confidence"])
                result["solar_panel_confidence"] = float(predictions["solar_confidence"])
            
            results.append(result)
        
        return {
            "status": "success",
            "num_roofs": len(results),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Batch classification error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/detect-solar-panels")
async def detect_solar_panels(
    image: str = Form(..., description="Base64 encoded image"),
    polygon: str = Form(..., description="JSON array of polygon coordinates"),
    return_mask: bool = Form(False, description="Return mask of detected solar panels")
):
    """
    Detect and localize solar panels on roof
    
    - **image**: Base64 encoded image
    - **polygon**: Polygon coordinates defining the roof region
    - **return_mask**: Whether to return mask of detected panels
    """
    try:
        mm = get_model_manager()
        if not mm:
            raise HTTPException(status_code=503, detail="Model manager not initialized")
        
        # Decode image
        image_np = decode_image(image)
        
        # Parse polygon
        polygon_coords = json.loads(polygon)
        
        # Extract roof region
        roof_crop = extract_roof_crop(image_np, polygon_coords)
        
        # Detect solar panels
        detection = mm.detect_solar_panels(roof_crop)
        
        result = {
            "has_solar_panels": detection["has_panels"],
            "confidence": float(detection["confidence"]),
            "panel_count": detection.get("count", 0),
            "coverage_percentage": float(detection.get("coverage", 0.0))
        }
        
        if detection["has_panels"] and "panel_locations" in detection:
            result["panel_locations"] = detection["panel_locations"]
        
        if return_mask and "mask" in detection:
            # Encode mask
            mask_uint8 = (detection["mask"] * 255).astype(np.uint8)
            _, buffer = cv2.imencode('.png', mask_uint8)
            mask_base64 = base64.b64encode(buffer).decode('utf-8')
            result["mask"] = f"data:image/png;base64,{mask_base64}"
        
        return {
            "status": "success",
            "solar_panel_detection": result
        }
        
    except Exception as e:
        logger.error(f"Solar panel detection error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze-roof-condition")
async def analyze_roof_condition(
    image: str = Form(..., description="Base64 encoded image"),
    polygon: str = Form(..., description="JSON array of polygon coordinates"),
    material_hint: Optional[str] = Form(None, description="Optional material type hint")
):
    """
    Analyze roof condition and estimate age
    
    - **image**: Base64 encoded image
    - **polygon**: Polygon coordinates defining the roof region
    - **material_hint**: Optional material type to improve analysis
    """
    try:
        mm = get_model_manager()
        if not mm:
            raise HTTPException(status_code=503, detail="Model manager not initialized")
        
        # Decode image
        image_np = decode_image(image)
        
        # Parse polygon
        polygon_coords = json.loads(polygon)
        
        # Extract roof region
        roof_crop = extract_roof_crop(image_np, polygon_coords)
        
        # Analyze condition
        analysis = mm.analyze_roof_condition(
            roof_crop,
            material_type=material_hint
        )
        
        result = {
            "overall_condition": analysis["condition"],
            "condition_score": float(analysis["score"]),
            "estimated_age_years": analysis.get("age_estimate", "unknown"),
            "issues_detected": analysis.get("issues", []),
            "maintenance_priority": analysis.get("priority", "medium")
        }
        
        # Add detailed findings if available
        if "detailed_analysis" in analysis:
            result["detailed_findings"] = {
                "wear_level": analysis["detailed_analysis"].get("wear", "normal"),
                "damage_areas": analysis["detailed_analysis"].get("damage_areas", []),
                "color_fading": analysis["detailed_analysis"].get("fading", "minimal"),
                "structural_concerns": analysis["detailed_analysis"].get("structural", [])
            }
        
        # Add recommendations
        if "recommendations" in analysis:
            result["recommendations"] = analysis["recommendations"]
        
        return {
            "status": "success",
            "condition_analysis": result
        }
        
    except Exception as e:
        logger.error(f"Condition analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))