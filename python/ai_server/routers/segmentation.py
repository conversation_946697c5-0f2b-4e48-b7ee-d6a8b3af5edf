from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
import numpy as np
import cv2
import base64
import io
from PIL import Image
import torch
import json
from shapely.geometry import Polygon, MultiPolygon
from shapely.ops import unary_union
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# Get model manager at runtime
def get_model_manager():
    """Get model manager from app state"""
    from main import model_manager
    return model_manager

def decode_image(image_data: str) -> np.ndarray:
    """Decode base64 image to numpy array"""
    try:
        # Remove data URL prefix if present
        if ',' in image_data:
            image_data = image_data.split(',')[1]
        
        # Decode base64
        image_bytes = base64.b64decode(image_data)
        
        # Convert to PIL Image
        image = Image.open(io.BytesIO(image_bytes))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Convert to numpy array
        return np.array(image)
    except Exception as e:
        logger.error(f"Error decoding image: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid image data: {str(e)}")

def encode_mask(mask: np.ndarray) -> str:
    """Encode mask to base64 PNG"""
    try:
        # Convert boolean mask to uint8
        mask_uint8 = (mask * 255).astype(np.uint8)
        
        # Encode to PNG
        _, buffer = cv2.imencode('.png', mask_uint8)
        
        # Convert to base64
        mask_base64 = base64.b64encode(buffer).decode('utf-8')
        
        return f"data:image/png;base64,{mask_base64}"
    except Exception as e:
        logger.error(f"Error encoding mask: {e}")
        raise HTTPException(status_code=500, detail=f"Error encoding mask: {str(e)}")

def mask_to_polygon(mask: np.ndarray, simplify_tolerance: float = 1.0) -> List[List[float]]:
    """Convert binary mask to polygon coordinates"""
    try:
        # Find contours
        contours, _ = cv2.findContours(
            mask.astype(np.uint8),
            cv2.RETR_EXTERNAL,
            cv2.CHAIN_APPROX_SIMPLE
        )
        
        if not contours:
            return []
        
        # Get the largest contour
        largest_contour = max(contours, key=cv2.contourArea)
        
        # Convert to polygon
        points = largest_contour.squeeze()
        
        # Ensure we have enough points
        if len(points) < 3:
            return []
        
        # Create Shapely polygon and simplify
        polygon = Polygon(points)
        if simplify_tolerance > 0:
            polygon = polygon.simplify(simplify_tolerance)
        
        # Convert to list of coordinates
        if polygon.is_empty:
            return []
        
        coords = list(polygon.exterior.coords)
        return [[float(x), float(y)] for x, y in coords]
        
    except Exception as e:
        logger.error(f"Error converting mask to polygon: {e}")
        return []

@router.post("/segment")
async def segment_image(
    image: str = Form(..., description="Base64 encoded image"),
    points: Optional[str] = Form(None, description="JSON array of point prompts"),
    boxes: Optional[str] = Form(None, description="JSON array of box prompts"),
    labels: Optional[str] = Form(None, description="JSON array of labels for points (1 for positive, 0 for negative)"),
    multimask: bool = Form(True, description="Whether to return multiple masks"),
    return_polygons: bool = Form(True, description="Whether to return polygon coordinates")
):
    """
    Segment objects in image using SAM 2
    
    - **image**: Base64 encoded image
    - **points**: Optional point prompts as JSON array [[x1,y1], [x2,y2], ...]
    - **boxes**: Optional box prompts as JSON array [[x1,y1,x2,y2], ...]
    - **labels**: Labels for points (1 for positive, 0 for negative)
    - **multimask**: Whether to return multiple mask predictions
    - **return_polygons**: Whether to convert masks to polygon coordinates
    """
    try:
        mm = get_model_manager()
        if not mm or not mm.sam_model:
            raise HTTPException(status_code=503, detail="SAM model not loaded")
        
        # Decode image
        image_np = decode_image(image)
        height, width = image_np.shape[:2]
        
        # Parse prompts
        point_prompts = None
        box_prompts = None
        point_labels = None
        
        if points:
            point_prompts = np.array(json.loads(points))
            if labels:
                point_labels = np.array(json.loads(labels))
            else:
                # Default all points to positive
                point_labels = np.ones(len(point_prompts))
        
        if boxes:
            box_prompts = np.array(json.loads(boxes))
        
        # Run SAM inference
        masks, scores, logits = mm.predict_sam(
            image_np,
            point_coords=point_prompts,
            point_labels=point_labels,
            boxes=box_prompts,
            multimask_output=multimask
        )
        
        # Process results
        results = []
        for i, (mask, score) in enumerate(zip(masks, scores)):
            result = {
                "id": i,
                "score": float(score),
                "mask": encode_mask(mask)
            }
            
            # Add polygon if requested
            if return_polygons:
                polygon = mask_to_polygon(mask)
                result["polygon"] = polygon
                
                # Calculate area
                if polygon:
                    poly = Polygon(polygon)
                    result["area"] = float(poly.area)
                    result["perimeter"] = float(poly.length)
            
            results.append(result)
        
        # Sort by score
        results.sort(key=lambda x: x["score"], reverse=True)
        
        return {
            "status": "success",
            "image_size": {"width": width, "height": height},
            "num_masks": len(results),
            "masks": results
        }
        
    except Exception as e:
        logger.error(f"Segmentation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/segment-batch")
async def segment_batch(
    images: List[str] = Form(..., description="List of base64 encoded images"),
    prompts: str = Form(..., description="JSON array of prompts for each image"),
    return_polygons: bool = Form(True, description="Whether to return polygon coordinates")
):
    """
    Segment multiple images in batch
    
    - **images**: List of base64 encoded images
    - **prompts**: JSON array containing prompts for each image
    - **return_polygons**: Whether to convert masks to polygon coordinates
    """
    try:
        mm = get_model_manager()
        if not mm or not mm.sam_model:
            raise HTTPException(status_code=503, detail="SAM model not loaded")
        
        # Parse prompts
        prompts_list = json.loads(prompts)
        
        if len(images) != len(prompts_list):
            raise HTTPException(
                status_code=400, 
                detail="Number of images and prompts must match"
            )
        
        results = []
        
        for image_data, image_prompts in zip(images, prompts_list):
            # Decode image
            image_np = decode_image(image_data)
            
            # Extract prompts
            point_prompts = None
            box_prompts = None
            point_labels = None
            
            if "points" in image_prompts:
                point_prompts = np.array(image_prompts["points"])
                point_labels = np.array(image_prompts.get("labels", [1] * len(point_prompts)))
            
            if "boxes" in image_prompts:
                box_prompts = np.array(image_prompts["boxes"])
            
            # Run SAM inference
            masks, scores, _ = mm.predict_sam(
                image_np,
                point_coords=point_prompts,
                point_labels=point_labels,
                boxes=box_prompts,
                multimask_output=True
            )
            
            # Get best mask
            best_idx = np.argmax(scores)
            best_mask = masks[best_idx]
            best_score = scores[best_idx]
            
            result = {
                "score": float(best_score),
                "mask": encode_mask(best_mask)
            }
            
            if return_polygons:
                polygon = mask_to_polygon(best_mask)
                result["polygon"] = polygon
                
                if polygon:
                    poly = Polygon(polygon)
                    result["area"] = float(poly.area)
                    result["perimeter"] = float(poly.length)
            
            results.append(result)
        
        return {
            "status": "success",
            "num_images": len(results),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Batch segmentation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/auto-segment")
async def auto_segment(
    image: str = Form(..., description="Base64 encoded image"),
    points_per_side: int = Form(32, description="Number of points per side for automatic mask generation"),
    pred_iou_thresh: float = Form(0.88, description="IoU threshold for filtering predictions"),
    stability_score_thresh: float = Form(0.95, description="Stability score threshold"),
    min_mask_region_area: int = Form(100, description="Minimum mask region area"),
    return_polygons: bool = Form(True, description="Whether to return polygon coordinates")
):
    """
    Automatically segment all objects in image
    
    - **image**: Base64 encoded image
    - **points_per_side**: Number of points to sample per side
    - **pred_iou_thresh**: IoU threshold for filtering
    - **stability_score_thresh**: Stability score threshold
    - **min_mask_region_area**: Minimum area for valid masks
    - **return_polygons**: Whether to convert masks to polygon coordinates
    """
    try:
        mm = get_model_manager()
        if not mm or not mm.sam_model:
            raise HTTPException(status_code=503, detail="SAM model not loaded")
        
        # Decode image
        image_np = decode_image(image)
        height, width = image_np.shape[:2]
        
        # Generate automatic masks
        masks = mm.generate_masks(
            image_np,
            points_per_side=points_per_side,
            pred_iou_thresh=pred_iou_thresh,
            stability_score_thresh=stability_score_thresh,
            min_mask_region_area=min_mask_region_area
        )
        
        # Process results
        results = []
        for i, mask_data in enumerate(masks):
            result = {
                "id": i,
                "score": float(mask_data["predicted_iou"]),
                "stability_score": float(mask_data["stability_score"]),
                "mask": encode_mask(mask_data["segmentation"])
            }
            
            if return_polygons:
                polygon = mask_to_polygon(mask_data["segmentation"])
                result["polygon"] = polygon
                
                if polygon:
                    poly = Polygon(polygon)
                    result["area"] = float(poly.area)
                    result["perimeter"] = float(poly.length)
                    
                    # Calculate bounding box
                    bounds = poly.bounds
                    result["bbox"] = [float(b) for b in bounds]
            
            results.append(result)
        
        # Sort by area (largest first)
        results.sort(key=lambda x: x.get("area", 0), reverse=True)
        
        return {
            "status": "success",
            "image_size": {"width": width, "height": height},
            "num_masks": len(results),
            "masks": results
        }
        
    except Exception as e:
        logger.error(f"Auto segmentation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/refine-mask")
async def refine_mask(
    image: str = Form(..., description="Base64 encoded image"),
    mask: str = Form(..., description="Base64 encoded initial mask"),
    positive_points: Optional[str] = Form(None, description="JSON array of positive refinement points"),
    negative_points: Optional[str] = Form(None, description="JSON array of negative refinement points"),
    return_polygon: bool = Form(True, description="Whether to return polygon coordinates")
):
    """
    Refine an existing mask with additional prompts
    
    - **image**: Base64 encoded image
    - **mask**: Base64 encoded initial mask
    - **positive_points**: Points to include in mask
    - **negative_points**: Points to exclude from mask
    - **return_polygon**: Whether to convert mask to polygon
    """
    try:
        mm = get_model_manager()
        if not mm or not mm.sam_model:
            raise HTTPException(status_code=503, detail="SAM model not loaded")
        
        # Decode inputs
        image_np = decode_image(image)
        mask_np = decode_image(mask)
        
        # Convert mask to binary
        mask_binary = (mask_np[:, :, 0] > 128).astype(np.uint8)
        
        # Parse points
        all_points = []
        all_labels = []
        
        if positive_points:
            pos_pts = json.loads(positive_points)
            all_points.extend(pos_pts)
            all_labels.extend([1] * len(pos_pts))
        
        if negative_points:
            neg_pts = json.loads(negative_points)
            all_points.extend(neg_pts)
            all_labels.extend([0] * len(neg_pts))
        
        if not all_points:
            raise HTTPException(status_code=400, detail="No refinement points provided")
        
        # Run refinement
        point_prompts = np.array(all_points)
        point_labels = np.array(all_labels)
        
        masks, scores, _ = model_manager.predict_sam(
            image_np,
            point_coords=point_prompts,
            point_labels=point_labels,
            mask_input=mask_binary,
            multimask_output=False
        )
        
        # Get result
        refined_mask = masks[0]
        score = scores[0]
        
        result = {
            "score": float(score),
            "mask": encode_mask(refined_mask)
        }
        
        if return_polygon:
            polygon = mask_to_polygon(refined_mask)
            result["polygon"] = polygon
            
            if polygon:
                poly = Polygon(polygon)
                result["area"] = float(poly.area)
                result["perimeter"] = float(poly.length)
        
        return {
            "status": "success",
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Mask refinement error: {e}")
        raise HTTPException(status_code=500, detail=str(e))