"""
Router API per gestione dataset e training classificatore materiali tetti.
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Form, UploadFile, File
from fastapi.responses import JSONResponse, FileResponse
from typing import List, Dict, Optional, Tuple
import json
import os
import sys
from pathlib import Path
import numpy as np
import base64
import io
from PIL import Image
import asyncio
from datetime import datetime
import logging
import torch
import shutil

# Add parent paths
sys.path.append(str(Path(__file__).parent.parent.parent))

logger = logging.getLogger(__name__)
router = APIRouter()

# Global variables for tracking
training_status = {
    "is_training": False,
    "current_epoch": 0,
    "total_epochs": 0,
    "train_loss": 0,
    "val_loss": 0,
    "val_accuracy": 0,
    "status": "idle",
    "started_at": None,
    "completed_at": None
}

dataset_status = {
    "total_images": 0,
    "total_roofs": 0,
    "annotated": 0,
    "pending_annotation": 0,
    "last_updated": None
}

@router.post("/dataset/download-tiles")
async def download_tiles(
    locations: List[List[float]] = Form(..., description="List of [lat, lon] coordinates"),
    zoom: int = Form(19, description="Google Maps zoom level"),
    size: int = Form(640, description="Image size in pixels")
):
    """
    Download Google Maps tiles for specified locations.
    """
    try:
        # Use the synchronous downloader which exists
        sys.path.append(str(Path(__file__).parent.parent.parent))
        from downloader import GoogleMapsDownloader
        
        # Create output directory
        output_dir = Path("dataset/roofs/raw_images")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        downloaded = []
        errors = []
        
        for lat, lon in locations:
            try:
                # Generate filename
                filename = f"sat_{lat:.6f}_{lon:.6f}_{zoom}.jpg"
                filepath = output_dir / filename
                
                # Download if not exists
                if not filepath.exists():
                    # Create downloader
                    downloader = GoogleMapsDownloader(
                        lat=lat,
                        lng=lon,
                        zoom=zoom,
                        size_x=size,
                        size_y=size,
                        scale=2,
                        maptype='satellite',
                        format='jpg'
                    )
                    
                    # Download and save image
                    downloader.save_image(str(filepath))
                    
                    downloaded.append({
                        "filename": filename,
                        "lat": lat,
                        "lon": lon,
                        "path": str(filepath)
                    })
                else:
                    downloaded.append({
                        "filename": filename,
                        "lat": lat,
                        "lon": lon,
                        "path": str(filepath),
                        "existed": True
                    })
                    
            except Exception as e:
                errors.append({
                    "lat": lat,
                    "lon": lon,
                    "error": str(e)
                })
        
        # Update dataset status
        dataset_status["total_images"] = len(list(output_dir.glob("*.jpg")))
        dataset_status["last_updated"] = datetime.now().isoformat()
        
        return {
            "status": "success",
            "downloaded": downloaded,
            "errors": errors,
            "total_downloaded": len(downloaded),
            "total_errors": len(errors)
        }
        
    except Exception as e:
        logger.error(f"Error downloading tiles: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/dataset/segment-roofs")
async def segment_roofs(
    image_paths: List[str] = Form(..., description="List of image paths to segment"),
    min_area_ratio: float = Form(0.005, description="Minimum roof area as ratio of image"),
    max_area_ratio: float = Form(0.4, description="Maximum roof area as ratio of image")
):
    """
    Segment roofs in images using SAM.
    """
    try:
        # Import SAM - check if available
        try:
            from segment_anything import sam_model_registry, SamAutomaticMaskGenerator
        except ImportError:
            raise HTTPException(status_code=500, detail="SAM not installed. Please run setup_ml.sh first")
        
        # Load SAM model
        model_type = "vit_h"
        checkpoint_path = Path("ai_server/models/sam_vit_h_4b8939.pth")
        
        if not checkpoint_path.exists():
            raise HTTPException(status_code=404, detail="SAM model checkpoint not found")
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        sam = sam_model_registry[model_type](checkpoint=str(checkpoint_path))
        sam.to(device=device)
        
        # Create mask generator
        mask_generator = SamAutomaticMaskGenerator(
            model=sam,
            points_per_side=32,
            pred_iou_thresh=0.88,
            stability_score_thresh=0.95,
            crop_n_layers=1,
            crop_n_points_downscale_factor=2,
            min_mask_region_area=100,
        )
        
        results = []
        
        for image_path in image_paths:
            try:
                # Load image
                image = Image.open(image_path)
                image_np = np.array(image)
                height, width = image_np.shape[:2]
                
                # Generate masks
                masks = mask_generator.generate(image_np)
                
                # Filter for roof candidates
                roof_masks = []
                min_area = height * width * min_area_ratio
                max_area = height * width * max_area_ratio
                
                for mask in masks:
                    area = mask['area']
                    if min_area < area < max_area:
                        # Calculate additional features
                        bbox = mask['bbox']  # x, y, w, h
                        aspect_ratio = bbox[2] / max(bbox[3], 1)
                        
                        # Filter by aspect ratio (roofs are usually regular)
                        if 0.3 < aspect_ratio < 3.0:
                            roof_masks.append({
                                "segmentation": mask['segmentation'].tolist(),
                                "area": int(area),
                                "bbox": bbox.tolist(),
                                "stability_score": float(mask['stability_score']),
                                "predicted_iou": float(mask['predicted_iou'])
                            })
                
                # Sort by area (largest first)
                roof_masks.sort(key=lambda x: x['area'], reverse=True)
                
                # Save results
                base_name = Path(image_path).stem
                result_file = Path("dataset/roofs/segmented") / f"{base_name}_masks.json"
                result_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(result_file, 'w') as f:
                    json.dump(roof_masks, f)
                
                results.append({
                    "image": image_path,
                    "num_roofs": len(roof_masks),
                    "masks_file": str(result_file)
                })
                
            except Exception as e:
                results.append({
                    "image": image_path,
                    "error": str(e)
                })
        
        # Update dataset status
        dataset_status["total_roofs"] = sum(r.get("num_roofs", 0) for r in results if "num_roofs" in r)
        dataset_status["last_updated"] = datetime.now().isoformat()
        
        return {
            "status": "success",
            "results": results,
            "total_processed": len(results),
            "total_roofs_found": dataset_status["total_roofs"]
        }
        
    except Exception as e:
        logger.error(f"Error segmenting roofs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/dataset/extract-crops")
async def extract_roof_crops(
    segmentation_files: List[str] = Form(..., description="List of segmentation JSON files")
):
    """
    Extract individual roof crops from segmented images.
    """
    try:
        all_crops = []
        
        for seg_file in segmentation_files:
            seg_path = Path(seg_file)
            if not seg_path.exists():
                continue
            
            # Load masks
            with open(seg_path, 'r') as f:
                masks = json.load(f)
            
            # Get corresponding image
            base_name = seg_path.stem.replace("_masks", "")
            image_path = Path("dataset/roofs/raw_images") / f"{base_name}.jpg"
            
            if not image_path.exists():
                continue
            
            # Load image
            image = Image.open(image_path)
            image_np = np.array(image)
            
            # Extract crops
            for i, mask_data in enumerate(masks[:10]):  # Limit to top 10 roofs per image
                try:
                    # Get bounding box
                    x, y, w, h = mask_data['bbox']
                    
                    # Add padding
                    padding = 10
                    x1 = max(0, int(x) - padding)
                    y1 = max(0, int(y) - padding)
                    x2 = min(image_np.shape[1], int(x + w) + padding)
                    y2 = min(image_np.shape[0], int(y + h) + padding)
                    
                    # Crop image
                    crop = image_np[y1:y2, x1:x2]
                    
                    # Save crop
                    crop_filename = f"{base_name}_roof_{i:03d}.jpg"
                    crop_path = Path("dataset/roofs/cropped") / crop_filename
                    crop_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    Image.fromarray(crop).save(crop_path)
                    
                    # Save mask
                    mask_array = np.array(mask_data['segmentation'], dtype=np.uint8)
                    mask_crop = mask_array[y1:y2, x1:x2]
                    
                    mask_filename = f"{base_name}_roof_{i:03d}_mask.png"
                    mask_path = Path("dataset/roofs/segmented") / mask_filename
                    
                    Image.fromarray(mask_crop * 255).save(mask_path)
                    
                    # Create annotation
                    annotation = {
                        "id": f"{base_name}_{i:03d}",
                        "filename": crop_filename,
                        "source_image": str(image_path),
                        "bbox": [x1, y1, x2, y2],
                        "mask_file": mask_filename,
                        "area": mask_data['area'],
                        "stability_score": mask_data.get('stability_score', 0),
                        "material": "unknown",
                        "needs_annotation": True,
                        "created_at": datetime.now().isoformat()
                    }
                    
                    all_crops.append(annotation)
                    
                except Exception as e:
                    logger.error(f"Error extracting crop {i} from {base_name}: {e}")
        
        # Save annotations
        annotations_file = Path("dataset/roofs/annotations/annotations.json")
        annotations_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Load existing if exists
        existing = []
        if annotations_file.exists():
            with open(annotations_file, 'r') as f:
                existing = json.load(f)
        
        # Merge with new
        existing.extend(all_crops)
        
        # Save updated
        with open(annotations_file, 'w') as f:
            json.dump(existing, f, indent=2)
        
        # Update status
        dataset_status["total_roofs"] = len(existing)
        dataset_status["pending_annotation"] = sum(1 for a in existing if a.get("needs_annotation", True))
        dataset_status["annotated"] = dataset_status["total_roofs"] - dataset_status["pending_annotation"]
        dataset_status["last_updated"] = datetime.now().isoformat()
        
        return {
            "status": "success",
            "new_crops": len(all_crops),
            "total_crops": len(existing),
            "annotations_file": str(annotations_file)
        }
        
    except Exception as e:
        logger.error(f"Error extracting crops: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dataset/annotations")
async def get_annotations(
    limit: int = 100,
    offset: int = 0,
    needs_annotation: Optional[bool] = None
):
    """
    Get dataset annotations for review/editing.
    """
    try:
        annotations_file = Path("dataset/roofs/annotations/annotations.json")
        
        if not annotations_file.exists():
            return {
                "annotations": [],
                "total": 0,
                "limit": limit,
                "offset": offset
            }
        
        with open(annotations_file, 'r') as f:
            annotations = json.load(f)
        
        # Filter if needed
        if needs_annotation is not None:
            annotations = [a for a in annotations 
                         if a.get("needs_annotation", True) == needs_annotation]
        
        # Paginate
        total = len(annotations)
        annotations = annotations[offset:offset + limit]
        
        return {
            "annotations": annotations,
            "total": total,
            "limit": limit,
            "offset": offset,
            "stats": dataset_status
        }
        
    except Exception as e:
        logger.error(f"Error getting annotations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/dataset/annotations/{annotation_id}")
async def update_annotation(
    annotation_id: str,
    material: str = Form(..., description="Material classification"),
    confidence: float = Form(1.0, description="Annotation confidence")
):
    """
    Update annotation with material classification.
    """
    try:
        annotations_file = Path("dataset/roofs/annotations/annotations.json")
        
        if not annotations_file.exists():
            raise HTTPException(status_code=404, detail="Annotations file not found")
        
        # Load annotations
        with open(annotations_file, 'r') as f:
            annotations = json.load(f)
        
        # Find and update
        updated = False
        for ann in annotations:
            if ann.get("id") == annotation_id:
                ann["material"] = material
                ann["confidence"] = confidence
                ann["needs_annotation"] = False
                ann["annotated_at"] = datetime.now().isoformat()
                updated = True
                break
        
        if not updated:
            raise HTTPException(status_code=404, detail="Annotation not found")
        
        # Save updated
        with open(annotations_file, 'w') as f:
            json.dump(annotations, f, indent=2)
        
        # Update status
        dataset_status["pending_annotation"] = sum(1 for a in annotations if a.get("needs_annotation", True))
        dataset_status["annotated"] = len(annotations) - dataset_status["pending_annotation"]
        dataset_status["last_updated"] = datetime.now().isoformat()
        
        return {
            "status": "success",
            "annotation_id": annotation_id,
            "material": material,
            "stats": dataset_status
        }
        
    except Exception as e:
        logger.error(f"Error updating annotation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/dataset/prepare-training")
async def prepare_training_data(
    train_ratio: float = Form(0.7, description="Training set ratio"),
    val_ratio: float = Form(0.15, description="Validation set ratio")
):
    """
    Split dataset into train/val/test sets.
    """
    try:
        annotations_file = Path("dataset/roofs/annotations/annotations.json")
        
        if not annotations_file.exists():
            raise HTTPException(status_code=404, detail="Annotations file not found")
        
        with open(annotations_file, 'r') as f:
            annotations = json.load(f)
        
        # Filter only annotated samples
        annotated = [a for a in annotations 
                    if not a.get("needs_annotation", True) 
                    and a.get("material", "unknown") != "unknown"]
        
        if len(annotated) < 10:
            raise HTTPException(
                status_code=400, 
                detail=f"Not enough annotated samples. Found {len(annotated)}, need at least 10"
            )
        
        # Shuffle
        import random
        random.shuffle(annotated)
        
        # Split
        n_total = len(annotated)
        n_train = int(n_total * train_ratio)
        n_val = int(n_total * val_ratio)
        
        splits = {
            "train": annotated[:n_train],
            "val": annotated[n_train:n_train + n_val],
            "test": annotated[n_train + n_val:]
        }
        
        # Save splits
        for split_name, split_data in splits.items():
            split_file = Path(f"dataset/roofs/annotations/{split_name}.json")
            with open(split_file, 'w') as f:
                json.dump(split_data, f, indent=2)
        
        # Count by material
        material_counts = {}
        for ann in annotated:
            material = ann.get("material", "unknown")
            material_counts[material] = material_counts.get(material, 0) + 1
        
        return {
            "status": "success",
            "total_samples": n_total,
            "splits": {
                "train": len(splits["train"]),
                "val": len(splits["val"]),
                "test": len(splits["test"])
            },
            "materials": material_counts
        }
        
    except Exception as e:
        logger.error(f"Error preparing training data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/training/start")
async def start_training(
    background_tasks: BackgroundTasks,
    epochs: int = Form(50, description="Number of epochs"),
    batch_size: int = Form(32, description="Batch size"),
    learning_rate: float = Form(0.0001, description="Learning rate"),
    model_name: str = Form("efficientnet_b3", description="Model architecture")
):
    """
    Start training the roof material classifier.
    """
    global training_status
    
    if training_status["is_training"]:
        raise HTTPException(status_code=400, detail="Training already in progress")
    
    try:
        # Check if data is ready
        train_file = Path("dataset/roofs/annotations/train.json")
        val_file = Path("dataset/roofs/annotations/val.json")
        
        if not train_file.exists() or not val_file.exists():
            raise HTTPException(status_code=400, detail="Training data not prepared. Run prepare-training first.")
        
        # Update status
        training_status.update({
            "is_training": True,
            "current_epoch": 0,
            "total_epochs": epochs,
            "status": "starting",
            "started_at": datetime.now().isoformat()
        })
        
        # Start training in background
        background_tasks.add_task(
            run_training,
            epochs=epochs,
            batch_size=batch_size,
            learning_rate=learning_rate,
            model_name=model_name
        )
        
        return {
            "status": "training_started",
            "training_id": datetime.now().isoformat(),
            "config": {
                "epochs": epochs,
                "batch_size": batch_size,
                "learning_rate": learning_rate,
                "model_name": model_name
            }
        }
        
    except Exception as e:
        training_status["is_training"] = False
        training_status["status"] = "error"
        logger.error(f"Error starting training: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def run_training(epochs: int, batch_size: int, learning_rate: float, model_name: str):
    """
    Background task to run training.
    """
    global training_status
    
    try:
        # Import training module
        sys.path.append(str(Path(__file__).parent.parent.parent))
        from train_roof_classifier import RoofMaterialDataset, RoofMaterialClassifier, Trainer
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Create datasets
        train_dataset = RoofMaterialDataset(
            annotations_file="dataset/roofs/annotations/train.json",
            images_dir="dataset/roofs/cropped",
            augment=True
        )
        
        val_dataset = RoofMaterialDataset(
            annotations_file="dataset/roofs/annotations/val.json",
            images_dir="dataset/roofs/cropped",
            augment=False
        )
        
        # Create dataloaders
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=2
        )
        
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=2
        )
        
        # Create model
        model = RoofMaterialClassifier(
            num_classes=10,
            model_name=model_name,
            pretrained=True
        )
        
        # Create trainer
        trainer = Trainer(
            model=model,
            device=device,
            learning_rate=learning_rate
        )
        
        # Training loop with status updates
        for epoch in range(epochs):
            training_status["current_epoch"] = epoch + 1
            training_status["status"] = f"Training epoch {epoch + 1}/{epochs}"
            
            # Train epoch
            train_loss = trainer.train_epoch(train_loader)
            training_status["train_loss"] = train_loss
            
            # Validate
            val_loss, val_acc, _, _ = trainer.validate(val_loader)
            training_status["val_loss"] = val_loss
            training_status["val_accuracy"] = val_acc
            
            # Save checkpoint
            if val_acc > trainer.best_val_acc:
                trainer.best_val_acc = val_acc
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'val_acc': val_acc
                }, "models/roof_classifier/best_model.pth")
        
        # Training completed
        training_status.update({
            "is_training": False,
            "status": "completed",
            "completed_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Training error: {e}")
        training_status.update({
            "is_training": False,
            "status": f"error: {str(e)}"
        })

@router.get("/training/status")
async def get_training_status():
    """
    Get current training status.
    """
    return training_status

@router.post("/training/stop")
async def stop_training():
    """
    Stop ongoing training.
    """
    global training_status
    
    if not training_status["is_training"]:
        raise HTTPException(status_code=400, detail="No training in progress")
    
    training_status["is_training"] = False
    training_status["status"] = "stopped"
    
    return {"status": "training_stopped"}

@router.get("/dataset/status")
async def get_dataset_status():
    """
    Get dataset statistics.
    """
    return dataset_status

@router.get("/dataset/image/{image_name}")
async def get_image(image_name: str):
    """
    Get a specific image from the dataset.
    """
    # Check in different directories
    paths_to_check = [
        Path("dataset/roofs/raw_images") / image_name,
        Path("dataset/roofs/cropped") / image_name,
        Path("dataset/roofs/segmented") / image_name
    ]
    
    for path in paths_to_check:
        if path.exists():
            return FileResponse(path)
    
    raise HTTPException(status_code=404, detail="Image not found")