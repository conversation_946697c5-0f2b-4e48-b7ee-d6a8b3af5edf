from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional, Dict
import numpy as np
from pydantic import BaseModel
import logging
from datetime import datetime

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from services.active_learning_service import ActiveLearningService

logger = logging.getLogger(__name__)
router = APIRouter()

class ActiveLearningRequest(BaseModel):
    """Active learning iteration request"""
    batch_id: str
    sample_size: int = 100
    strategy: str = "uncertainty"  # uncertainty, diversity, hybrid
    confidence_threshold: float = 0.8

class SampleAnnotation(BaseModel):
    """Sample selected for annotation"""
    id: str
    tile_id: str
    bbox: List[float]
    uncertainty_score: float
    predicted_mask: Optional[str]
    predicted_class: Optional[str]
    priority: float

class ActiveLearningResponse(BaseModel):
    """Active learning response"""
    iteration_id: str
    batch_id: str
    num_samples: int
    samples: List[SampleAnnotation]
    cvat_task_id: Optional[str]
    estimated_time_hours: float

def get_al_service():
    """Get active learning service dependency"""
    from main import model_manager
    if not model_manager:
        raise HTTPException(status_code=503, detail="Model manager not available")
    return ActiveLearningService(model_manager)

@router.post("/start-iteration", response_model=ActiveLearningResponse)
async def start_active_learning_iteration(
    request: ActiveLearningRequest,
    al_service: ActiveLearningService = Depends(get_al_service)
):
    """
    Start a new active learning iteration
    
    Selects the most informative samples for human annotation
    """
    try:
        # Generate iteration ID
        iteration_id = f"al_{request.batch_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Get unlabeled samples from batch
        unlabeled_samples = await get_unlabeled_samples(request.batch_id)
        
        if not unlabeled_samples:
            raise HTTPException(status_code=404, detail="No unlabeled samples found")
        
        # Select samples based on strategy
        selected_samples = await al_service.select_samples(
            unlabeled_samples,
            request.sample_size,
            request.strategy
        )
        
        # Create CVAT task if configured
        cvat_task_id = None
        if al_service.cvat_enabled:
            cvat_task_id = await al_service.create_cvat_task(
                iteration_id,
                selected_samples
            )
        
        # Estimate annotation time
        estimated_time = estimate_annotation_time(len(selected_samples))
        
        return ActiveLearningResponse(
            iteration_id=iteration_id,
            batch_id=request.batch_id,
            num_samples=len(selected_samples),
            samples=selected_samples,
            cvat_task_id=cvat_task_id,
            estimated_time_hours=estimated_time
        )
        
    except Exception as e:
        logger.error(f"Error starting active learning: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/update-model")
async def update_model_with_annotations(
    iteration_id: str,
    annotations: List[Dict],
    al_service: ActiveLearningService = Depends(get_al_service)
):
    """
    Update model with new annotations from completed iteration
    """
    try:
        # Validate annotations
        if not annotations:
            raise HTTPException(status_code=400, detail="No annotations provided")
        
        # Update model (in production, this would trigger fine-tuning)
        update_result = await al_service.update_model(iteration_id, annotations)
        
        return {
            'iteration_id': iteration_id,
            'annotations_processed': len(annotations),
            'model_updated': update_result['success'],
            'new_model_version': update_result.get('version'),
            'metrics_improvement': update_result.get('metrics_improvement', {})
        }
        
    except Exception as e:
        logger.error(f"Error updating model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/iteration-status/{iteration_id}")
async def get_iteration_status(
    iteration_id: str,
    al_service: ActiveLearningService = Depends(get_al_service)
):
    """
    Get status of an active learning iteration
    """
    try:
        status = await al_service.get_iteration_status(iteration_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="Iteration not found")
        
        return status
        
    except Exception as e:
        logger.error(f"Error getting iteration status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/statistics/{batch_id}")
async def get_active_learning_statistics(
    batch_id: str,
    al_service: ActiveLearningService = Depends(get_al_service)
):
    """
    Get active learning statistics for a batch
    """
    try:
        stats = await al_service.get_batch_statistics(batch_id)
        
        return {
            'batch_id': batch_id,
            'total_iterations': stats['total_iterations'],
            'total_samples_annotated': stats['total_samples_annotated'],
            'model_performance_trend': stats['performance_trend'],
            'confidence_distribution': stats['confidence_distribution'],
            'annotation_time_stats': stats['annotation_time_stats']
        }
        
    except Exception as e:
        logger.error(f"Error getting statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def get_unlabeled_samples(batch_id: str) -> List[Dict]:
    """
    Get unlabeled samples from database
    
    In production, this would query the database for tiles
    without annotations or with low confidence
    """
    # Mock implementation
    samples = []
    
    for i in range(500):  # Simulate 500 unlabeled samples
        sample = {
            'id': f"sample_{batch_id}_{i}",
            'tile_id': f"tile_{i}",
            'bbox': [
                9.18 + np.random.random() * 0.01,
                45.46 + np.random.random() * 0.01,
                9.19 + np.random.random() * 0.01,
                45.47 + np.random.random() * 0.01
            ],
            'has_annotation': False,
            'predicted_confidence': np.random.random() * 0.9
        }
        samples.append(sample)
    
    return samples

def estimate_annotation_time(num_samples: int) -> float:
    """
    Estimate time required for annotation
    
    Based on empirical data:
    - Average time per roof: 1.5 minutes
    - With pre-annotations: 0.5 minutes
    """
    time_per_sample = 0.5  # minutes with pre-annotation
    total_minutes = num_samples * time_per_sample
    
    # Add overhead for breaks, review, etc.
    overhead_factor = 1.2
    
    return (total_minutes * overhead_factor) / 60  # Convert to hours