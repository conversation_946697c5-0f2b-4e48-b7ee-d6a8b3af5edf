from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
import osmnx as ox
import geopandas as gpd
from shapely.geometry import box, Polygon
import logging
import numpy as np
from pydantic import BaseModel

logger = logging.getLogger(__name__)
router = APIRouter()

# Configure osmnx
ox.settings.use_cache = True
ox.settings.log_console = False

class OSMBuilding(BaseModel):
    """OSM building model"""
    osm_id: str
    geometry: dict  # GeoJSON geometry
    building_type: Optional[str]
    height: Optional[float]
    levels: Optional[int]
    roof_shape: Optional[str]
    roof_material: Optional[str]
    confidence: float

class OSMResponse(BaseModel):
    """OSM query response"""
    num_buildings: int
    buildings: List[OSMBuilding]
    coverage_percentage: float
    bbox: List[float]

@router.get("/buildings", response_model=OSMResponse)
async def get_osm_buildings(
    min_lon: float = Query(..., description="Minimum longitude"),
    min_lat: float = Query(..., description="Minimum latitude"),
    max_lon: float = Query(..., description="Maximum longitude"),
    max_lat: float = Query(..., description="Maximum latitude")
):
    """
    Get OpenStreetMap building footprints for a bounding box
    
    - **min_lon**: Minimum longitude
    - **min_lat**: Minimum latitude
    - **max_lon**: Maximum longitude
    - **max_lat**: Maximum latitude
    """
    try:
        # Create bounding box
        bbox_geom = box(min_lon, min_lat, max_lon, max_lat)
        
        # Try to fetch buildings from OSM
        try:
            buildings_gdf = ox.geometries_from_bbox(
                max_lat, min_lat, max_lon, min_lon,
                tags={'building': True}
            )
            
            # Convert to our format
            buildings = []
            for idx, building in buildings_gdf.iterrows():
                # Extract attributes
                building_dict = {
                    'osm_id': str(idx[1]) if isinstance(idx, tuple) else str(idx),
                    'geometry': building.geometry.__geo_interface__,
                    'building_type': building.get('building', 'yes'),
                    'height': parse_height(building.get('height')),
                    'levels': parse_int(building.get('building:levels')),
                    'roof_shape': building.get('roof:shape'),
                    'roof_material': building.get('roof:material'),
                    'confidence': calculate_osm_confidence(building)
                }
                buildings.append(OSMBuilding(**building_dict))
            
            # Calculate coverage
            total_area = bbox_geom.area
            buildings_area = buildings_gdf.geometry.area.sum()
            coverage = (buildings_area / total_area) * 100 if total_area > 0 else 0
            
        except Exception as e:
            logger.warning(f"OSM query failed: {e}. Returning empty result.")
            buildings = []
            coverage = 0.0
        
        return OSMResponse(
            num_buildings=len(buildings),
            buildings=buildings,
            coverage_percentage=coverage,
            bbox=[min_lon, min_lat, max_lon, max_lat]
        )
        
    except Exception as e:
        logger.error(f"Error fetching OSM buildings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/align-footprints")
async def align_osm_footprints(
    osm_buildings: List[dict],
    satellite_metadata: dict
):
    """
    Align OSM building footprints with satellite imagery
    
    This corrects the typical 3-4m offset between OSM and satellite data
    
    - **osm_buildings**: List of OSM buildings
    - **satellite_metadata**: Metadata about the satellite image
    """
    try:
        # Extract ground sample distance and acquisition angle
        gsd = satellite_metadata.get('gsd', 0.3)  # Default 30cm/pixel
        acquisition_angle = satellite_metadata.get('acquisition_angle', 0)
        
        # Calculate offset based on acquisition angle
        # This is simplified - in production would use more sophisticated alignment
        offset_meters = 3.5  # Average offset
        offset_x = offset_meters * np.sin(np.radians(acquisition_angle))
        offset_y = offset_meters * np.cos(np.radians(acquisition_angle))
        
        # Convert to degrees (approximate)
        offset_lon = offset_x / 111320.0  # meters to degrees longitude
        offset_lat = offset_y / 110540.0  # meters to degrees latitude
        
        # Apply offset to buildings
        aligned_buildings = []
        for building in osm_buildings:
            geom = Polygon(building['geometry']['coordinates'][0])
            aligned_geom = translate(geom, xoff=offset_lon, yoff=offset_lat)
            
            building_copy = building.copy()
            building_copy['geometry'] = aligned_geom.__geo_interface__
            building_copy['alignment_offset'] = {
                'x_meters': offset_x,
                'y_meters': offset_y
            }
            aligned_buildings.append(building_copy)
        
        return {
            'num_buildings': len(aligned_buildings),
            'buildings': aligned_buildings,
            'offset_applied': {
                'x_degrees': offset_lon,
                'y_degrees': offset_lat,
                'x_meters': offset_x,
                'y_meters': offset_y
            }
        }
        
    except Exception as e:
        logger.error(f"Error aligning footprints: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generate-prompts")
async def generate_sam_prompts(
    osm_buildings: List[dict],
    image_size: List[int]  # [width, height]
):
    """
    Generate SAM point prompts from OSM building footprints
    
    - **osm_buildings**: Aligned OSM building footprints
    - **image_size**: Size of the image [width, height]
    """
    try:
        prompts = []
        
        for building in osm_buildings:
            geom = Polygon(building['geometry']['coordinates'][0])
            
            # Sample points inside the polygon
            interior_points = sample_interior_points(geom, n_points=5)
            
            # Sample points outside (negative prompts)
            exterior_points = sample_exterior_points(geom, n_points=3)
            
            prompt = {
                'building_id': building.get('osm_id'),
                'positive_points': interior_points,
                'negative_points': exterior_points,
                'confidence': building.get('confidence', 0.5)
            }
            prompts.append(prompt)
        
        return {
            'num_prompts': len(prompts),
            'prompts': prompts
        }
        
    except Exception as e:
        logger.error(f"Error generating prompts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def parse_height(height_str: Optional[str]) -> Optional[float]:
    """Parse height string from OSM"""
    if not height_str:
        return None
    try:
        # Remove units and convert to float
        height_str = height_str.replace('m', '').replace('meters', '').strip()
        return float(height_str)
    except:
        return None

def parse_int(value: Optional[str]) -> Optional[int]:
    """Parse integer from OSM"""
    if not value:
        return None
    try:
        return int(float(value))
    except:
        return None

def calculate_osm_confidence(building: dict) -> float:
    """Calculate confidence score for OSM building"""
    confidence = 0.3  # Base confidence
    
    # Increase confidence based on available attributes
    if building.get('building') != 'yes':  # Specific building type
        confidence += 0.2
    if building.get('height') or building.get('building:levels'):
        confidence += 0.15
    if building.get('roof:shape'):
        confidence += 0.15
    if building.get('roof:material'):
        confidence += 0.1
    if building.get('addr:housenumber'):  # Has address
        confidence += 0.1
    
    return min(confidence, 0.95)

def translate(geom: Polygon, xoff: float, yoff: float) -> Polygon:
    """Translate a polygon by offset"""
    from shapely.affinity import translate as shapely_translate
    return shapely_translate(geom, xoff=xoff, yoff=yoff)

def sample_interior_points(polygon: Polygon, n_points: int = 5) -> List[List[float]]:
    """Sample points inside a polygon"""
    points = []
    bounds = polygon.bounds
    
    attempts = 0
    while len(points) < n_points and attempts < n_points * 10:
        # Random point within bounds
        x = np.random.uniform(bounds[0], bounds[2])
        y = np.random.uniform(bounds[1], bounds[3])
        
        # Check if inside polygon
        if polygon.contains(Point(x, y)):
            points.append([x, y])
        
        attempts += 1
    
    return points

def sample_exterior_points(polygon: Polygon, n_points: int = 3) -> List[List[float]]:
    """Sample points outside but near a polygon"""
    from shapely.geometry import Point
    points = []
    
    # Buffer the polygon slightly
    buffer_dist = 0.00001  # ~1 meter
    buffered = polygon.buffer(buffer_dist * 3)
    
    bounds = buffered.bounds
    attempts = 0
    
    while len(points) < n_points and attempts < n_points * 10:
        # Random point within buffered bounds
        x = np.random.uniform(bounds[0], bounds[2])
        y = np.random.uniform(bounds[1], bounds[3])
        point = Point(x, y)
        
        # Check if outside original but inside buffer
        if not polygon.contains(point) and buffered.contains(point):
            points.append([x, y])
        
        attempts += 1
    
    return points