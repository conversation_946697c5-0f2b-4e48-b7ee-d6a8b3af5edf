import os
import sys
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from dotenv import load_dotenv
import logging
import time
import random

# Load environment variables
load_dotenv(Path(__file__).parent.parent.parent / "config" / ".env")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Simulate model status
models_status = {
    "sam2": "loaded",
    "roof_classifier": "loaded", 
    "swin_unet": "not_loaded"
}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup and shutdown events"""
    logger.info("Starting AI server (simplified version)...")
    logger.info("Note: This is a mock server for development. Models are simulated.")
    yield
    logger.info("Shutting down AI server...")

# Create FastAPI app
app = FastAPI(
    title="AstraMeccanica AI Server",
    description="AI inference server for roof annotation system (Development Version)",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "AstraMeccanica AI Server",
        "status": "running",
        "device": "cpu (mock)",
        "models_loaded": list(models_status.keys())
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "models": models_status
    }

@app.get("/api/models")
async def list_models():
    """List available models and their status"""
    return {
        "models": [
            {
                "name": "sam2",
                "type": "segmentation",
                "status": models_status["sam2"],
                "version": "2.0-hiera-large",
                "device": "cpu"
            },
            {
                "name": "roof_classifier",
                "type": "classification",
                "status": models_status["roof_classifier"],
                "version": "3.1-vit",
                "device": "cpu"
            },
            {
                "name": "swin_unet",
                "type": "segmentation",
                "status": models_status["swin_unet"],
                "version": "1.2-efficientnet-b5",
                "device": "cpu"
            }
        ]
    }

@app.post("/api/segmentation/segment")
async def segment_roofs(request: dict):
    """Mock roof segmentation endpoint"""
    # Simulate processing time
    time.sleep(random.uniform(0.5, 1.5))
    
    # Generate mock results
    num_roofs = random.randint(3, 8)
    roofs = []
    
    for i in range(num_roofs):
        roofs.append({
            "id": f"roof_{i+1}",
            "confidence": random.uniform(0.8, 0.95),
            "area": random.uniform(50, 200),
            "bbox": {
                "min_x": random.uniform(0, 400),
                "min_y": random.uniform(0, 400),
                "max_x": random.uniform(400, 512),
                "max_y": random.uniform(400, 512)
            },
            "polygon": [[random.uniform(0, 512), random.uniform(0, 512)] for _ in range(6)]
        })
    
    return {
        "success": True,
        "roofs_detected": num_roofs,
        "roofs": roofs,
        "processing_time": random.uniform(0.5, 1.5)
    }

@app.post("/api/classification/classify")
async def classify_roofs(request: dict):
    """Mock roof classification endpoint"""
    # Simulate processing time
    time.sleep(random.uniform(0.2, 0.5))
    
    materials = ["terracotta", "cement", "metal", "asphalt"]
    roof_types = ["gable", "hip", "flat", "shed"]
    
    return {
        "success": True,
        "classifications": [
            {
                "roof_id": roof_id,
                "material": random.choice(materials),
                "material_confidence": random.uniform(0.7, 0.95),
                "roof_type": random.choice(roof_types),
                "roof_type_confidence": random.uniform(0.7, 0.95),
                "has_solar_panels": random.choice([True, False]),
                "solar_confidence": random.uniform(0.8, 0.95)
            }
            for roof_id in request.get("roof_ids", ["roof_1"])
        ]
    }

@app.post("/api/osm/query")
async def query_osm(request: dict):
    """Mock OSM query endpoint"""
    # Simulate OSM building data
    num_buildings = random.randint(5, 15)
    
    buildings = []
    for i in range(num_buildings):
        buildings.append({
            "osm_id": f"way/{random.randint(100000, 999999)}",
            "building_type": random.choice(["yes", "residential", "commercial", "industrial"]),
            "name": f"Building {i+1}" if random.random() > 0.7 else None,
            "geometry": {
                "type": "Polygon",
                "coordinates": [[[random.uniform(9.18, 9.19), random.uniform(45.46, 45.47)] for _ in range(5)]]
            }
        })
    
    return {
        "success": True,
        "buildings": buildings,
        "total": num_buildings,
        "bbox": request.get("bbox")
    }

@app.post("/api/active-learning/select-samples")
async def select_samples(request: dict):
    """Mock active learning sample selection"""
    strategy = request.get("strategy", "uncertainty")
    num_samples = request.get("num_samples", 100)
    
    samples = []
    for i in range(num_samples):
        samples.append({
            "tile_id": f"tile_{random.randint(1000, 9999)}",
            "uncertainty_score": random.uniform(0.4, 0.9),
            "diversity_score": random.uniform(0.3, 0.8),
            "priority": random.uniform(0.5, 1.0)
        })
    
    # Sort by priority
    samples.sort(key=lambda x: x["priority"], reverse=True)
    
    return {
        "success": True,
        "strategy": strategy,
        "samples": samples,
        "total_selected": len(samples)
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)