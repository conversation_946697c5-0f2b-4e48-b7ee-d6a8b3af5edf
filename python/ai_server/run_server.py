#!/usr/bin/env python3
"""
Run the AI server with proper Python path configuration
"""
import os
import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Now import and run the main app
import uvicorn
from main import app

if __name__ == "__main__":
    print("Starting AstraMeccanica AI Server...")
    print(f"Python path: {sys.path}")
    uvicorn.run(app, host="0.0.0.0", port=8001)