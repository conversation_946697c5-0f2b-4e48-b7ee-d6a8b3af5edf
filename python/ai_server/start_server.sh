#!/bin/bash

# Start AI server script

echo "Starting AstraMeccanica AI Server..."

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install requirements
echo "Installing requirements..."
pip install -r requirements.txt

# Create models directory if it doesn't exist
mkdir -p models

# Start the server
echo "Starting FastAPI server..."
uvicorn main:app --host 0.0.0.0 --port 8001 --reload

# Note: Using port 8001 to avoid conflict with other services