import numpy as np
from PIL import Image
import io
import base64
from fastapi import UploadFile
import cv2
from typing import <PERSON>ple, Optional, List, Dict, Union
import torch
from torchvision import transforms
from shapely.geometry import Polygon, Point
import rasterio
from rasterio.windows import Window

async def decode_image(file: UploadFile) -> np.ndarray:
    """Decode uploaded image to numpy array"""
    contents = await file.read()
    image = Image.open(io.BytesIO(contents))
    
    # Convert to RGB if needed
    if image.mode != 'RGB':
        image = image.convert('RGB')
    
    # Convert to numpy array
    return np.array(image)

def encode_masks(mask: np.ndarray) -> str:
    """Encode mask as base64 string"""
    # Convert boolean mask to uint8
    if mask.dtype == bool:
        mask = mask.astype(np.uint8) * 255
    
    # Encode as PNG
    success, buffer = cv2.imencode('.png', mask)
    if success:
        return base64.b64encode(buffer).decode('utf-8')
    else:
        return ""

def resize_image(image: np.ndarray, max_size: int = 1024) -> np.ndarray:
    """Resize image if larger than max_size"""
    h, w = image.shape[:2]
    
    if max(h, w) > max_size:
        scale = max_size / max(h, w)
        new_h = int(h * scale)
        new_w = int(w * scale)
        return cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
    
    return image

def decode_mask(file):
    """Decode mask from file"""
    return decode_image(file)

async def extract_roi_from_mask(image: np.ndarray, mask: np.ndarray):
    """Extract region of interest from image using mask"""
    # Apply mask to image
    masked = image.copy()
    masked[mask == 0] = 0
    
    # Find bounding box
    rows = np.any(mask, axis=1)
    cols = np.any(mask, axis=0)
    
    if not rows.any() or not cols.any():
        return image  # Return original if mask is empty
    
    rmin, rmax = np.where(rows)[0][[0, -1]]
    cmin, cmax = np.where(cols)[0][[0, -1]]
    
    # Crop to bounding box
    return masked[rmin:rmax+1, cmin:cmax+1]

def load_image(image_path: str, target_size: Optional[Tuple[int, int]] = None) -> np.ndarray:
    """Load an image from file path"""
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")
    
    # Convert BGR to RGB
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    if target_size:
        image = cv2.resize(image, target_size)
    
    return image

def load_image_from_bytes(image_bytes: bytes, target_size: Optional[Tuple[int, int]] = None) -> np.ndarray:
    """Load an image from bytes"""
    image = Image.open(io.BytesIO(image_bytes))
    image = np.array(image)
    
    if target_size:
        image = cv2.resize(image, target_size)
    
    return image

def load_geotiff_window(geotiff_path: str, bbox: List[float]) -> Tuple[np.ndarray, Dict]:
    """Load a window from a GeoTIFF file based on geographic bbox"""
    with rasterio.open(geotiff_path) as src:
        # Convert geographic coordinates to pixel coordinates
        window = rasterio.windows.from_bounds(*bbox, src.transform)
        
        # Read the window
        image = src.read(window=window)
        
        # Get metadata
        metadata = {
            'transform': rasterio.windows.transform(window, src.transform),
            'crs': src.crs,
            'bounds': rasterio.windows.bounds(window, src.transform)
        }
        
        # Convert to RGB if needed
        if image.shape[0] >= 3:
            image = np.transpose(image[:3], (1, 2, 0))
        
        return image, metadata

def preprocess_for_sam(image: np.ndarray) -> torch.Tensor:
    """Preprocess image for SAM model"""
    # SAM expects RGB images with values in [0, 255]
    # The model will handle normalization internally
    return image

def preprocess_for_classification(image: np.ndarray) -> torch.Tensor:
    """Preprocess image for classification model"""
    # Standard ImageNet normalization
    transform = transforms.Compose([
        transforms.ToPILImage(),
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    return transform(image)

def mask_to_polygon(mask: np.ndarray, min_area: float = 10.0) -> List[Polygon]:
    """Convert binary mask to polygons"""
    contours, _ = cv2.findContours(
        mask.astype(np.uint8), 
        cv2.RETR_EXTERNAL, 
        cv2.CHAIN_APPROX_SIMPLE
    )
    
    polygons = []
    for contour in contours:
        if cv2.contourArea(contour) >= min_area:
            # Simplify contour
            epsilon = 0.01 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            if len(approx) >= 3:
                points = approx.squeeze().tolist()
                polygon = Polygon(points)
                if polygon.is_valid:
                    polygons.append(polygon)
    
    return polygons

def polygon_to_mask(polygon: Polygon, image_shape: Tuple[int, int]) -> np.ndarray:
    """Convert polygon to binary mask"""
    mask = np.zeros(image_shape[:2], dtype=np.uint8)
    
    # Get polygon coordinates
    coords = np.array(polygon.exterior.coords, dtype=np.int32)
    
    # Fill polygon
    cv2.fillPoly(mask, [coords], 1)
    
    return mask

def generate_point_prompts(polygon: Polygon, n_positive: int = 5, n_negative: int = 3) -> Dict:
    """Generate point prompts for SAM from a polygon"""
    bounds = polygon.bounds
    
    positive_points = []
    negative_points = []
    
    # Generate positive points inside polygon
    while len(positive_points) < n_positive:
        x = np.random.uniform(bounds[0], bounds[2])
        y = np.random.uniform(bounds[1], bounds[3])
        point = Point(x, y)
        if polygon.contains(point):
            positive_points.append([x, y])
    
    # Generate negative points outside polygon
    margin = 20  # pixels
    while len(negative_points) < n_negative:
        x = np.random.uniform(bounds[0] - margin, bounds[2] + margin)
        y = np.random.uniform(bounds[1] - margin, bounds[3] + margin)
        point = Point(x, y)
        if not polygon.contains(point):
            negative_points.append([x, y])
    
    return {
        'points': np.array(positive_points + negative_points),
        'labels': np.array([1] * n_positive + [0] * n_negative)
    }

def encode_image_to_base64(image: np.ndarray) -> str:
    """Encode numpy image to base64 string"""
    pil_image = Image.fromarray(image)
    buffer = io.BytesIO()
    pil_image.save(buffer, format='PNG')
    return base64.b64encode(buffer.getvalue()).decode()

def decode_base64_to_image(base64_string: str) -> np.ndarray:
    """Decode base64 string to numpy image"""
    image_bytes = base64.b64decode(base64_string)
    image = Image.open(io.BytesIO(image_bytes))
    return np.array(image)

def apply_nms_to_masks(masks: List[np.ndarray], scores: List[float], iou_threshold: float = 0.5) -> List[int]:
    """Apply Non-Maximum Suppression to masks"""
    if not masks:
        return []
    
    # Convert masks to bounding boxes for NMS
    boxes = []
    for mask in masks:
        contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours:
            x, y, w, h = cv2.boundingRect(contours[0])
            boxes.append([x, y, x + w, y + h])
        else:
            boxes.append([0, 0, 1, 1])
    
    # Apply NMS
    boxes = torch.tensor(boxes, dtype=torch.float32)
    scores = torch.tensor(scores, dtype=torch.float32)
    keep_indices = torch.ops.torchvision.nms(boxes, scores, iou_threshold)
    
    return keep_indices.tolist()

def calculate_mask_iou(mask1: np.ndarray, mask2: np.ndarray) -> float:
    """Calculate IoU between two binary masks"""
    intersection = np.logical_and(mask1, mask2).sum()
    union = np.logical_or(mask1, mask2).sum()
    
    if union == 0:
        return 0.0
    
    return intersection / union