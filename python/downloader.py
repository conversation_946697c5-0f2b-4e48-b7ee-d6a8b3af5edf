#!/usr/bin/env python3
"""
Map Tile Downloader and Compositor

Downloads map tiles from Google Maps based on bounding box coordinates and zoom level,
then combines them into a single image with georeferencing information.
"""

import os
import sys
import json
import time
import math
import urllib.request
import logging
import argparse
import io
import random
import ssl
import certifi
from pathlib import Path
from threading import Thread
from PIL import Image
import numpy as np
import cv2
from osgeo import gdal, osr

# Configure GDAL to use exceptions
gdal.UseExceptions()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger('tile_downloader')

# Constants
TILE_SIZE = 256  # Google Maps tile size in pixels

# ------------------Interchange between WGS-84 and Web Mercator-------------------------
# WGS-84 to Web Mercator
def wgs_to_mercator(x, y):
    y = 85.0511287798 if y > 85.0511287798 else y
    y = -85.0511287798 if y < -85.0511287798 else y

    x2 = x * 20037508.34 / 180
    y2 = math.log(math.tan((90 + y) * math.pi / 360)) / (math.pi / 180)
    y2 = y2 * 20037508.34 / 180
    return x2, y2

# Web Mercator to WGS-84
def mercator_to_wgs(x, y):
    x2 = x / 20037508.34 * 180
    y2 = y / 20037508.34 * 180
    y2 = 180 / math.pi * (2 * math.atan(math.exp(y2 * math.pi / 180)) - math.pi / 2)
    return x2, y2

def lat_lon_to_tile(lat, lon, zoom):
    """Convert latitude, longitude to tile coordinates at given zoom level using Leaflet's algorithm.
    This matches the tiling scheme used by Leaflet in the frontend."""
    if isinstance(lon, (int, float)) and isinstance(lat, (int, float)):
        # Constrain latitude to valid range
        lat = min(max(lat, -85.0511), 85.0511)
        
        # Same algorithm as Leaflet uses (L.CRS.EPSG3857)
        n = 2.0 ** zoom
        x_tile = math.floor((lon + 180) / 360 * n)
        y_tile = math.floor((1 - math.log(math.tan(lat * math.pi / 180) + 1 / math.cos(lat * math.pi / 180)) / math.pi) / 2 * n)
        
        return x_tile, y_tile
    else:
        raise TypeError("Longitude and latitude must be numeric values")

def get_tile_bounds(bbox, zoom):
    """Calculate tile coordinates needed to cover the bounding box."""
    min_lon, min_lat, max_lon, max_lat = bbox
    
    # Get tile coordinates for each corner
    min_x, max_y = lat_lon_to_tile(min_lat, min_lon, zoom)  # Southwest corner
    max_x, min_y = lat_lon_to_tile(max_lat, max_lon, zoom)  # Northeast corner
    
    # Add 1 to max_x and max_y to ensure we cover the entire area
    return min_x, min_y, max_x + 1, max_y + 1

def pixls_to_mercator(zb):
    """
    Get the web Mercator projection coordinates of the area based on tile coordinates
    """
    inx, iny = zb["LT"]  # left top
    inx2, iny2 = zb["RB"]  # right bottom
    length = 20037508.3427892
    sum = 2 ** zb["z"]
    LTx = inx / sum * length * 2 - length
    LTy = -(iny / sum * length * 2) + length

    RBx = (inx2 + 1) / sum * length * 2 - length
    RBy = -((iny2 + 1) / sum * length * 2) + length

    # LT=left top, RB=right bottom
    # Returns the projected coordinates of the four corners
    res = {'LT': (LTx, LTy), 'RB': (RBx, RBy),
           'LB': (LTx, RBy), 'RT': (RBx, LTy)}
    return res

def tile_to_pixls(zb):
    """
    Tile coordinates are converted to pixel coordinates of the four corners
    """
    out = {}
    width = (zb["RT"][0] - zb["LT"][0] + 1) * 256
    height = (zb["LB"][1] - zb["LT"][1] + 1) * 256
    out["LT"] = (0, 0)
    out["RT"] = (width, 0)
    out["LB"] = (0, -height)
    out["RB"] = (width, -height)
    return out

def getExtent(x1, y1, x2, y2, z, source="Google"):
    """
    Get the geographic extent of the area covered by tiles
    """
    # Ensure coordinates are not identical
    if x1 == x2:
        x2 += 0.000001  # Add a small delta to prevent identical coordinates
    if y1 == y2:
        y2 += 0.000001  # Add a small delta to prevent identical coordinates
        
    pos1x, pos1y = lat_lon_to_tile(y1, x1, z)
    pos2x, pos2y = lat_lon_to_tile(y2, x2, z)
    
    # Ensure we have distinct tile positions
    if pos1x == pos2x:
        pos2x += 1
    if pos1y == pos2y:
        pos2y += 1
        
    Xframe = pixls_to_mercator(
        {"LT": (pos1x, pos1y), "RT": (pos2x, pos1y), "LB": (pos1x, pos2y), "RB": (pos2x, pos2y), "z": z})
    for i in ["LT", "LB", "RT", "RB"]:
        Xframe[i] = mercator_to_wgs(*Xframe[i])
    return Xframe

class Downloader(Thread):
    """
    Multiple threads downloader for map tiles
    """
    def __init__(self, index, count, urls, datas):
        # index represents the thread number
        # count represents the total number of threads
        # urls represents the list of URLs to be downloaded
        # datas represents the list where to store the downloaded data
        super().__init__()
        self.urls = urls
        self.datas = datas
        self.index = index
        self.count = count
        self.failed_downloads = []
        self.logged_url = False
        self.logged_response = False

    def download(self, url):
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://www.google.com/maps/', # Add referer to mimic browser behavior
        }
        header = urllib.request.Request(url, headers=headers)
        max_retries = 5
        err = 0
        last_error = None

        # Log a maximum of one URL for debugging
        if hasattr(self, 'logged_url') and not self.logged_url:
            logger.info(f"Sample tile URL: {url}")
            self.logged_url = True
        
        while err < max_retries:
            try:
                # Add a delay between retries with exponential backoff
                if err > 0:
                    # Calculate delay with exponential backoff and jitter
                    delay = (2 ** err) + (random.random() * 0.5)
                    time.sleep(delay)

                # Create a custom SSL context that uses certifi's certificate bundle
                ssl_context = ssl.create_default_context(cafile=certifi.where())
                
                # Enable detailed debug for SSL issues if needed
                # ssl_context.check_hostname = False
                # ssl_context.verify_mode = ssl.CERT_NONE
                
                response = urllib.request.urlopen(header, timeout=10, context=ssl_context)
                
                # Log response details for debugging
                if hasattr(self, 'logged_response') and not self.logged_response:
                    self.logged_response = True
                    logger.info(f"Response code: {response.getcode()}, Content-Type: {response.info().get_content_type()}")
                
                data = response.read()
                return data
            except Exception as e:
                err += 1
                last_error = str(e)
                logger.warning(f"Download retry {err}/{max_retries} for {url}: {e}")
                # Continue to next retry

        # If we get here, all retries failed
        self.failed_downloads.append(url)
        logger.error(f"All download attempts failed for {url}: {last_error}")
        # Return a transparent 256x256 image as a placeholder
        return self._generate_placeholder_tile()
    
    def _generate_placeholder_tile(self):
        """Generate a placeholder tile for failed downloads"""
        # Create a 256x256 transparent image
        img = Image.new('RGBA', (256, 256), (255, 255, 255, 128))
        # Convert to bytes
        img_byte_arr = io.BytesIO()
        img.save(img_byte_arr, format='PNG')
        return img_byte_arr.getvalue()

    def run(self):
        for i, url in enumerate(self.urls):
            if i % self.count != self.index:
                continue
            self.datas[i] = self.download(url)

def get_url(source, x, y, z, style):
    """
    Get the URL for a specific map tile
    """
    api_key = os.environ.get('GOOGLE_MAPS_API_KEY')
    if not api_key:
        raise Exception("GOOGLE_MAPS_API_KEY environment variable not set")
    
    if source == 'Google':
        # Map style codes:
        # m = standard roadmap
        # s = satellite only
        # y = satellite with labels (hybrid)
        # t = terrain only
        # p = terrain with labels
        # h = labels only (transparent)
        
        # Use the same URL format and subdomains that Leaflet uses in the frontend
        subdomain = f"mt{x % 4}"  # Cycle through mt0, mt1, mt2, mt3 based on tile x coordinate
        url = f"https://{subdomain}.google.com/vt/lyrs={style}&x={x}&y={y}&z={z}&key={api_key}"
    else:
        raise Exception(f"Unknown Map Source: {source}")
    return url

def get_urls(x1, y1, x2, y2, z, source, style):
    """
    Get all tile URLs for the given bounding box and zoom level
    """
    # Ensure the bounding box has a reasonable minimum size
    min_area_degrees = 0.001  # Increased minimum area size in degrees
    
    # Check and adjust coordinates if they are too close or identical
    if abs(x2 - x1) < min_area_degrees:
        # If the width is too small, expand from the center
        center_x = (x1 + x2) / 2
        x1 = center_x - min_area_degrees
        x2 = center_x + min_area_degrees
    
    if abs(y2 - y1) < min_area_degrees:
        # If the height is too small, expand from the center
        center_y = (y1 + y2) / 2
        y1 = center_y - min_area_degrees
        y2 = center_y + min_area_degrees
    
    logger.info(f"Adjusted bounding box: [{x1}, {y1}, {x2}, {y2}]")
    
    # Convert bounding box to tile coordinates
    # Southwest corner
    min_tile_x, max_tile_y = lat_lon_to_tile(y1, x1, z)
    # Northeast corner
    max_tile_x, min_tile_y = lat_lon_to_tile(y2, x2, z)
    
    # Ensure we have a reasonable number of tiles (at least 5x5 grid)
    min_tiles = 5  # Minimum size in each dimension
    
    # Calculate current dimensions
    current_width = max_tile_x - min_tile_x + 1
    current_height = max_tile_y - min_tile_y + 1
    
    # Adjust if too small
    if current_width < min_tiles:
        # Calculate how many tiles to add on each side
        tiles_to_add = min_tiles - current_width
        tiles_left = tiles_to_add // 2
        tiles_right = tiles_to_add - tiles_left
        min_tile_x = max(0, min_tile_x - tiles_left)
        max_tile_x = max_tile_x + tiles_right
    
    if current_height < min_tiles:
        # Calculate how many tiles to add on each side
        tiles_to_add = min_tiles - current_height
        tiles_top = tiles_to_add // 2
        tiles_bottom = tiles_to_add - tiles_top
        min_tile_y = max(0, min_tile_y - tiles_top)
        max_tile_y = max_tile_y + tiles_bottom
    
    # Calculate dimensions in tiles
    lenx = max_tile_x - min_tile_x + 1
    leny = max_tile_y - min_tile_y + 1
    total_tiles = lenx * leny
    
    logger.info(f"Total tiles: {lenx} x {leny} = {total_tiles}")
    
    # Generate URLs for all tiles in the grid
    urls = [get_url(source, i, j, z, style) for j in range(min_tile_y, max_tile_y + 1) for i in range(min_tile_x, max_tile_x + 1)]
    return urls, min_tile_x, min_tile_y, lenx, leny

def download_tiles(urls, multi=10):
    """
    Download multiple tiles using multiple threads
    """
    url_len = len(urls)
    datas = [None] * url_len
    
    if multi < 1 or multi > 50:
        logger.warning(f"Invalid thread count {multi}, using 10 instead")
        multi = 10
        
    logger.info(f"Downloading {url_len} tiles using {multi} threads")
    
    tasks = [Downloader(i, multi, urls, datas) for i in range(multi)]
    
    # Start all download threads
    for i in tasks:
        i.start()
    
    # Wait for all threads to complete
    for i in tasks:
        i.join()
    
    # Check for failed downloads
    failed_downloads = []
    for task in tasks:
        failed_downloads.extend(task.failed_downloads)
    
    if failed_downloads:
        logger.warning(f"{len(failed_downloads)} tiles failed to download but were replaced with placeholders")
    
    return datas

def merge_tiles(datas, lenx, leny):
    """
    Merge tile data into a single image
    """
    outpic = Image.new('RGBA', (lenx * 256, leny * 256), (0, 0, 0, 0))
    for i, data in enumerate(datas):
        if data is None: continue
        try:
            picio = io.BytesIO(data)
            small_pic = Image.open(picio)
            y, x = i // lenx, i % lenx
            outpic.paste(small_pic, (x * 256, y * 256))
        except Exception as e:
            logger.error(f"Error merging tile {i}: {e}")
    return outpic

def saveTiff(r, g, b, gt, filePath):
    """
    Save RGB arrays as a GeoTIFF
    """
    driver = gdal.GetDriverByName('GTiff')
    # Create a 3-band dataset
    dset_output = driver.Create(filePath, r.shape[1], r.shape[0], 3, gdal.GDT_Byte)
    dset_output.SetGeoTransform(gt)
    try:
        proj = osr.SpatialReference()
        proj.ImportFromEPSG(4326)
        dset_output.SetSpatialRef(proj)
    except Exception as e:
        logger.error(f"Error setting coordinate system: {e}")
    dset_output.GetRasterBand(1).WriteArray(r)
    dset_output.GetRasterBand(2).WriteArray(g)
    dset_output.GetRasterBand(3).WriteArray(b)
    dset_output.FlushCache()
    dset_output = None

def create_world_file(image_path, bbox, zoom, tile_bounds):
    """Create a world file for georeferencing."""
    min_lon, min_lat, max_lon, max_lat = bbox
    min_x, min_y, max_x, max_y = tile_bounds
    
    # Calculate pixel size
    width_px = max(1, (max_x - min_x) * TILE_SIZE)
    height_px = max(1, (max_y - min_y) * TILE_SIZE)
    
    # Handle case where coordinates are identical or very close
    lon_diff = max(0.000001, max_lon - min_lon)  # Prevent division by zero
    lat_diff = max(0.000001, max_lat - min_lat)  # Ensure minimum difference
    
    lon_per_pixel = lon_diff / width_px
    lat_per_pixel = lat_diff / height_px
    
    # World file format (for JPEG it's .jgw or .jpgw)
    # Line 1: pixel size in x-direction (lon)
    # Line 2: rotation term (0)
    # Line 3: rotation term (0)
    # Line 4: pixel size in y-direction (negative lat as y increases downward)
    # Line 5: x-coordinate of center of upper left pixel (min_lon)
    # Line 6: y-coordinate of center of upper left pixel (max_lat)
    
    world_file_path = image_path.replace('.jpg', '.jgw').replace('.jpeg', '.jgw').replace('.tif', '.tfw')
    
    with open(world_file_path, 'w') as f:
        f.write(f"{lon_per_pixel}\n")  # x-scale
        f.write("0.0\n")  # rotation
        f.write("0.0\n")  # rotation
        f.write(f"{-lat_per_pixel}\n")  # negative y-scale
        f.write(f"{min_lon}\n")  # top-left x
        f.write(f"{max_lat}\n")  # top-left y
    
    # Also create a simple metadata file
    metadata_path = image_path + '.meta'
    with open(metadata_path, 'w') as f:
        metadata = {
            'bbox': bbox,
            'zoom': zoom,
            'created': time.strftime('%Y-%m-%d %H:%M:%S'),
            'projection': 'EPSG:4326',  # WGS84
            'tile_bounds': {
                'min_x': min_x,
                'min_y': min_y,
                'max_x': max_x,
                'max_y': max_y
            }
        }
        json.dump(metadata, f, indent=2)

def create_composite_image(bbox, zoom, style, output_path, server="Google", threads=10):
    """
    Create a composite image from tiles downloaded in parallel
    """
    min_lon, min_lat, max_lon, max_lat = bbox
    
    # Get URLs and tile parameters
    urls, pos1x, pos1y, lenx, leny = get_urls(min_lon, min_lat, max_lon, max_lat, zoom, server, style)
    
    # Calculate total number of tiles
    total_tiles = lenx * leny
    logger.info(f"Creating composite image from {total_tiles} tiles ({lenx}x{leny})")
    
    # Generate URLs for all tiles and download them in parallel
    datas = download_tiles(urls, multi=threads)
    
    # Merge tiles into a single image
    outpic = merge_tiles(datas, lenx, leny)
    logger.info("Tiles merged into composite image")
    
    # Convert to RGB for GeoTIFF
    outpic = outpic.convert('RGB')
    r, g, b = cv2.split(np.array(outpic))
    
    # Get extent information
    extent = getExtent(min_lon, min_lat, max_lon, max_lat, zoom)
    
    # Calculate geotransform
    top_left_x_wgs = extent['LT'][0]
    top_left_y_wgs = extent['LT'][1]
    
    # Ensure we don't have zero pixel size (preventing division by zero)
    width_px = max(1, lenx * 256)
    height_px = max(1, leny * 256)
    
    pixel_width_deg = (extent['RB'][0] - extent['LT'][0]) / width_px
    pixel_height_deg = (extent['RB'][1] - extent['LT'][1]) / height_px  # Will be negative
    
    gt = (top_left_x_wgs, pixel_width_deg, 0, top_left_y_wgs, 0, pixel_height_deg)
    
    # Save as GeoTIFF
    saveTiff(r, g, b, gt, output_path)
    logger.info(f"Image saved to {output_path}")
    
    # Create world file for additional compatibility
    tile_bounds = (pos1x, pos1y, pos1x + lenx - 1, pos1y + leny - 1)
    create_world_file(output_path, bbox, zoom, tile_bounds)
    
    return output_path

def main():
    """Main entry point for the script"""
    parser = argparse.ArgumentParser(description='Download and compose map tiles.')
    parser.add_argument('--left', type=float, required=True, help='Left longitude (min longitude)')
    parser.add_argument('--bottom', type=float, required=True, help='Bottom latitude (min latitude)')
    parser.add_argument('--right', type=float, required=True, help='Right longitude (max longitude)')
    parser.add_argument('--top', type=float, required=True, help='Top latitude (max latitude)')
    parser.add_argument('--zoom', type=int, required=True, help='Zoom level (0-21)')
    parser.add_argument('--output', type=str, required=True, help='Output file path (.tif)')
    parser.add_argument('--style', type=str, default='s', 
                        choices=['m', 's', 'y', 't', 'p', 'h'], 
                        help='Map style: m=roadmap, s=satellite, y=hybrid, t=terrain, p=terrain+labels, h=labels')
    parser.add_argument('--threads', type=int, default=10, help='Number of download threads (1-50)')
    
    args = parser.parse_args()
    
    # Validate zoom level
    if args.zoom < 0 or args.zoom > 21:
        logger.error("Zoom level must be between 0 and 21")
        sys.exit(1)
        
    # Ensure output directory exists
    output_dir = os.path.dirname(args.output)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # Ensure the output file has the correct extension
    if not args.output.lower().endswith('.tif'):
        args.output += '.tif'
    
    # Set up bounding box as [min_lon, min_lat, max_lon, max_lat]
    bbox = [args.left, args.bottom, args.right, args.top]
    
    try:
        start_time = time.time()
        logger.info(f"Starting download at {time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Downloading area: {bbox} at zoom level {args.zoom}")
        
        # Create the composite image
        output_path = create_composite_image(
            bbox, args.zoom, args.style, args.output, "Google", args.threads
        )
        
        total_time = time.time() - start_time
        logger.info(f"Process completed in {total_time:.2f} seconds")
        print(f"Output file: {output_path}")
        
    except Exception as e:
        logger.error(f"Error in main function: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()