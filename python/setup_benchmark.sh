#!/bin/bash

echo "==========================================="
echo "Setup for ML Benchmark on Ryzen Z1 Extreme"
echo "==========================================="

# Check Python version
python_version=$(python3 --version 2>&1 | grep -Po '(?<=Python )\d+\.\d+')
echo "Python version: $python_version"

# Create virtual environment
echo "Creating virtual environment..."
python3 -m venv benchmark_env
source benchmark_env/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install CPU-optimized packages
echo "Installing CPU-optimized packages..."
pip install numpy scipy scikit-learn opencv-python-headless psutil

# Install PyTorch for AMD CPU
echo "Installing PyTorch (CPU optimized)..."
# For AMD Ryzen, use CPU-optimized version
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# Optional: Install AMD ROCm version if you have AMD GPU
# Check if ROCm is available
if command -v rocm-smi &> /dev/null; then
    echo "ROCm detected! Installing PyTorch with ROCm support..."
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/rocm5.6
fi

# Install additional ML libraries
echo "Installing additional ML libraries..."
pip install pillow matplotlib tqdm pandas

echo ""
echo "==========================================="
echo "Setup complete!"
echo "==========================================="
echo ""
echo "To run the benchmark:"
echo "  1. Activate the environment: source benchmark_env/bin/activate"
echo "  2. Run the benchmark: python3 benchmark_ml.py"
echo ""
echo "For Windows users with Ryzen Z1 Extreme:"
echo "  - Make sure to install Visual C++ redistributables"
echo "  - Consider using Anaconda for easier setup"
echo "  - If you have integrated AMD graphics, ROCm might not be supported"
echo ""