#!/bin/bash

echo "=== TESTING SAM INTEGRATION ==="

# Test SAM server directly
echo "1. Testing SAM server health..."
curl -s http://*************:8080/health | python3 -m json.tool

# Test backend proxy
echo -e "\n2. Testing backend proxy health..."
curl -s http://localhost:5000/api/sam/local/health | python3 -m json.tool

# Test status endpoint
echo -e "\n3. Testing overall SAM status..."
curl -s http://localhost:5000/api/sam/status | python3 -m json.tool

echo -e "\n=== FRONTEND ACCESS ==="
echo "Navigate to: http://localhost:5173/sam-segmentation"
echo "Upload an image and click to segment!"